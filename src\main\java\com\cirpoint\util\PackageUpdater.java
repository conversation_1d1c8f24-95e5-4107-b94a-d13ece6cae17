package com.cirpoint.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Properties;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

/**
 * 更新包制作工具
 * <p>
 * 功能说明：
 * 本工具用于自动化制作系统更新包，主要包含以下功能：
 * 1. 导出Thingworx实体配置
 * 2. 收集更新的文件
 * 3. 执行Maven打包
 * 4. 复制构建产物
 * <p>
 * 详细流程：
 * 1. 导出Thingworx实体配置
 * - 从配置文件读取上次更新时间
 * - 调用Thingworx API获取所有实体清单
 * - 根据lastModifiedDate筛选需要导出的实体
 * - 将筛选出的实体配置导出为XML文件
 * - 保存到指定目录：[TOMCAT_WEBAPP_PATH]/twx/twxXml/
 * <p>
 * 2. 收集更新的文件
 * - 遍历TOMCAT_WEBAPP_PATH目录下的所有文件
 * - 排除.svn目录下的所有文件
 * - 根据文件的最后修改时间，筛选出在上次更新之后修改的文件
 * - 保持目录结构复制到更新包目录
 * <p>
 * 3. 执行Maven打包
 * - 检查Maven环境变量（MAVEN_HOME或M2_HOME）
 * - 执行mvn package命令（跳过测试）
 * - 实时输出构建日志
 * <p>
 * 4. 复制构建产物
 * - 复制target目录下的构建产物到更新包目录
 * - 保持目录结构
 * <p>
 * 5. 复制部署相关文件
 * - 复制部署所需的配置文件和脚本
 * - 确保更新包包含所有必要的部署文件
 * <p>
 * 配置说明：
 * - TOMCAT_WEBAPP_PATH：Tomcat的webapps目录路径
 * - UPDATE_PACKAGE_BASE_PATH：更新包存放的基础路径
 * - PROJECT_PATH：项目根目录
 * - TWX_DOMAIN：Thingworx服务器地址
 * - TWX_APPKEY：Thingworx应用密钥
 * <p>
 * 使用说明：
 * 1. 确保已正确设置所有配置项
 * 2. 确保Maven环境变量已正确配置
 * 3. 确保Thingworx服务器可访问且AppKey有效
 * 4. 运行程序，等待更新包制作完成
 * 5. 更新包将保存在[UPDATE_PACKAGE_BASE_PATH]/[当前日期]/目录下
 * <p>
 * 注意事项：
 * 1. 每次运行后会自动更新配置文件中的lastUpdateDate
 * 2. 如需重新生成某个时间段的更新包，可手动修改配置文件中的lastUpdateDate
 * 3. 更新包目录如果已存在会被自动删除并重新创建
 * 4. 所有操作都会记录到日志文件中，方便追踪和排错
 */
public class PackageUpdater {
	private static final String TOMCAT_WEBAPP_PATH = "D:\\soft\\dev\\Tomcat8.5\\webapps\\DataPackageManagement";
	private static final String UPDATE_PACKAGE_BASE_PATH = "F:\\01 项目\\15 812二期\\部署更新包";
	private static final String PROJECT_PATH = "D:\\eclipse-work\\FileHandle\\";
	private static final String UPDATE_PROPERTIES_PATH = PROJECT_PATH + "\\src\\main\\resources\\update.properties";
	private static final String TWX_XML_PATH = TOMCAT_WEBAPP_PATH + "\\twx\\_xmls\\";
	private static final String TWX_DOMAIN = "http://twx:8011";
	private static final String TWX_APPKEY = "8ea8ace9-1167-4417-939f-a7ddc58d9429";
	private static final String MAVEN_REPOSITORY_PATH = "D:\\soft\\dev\\apache-maven-3.9.9\\repository";

	// AES加密密钥（16字节）
	private static final String ENCRYPT_KEY = "CirPoint2025Key!";
	private static final String ENCRYPT_ALGORITHM = "AES";

	private static PrintWriter logWriter;
	private static String updatePackagePath;

	public static void main(String[] args) throws IOException, ParseException {
		update();
	}

	public static void update() {
		try {
			// 创建以当前日期命名的更新包目录
			String currentDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
			updatePackagePath = UPDATE_PACKAGE_BASE_PATH + File.separator + currentDate;
			FileUtil.del(updatePackagePath);
			Files.createDirectories(Paths.get(updatePackagePath));

			// 初始化日志文件（放在更新包目录外）
			File logFile = new File(UPDATE_PACKAGE_BASE_PATH, "update_" + currentDate + ".log");
			logWriter = new PrintWriter(new FileWriter(logFile), true);

			// 开始执行更新流程
			log("开始执行更新包制作流程...");

			// 读取上次更新日期
			String lastUpdateDate = getLastUpdateDate();
			log("读取到上次更新日期: " + lastUpdateDate);

			// 下载Thingworx实体XML文件
			downloadThingworxEntities(lastUpdateDate);

			// 1. 收集更新的文件
			collectUpdatedFiles(lastUpdateDate);

			// 1.1 收集更新的Maven仓库文件
			copyUpdatedMavenRepositoryFiles(lastUpdateDate);

			// 2. 执行Maven打包
			executeMavenBuild();

			// 3. 复制构建产物
			copyBuildArtifacts();

			// 4. 复制源代码文件
			copySrcFiles();

			// 5. 编译Deploy.java
			compileDeployJava();

			// 5. 复制部署相关文件
			copyDeploymentFiles();

			// 更新最后更新日期
			updateLastUpdateDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));

			// 将更新包打包为zip
			createZipPackage();

			log("更新包制作完成，路径：" + updatePackagePath);

		} catch (Exception e) {
			logError("更新包制作过程中发生错误：" + e.getMessage());
			e.printStackTrace(logWriter);
			System.exit(1);
		} finally {
			if (logWriter != null) {
				logWriter.close();
			}
		}
	}

	private static void collectUpdatedFiles(String lastUpdateDate) throws IOException, ParseException {
		log("开始收集自 " + lastUpdateDate + " 以来更新的文件...");

		Path sourcePath = Paths.get(TOMCAT_WEBAPP_PATH);
		if (!Files.exists(sourcePath)) {
			throw new IOException("源目录不存在：" + TOMCAT_WEBAPP_PATH);
		}

		final long lastUpdateTime = new SimpleDateFormat("yyyy-MM-dd").parse(lastUpdateDate).getTime();

		Files.walkFileTree(sourcePath, new SimpleFileVisitor<Path>() {
			@Override
			public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) {
				String dirName = dir.getFileName().toString();
				// 如果是.svn目录或以点号开头的目录，跳过该目录及其所有子目录
				if (dirName.equals(".svn") || dirName.startsWith(".")) {
					log("跳过目录：" + dir);
					return FileVisitResult.SKIP_SUBTREE;
				}

				// 检查路径中是否包含ai-docs或docs目录
				Path relativePath = sourcePath.relativize(dir);
				String relativePathStr = relativePath.toString();
				String separator = File.separator;
				if (relativePathStr.contains(separator + "ai-docs") || relativePathStr.contains(separator + "docs") ||
						dirName.equals("ai-docs") || dirName.equals("docs")) {
					log("跳过文档目录：" + dir);
					return FileVisitResult.SKIP_SUBTREE;
				}

				return FileVisitResult.CONTINUE;
			}

			@Override
			public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
				// 如果文件在.svn目录下或者在以点号开头的目录下，跳过
				if (file.toString().contains("\\.svn\\") || file.toString().matches(".*\\\\\\.[^\\\\]+\\\\.*")) {
					return FileVisitResult.CONTINUE;
				}

				// 检查是否是需要忽略的特定文件
				String fileName = file.getFileName().toString();
				if (fileName.equals(".cursorignore") || fileName.equals(".cursorindexingignore") || fileName.equals(".gitignore")) {
					log("跳过忽略文件：" + fileName);
					return FileVisitResult.CONTINUE;
				}

				if (attrs.lastModifiedTime().toMillis() > lastUpdateTime) {
					// 获取相对路径
					Path relativePath = sourcePath.relativize(file);
					Path targetPath = Paths.get(updatePackagePath, "DataPackageManagement", relativePath.toString());

					// 创建目标目录
					Files.createDirectories(targetPath.getParent());

					// 复制文件
					Files.copy(file, targetPath, StandardCopyOption.REPLACE_EXISTING);
					log("收集文件：" + relativePath);
				}
				return FileVisitResult.CONTINUE;
			}
		});

		log("文件收集完成");
	}

	/**
	 * 复制Maven仓库中自上次更新以来的文件
	 */
	private static void copyUpdatedMavenRepositoryFiles(String lastUpdateDate) throws IOException, ParseException {
		log("开始收集Maven仓库中自 " + lastUpdateDate + " 以来更新的文件...");

		Path sourcePath = Paths.get(MAVEN_REPOSITORY_PATH);
		if (!Files.exists(sourcePath)) {
			throw new IOException("Maven仓库目录不存在：" + MAVEN_REPOSITORY_PATH);
		}

		// 创建目标目录
		Path targetBasePath = Paths.get(updatePackagePath, "repository");
		Files.createDirectories(targetBasePath);

		// 解析上次更新时间
		final long lastUpdateTime = new SimpleDateFormat("yyyy-MM-dd").parse(lastUpdateDate).getTime();

		// 文件计数器
		final int[] fileCount = {0};

		Files.walkFileTree(sourcePath, new SimpleFileVisitor<Path>() {
			@Override
			public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
				// 只处理lastUpdateDate之后修改的文件
				if (attrs.lastModifiedTime().toMillis() > lastUpdateTime) {
					// 获取相对路径
					Path relativePath = sourcePath.relativize(file);
					Path targetPath = targetBasePath.resolve(relativePath);

					// 创建目标目录
					Files.createDirectories(targetPath.getParent());

					// 复制文件
					Files.copy(file, targetPath, StandardCopyOption.REPLACE_EXISTING);
					log("收集Maven仓库文件：" + relativePath);
					fileCount[0]++;
				}
				return FileVisitResult.CONTINUE;
			}
		});

		log("Maven仓库文件收集完成，共收集 " + fileCount[0] + " 个文件");
	}

	private static void executeMavenBuild() throws IOException, InterruptedException {
		log("开始执行Maven打包...");

		String mavenPath = System.getenv("MAVEN_HOME");
		if (mavenPath == null || mavenPath.isEmpty()) {
			mavenPath = System.getenv("M2_HOME");
		}

		Process process = getProcess(mavenPath);

		// 实时输出构建日志
		try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
			String line;
			while ((line = reader.readLine()) != null) {
				log(line);
			}
		}

		int exitCode = process.waitFor();
		if (exitCode != 0) {
			throw new IOException("Maven构建失败，退出码：" + exitCode);
		}

		log("Maven打包完成");
	}

	private static Process getProcess(String mavenPath) throws IOException {
		if (mavenPath == null || mavenPath.isEmpty()) {
			throw new IOException("未找到Maven环境变量，请确保设置了MAVEN_HOME或M2_HOME环境变量");
		}

		String mvnCmd = mavenPath + "\\bin\\mvn.cmd";
		if (!new File(mvnCmd).exists()) {
			throw new IOException("Maven执行文件不存在：" + mvnCmd);
		}

		ProcessBuilder pb = new ProcessBuilder(mvnCmd, "package", "-DskipTests");
		pb.directory(new File(PROJECT_PATH));
		pb.redirectErrorStream(true);
		return pb.start();
	}

	private static void copyBuildArtifacts() throws IOException {
		log("开始复制构建产物...");

		// 创建file-handle目录
		Path fileHandleDir = Paths.get(updatePackagePath, "file-handle");
		Files.createDirectories(fileHandleDir);
		Path libDir = fileHandleDir.resolve("lib");
		Files.createDirectories(libDir);

		// 获取上次更新时间
		String lastUpdateDate = getLastUpdateDate();
		long lastUpdateTime;
		try {
			lastUpdateTime = new SimpleDateFormat("yyyy-MM-dd").parse(lastUpdateDate).getTime();
		} catch (ParseException e) {
			throw new IOException("解析上次更新时间失败", e);
		}

		// 复制JAR文件
		Path jarSource = Paths.get(PROJECT_PATH, "target", "build", "file-handle.jar");
		Path jarTarget = Paths.get(fileHandleDir.toString(), "file-handle.jar");
		Files.copy(jarSource, jarTarget, StandardCopyOption.REPLACE_EXISTING);
		log("已复制：file-handle.jar");

		// 复制startup-prod.bat文件
		Path startupBatSource = Paths.get(PROJECT_PATH, "target", "build", "startup-prod.bat");
		Path startupBatTarget = Paths.get(fileHandleDir.toString(), "startup-prod.bat");
		Files.copy(startupBatSource, startupBatTarget, StandardCopyOption.REPLACE_EXISTING);
		log("已复制：startup-prod.bat");

		// 复制lib目录下的jar包
		Path libSource = Paths.get(PROJECT_PATH, "target", "build", "lib");
		if (Files.exists(libSource)) {
			Files.walkFileTree(libSource, new SimpleFileVisitor<Path>() {
				@Override
				public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
					if (file.toString().endsWith(".jar")) {
						// 检查文件的最后修改时间
						if (attrs.lastModifiedTime().toMillis() > lastUpdateTime) {
							Path relativePath = libSource.relativize(file);
							Path targetPath = libDir.resolve(relativePath);
							Files.createDirectories(targetPath.getParent());
							Files.copy(file, targetPath, StandardCopyOption.REPLACE_EXISTING);
							log("已复制新增/更新的jar包：" + relativePath);
						}
					}
					return FileVisitResult.CONTINUE;
				}
			});
		}

		// 复制resources目录
		Path resourcesSource = Paths.get(PROJECT_PATH, "target", "build", "resources");
		Path resourcesTarget = Paths.get(fileHandleDir.toString(), "resources");
		copyDirectory(resourcesSource, resourcesTarget);
		log("已复制：resources目录");

		// 加密部署目录中包含"合格证"的docx文件
		encryptCertificateFiles(resourcesTarget);
	}

	/**
	 * 复制源代码文件
	 * 将项目的源代码文件和配置文件复制到更新包目录的FileHandle目录下
	 * 复制所有源代码文件，不进行时间戳过滤
	 */
	private static void copySrcFiles() throws IOException, ParseException {
		log("开始复制源代码文件和项目配置文件...");

		// 移除基于lastUpdateDate的过滤条件，确保复制所有源代码文件
		log("复制所有源代码文件，不进行时间戳过滤");

		// 验证src目录是否存在
		final Path srcPath = Paths.get(PROJECT_PATH, "src");
		if (!Files.exists(srcPath)) {
			throw new IOException("源代码目录不存在：" + srcPath);
		}

		// 创建FileHandle目录
		final Path fileHandleDir = Paths.get(updatePackagePath, "FileHandle");
		Files.createDirectories(fileHandleDir);

		// 文件计数器
		final int[] fileCount = {0};

		// 使用walkFileTree复制src目录下所有文件到FileHandle
		Files.walkFileTree(srcPath, new SimpleFileVisitor<Path>() {
			@Override
			public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
				// 获取相对路径
				Path relativePath = srcPath.relativize(dir);
				Path targetDir = Paths.get(fileHandleDir.toString(), "src").resolve(relativePath);
				Files.createDirectories(targetDir);
				return FileVisitResult.CONTINUE;
			}

			@Override
			public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
				// 复制所有文件，不进行时间戳过滤
				// 获取相对路径
				Path relativePath = srcPath.relativize(file);
				Path targetPath = Paths.get(fileHandleDir.toString(), "src").resolve(relativePath);

				// 创建目标目录
				Files.createDirectories(targetPath.getParent());

				// 复制文件
				Files.copy(file, targetPath, StandardCopyOption.REPLACE_EXISTING);
				fileCount[0]++;

				if (fileCount[0] <= 10 || fileCount[0] % 100 == 0) {
					log("复制到FileHandle: " + relativePath);
				}
				return FileVisitResult.CONTINUE;
			}
		});

		log("已复制 " + fileCount[0] + " 个源代码文件到FileHandle目录");

		// 复制单个文件
		// 定义需要复制的文件列表
		String[] filesToCopy = {
				"pom.xml",
				"readme.md",
				"startup-prod.bat"
		};

		// 复制每个文件（不进行时间戳过滤）
		int configFilesCount = 0;
		for (String fileName : filesToCopy) {
			if (copyFileWithoutTimeFilter(fileName, fileHandleDir)) {
				configFilesCount++;
			}
		}

		log("源代码文件和项目配置文件复制完成，共复制 " + (fileCount[0] + configFilesCount) + " 个文件");

		// 加密FileHandle源码中的合格证文件
		log("开始加密FileHandle源码中的合格证文件...");
		Path srcResourcesTarget = Paths.get(fileHandleDir.toString(), "src", "main", "resources");
		encryptCertificateFiles(srcResourcesTarget);
	}

	/**
	 * 复制单个文件（如果文件已更新或为必要文件）
	 *
	 * @param fileName       文件名
	 * @param targetDir      目标目录
	 * @param lastUpdateTime 上次更新时间
	 * @return 是否成功复制了文件
	 */
	private static boolean copyFileIfUpdated(String fileName, Path targetDir, long lastUpdateTime) throws IOException {
		Path sourcePath = Paths.get(PROJECT_PATH, fileName);
		Path targetPath = Paths.get(targetDir.toString(), fileName);

		if (Files.exists(sourcePath)) {
			BasicFileAttributes attrs = Files.readAttributes(sourcePath, BasicFileAttributes.class);
			if (attrs.lastModifiedTime().toMillis() > lastUpdateTime) {
				Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
				log("已复制：" + fileName + "到FileHandle目录（文件已更新）");
			} else {
				// 即使文件没有更新，也复制它，因为它是项目必要的文件
				Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
				log("已复制：" + fileName + "到FileHandle目录（文件未更新，但为必要文件）");
			}
			return true;
		} else {
			logError(fileName + "文件不存在：" + sourcePath);
			return false;
		}
	}

	/**
	 * 复制单个文件（不进行时间戳过滤）
	 *
	 * @param fileName  文件名
	 * @param targetDir 目标目录
	 * @return 是否成功复制了文件
	 */
	private static boolean copyFileWithoutTimeFilter(String fileName, Path targetDir) throws IOException {
		Path sourcePath = Paths.get(PROJECT_PATH, fileName);
		Path targetPath = Paths.get(targetDir.toString(), fileName);

		if (Files.exists(sourcePath)) {
			Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
			log("已复制：" + fileName + "到FileHandle目录");
			return true;
		} else {
			logError(fileName + "文件不存在：" + sourcePath);
			return false;
		}
	}

	private static void copyDeploymentFiles() throws IOException {
		log("开始复制部署相关文件...");

		String[] deployFiles = {
				"deploy/Deploy.java",
				"deploy/一键更新部署.bat",
				"deploy/Deploy.class"
		};

		for (String file : deployFiles) {
			Path source = Paths.get(TOMCAT_WEBAPP_PATH, file);
			Path target = Paths.get(updatePackagePath, Paths.get(file).getFileName().toString());
			Files.copy(source, target, StandardCopyOption.REPLACE_EXISTING);
			log("已复制：" + file);
		}
	}

	private static void copyDirectory(Path source, Path target) throws IOException {
		Files.walkFileTree(source, new SimpleFileVisitor<Path>() {
			@Override
			public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
				Path targetDir = target.resolve(source.relativize(dir));
				Files.createDirectories(targetDir);
				return FileVisitResult.CONTINUE;
			}

			@Override
			public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
				Files.copy(file, target.resolve(source.relativize(file)), StandardCopyOption.REPLACE_EXISTING);
				return FileVisitResult.CONTINUE;
			}
		});
	}

	/**
	 * 加密部署目录中包含"合格证"的docx文件
	 *
	 * @param resourcesTarget resources目录的目标路径
	 */
	private static void encryptCertificateFiles(Path resourcesTarget) {
		try {
			log("开始加密包含'合格证'的docx文件...");

			// 构建templates目录路径
			Path templatesDir = resourcesTarget.resolve("static").resolve("templates");

			if (!Files.exists(templatesDir)) {
				log("templates目录不存在，跳过加密操作：" + templatesDir);
				return;
			}

			// 遍历templates目录，查找包含"合格证"的docx文件
			Files.walkFileTree(templatesDir, new SimpleFileVisitor<Path>() {
				@Override
				public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
					String fileName = file.getFileName().toString();
					// 检查文件名是否包含"合格证"且为docx文件
					if (fileName.contains("合格证") && fileName.toLowerCase().endsWith(".docx")) {
						try {
							// 加密文件
							encryptFile(file);
							log("已加密合格证文件：" + fileName);
						} catch (Exception e) {
							logError("加密合格证文件失败：" + fileName + "，错误：" + e.getMessage());
						}
					}
					return FileVisitResult.CONTINUE;
				}
			});

			log("合格证文件加密操作完成");

		} catch (IOException e) {
			logError("加密合格证文件过程中发生错误：" + e.getMessage());
		}
	}

	private static String getLastUpdateDate() throws IOException {
		Properties props = new Properties();
		try (FileInputStream fis = new FileInputStream(UPDATE_PROPERTIES_PATH)) {
			props.load(fis);
			return props.getProperty("last.update.date", "2025-02-10");
		}
	}

	private static void updateLastUpdateDate(String date) throws IOException {
		Properties props = new Properties();
		// 如果文件存在，先加载现有配置
		if (Files.exists(Paths.get(UPDATE_PROPERTIES_PATH))) {
			try (FileInputStream fis = new FileInputStream(UPDATE_PROPERTIES_PATH)) {
				props.load(fis);
			}
		}
		// 更新日期
		props.setProperty("last.update.date", date);
		// 保存配置
		try (FileOutputStream fos = new FileOutputStream(UPDATE_PROPERTIES_PATH)) {
			props.store(fos, "Last update date");
		}
		log("更新最后更新日期为: " + date);
	}

	private static void log(String message) {
		String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
		String logMessage = String.format("[%s] %s", timestamp, message);
		System.out.println(logMessage);
		if (logWriter != null) {
			logWriter.println(logMessage);
		}
	}

	private static void logError(String message) {
		String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
		String logMessage = String.format("[%s] ERROR: %s", timestamp, message);
		System.err.println(logMessage);
		if (logWriter != null) {
			logWriter.println(logMessage);
		}
	}

	/**
	 * 下载Thingworx实体XML文件
	 */
	private static void downloadThingworxEntities(String lastUpdateDate) throws ParseException, IOException {
		log("开始下载Thingworx实体XML文件...");

		// 确保目标目录存在
		FileUtil.mkdir(TWX_XML_PATH);

		// 获取实体清单
		String url = TWX_DOMAIN + "/Thingworx/Resources/SearchFunctions/Services/SpotlightSearch?method=POST&Accept=application/json&appKey=" + TWX_APPKEY;
		String requestBody = "{\"searchExpression\":\"**\",\"withPermissions\":true,\"sortBy\":\"lastModifiedDate\"," +
				"\"isAscending\":false,\"searchDescriptions\":true,\"aspects\":{\"isSystemObject\":false}," +
				"\"types\":{\"items\":[\"Thing\",\"ThingShape\",\"ThingTemplate\"]},\"projectName\":\"\",\"searchText\":\"\",\"showSystemObjects\":false," +
				"\"tags\":[],\"thingShapes\":{},\"thingTemplates\":{}}";

		try {
			log("发送请求到URL: " + url);

			// 发送请求获取实体清单
			String result = HttpUtil.createPost(url)
					.header("accept", "application/json")
					.header("content-type", "application/json")
					.header("x-requested-with", "XMLHttpRequest")
					.body(requestBody)
					.execute()
					.body();


			if (result == null || result.trim().isEmpty()) {
				throw new IOException("服务器返回空响应");
			}

			JSONObject json = JSONUtil.parseObj(result);

			JSONArray rows = json.getJSONArray("rows");
			if (rows == null) {
				throw new IOException("未找到rows数组");
			}

			log("获取到" + rows.size() + "个实体");

			// 解析lastUpdateDate
			long lastUpdateTimestamp = new SimpleDateFormat("yyyy-MM-dd").parse(lastUpdateDate).getTime();
			log("上次更新时间戳: " + lastUpdateTimestamp);

			int updatedCount = 0;
			// 遍历实体清单
			for (int i = 0; i < rows.size(); i++) {
				JSONObject entity = rows.getJSONObject(i);
				if (entity == null) {
					log("警告: 第" + i + "个实体为空，跳过");
					continue;
				}

				Long entityLastModified = entity.getLong("lastModifiedDate");
				if (entityLastModified == null) {
					log("警告: 实体没有lastModifiedDate字段，跳过");
					continue;
				}

				// 只处理在lastUpdateDate之后修改的实体
				if (entityLastModified > lastUpdateTimestamp) {
					String entityName = entity.getStr("name");
					String entityType = entity.getStr("type");

					if (entityName == null || entityType == null) {
						log("警告: 实体名称或类型为空，跳过");
						continue;
					}
					// 下载实体的XML文件
					downloadEntityXml(entityName, entityType);
					updatedCount++;
				}
			}

			log("成功处理 " + updatedCount + " 个更新的实体");

		} catch (Exception e) {
			log("下载Thingworx实体时发生错误：" + e.getMessage());
			e.printStackTrace(logWriter);
			throw e;
		}

		log("Thingworx实体XML文件下载完成");
	}

	/**
	 * 下载单个实体的XML文件
	 */
	private static void downloadEntityXml(String entityName, String entityType) {
		try {
			String url = String.format("%s/Thingworx/Exporter/%ss/%s?forSourceControl=true&Accept=text/xml&appKey=%s",
					TWX_DOMAIN, entityType, entityName, TWX_APPKEY);

			log("下载实体XML，URL: " + url);

			String fileName = entityType + "s_" + entityName.replace(".", "_") + ".xml";
			String filePath = TWX_XML_PATH + fileName;

			log("下载实体XML：" + entityName + " 到文件：" + filePath);

			// 使用HttpUtil下载文件
			String response = HttpUtil.createGet(url)
					.header("accept", "text/xml")
					.header("upgrade-insecure-requests", "1")
					.execute()
					.body();

			if (response == null || response.trim().isEmpty()) {
				throw new IOException("下载XML文件失败：服务器返回空响应");
			}

			// 保存响应内容到文件
			FileUtil.writeString(response, filePath, "UTF-8");

			log("实体XML下载完成：" + fileName);
		} catch (Exception e) {
			log("下载实体XML失败 " + entityName + ": " + e.getMessage());
			e.printStackTrace(logWriter);
		}
	}

	/**
	 * 编译deploy/Deploy.java文件
	 */
	private static void compileDeployJava() throws IOException {
		log("开始编译Deploy.java...");

		// 复制Deploy.java文件到deploy目录
		File sourceFile = new File(PROJECT_PATH + "src\\main\\java\\com\\cirpoint\\util\\Deploy.java");
		File targetDir = new File(TOMCAT_WEBAPP_PATH + "\\deploy");
		FileUtil.mkdir(targetDir);
		File targetFile = new File(targetDir, "Deploy.java");
		FileUtil.copy(sourceFile, targetFile, true);
		log("已复制Deploy.java到：" + targetFile.getAbsolutePath());

		// 读取文件内容并删除前两行
		List<String> lines = FileUtil.readLines(targetFile, StandardCharsets.UTF_8);
		if (lines.size() > 2) {
			lines = lines.subList(2, lines.size());
			// 重新写入文件
			FileUtil.writeLines(lines, targetFile, StandardCharsets.UTF_8);
			log("已删除Deploy.java的前两行");
		}

		// 获取JDK路径
		String javaHome = System.getProperty("java.home");
		if (javaHome.endsWith("jre")) {
			javaHome = javaHome.substring(0, javaHome.length() - 4);
		}
		String javac = javaHome + "\\bin\\javac.exe";

		// 检查javac是否存在
		if (!new File(javac).exists()) {
			throw new IOException("找不到javac编译器：" + javac);
		}

		// 构建Deploy.java的路径
		String deployJavaPath = TOMCAT_WEBAPP_PATH + "\\deploy\\Deploy.java";
		if (!new File(deployJavaPath).exists()) {
			throw new IOException("找不到Deploy.java文件：" + deployJavaPath);
		}

		try {
			log("使用编译器：" + javac);
			log("编译文件：" + deployJavaPath);

			// 构建编译命令，添加-encoding UTF-8参数
			ProcessBuilder pb = new ProcessBuilder(
					javac,
					"-encoding",
					"UTF-8",
					deployJavaPath
			);
			pb.directory(new File(TOMCAT_WEBAPP_PATH + "\\deploy"));
			pb.redirectErrorStream(true);
			Process process = pb.start();

			// 读取编译输出
			try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
				String line;
				while ((line = reader.readLine()) != null) {
					log("编译输出：" + line);
				}
			}

			int exitCode = process.waitFor();
			if (exitCode != 0) {
				throw new IOException("Deploy.java编译失败，退出码：" + exitCode);
			}

			// 检查编译后的class文件是否存在
			String deployClassPath = TOMCAT_WEBAPP_PATH + "\\deploy\\Deploy.class";
			if (!new File(deployClassPath).exists()) {
				throw new IOException("编译后的Deploy.class文件不存在");
			}

			log("Deploy.java编译完成");

		} catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			throw new IOException("编译过程被中断", e);
		}
	}

	/**
	 * 将更新包打包为zip文件
	 */
	private static void createZipPackage() {
		log("开始打包更新包为zip文件...");
		String currentDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
		String zipPath = UPDATE_PACKAGE_BASE_PATH + File.separator + currentDate + ".zip";

		// 删除已存在的zip文件
		FileUtil.del(zipPath);

		// 压缩文件夹
		ZipUtil.zip(updatePackagePath, zipPath);

		log("更新包已打包为：" + zipPath);
	}

	/**
	 * 加密文件
	 *
	 * @param filePath 要加密的文件路径
	 */
	private static void encryptFile(Path filePath) throws Exception {
		log("开始加密文件：" + filePath.getFileName());

		// 读取原文件内容
		byte[] fileContent = Files.readAllBytes(filePath);

		// 创建AES密钥
		SecretKeySpec secretKey = new SecretKeySpec(ENCRYPT_KEY.getBytes(StandardCharsets.UTF_8), ENCRYPT_ALGORITHM);

		// 创建加密器
		Cipher cipher = Cipher.getInstance(ENCRYPT_ALGORITHM);
		cipher.init(Cipher.ENCRYPT_MODE, secretKey);

		// 加密文件内容
		byte[] encryptedContent = cipher.doFinal(fileContent);

		// 生成加密后的文件名（添加.enc后缀）
		String originalFileName = filePath.getFileName().toString();
		String encryptedFileName = originalFileName + ".enc";
		Path encryptedFilePath = filePath.getParent().resolve(encryptedFileName);

		// 写入加密后的内容
		Files.write(encryptedFilePath, encryptedContent);

		// 删除原文件
		Files.delete(filePath);

		log("文件加密完成：" + originalFileName + " -> " + encryptedFileName);
	}
}