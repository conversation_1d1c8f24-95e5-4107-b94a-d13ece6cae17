package com.cirpoint.util;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.FileAppender;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import java.io.File;

@Slf4j
public class ArchiveLogger {
    private static final String LOG_PATTERN = "%d{yyyy-MM-dd HH:mm:ss.SSS} - %msg%n"; // 简化后的格式
    private Logger archiveLogger;
    @Getter
	private String logFilePath;

    public ArchiveLogger(String modelName, String phaseName, String pushUser, String logType) {
    try {
        String timestamp = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
        String baseDirName = String.format("%s-%s-%s-%s", modelName, phaseName, pushUser, timestamp);
        String logDir = System.getProperty("archive.log.dir", "logs/archive-push");
        File baseDir = new File(logDir, baseDirName);
        if (!baseDir.exists()) {
            baseDir.mkdirs();
        }
        this.logFilePath = new File(baseDir, logType + ".log").getAbsolutePath();

        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        // 生成唯一的Logger名称，包含时间戳和日志类型
        String loggerName = String.format("ArchiveLogger-%s-%s", timestamp, logType);
        this.archiveLogger = loggerContext.getLogger(loggerName);

        // 移除已有的Appender，避免重复
        archiveLogger.detachAndStopAllAppenders();

        // 创建新的FileAppender
        FileAppender<ILoggingEvent> fileAppender = new FileAppender<>();
        fileAppender.setContext(loggerContext);
        fileAppender.setFile(logFilePath);

        PatternLayoutEncoder encoder = new PatternLayoutEncoder();
        encoder.setContext(loggerContext);
        encoder.setPattern(LOG_PATTERN);
        encoder.start();

        fileAppender.setEncoder(encoder);
        fileAppender.start();

        // 禁止日志传递到父Logger
        archiveLogger.setAdditive(false);
        archiveLogger.setLevel(Level.INFO);
        archiveLogger.addAppender(fileAppender);

        info("分类日志文件创建成功：" + logFilePath);
    } catch (Exception e) {
        log.error("创建档案推送日志记录器失败", e);
    }
}

    public void info(String message) {
        archiveLogger.info(message);
        log.info(message);
    }
    
    /**
     * 支持带有一个参数的占位符格式日志
     * @param format 带有{}占位符的日志格式
     * @param arg 要替换占位符的参数
     */
    public void info(String format, Object arg) {
        String message = format.replace("{}", arg.toString());
        archiveLogger.info(message);
        log.info(format, arg);
    }
    
    /**
     * 支持带有两个参数的占位符格式日志
     * @param format 带有{}占位符的日志格式
     * @param arg1 第一个要替换占位符的参数
     * @param arg2 第二个要替换占位符的参数
     */
    public void info(String format, Object arg1, Object arg2) {
        String message = format;
        int index = message.indexOf("{}");
        if (index != -1) {
            message = message.substring(0, index) + arg1 + message.substring(index + 2);
            index = message.indexOf("{}");
            if (index != -1) {
                message = message.substring(0, index) + arg2 + message.substring(index + 2);
            }
        }
        archiveLogger.info(message);
        log.info(format, arg1, arg2);
    }
    
    /**
     * 支持带有多个参数的占位符格式日志
     * @param format 带有{}占位符的日志格式
     * @param arguments 要替换占位符的参数数组
     */
    public void info(String format, Object... arguments) {
        String message = format;
        for (Object arg : arguments) {
            int index = message.indexOf("{}");
            if (index != -1) {
                message = message.substring(0, index) + arg + message.substring(index + 2);
            } else {
                break;
            }
        }
        archiveLogger.info(message);
        log.info(format, arguments);
    }

    public void error(String message, Throwable t) {
        archiveLogger.error(message, t);
        log.error(message, t);
    }

    public void error(String message) {
        archiveLogger.error(message);
        log.error(message);
    }

    public void warn(String message) {
        archiveLogger.warn(message);
        log.warn(message);
    }

}