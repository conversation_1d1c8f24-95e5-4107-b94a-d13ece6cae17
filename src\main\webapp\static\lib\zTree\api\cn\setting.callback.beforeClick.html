<div class="apiDetail">
<div>
	<h2><span>Function(treeId, treeNode, clickFlag)</span><span class="path">setting.callback.</span>beforeClick</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.core</span> 核心 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>用于捕获单击节点之前的事件回调函数，并且根据返回值确定是否允许单击操作</p>
			<p>默认值：null</p>
		</div>
	</div>
	<h3>Function 参数说明</h3>
	<div class="desc">
	<h4><b>treeId</b><span>String</span></h4>
	<p>对应 zTree 的 <b class="highlight_red">treeId</b>，便于用户操控</p>
	<h4 class="topLine"><b>treeNode</b><span>JSON</span></h4>
	<p>被单击的节点 JSON 数据对象</p>
	<h4 class="topLine"><b>clickFlag</b><span>Number</span></h4>
	<p>节点被点击后的选中操作类型，详细看下表</p>
	<table width="100%" border="0" cellspacing="1" cellpadding="0">
		<thead><tr><td>clickFlag</td><td title="是否允许多点选中">selectedMulti</td><td title="是否按下 Ctrl 或 Cmd 键">autoCancelSelected<br/>&&<br/>event.ctrlKey / metaKey</td><td title="节点当前选择状态">isSelected</td><td>选中操作</td></tr></thead>
		<tbody>
			<tr><td>1</td><td>true</td><td>false</td><td>false</td><td>普通选中</td></tr>
			<tr><td>1</td><td>true</td><td>false</td><td>true</td><td>普通选中</td></tr>
			<tr><td>2</td><td>true</td><td>true</td><td>false</td><td>追加选中</td></tr>
			<tr><td>0</td><td>true</td><td>true</td><td>true</td><td>取消选中</td></tr>
			<tr><td>1</td><td>false</td><td>false</td><td>false</td><td>普通选中</td></tr>
			<tr><td>1</td><td>false</td><td>false</td><td>true</td><td>普通选中</td></tr>
			<tr><td>1</td><td>false</td><td>true</td><td>false</td><td>普通选中</td></tr>
			<tr><td>0</td><td>false</td><td>true</td><td>true</td><td>取消选中</td></tr>
		</tbody>
	</table>
	<h4 class="topLine"><b>返回值</b><span>Boolean</span></h4>
	<p>返回值是 true / false</p>
	<p class="highlight_red">如果返回 false，zTree 将不会选中节点，也无法触发 onClick 事件回调函数</p>
	</div>
	<h3>setting & function 举例</h3>
	<h4>1. 禁止节点被选中</h4>
	<pre xmlns=""><code>function zTreeBeforeClick(treeId, treeNode, clickFlag) {
    return (treeNode.id !== 1);
};
var setting = {
	callback: {
		beforeClick: zTreeBeforeClick
	}
};
......</code></pre>
</div>
</div>