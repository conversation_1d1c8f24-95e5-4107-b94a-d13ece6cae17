package com.cirpoint.service;

import cn.hutool.json.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import lombok.extern.slf4j.Slf4j;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest
public class ExtApiServiceTest {

	@Autowired
	private ExtApiService extApiService;

	@Test
	public void testGetTable() {
		String tableId = "2157"; // 这里替换成实际的tableId
		try {
			String result = extApiService.getTableData(tableId);
			log.info("获取到的表格文件路径: {}", result);
			assertNotNull(result, "返回结果不应为空");
		} catch (Exception e) {
			log.error("测试获取表格失败", e);
			fail("测试获取表格时发生异常: " + e.getMessage());
		}
	}

	@Test
	public void testGetActualTable() {
		// 获取实际汇总表
		String treeId = "63"; // 这里替换成实际的treeId
		String tableConfigId = "41"; // 这里替换成实际的tableConfigId
		try {
			JSONObject rs = extApiService.getActualTable(treeId, tableConfigId);
			log.info(rs.toString());
		} catch (Exception e) {
			log.error("测试获取实际汇总表失败", e);
			fail("测试获取实际汇总表时发生异常: " + e.getMessage());
		}
	}
}