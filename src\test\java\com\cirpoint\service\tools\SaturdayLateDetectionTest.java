package com.cirpoint.service.tools;

import com.cirpoint.constant.WorkTimeConstants;
import com.cirpoint.model.worktime.AttendanceRecord;
import com.cirpoint.model.worktime.LateEarlyResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 周六上午迟到判定测试类
 * 验证周六上午08:30和非周六08:05的迟到判定逻辑
 */
@SpringBootTest
@TestPropertySource(properties = {
    "file.temp.path=target/test-temp"
})
public class SaturdayLateDetectionTest {

    private WorkTimeService workTimeService;
    private CrossDayValidator crossDayValidator;
    private DuplicateRecordProcessor duplicateRecordProcessor;

    @BeforeEach
    public void setUp() {
        crossDayValidator = new CrossDayValidator();
        duplicateRecordProcessor = new DuplicateRecordProcessor();
        workTimeService = new WorkTimeService(crossDayValidator, duplicateRecordProcessor);
    }

    @Test
    @DisplayName("测试周六上午迟到判定 - 08:30为分界线")
    public void testSaturdayMorningLateDetection() {
        // 2025-07-05是周六
        LocalDate saturday = LocalDate.of(2025, 7, 5);
        
        // 测试08:29 - 不迟到
        LateEarlyResult result1 = createAndCalculateResult("张三", "001", saturday, 8, 29);
        assertEquals(0, result1.getMorningLateCount(), "周六08:29应该不被判定为迟到");
        
        // 测试08:30 - 不迟到（边界值）
        LateEarlyResult result2 = createAndCalculateResult("李四", "002", saturday, 8, 30);
        assertEquals(0, result2.getMorningLateCount(), "周六08:30应该不被判定为迟到");
        
        // 测试08:31 - 迟到
        LateEarlyResult result3 = createAndCalculateResult("王五", "003", saturday, 8, 31);
        assertEquals(1, result3.getMorningLateCount(), "周六08:31应该被判定为迟到");
        
        // 测试08:06 - 不迟到（在周六宽松时间内）
        LateEarlyResult result4 = createAndCalculateResult("赵六", "004", saturday, 8, 6);
        assertEquals(0, result4.getMorningLateCount(), "周六08:06应该不被判定为迟到");
    }

    @Test
    @DisplayName("测试非周六上午迟到判定 - 08:05为分界线")
    public void testWeekdayMorningLateDetection() {
        // 2025-07-04是周五
        LocalDate friday = LocalDate.of(2025, 7, 4);
        
        // 测试08:04 - 不迟到
        LateEarlyResult result1 = createAndCalculateResult("张三", "001", friday, 8, 4);
        assertEquals(0, result1.getMorningLateCount(), "周五08:04应该不被判定为迟到");
        
        // 测试08:05 - 不迟到（边界值）
        LateEarlyResult result2 = createAndCalculateResult("李四", "002", friday, 8, 5);
        assertEquals(0, result2.getMorningLateCount(), "周五08:05应该不被判定为迟到");
        
        // 测试08:06 - 迟到
        LateEarlyResult result3 = createAndCalculateResult("王五", "003", friday, 8, 6);
        assertEquals(1, result3.getMorningLateCount(), "周五08:06应该被判定为迟到");
        
        // 测试08:30 - 迟到（在非周六严格时间外）
        LateEarlyResult result4 = createAndCalculateResult("赵六", "004", friday, 8, 30);
        assertEquals(1, result4.getMorningLateCount(), "周五08:30应该被判定为迟到");
    }

    @Test
    @DisplayName("测试周日上午迟到判定 - 应使用08:05分界线")
    public void testSundayMorningLateDetection() {
        // 2025-07-06是周日
        LocalDate sunday = LocalDate.of(2025, 7, 6);
        
        // 测试08:05 - 不迟到
        LateEarlyResult result1 = createAndCalculateResult("张三", "001", sunday, 8, 5);
        assertEquals(0, result1.getMorningLateCount(), "周日08:05应该不被判定为迟到");
        
        // 测试08:06 - 迟到
        LateEarlyResult result2 = createAndCalculateResult("李四", "002", sunday, 8, 6);
        assertEquals(1, result2.getMorningLateCount(), "周日08:06应该被判定为迟到");
    }

    @Test
    @DisplayName("测试下午迟到判定不受周六影响")
    public void testAfternoonLateDetectionUnaffected() {
        LocalDate saturday = LocalDate.of(2025, 7, 5);
        LocalDate friday = LocalDate.of(2025, 7, 4);
        
        // 周六下午12:30 - 不迟到
        LateEarlyResult satResult1 = createAndCalculateAfternoonResult("张三", "001", saturday, 12, 30);
        assertEquals(0, satResult1.getAfternoonLateCount(), "周六下午12:30应该不被判定为迟到");
        
        // 周六下午12:31 - 迟到
        LateEarlyResult satResult2 = createAndCalculateAfternoonResult("李四", "002", saturday, 12, 31);
        assertEquals(1, satResult2.getAfternoonLateCount(), "周六下午12:31应该被判定为迟到");
        
        // 周五下午12:30 - 不迟到
        LateEarlyResult friResult1 = createAndCalculateAfternoonResult("王五", "003", friday, 12, 30);
        assertEquals(0, friResult1.getAfternoonLateCount(), "周五下午12:30应该不被判定为迟到");
        
        // 周五下午12:31 - 迟到
        LateEarlyResult friResult2 = createAndCalculateAfternoonResult("赵六", "004", friday, 12, 31);
        assertEquals(1, friResult2.getAfternoonLateCount(), "周五下午12:31应该被判定为迟到");
    }

    /**
     * 创建上午打卡记录并计算迟到早退结果
     */
    private LateEarlyResult createAndCalculateResult(String employeeName, String employeeId, LocalDate date, int hour, int minute) {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 创建上午进入记录
        AttendanceRecord entryRecord = new AttendanceRecord();
        entryRecord.setEmployeeName(employeeName);
        entryRecord.setEmployeeId(employeeId);
        entryRecord.setAccessPoint("910入");
        entryRecord.setDirection(WorkTimeConstants.DIRECTION_IN);
        entryRecord.setEventTime(date.atTime(hour, minute));
        records.add(entryRecord);
        
        // 创建上午离开记录
        AttendanceRecord exitRecord = new AttendanceRecord();
        exitRecord.setEmployeeName(employeeName);
        exitRecord.setEmployeeId(employeeId);
        exitRecord.setAccessPoint("910出");
        exitRecord.setDirection(WorkTimeConstants.DIRECTION_OUT);
        exitRecord.setEventTime(date.atTime(11, 30));
        records.add(exitRecord);
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = WorkTimeService.class.getDeclaredMethod(
                "calculateDailyLateEarly", String.class, String.class, LocalDate.class, List.class);
            method.setAccessible(true);
            return (LateEarlyResult) method.invoke(workTimeService, employeeName, employeeId, date, records);
        } catch (Exception e) {
            throw new RuntimeException("测试方法调用失败", e);
        }
    }

    /**
     * 创建下午打卡记录并计算迟到早退结果
     */
    private LateEarlyResult createAndCalculateAfternoonResult(String employeeName, String employeeId, LocalDate date, int hour, int minute) {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 创建下午进入记录
        AttendanceRecord entryRecord = new AttendanceRecord();
        entryRecord.setEmployeeName(employeeName);
        entryRecord.setEmployeeId(employeeId);
        entryRecord.setAccessPoint("910入");
        entryRecord.setDirection(WorkTimeConstants.DIRECTION_IN);
        entryRecord.setEventTime(date.atTime(hour, minute));
        records.add(entryRecord);
        
        // 创建下午离开记录
        AttendanceRecord exitRecord = new AttendanceRecord();
        exitRecord.setEmployeeName(employeeName);
        exitRecord.setEmployeeId(employeeId);
        exitRecord.setAccessPoint("910出");
        exitRecord.setDirection(WorkTimeConstants.DIRECTION_OUT);
        exitRecord.setEventTime(date.atTime(17, 0));
        records.add(exitRecord);
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = WorkTimeService.class.getDeclaredMethod(
                "calculateDailyLateEarly", String.class, String.class, LocalDate.class, List.class);
            method.setAccessible(true);
            return (LateEarlyResult) method.invoke(workTimeService, employeeName, employeeId, date, records);
        } catch (Exception e) {
            throw new RuntimeException("测试方法调用失败", e);
        }
    }
}
