package com.cirpoint.util;

import org.junit.jupiter.api.Test;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Access数据库测试类
 */
public class AccessDatabaseTest {
    
    private static final String DB_PATH = "E:/test.mdb";
    
    @Test
    public void testCreateDatabaseAndTables() {
        // 1. 创建数据库
        boolean dbCreated = AccessDatabaseUtil.createAccessDatabase(DB_PATH);
        System.out.println("数据库创建" + (dbCreated ? "成功" : "失败"));
        
        // 2. 创建角色表
        Map<String, String> roleColumns = new HashMap<>();
        roleColumns.put("role_id", "COUNTER PRIMARY KEY");
        roleColumns.put("role_name", "TEXT(50) NOT NULL");
        roleColumns.put("role_desc", "TEXT(200)");
        roleColumns.put("create_time", "DATETIME DEFAULT NOW()");
        
        boolean roleTableCreated = AccessDatabaseUtil.createTable(DB_PATH, "roles", roleColumns);
        System.out.println("角色表创建" + (roleTableCreated ? "成功" : "失败"));
        
        // 3. 创建用户表
        Map<String, String> userColumns = new HashMap<>();
        userColumns.put("user_id", "COUNTER PRIMARY KEY");
        userColumns.put("username", "TEXT(50) NOT NULL");
        userColumns.put("password", "TEXT(100) NOT NULL");
        userColumns.put("real_name", "TEXT(50)");
        userColumns.put("email", "TEXT(100)");
        userColumns.put("role_id", "LONG NOT NULL");
        userColumns.put("create_time", "DATETIME DEFAULT NOW()");
        userColumns.put("status", "INTEGER DEFAULT 1");
        
        boolean userTableCreated = AccessDatabaseUtil.createTable(DB_PATH, "users", userColumns);
        System.out.println("用户表创建" + (userTableCreated ? "成功" : "失败"));
        
        // 4. 插入角色数据
        insertRoleData();
        
        // 5. 插入用户数据
        insertUserData();
        
        // 6. 查询并显示数据
        queryAndDisplayData();
    }
    
    private void insertRoleData() {
        // 插入角色数据
        Map<String, Object> adminRole = new HashMap<>();
        adminRole.put("role_name", "管理员");
        adminRole.put("role_desc", "系统管理员，拥有所有权限");
        AccessDatabaseUtil.insertRecord(DB_PATH, "roles", adminRole);
        
        Map<String, Object> userRole = new HashMap<>();
        userRole.put("role_name", "普通用户");
        userRole.put("role_desc", "普通用户，具有基本操作权限");
        AccessDatabaseUtil.insertRecord(DB_PATH, "roles", userRole);
        
        Map<String, Object> guestRole = new HashMap<>();
        guestRole.put("role_name", "访客");
        guestRole.put("role_desc", "访客用户，只有查看权限");
        AccessDatabaseUtil.insertRecord(DB_PATH, "roles", guestRole);
    }
    
    private void insertUserData() {
        // 插入用户数据
        Map<String, Object> admin = new HashMap<>();
        admin.put("username", "admin");
        admin.put("password", "123456");
        admin.put("real_name", "系统管理员");
        admin.put("email", "<EMAIL>");
        admin.put("role_id", 1);
        AccessDatabaseUtil.insertRecord(DB_PATH, "users", admin);
        
        Map<String, Object> user1 = new HashMap<>();
        user1.put("username", "zhangsan");
        user1.put("password", "123456");
        user1.put("real_name", "张三");
        user1.put("email", "<EMAIL>");
        user1.put("role_id", 2);
        AccessDatabaseUtil.insertRecord(DB_PATH, "users", user1);
        
        Map<String, Object> user2 = new HashMap<>();
        user2.put("username", "lisi");
        user2.put("password", "123456");
        user2.put("real_name", "李四");
        user2.put("email", "<EMAIL>");
        user2.put("role_id", 2);
        AccessDatabaseUtil.insertRecord(DB_PATH, "users", user2);
        
        Map<String, Object> guest = new HashMap<>();
        guest.put("username", "guest");
        guest.put("password", "123456");
        guest.put("real_name", "访客用户");
        guest.put("email", "<EMAIL>");
        guest.put("role_id", 3);
        AccessDatabaseUtil.insertRecord(DB_PATH, "users", guest);
    }
    
    private void queryAndDisplayData() {
        // 查询角色数据
        System.out.println("\n角色表数据：");
        List<Map<String, Object>> roles = AccessDatabaseUtil.queryRecords(DB_PATH, "SELECT * FROM roles");
        for (Map<String, Object> role : roles) {
            System.out.println(role);
        }
        
        // 查询用户数据
        System.out.println("\n用户表数据：");
        List<Map<String, Object>> users = AccessDatabaseUtil.queryRecords(
            DB_PATH,
            "SELECT u.*, r.role_name FROM users u JOIN roles r ON u.role_id = r.role_id"
        );
        for (Map<String, Object> user : users) {
            System.out.println(user);
        }
    }
    
    @Test
    public void testExportToExcel() {
        String excelPath = "E:/test_db_export.xlsx";
        boolean success = AccessToExcelUtil.exportToExcel(DB_PATH, excelPath);
        System.out.println("数据库导出到Excel " + (success ? "成功" : "失败"));
        System.out.println("Excel文件路径: " + excelPath);
    }
}
