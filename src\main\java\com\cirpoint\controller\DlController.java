package com.cirpoint.controller;

import cn.hutool.json.JSONArray;
import com.cirpoint.model.Result;
import com.cirpoint.service.DlService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 电缆业务控制器
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@Slf4j
@RestController
@RequestMapping("/api/dl")
@Tag(name = "电缆管理", description = "电缆相关的业务接口")
public class DlController {
	private final DlService dlService;

	@Autowired
	public DlController(DlService dlService) {
		this.dlService = dlService;
	}

	@PostMapping(value = "/sync-report", produces = MediaType.APPLICATION_JSON_VALUE)
	@Operation(
			summary = "同步电缆检测报告",
			description = "从指定目录同步电缆检测报告文件，返回处理结果列表",
			responses = {
					@ApiResponse(
							responseCode = "200",
							description = "同步成功",
							content = @Content(mediaType = "application/json", schema = @Schema(implementation = Result.class))
					)
			}
	)
	public ResponseEntity<?> syncDlReport(@RequestParam String processTreeId) {
		try {
			log.info("收到同步电缆检测报告请求，processTreeId: {}", processTreeId);
			JSONArray result = dlService.syncDlReport(processTreeId);
			log.info("同步完成，处理文件数：{}", result.size());
			return Result.ok("同步成功，共处理 " + result.size() + " 个文件", result);
		} catch (Exception e) {
			log.error("同步电缆检测报告失败", e);
			return Result.error("同步失败：" + e.getMessage());
		}
	}

	@PostMapping(value = "/generate-mock", produces = MediaType.APPLICATION_JSON_VALUE)
	@Operation(
			summary = "生成模拟测试文件",
			description = "在指定目录下生成模拟的电缆检测报告文件，用于测试",
			responses = {
					@ApiResponse(
							responseCode = "200",
							description = "生成成功",
							content = @Content(mediaType = "application/json", schema = @Schema(implementation = Result.class))
					)
			}
	)
	public ResponseEntity<?> generateMockFiles() {
		try {
			log.info("收到生成模拟文件请求");
			int count = dlService.generateMockFiles();
			return Result.ok("成功生成 " + count + " 个模拟文件");
		} catch (Exception e) {
			log.error("生成模拟文件失败", e);
			return Result.error("生成失败：" + e.getMessage());
		}
	}
} 