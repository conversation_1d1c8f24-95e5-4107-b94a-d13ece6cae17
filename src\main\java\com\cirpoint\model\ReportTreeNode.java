package com.cirpoint.model;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ReportTreeNode {
	private String id;
	private String pid;
	private String name;
	private String saveData;
	private String tableNum;
	private String htmlData;
	private String signHtml;
	private int levelNum;
	private int sort;
	private String type;
	private String tableStatus;
	private String tableHeader;
	private String dataSource;
	private String reportType;
	private String dataType;
	private String security;
	private String fileName;
	private String fileFormat;
	private String filePath;
	private List<ReportTreeNode> children = new ArrayList<ReportTreeNode>();

	public void add(ReportTreeNode node) {
		children.add(node);
	}

	@Override
	public String toString() {
		return "TreeNode [id=" + id + ", pid=" + pid + ", name=" + name + ", children=" + children + ", type=" + type + "]";
	}

}
