package com.cirpoint.util;

import java.io.File;
import java.io.FileWriter;
import java.io.BufferedWriter;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import lombok.extern.slf4j.Slf4j;
import cn.hutool.core.map.MapUtil;

/**
 * Access数据库操作工具类
 */
@Slf4j
public class AccessDatabaseUtil {

    public static void main(String[] args) {
        String dbPath = "E:\\微信数据\\WeChat Files\\wxid_4781297790212\\FileStorage\\File\\2025-03\\质量特性综合测试系统.mdb";
//        List<String> sqlStatements = AccessDatabaseUtil.exportTableStructureToSQL(dbPath);
//        for (String sql : sqlStatements) {
//            System.out.println(sql);
//        }

// 方式二：直接保存到文件
        String outputPath = "C:/TestOut/table_structure.sql";
        AccessDatabaseUtil.saveTableStructureToFile(dbPath, outputPath);
    }
    
    /**
     * 创建一个新的Access数据库文件(.mdb)
     * 
     * @param filePath 要创建的数据库文件完整路径（包含文件名）
     * @return boolean 创建是否成功
     */
    public static boolean createAccessDatabase(String filePath) {
        // 检查文件路径是否为空
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new IllegalArgumentException("文件路径不能为空");
        }

        // 确保文件以.mdb结尾
        if (!filePath.toLowerCase().endsWith(".mdb")) {
            filePath += ".mdb";
        }

        File file = new File(filePath);
        
        // 检查目标文件是否已存在
        if (file.exists()) {
            log.warn("文件已存在: {}", filePath);
            return false;
        }

        // 确保目标目录存在
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }

        // 使用JDBC创建Access数据库
        try {
            Class.forName("net.ucanaccess.jdbc.UcanaccessDriver");
            String url = "jdbc:ucanaccess://" + filePath + ";newDatabaseVersion=V2003";
            try (Connection conn = DriverManager.getConnection(url)) {
                // 如果连接成功，说明数据库文件已经创建
                log.info("成功创建Access数据库文件: {}", filePath);
                return true;
            }
        } catch (Exception e) {
            log.error("创建Access数据库文件失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取数据库连接
     * 
     * @param dbPath 数据库文件路径
     * @return Connection 数据库连接
     * @throws SQLException 如果连接失败
     */
    public static Connection getConnection(String dbPath) throws SQLException {
        try {
            Class.forName("net.ucanaccess.jdbc.UcanaccessDriver");
            
            // 构建连接URL,添加额外参数
            StringBuilder urlBuilder = new StringBuilder("jdbc:ucanaccess://")
                .append(dbPath)
                .append(";memory=false")            // 不使用内存模式
                .append(";immediatelyReleaseResources=true"); // 立即释放资源

            // 创建连接属性
            Properties props = new Properties();
            // 设置其他连接属性
            props.setProperty("user", "");
            props.setProperty("password", "");
            
            log.debug("数据库连接URL: {}", urlBuilder.toString());
            
            return DriverManager.getConnection(urlBuilder.toString(), props);
        } catch (ClassNotFoundException e) {
            throw new SQLException("UCanAccess驱动程序未找到", e);
        } catch (SQLException e) {
            log.error("数据库连接失败: {}", e.getMessage());
            throw new SQLException("数据库连接失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建数据表
     * 
     * @param dbPath 数据库文件路径
     * @param tableName 表名
     * @param columns 列定义，key为列名，value为列的SQL定义（如 "TEXT(50) NOT NULL"）
     * @return boolean 是否创建成功
     */
    public static boolean createTable(String dbPath, String tableName, Map<String, String> columns) {
        if (MapUtil.isEmpty(columns)) {
            throw new IllegalArgumentException("列定义不能为空");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE ").append(tableName).append(" (");
        
        List<String> columnDefs = new ArrayList<>();
        columns.forEach((name, def) -> columnDefs.add(name + " " + def));
        sql.append(String.join(", ", columnDefs));
        sql.append(")");

        try (Connection conn = getConnection(dbPath);
             Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(sql.toString());
            log.info("成功创建数据表: {}", tableName);
            return true;
        } catch (SQLException e) {
            log.error("创建数据表失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 删除数据表
     * 
     * @param dbPath 数据库文件路径
     * @param tableName 表名
     * @return boolean 是否删除成功
     */
    public static boolean dropTable(String dbPath, String tableName) {
        String sql = "DROP TABLE " + tableName;
        try (Connection conn = getConnection(dbPath);
             Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(sql);
            log.info("成功删除数据表: {}", tableName);
            return true;
        } catch (SQLException e) {
            log.error("删除数据表失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 插入数据记录
     * 
     * @param dbPath 数据库文件路径
     * @param tableName 表名
     * @param data 数据记录，key为列名，value为列值
     * @return boolean 是否插入成功
     */
    public static boolean insertRecord(String dbPath, String tableName, Map<String, Object> data) {
        if (MapUtil.isEmpty(data)) {
            throw new IllegalArgumentException("数据记录不能为空");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");
        sql.append(String.join(", ", data.keySet()));
        sql.append(") VALUES (");
        
        // 使用Java 8兼容的方式创建问号占位符
        String[] questionMarks = new String[data.size()];
        java.util.Arrays.fill(questionMarks, "?");
        sql.append(String.join(", ", questionMarks));
        
        sql.append(")");

        try (Connection conn = getConnection(dbPath);
             PreparedStatement pstmt = conn.prepareStatement(sql.toString())) {
            int paramIndex = 1;
            for (Object value : data.values()) {
                pstmt.setObject(paramIndex++, value);
            }
            int rows = pstmt.executeUpdate();
            log.info("成功插入{}条记录到表: {}", rows, tableName);
            return rows > 0;
        } catch (SQLException e) {
            log.error("插入数据记录失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 更新数据记录
     * 
     * @param dbPath 数据库文件路径
     * @param tableName 表名
     * @param data 更新的数据，key为列名，value为新值
     * @param whereClause WHERE子句（不包含"WHERE"关键字）
     * @param whereParams WHERE子句的参数值
     * @return boolean 是否更新成功
     */
    public static boolean updateRecord(String dbPath, String tableName, 
                                     Map<String, Object> data, 
                                     String whereClause, 
                                     Object... whereParams) {
        if (MapUtil.isEmpty(data)) {
            throw new IllegalArgumentException("更新数据不能为空");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ").append(tableName).append(" SET ");
        sql.append(String.join(" = ?, ", data.keySet())).append(" = ?");
        if (whereClause != null && !whereClause.trim().isEmpty()) {
            sql.append(" WHERE ").append(whereClause);
        }

        try (Connection conn = getConnection(dbPath);
             PreparedStatement pstmt = conn.prepareStatement(sql.toString())) {
            int paramIndex = 1;
            for (Object value : data.values()) {
                pstmt.setObject(paramIndex++, value);
            }
            if (whereParams != null) {
                for (Object param : whereParams) {
                    pstmt.setObject(paramIndex++, param);
                }
            }
            int rows = pstmt.executeUpdate();
            log.info("成功更新{}条记录在表: {}", rows, tableName);
            return rows > 0;
        } catch (SQLException e) {
            log.error("更新数据记录失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 删除数据记录
     * 
     * @param dbPath 数据库文件路径
     * @param tableName 表名
     * @param whereClause WHERE子句（不包含"WHERE"关键字）
     * @param whereParams WHERE子句的参数值
     * @return boolean 是否删除成功
     */
    public static boolean deleteRecord(String dbPath, String tableName, 
                                     String whereClause, 
                                     Object... whereParams) {
        StringBuilder sql = new StringBuilder();
        sql.append("DELETE FROM ").append(tableName);
        if (whereClause != null && !whereClause.trim().isEmpty()) {
            sql.append(" WHERE ").append(whereClause);
        }

        try (Connection conn = getConnection(dbPath);
             PreparedStatement pstmt = conn.prepareStatement(sql.toString())) {
            if (whereParams != null) {
                for (int i = 0; i < whereParams.length; i++) {
                    pstmt.setObject(i + 1, whereParams[i]);
                }
            }
            int rows = pstmt.executeUpdate();
            log.info("成功删除{}条记录从表: {}", rows, tableName);
            return rows > 0;
        } catch (SQLException e) {
            log.error("删除数据记录失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 查询数据记录
     * 
     * @param dbPath 数据库文件路径
     * @param sql SQL查询语句
     * @param params 查询参数
     * @return List<Map<String, Object>> 查询结果列表，每个Map代表一条记录
     */
    public static List<Map<String, Object>> queryRecords(String dbPath, String sql, Object... params) {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try (Connection conn = getConnection(dbPath);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            if (params != null) {
                for (int i = 0; i < params.length; i++) {
                    pstmt.setObject(i + 1, params[i]);
                }
            }
            
            try (ResultSet rs = pstmt.executeQuery()) {
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();
                
                while (rs.next()) {
                    Map<String, Object> row = MapUtil.newHashMap(columnCount);
                    for (int i = 1; i <= columnCount; i++) {
                        row.put(metaData.getColumnName(i), rs.getObject(i));
                    }
                    results.add(row);
                }
            }
            
            log.info("成功查询到{}条记录", results.size());
            return results;
        } catch (SQLException e) {
            log.error("查询数据记录失败: {}", e.getMessage());
            return results;
        }
    }

    /**
     * 导出数据库中所有表的结构为SQL语句
     * 
     * @param dbPath 数据库文件路径
     * @return List<String> SQL语句列表
     */
    public static List<String> exportTableStructureToSQL(String dbPath) {
        List<String> sqlStatements = new ArrayList<>();
        
        try (Connection conn = getConnection(dbPath)) {
            DatabaseMetaData metaData = conn.getMetaData();
            
            // 获取所有表
            try (ResultSet tables = metaData.getTables(null, null, "%", new String[]{"TABLE"})) {
                while (tables.next()) {
                    String tableName = tables.getString("TABLE_NAME");
                    // 排除系统表
                    if (!tableName.startsWith("MSys")) {
                        StringBuilder createTableSQL = new StringBuilder();
                        createTableSQL.append("CREATE TABLE ").append(tableName).append(" (\n");
                        
                        // 获取表的列信息
                        List<String> columnDefs = new ArrayList<>();
                        List<String> primaryKeys = new ArrayList<>();
                        
                        // 获取主键信息
                        try (ResultSet pks = metaData.getPrimaryKeys(null, null, tableName)) {
                            while (pks.next()) {
                                primaryKeys.add(pks.getString("COLUMN_NAME"));
                            }
                        }
                        
                        // 获取列信息
                        try (ResultSet columns = metaData.getColumns(null, null, tableName, null)) {
                            while (columns.next()) {
                                StringBuilder columnDef = new StringBuilder();
                                String columnName = columns.getString("COLUMN_NAME");
                                String dataType = columns.getString("TYPE_NAME");
                                int columnSize = columns.getInt("COLUMN_SIZE");
                                int nullable = columns.getInt("NULLABLE");
                                String defaultValue = columns.getString("COLUMN_DEF");
                                String remarks = columns.getString("REMARKS");  // 获取字段说明
                                
                                // 构建列定义
                                columnDef.append("    ").append(columnName).append(" ");
                                
                                // 处理数据类型
                                switch (dataType.toUpperCase()) {
                                    case "VARCHAR":
                                    case "CHAR":
                                    case "TEXT":
                                        columnDef.append(dataType).append("(").append(columnSize).append(")");
                                        break;
                                    case "COUNTER":
                                        columnDef.append("INTEGER IDENTITY(1,1)");
                                        break;
                                    default:
                                        columnDef.append(dataType);
                                }
                                
                                // 处理是否可空
                                if (nullable == DatabaseMetaData.columnNoNulls) {
                                    columnDef.append(" NOT NULL");
                                }
                                
                                // 处理默认值
                                if (defaultValue != null) {
                                    columnDef.append(" DEFAULT ").append(defaultValue);
                                }
                                
                                // 添加字段说明作为注释
                                if (remarks != null && !remarks.trim().isEmpty()) {
                                    columnDef.append(" /* ").append(remarks.trim()).append(" */");
                                }
                                
                                columnDefs.add(columnDef.toString());
                            }
                        }
                        
                        // 添加主键约束
                        if (!primaryKeys.isEmpty()) {
                            columnDefs.add("    PRIMARY KEY (" + String.join(", ", primaryKeys) + ")");
                        }
                        
                        createTableSQL.append(String.join(",\n", columnDefs));
                        createTableSQL.append("\n);");
                        
                        // 添加到SQL语句列表
                        sqlStatements.add(createTableSQL.toString());
                        
                        // 获取索引信息
                        try (ResultSet indexes = metaData.getIndexInfo(null, null, tableName, false, false)) {
                            Map<String, List<String>> indexMap = new HashMap<>();
                            
                            while (indexes.next()) {
                                String indexName = indexes.getString("INDEX_NAME");
                                if (indexName != null && !indexName.startsWith("PRIMARY")) {
                                    String columnName = indexes.getString("COLUMN_NAME");
                                    indexMap.computeIfAbsent(indexName, k -> new ArrayList<>()).add(columnName);
                                }
                            }
                            
                            // 生成创建索引的SQL
                            for (Map.Entry<String, List<String>> entry : indexMap.entrySet()) {
                                StringBuilder indexSQL = new StringBuilder();
                                indexSQL.append("CREATE INDEX ").append(entry.getKey())
                                       .append(" ON ").append(tableName).append(" (")
                                       .append(String.join(", ", entry.getValue()))
                                       .append(");");
                                sqlStatements.add(indexSQL.toString());
                            }
                        }
                    }
                }
            }
            
            log.info("成功导出{}张表的结构", sqlStatements.size());
            return sqlStatements;
            
        } catch (SQLException e) {
            log.error("导出表结构失败: {}", e.getMessage());
            return sqlStatements;
        }
    }
    
    /**
     * 将表结构SQL语句保存到文件
     * 
     * @param dbPath 数据库文件路径
     * @param outputPath 输出文件路径
     * @return boolean 是否保存成功
     */
    public static boolean saveTableStructureToFile(String dbPath, String outputPath) {
        try {
            List<String> sqlStatements = exportTableStructureToSQL(dbPath);
            if (sqlStatements.isEmpty()) {
                return false;
            }
            
            File outputFile = new File(outputPath);
            // 确保目标目录存在
            File parentDir = outputFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 写入文件
            try (FileWriter writer = new FileWriter(outputFile);
                 BufferedWriter bufferedWriter = new BufferedWriter(writer)) {
                
                for (String sql : sqlStatements) {
                    bufferedWriter.write(sql);
                    bufferedWriter.write("\n\n");
                }
                
                log.info("成功将表结构保存到文件: {}", outputPath);
                return true;
            }
            
        } catch (Exception e) {
            log.error("保存表结构到文件失败: {}", e.getMessage());
            return false;
        }
    }
}
