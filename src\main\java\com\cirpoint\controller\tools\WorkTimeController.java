package com.cirpoint.controller.tools;

import cn.hutool.json.JSONObject;
import com.cirpoint.exception.WorkTimeProcessException;
import com.cirpoint.service.tools.WorkTimeService;
import com.cirpoint.service.tools.WorkTimeTestDataGenerator;
import com.cirpoint.util.FileDownloadUtil;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 员工工时统计工具控制器
 * 提供文件上传、工时计算和结果下载功能
 */
@Slf4j
@RestController
@RequestMapping("/api/worktime")
public class WorkTimeController {
    
    private final WorkTimeService workTimeService;
    private final WorkTimeTestDataGenerator testDataGenerator;
    
    @Autowired
    public WorkTimeController(WorkTimeService workTimeService, WorkTimeTestDataGenerator testDataGenerator) {
        this.workTimeService = workTimeService;
        this.testDataGenerator = testDataGenerator;
    }
    
    /**
     * 上传并处理工时统计文件
     * @param file 上传的Excel文件
     * @return 处理结果
     */
    @PostMapping("/upload")
    public ResponseEntity<?> uploadAndProcess(@RequestParam("file") MultipartFile file) {
        JSONObject result = new JSONObject();
        
        try {
            log.info("收到工时统计文件上传请求，文件名: {}", file.getOriginalFilename());
            
            // 验证文件基本信息
            if (file.isEmpty()) {
                result.set("success", false);
                result.set("message", "请选择要上传的文件");
                return ResponseEntity.ok(result);
            }
            
            // 验证文件类型
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                result.set("success", false);
                result.set("message", "文件名无效");
                return ResponseEntity.ok(result);
            }

            if (!originalFilename.toLowerCase().endsWith(".xlsx")) {
                result.set("success", false);
                result.set("message", "请上传Excel格式的文件（.xlsx）");
                return ResponseEntity.ok(result);
            }
            
            // 验证文件大小（100MB限制）
            if (file.getSize() > 100 * 1024 * 1024) {
                result.set("success", false);
                result.set("message", "文件大小不能超过100MB");
                return ResponseEntity.ok(result);
            }
            
            // 处理文件 - 使用新的综合统计功能
            byte[] resultBytes = workTimeService.processComprehensiveStatistics(file);

            // 创建临时文件用于下载
            String tempFileName = System.currentTimeMillis() + "_综合考勤统计结果.xlsx";
            Path tempPath = Paths.get(System.getProperty("java.io.tmpdir"), tempFileName);

            try (FileOutputStream fos = new FileOutputStream(tempPath.toFile())) {
                fos.write(resultBytes);
            }

            log.info("综合考勤统计处理完成，生成结果文件: {}", tempFileName);

            // 返回成功结果，包含新的下载链接
            result.set("success", true);
            result.set("message", "综合考勤统计处理完成（包含工时统计和迟到早退统计）");
            result.set("downloadUrl", "/api/worktime/download-late-early?token=" + tempFileName);
            result.set("fileName", "综合考勤统计结果.xlsx");
            
            return ResponseEntity.ok(result);
            
        } catch (WorkTimeProcessException e) {
            log.error("工时统计处理失败: {}", e.getUserMessage(), e);
            result.set("success", false);
            result.set("message", e.getUserMessage());
            result.set("errorCode", e.getErrorCode());
            return ResponseEntity.ok(result);
            
        } catch (IOException e) {
            log.error("文件处理IO异常", e);
            result.set("success", false);
            result.set("message", "文件处理失败，请重试");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("工时统计处理发生未知错误", e);
            result.set("success", false);
            result.set("message", "系统处理异常，请联系管理员");
            return ResponseEntity.ok(result);
        }
    }
    

    
    /**
     * 生成测试数据
     * @param employeeCount 员工数量（默认5）
     * @param days 天数（默认7）
     * @return 测试数据文件下载响应
     */
    @GetMapping("/generate-test-data")
    public ResponseEntity<?> generateTestData(
            @RequestParam(value = "employeeCount", defaultValue = "5") int employeeCount,
            @RequestParam(value = "days", defaultValue = "7") int days) {
        
        log.info("收到生成测试数据请求，员工数量: {}, 天数: {}", employeeCount, days);
        
        try {
            // 参数验证
            if (employeeCount < 1 || employeeCount > 50) {
                return new ResponseEntity<>("员工数量必须在1-50之间", HttpStatus.BAD_REQUEST);
            }
            
            if (days < 1 || days > 30) {
                return new ResponseEntity<>("天数必须在1-30之间", HttpStatus.BAD_REQUEST);
            }
            
            // 生成测试数据
            byte[] testDataBytes = testDataGenerator.generateTestExcel(employeeCount, days);
            
            // 创建临时文件
            String tempFileName = System.currentTimeMillis() + "_工时统计测试数据.xlsx";
            Path tempPath = Paths.get(System.getProperty("java.io.tmpdir"), tempFileName);
            
            try (FileOutputStream fos = new FileOutputStream(tempPath.toFile())) {
                fos.write(testDataBytes);
            }
            
            log.info("测试数据生成完成，文件: {}", tempFileName);
            
            // 直接返回文件下载
            return FileDownloadUtil.fileResponseAndDelete(tempPath.toFile(), 
                    String.format("工时统计测试数据_%d人_%d天.xlsx", employeeCount, days));
            
        } catch (Exception e) {
            log.error("生成测试数据失败", e);
            return new ResponseEntity<>("生成测试数据失败: " + e.getMessage(), 
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    




    /**
     * 下载迟到早退统计结果文件
     * @param token 文件标识
     * @return 文件下载响应
     */
    @GetMapping("/download-late-early")
    public ResponseEntity<?> downloadLateEarlyResult(@RequestParam("token") String token) {
        log.info("收到迟到早退统计结果下载请求，文件标识: {}", token);

        try {
            // 验证token格式
            if (token == null || token.trim().isEmpty()) {
                log.error("下载token为空");
                return new ResponseEntity<>("下载链接无效", HttpStatus.BAD_REQUEST);
            }

            // 构建文件路径
            String filePath = System.getProperty("java.io.tmpdir") + File.separator + token;
            File file = new File(filePath);

            // 验证文件存在
            if (!file.exists()) {
                log.error("文件不存在: {}", filePath);
                return new ResponseEntity<>("文件不存在或已过期", HttpStatus.NOT_FOUND);
            }

            // 验证文件大小
            if (file.length() == 0) {
                log.error("文件大小为零: {}", filePath);
                return new ResponseEntity<>("文件内容为空", HttpStatus.BAD_REQUEST);
            }

            log.info("开始下载迟到早退统计文件: {}, 大小: {}KB", token, file.length() / 1024);

            // 使用FileDownloadUtil处理文件下载并删除临时文件
            return FileDownloadUtil.fileResponseAndDelete(file, token);

        } catch (Exception e) {
            log.error("迟到早退统计文件下载发生错误", e);
            return new ResponseEntity<>("下载失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
