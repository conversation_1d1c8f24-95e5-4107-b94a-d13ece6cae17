package com.cirpoint.model;

import java.io.Serializable;

import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.layout.Document;
import lombok.Getter;
import lombok.Setter;

/**
 * PDF导出选项封装类
 * 用于扩展PDF导出的参数设置
 */
@Setter
@Getter
public class PdfOptions implements Serializable {
    private static final long serialVersionUID = 1L;

	// Getter和Setter方法
	/**
     * 页面大小（A1、A2、A3、A4等）
     */
    private String pageSize = "A4";
    
    /**
     * 页面方向（portrait-纵向、landscape-横向）
     */
    private String pageOrientation = "landscape";
    
    // 默认构造函数
    public PdfOptions() {}
    
    // 带参数的构造函数
    public PdfOptions(String pageSize, String pageOrientation) {
        this.pageSize = pageSize;
        this.pageOrientation = pageOrientation;
    }
    
    /**
     * 根据设置的页面大小获取对应的PageSize对象
     * 
     * @return PageSize对象
     */
    public PageSize createPageSize() {
        PageSize customPageSize;
        switch (this.pageSize.toUpperCase()) {
            case "A1":
                customPageSize = PageSize.A1;
                break;
            case "A2":
                customPageSize = PageSize.A2;
                break;
            case "A3":
                customPageSize = PageSize.A3;
                break;
            default:
                customPageSize = PageSize.A4;
                break;
        }
        
        // 根据方向参数旋转页面
        if ("landscape".equalsIgnoreCase(this.pageOrientation)) {
            customPageSize = customPageSize.rotate();
        }
        
        return customPageSize;
    }
    
    /**
     * 创建Document对象
     * 
     * @param pdfDoc PDF文档对象
     * @return 创建的Document对象
     */
    public Document createDocument(PdfDocument pdfDoc) {
        Document doc = new Document(pdfDoc, this.createPageSize());
        // 设置页边距
        doc.setMargins(10f, 10f, 12f, 10f);
        return doc;
    }

	@Override
    public String toString() {
        return "PdfOptions{" +
                "pageSize='" + pageSize + '\'' +
                ", pageOrientation='" + pageOrientation + '\'' +
                '}';
    }
} 