package com.cirpoint.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * BPM系统模拟数据服务
 * 生成技术状态更改单和不合格品审理单的模拟数据
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
public class BpmDataService {

    /**
     * 获取所有用户信息
     * 从SYS_USER表查询用户数据，转换为BPM系统格式
     *
     * @return 用户信息列表
     * @throws Exception 查询异常
     */
    public List<JSONObject> getAllUsers() throws Exception {
        log.info("开始查询SYS_USER表获取用户信息");

        try {
            // 查询SYS_USER表，获取用户基本信息
            String sql = "SELECT USER_ID, USER_NAME, USER_FULLNAME FROM SYS_USER WHERE USER_FULLNAME IS NOT NULL ORDER BY USER_ID";
            JSONArray queryResult = Util.postQuerySql(sql);

            if (queryResult == null || queryResult.isEmpty()) {
                log.warn("SYS_USER表中未查询到用户数据");
                return new ArrayList<>();
            }

            List<JSONObject> userList = new ArrayList<>();
            Random random = new Random();

            // 转换数据格式
            for (int i = 0; i < queryResult.size(); i++) {
                Object rowObj = queryResult.get(i);
                if (rowObj instanceof JSONObject) {
                    JSONObject row = (JSONObject) rowObj;

                    // 获取原始数据
                    String userFullName = row.getStr("USER_FULLNAME");
                    Integer userId = row.getInt("USER_ID");

                    if (userFullName != null && !userFullName.trim().isEmpty()) {
                        // 创建BPM格式的用户数据
                        JSONObject bpmUser = JSONUtil.createObj();
                        bpmUser.set("USERNAME", userFullName.trim());

                        // 生成10位数字的USERID（基于原USER_ID + 随机数）
                        String bpmUserId = generateBpmUserId(userId, random);
                        bpmUser.set("USERID", bpmUserId);

                        userList.add(bpmUser);
                        log.debug("转换用户数据: {} -> {}", userFullName, bpmUserId);
                    }
                }
            }

            log.info("成功查询并转换{}条用户数据", userList.size());
            return userList;

        } catch (Exception e) {
            log.error("查询用户信息失败", e);
            throw new Exception("查询用户信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成10位数字的BPM用户ID
     * 基于原始USER_ID和随机数生成
     *
     * @param originalUserId 原始用户ID
     * @param random 随机数生成器
     * @return 10位数字字符串
     */
    /**
     * 生成10位数字的BPM用户ID
     * 基于原始USER_ID和随机数生成，确保固定长度为10位
     *
     * @param originalUserId 原始用户ID
     * @param random 随机数生成器
     * @return 10位数字字符串
     */
    private String generateBpmUserId(Integer originalUserId, Random random) {
        if (originalUserId == null) {
            originalUserId = 0;
        }

        // 确保原始ID不超过6位，为随机数留出空间
        int baseId = Math.abs(originalUserId % 1000000); // 最多6位，确保为正数

        // 生成4位随机数
        int randomSuffix = random.nextInt(10000); // 0-9999

        // 组合成10位数字
        String bpmUserId = String.format("%06d%04d", baseId, randomSuffix);

        return bpmUserId;
    }
}
