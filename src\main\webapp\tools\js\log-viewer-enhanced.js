/**
 * 日志查看器前端控制器 - 增强版
 * 实现SSE实时日志、历史日志分页、搜索过滤、文件下载等功能
 * 
 * 增强功能：
 * - 支持后端发送的List<LogEntry>数组格式
 * - 增强错误处理和调试日志
 * - 向前兼容单个LogEntry对象格式
 */
class LogViewer {
    constructor() {
        this.eventSource = null;
        this.isRealTimeActive = false;
        this.currentPage = 0;
        this.pageSize = 2000; // 与HTML默认值保持一致
        this.currentFile = '';
        this.maxDisplayLines = 2000; // 实时日志最大显示行数，与默认pageSize一致
        this.displayedLines = 0;
        this.searchParams = {};
        
        // 智能滚动状态管理
        this.autoScrollEnabled = true;
        this.userScrolledAway = false;
        this.lastScrollPosition = 0;
        this.scrollDetectionTimeout = null;
        this.newLogsIndicator = null;
        
        // 行号管理
        this.realtimeLineNumber = 0;
        this.historyLineOffset = 0;
        this.lineNumberConfig = {
            showRealtimeLineNumbers: true,
            showHistoryLineNumbers: true,
            lineNumberWidth: 40,
            startFromOne: true,
            resetOnClear: true
        };
        
        this.init();
    }

    /**
     * 新日志指示器类
     */
    createNewLogsIndicator() {
        return {
            pendingLogsCount: 0,
            isVisible: false,
            element: null,
            
            init: () => {
                // 创建指示器DOM元素
                this.newLogsIndicator.element = document.createElement('div');
                this.newLogsIndicator.element.className = 'new-logs-indicator hidden';
                this.newLogsIndicator.element.innerHTML = `
                    <span class="new-logs-count">0</span>
                    <span class="new-logs-text">条新日志</span>
                    <span class="new-logs-action">点击查看</span>
                `;
                
                // 添加点击事件
                this.newLogsIndicator.element.addEventListener('click', () => {
                    this.scrollToBottomAndClearIndicator();
                });
                
                // 插入到实时日志容器中
                const container = document.getElementById('realtimeLogContainer');
                container.style.position = 'relative';
                container.appendChild(this.newLogsIndicator.element);
            },
            
            addNewLog: () => {
                this.newLogsIndicator.pendingLogsCount++;
                this.newLogsIndicator.updateDisplay();
            },
            
            show: () => {
                if (!this.newLogsIndicator.isVisible && this.newLogsIndicator.pendingLogsCount > 0) {
                    this.newLogsIndicator.isVisible = true;
                    this.newLogsIndicator.element.classList.remove('hidden');
                    this.newLogsIndicator.element.classList.add('show');
                }
            },
            
            hide: () => {
                if (this.newLogsIndicator.isVisible) {
                    this.newLogsIndicator.isVisible = false;
                    this.newLogsIndicator.element.classList.remove('show');
                    this.newLogsIndicator.element.classList.add('hidden');
                    this.newLogsIndicator.pendingLogsCount = 0;
                    this.newLogsIndicator.updateDisplay();
                }
            },
            
            updateDisplay: () => {
                if (this.newLogsIndicator.element) {
                    const countSpan = this.newLogsIndicator.element.querySelector('.new-logs-count');
                    countSpan.textContent = this.newLogsIndicator.pendingLogsCount;
                }
            }
        };
    }

    /**
     * 检测用户是否接近日志底部
     */
    isNearBottom(container, threshold = 50) {
        return (container.scrollTop + container.clientHeight + threshold) >= container.scrollHeight;
    }

    /**
     * 检测用户是否手动滚动到上面
     */
    userScrolledUp(container, threshold = 100) {
        const maxScroll = container.scrollHeight - container.clientHeight;
        return container.scrollTop < (maxScroll - threshold);
    }

    /**
     * 智能自动滚动决策
     */
    shouldAutoScroll(container) {
        const autoScrollChecked = document.getElementById('autoScroll').checked;
        const nearBottom = this.isNearBottom(container);
        const scrolledUp = this.userScrolledUp(container);
        
        console.log('滚动决策:', {
            autoScrollChecked,
            nearBottom,
            scrolledUp,
            userScrolledAway: this.userScrolledAway,
            scrollTop: container.scrollTop,
            scrollHeight: container.scrollHeight,
            clientHeight: container.clientHeight
        });
        
        // 只有在自动滚动启用、用户接近底部、且没有手动滚动到上面时才自动滚动
        return autoScrollChecked && nearBottom && !scrolledUp && !this.userScrolledAway;
    }

    /**
     * 滚动到底部并清除新日志指示器
     */
    scrollToBottomAndClearIndicator() {
        const container = document.getElementById('realtimeLogContent');
        container.scrollTop = container.scrollHeight;
        this.userScrolledAway = false;
        if (this.newLogsIndicator) {
            this.newLogsIndicator.hide();
        }
        console.log('手动滚动到底部，清除指示器');
    }

    /**
     * 初始化滚动监听器
     */
    initScrollListener() {
        const container = document.getElementById('realtimeLogContent');
        let scrollTimeout = null;
        
        container.addEventListener('scroll', () => {
            // 防抖处理，避免频繁触发
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            
            scrollTimeout = setTimeout(() => {
                const currentScroll = container.scrollTop;
                const maxScroll = container.scrollHeight - container.clientHeight;
                
                // 检测用户是否手动滚动到上面
                if (currentScroll < this.lastScrollPosition && currentScroll < maxScroll - 50) {
                    this.userScrolledAway = true;
                    console.log('检测到用户向上滚动，暂停自动滚动');
                }
                
                // 检测用户是否回到底部附近
                if (this.isNearBottom(container, 50)) {
                    this.userScrolledAway = false;
                    if (this.newLogsIndicator && this.newLogsIndicator.isVisible) {
                        // 用户回到底部，清除新日志指示器
                        setTimeout(() => {
                            if (this.isNearBottom(container, 50)) {
                                this.newLogsIndicator.hide();
                                console.log('用户回到底部，清除新日志指示器');
                            }
                        }, 1000);
                    }
                }
                
                this.lastScrollPosition = currentScroll;
            }, 100); // 100ms防抖
        });
        
        console.log('滚动监听器已初始化');
    }

    /**
     * 获取下一个实时日志行号
     */
    getNextRealtimeLineNumber() {
        if (this.lineNumberConfig.showRealtimeLineNumbers) {
            return ++this.realtimeLineNumber;
        }
        return null;
    }

    /**
     * 计算历史日志行号
     */
    calculateHistoryLineNumber(index) {
        if (this.lineNumberConfig.showHistoryLineNumbers) {
            return this.currentPage * this.pageSize + index + 1;
        }
        return null;
    }

    /**
     * 重置实时日志行号
     */
    resetRealtimeLineNumber() {
        if (this.lineNumberConfig.resetOnClear) {
            this.realtimeLineNumber = 0;
            console.log('实时日志行号已重置');
        }
    }

    /**
     * 复制日志行内容到剪贴板
     */
    async copyLogLine(lineNumber, content) {
        try {
            const textToCopy = `${lineNumber}: ${content}`;
            await navigator.clipboard.writeText(textToCopy);
            this.showSuccess(`已复制第 ${lineNumber} 行到剪贴板`);
            console.log('日志行复制成功:', lineNumber);
        } catch (error) {
            console.error('复制失败:', error);
            this.showError('复制失败，请手动选择内容');
        }
    }

    /**
     * 初始化应用
     */
    init() {
        // 同步HTML中的默认值到JavaScript变量
        this.syncUIValues();

        this.bindEvents();
        this.loadFileList();
        this.updateSystemStatus();

        // 初始化新日志指示器
        this.newLogsIndicator = this.createNewLogsIndicator();
        this.newLogsIndicator.init();

        // 初始化滚动监听
        this.initScrollListener();

        // 定期更新系统状态
        setInterval(() => this.updateSystemStatus(), 5000);
    }

    /**
     * 同步UI中的默认值到JavaScript变量
     */
    syncUIValues() {
        // 同步历史日志页面大小
        const pageSizeSelect = document.getElementById('pageSize');
        if (pageSizeSelect && pageSizeSelect.value) {
            this.pageSize = parseInt(pageSizeSelect.value);
            console.log('同步历史日志页面大小:', this.pageSize);
        }

        // 同步实时日志最大行数
        const maxLinesSelect = document.getElementById('maxLines');
        if (maxLinesSelect && maxLinesSelect.value) {
            this.maxDisplayLines = parseInt(maxLinesSelect.value);
            console.log('同步实时日志最大行数:', this.maxDisplayLines);
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 实时日志控制
        document.getElementById('toggleRealtime').addEventListener('click', () => {
            this.toggleRealTime();
        });

        document.getElementById('clearLogs').addEventListener('click', () => {
            this.clearRealtimeLogs();
        });

        document.getElementById('autoScroll').addEventListener('change', (e) => {
            this.autoScroll = e.target.checked;
        });

        document.getElementById('maxLines').addEventListener('change', (e) => {
            this.maxDisplayLines = parseInt(e.target.value);
            this.trimDisplayedLogs();
        });

        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 文件列表
        document.getElementById('refreshFiles').addEventListener('click', () => {
            this.loadFileList();
        });

        // 历史日志控制
        document.getElementById('historyFileSelect').addEventListener('change', (e) => {
            this.currentFile = e.target.value;
            this.currentPage = 0;
            this.loadHistoryLogs();
            document.getElementById('downloadFile').disabled = !e.target.value;
        });

        document.getElementById('pageSize').addEventListener('change', (e) => {
            this.pageSize = parseInt(e.target.value);
            this.currentPage = 0;
            this.loadHistoryLogs();
        });

        document.getElementById('downloadFile').addEventListener('click', () => {
            this.downloadFile();
        });

        // 搜索功能
        document.getElementById('searchBtn').addEventListener('click', () => {
            this.performSearch();
        });

        document.getElementById('clearSearch').addEventListener('click', () => {
            this.clearSearch();
        });

        // 搜索框回车事件
        document.getElementById('searchKeyword').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        // 提示框关闭
        document.querySelectorAll('.toast-close').forEach(button => {
            button.addEventListener('click', (e) => {
                e.target.closest('.toast').classList.add('hidden');
            });
        });

        // 窗口关闭时清理资源
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        // 更新标签按钮状态
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新标签内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}Tab`).classList.add('active');

        // 如果切换到历史日志标签，加载文件列表到下拉框
        if (tabName === 'history') {
            this.populateHistoryFileSelect();
        }
    }

    /**
     * 切换实时日志状态
     */
    toggleRealTime() {
        if (this.isRealTimeActive) {
            this.stopRealTime();
        } else {
            this.startRealTime();
        }
    }

    /**
     * 启动实时日志 - 增强版
     */
    startRealTime() {
        if (this.eventSource) {
            this.eventSource.close();
        }

        this.showLoading();
        
        console.log('启动SSE连接到:', `${window.location.origin}/api/logs/stream`);
        this.eventSource = new EventSource(`${window.location.origin}/api/logs/stream`);
        
        this.eventSource.onopen = () => {
            console.log('SSE连接已建立');
            this.isRealTimeActive = true;
            this.updateRealtimeStatus();
            this.updateConnectionStatus('online', '已连接');
            this.hideLoading();
            this.showSuccess('实时日志连接成功');
        };

        // 增强版事件处理 - 监听"logs"事件和默认消息事件
        this.eventSource.addEventListener('logs', (event) => {
            this.handleLogData(event.data, 'logs事件');
        });
        
        // 同时监听默认消息事件（兼容性）
        this.eventSource.onmessage = (event) => {
            this.handleLogData(event.data, '默认消息事件');
        };
        
        // 监听"recent-logs"事件（新客户端的历史日志）
        this.eventSource.addEventListener('recent-logs', (event) => {
            this.handleLogData(event.data, '历史日志事件');
        });

        this.eventSource.onerror = (error) => {
            console.error('SSE连接错误:', error);
            this.updateConnectionStatus('offline', '连接断开');
            
            if (this.isRealTimeActive) {
                this.showError('实时日志连接断开，正在尝试重连...');
                // 自动重连
                setTimeout(() => {
                    if (this.isRealTimeActive) {
                        console.log('尝试重新连接...');
                        this.startRealTime();
                    }
                }, 3000);
            }
        };
    }

    /**
     * 统一处理SSE日志数据
     */
    handleLogData(rawData, eventType) {
        try {
            console.log(`接收到SSE数据 (${eventType}):`, rawData);
            const data = JSON.parse(rawData);
            
            // 支持数组和单对象格式
            if (Array.isArray(data)) {
                console.log(`${eventType} - 接收到${data.length}条日志记录`);
                data.forEach(logEntry => {
                    this.appendRealtimeLog(logEntry);
                });
            } else {
                // 单条日志格式（兼容性）
                console.log(`${eventType} - 接收到单条日志记录`);
                this.appendRealtimeLog(data);
            }
        } catch (error) {
            console.error(`解析日志数据失败 (${eventType}):`, error);
            console.error('原始数据:', rawData);
            // 优雅降级：显示原始数据
            this.appendRealtimeLog({
                message: rawData,
                level: 'UNKNOWN',
                timestamp: new Date().toISOString().replace('T', ' ').substring(0, 23)
            });
        }
    }

    /**
     * 停止实时日志
     */
    stopRealTime() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        
        console.log('SSE连接已关闭');
        this.isRealTimeActive = false;
        this.updateRealtimeStatus();
        this.updateConnectionStatus('offline', '未连接');
        this.showSuccess('实时日志已停止');
    }

    /**
     * 更新实时日志状态显示
     */
    updateRealtimeStatus() {
        const button = document.getElementById('toggleRealtime');
        const status = document.getElementById('realtimeStatus');
        
        if (this.isRealTimeActive) {
            button.classList.remove('btn-primary');
            button.classList.add('btn-secondary');
            status.textContent = '停止实时';
        } else {
            button.classList.remove('btn-secondary');
            button.classList.add('btn-primary');
            status.textContent = '启动实时';
        }
    }

    /**
     * 更新连接状态显示
     */
    updateConnectionStatus(status, text) {
        const indicator = document.getElementById('connectionIndicator');
        const connectionText = document.getElementById('connectionText');
        
        indicator.className = `status-indicator ${status}`;
        connectionText.textContent = text;
    }

    /**
     * 添加实时日志条目 - 增强版
     */
    appendRealtimeLog(logEntry) {
        const container = document.getElementById('realtimeLogContent');
        
        // 移除占位符
        const placeholder = container.querySelector('.log-placeholder');
        if (placeholder) {
            placeholder.remove();
        }

        // 获取行号并创建日志条目元素
        const lineNumber = this.getNextRealtimeLineNumber();
        const logElement = this.createLogElement(logEntry, true, lineNumber);
        container.appendChild(logElement);

        this.displayedLines++;
        this.updateDisplayedLinesCount();

        // 限制显示行数
        this.trimDisplayedLogs();

        // 智能自动滚动逻辑
        if (this.shouldAutoScroll(container)) {
            // 用户在底部附近，执行自动滚动
            container.scrollTop = container.scrollHeight;
            console.log('执行自动滚动到底部');
        } else if (document.getElementById('autoScroll').checked) {
            // 用户在查看历史内容，显示新日志指示器
            if (this.newLogsIndicator) {
                this.newLogsIndicator.addNewLog();
                this.newLogsIndicator.show();
            }
            console.log('用户在查看历史内容，显示新日志指示器');
        }

        // 添加新日志动画
        setTimeout(() => {
            logElement.classList.remove('new');
        }, 1000);
    }

    /**
     * 创建日志条目元素
     */
    createLogElement(logEntry, isNew = false, lineNumber = null) {
        const div = document.createElement('div');
        div.className = `log-entry ${logEntry.level || ''}${isNew ? ' new' : ''}`;
        
        // 添加行号属性
        if (lineNumber !== null) {
            div.setAttribute('data-line-number', lineNumber);
            
            // 添加行号点击事件
            div.addEventListener('click', (e) => {
                // 检查点击的是否是行号区域
                const rect = div.getBoundingClientRect();
                const clickX = e.clientX - rect.left;
                
                if (clickX <= this.lineNumberConfig.lineNumberWidth) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 格式化要复制的内容
                    const content = div.querySelector('.log-content').textContent;
                    this.copyLogLine(lineNumber, content);
                }
            });
        }
        
        // 格式化日志内容
        let content = '';
        if (logEntry.timestamp) {
            content += `[${logEntry.timestamp}] `;
        }
        if (logEntry.level) {
            content += `${logEntry.level} `;
        }
        if (logEntry.logger) {
            content += `${logEntry.logger} - `;
        }
        content += logEntry.message || logEntry.content || '';

        // 使用span包装内容，便于样式控制
        const contentSpan = document.createElement('span');
        contentSpan.className = 'log-content';
        contentSpan.textContent = content;
        div.appendChild(contentSpan);
        
        return div;
    }

    /**
     * 限制显示的日志行数
     */
    trimDisplayedLogs() {
        const container = document.getElementById('realtimeLogContent');
        const logEntries = container.querySelectorAll('.log-entry');
        
        if (logEntries.length > this.maxDisplayLines) {
            const removeCount = logEntries.length - this.maxDisplayLines;
            for (let i = 0; i < removeCount; i++) {
                logEntries[i].remove();
            }
            this.displayedLines = this.maxDisplayLines;
            this.updateDisplayedLinesCount();
        }
    }

    /**
     * 更新显示行数计数
     */
    updateDisplayedLinesCount() {
        document.getElementById('displayedLines').textContent = this.displayedLines;
    }

    /**
     * 清空实时日志显示
     */
    clearRealtimeLogs() {
        const container = document.getElementById('realtimeLogContent');
        container.innerHTML = '<div class="log-placeholder">实时日志已清空，等待新日志...</div>';
        this.displayedLines = 0;
        this.updateDisplayedLinesCount();
        
        // 重置实时日志行号
        this.resetRealtimeLineNumber();
    }

    /**
     * 加载文件列表
     */
    async loadFileList() {
        try {
            this.showLoading();
            const response = await fetch(`${window.location.origin}/api/logs/files`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const files = await response.json();
            console.log('加载的文件列表:', files);
            if (files && files.length > 0) {
                console.log('第一个文件对象结构:', files[0]);
            }
            this.renderFileList(files);
            this.populateHistoryFileSelect(files);
            
        } catch (error) {
            console.error('加载文件列表失败:', error);
            this.showError('加载文件列表失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 渲染文件列表
     */
    renderFileList(files) {
        const container = document.getElementById('fileList');
        
        if (!files || files.length === 0) {
            container.innerHTML = '<div class="loading">暂无日志文件</div>';
            return;
        }

        container.innerHTML = '';
        files.forEach(file => {
            const fileElement = document.createElement('div');
            fileElement.className = 'file-item';
            fileElement.innerHTML = `
                <div class="file-name">${file.fileName || file.name || 'Unknown'}</div>
                <div class="file-info">
                    大小: ${this.formatFileSize(file.fileSize || file.size || 0)} | 
                    修改时间: ${this.formatDate(file.lastModified)}
                </div>
            `;
            
            fileElement.addEventListener('click', () => {
                // 移除其他文件的选中状态
                container.querySelectorAll('.file-item').forEach(item => {
                    item.classList.remove('active');
                });
                
                // 选中当前文件
                fileElement.classList.add('active');
                
                // 切换到历史日志标签并加载该文件
                this.switchTab('history');
                const fileName = file.fileName || file.name;
                document.getElementById('historyFileSelect').value = fileName;
                this.currentFile = fileName;
                this.currentPage = 0;
                this.loadHistoryLogs();
                document.getElementById('downloadFile').disabled = false;
            });
            
            container.appendChild(fileElement);
        });
    }

    /**
     * 填充历史日志文件下拉框
     */
    populateHistoryFileSelect(files = null) {
        const select = document.getElementById('historyFileSelect');
        
        if (!files) {
            // 如果没有传入文件列表，从API获取
            fetch(`${window.location.origin}/api/logs/files`)
                .then(response => response.json())
                .then(files => this.populateHistoryFileSelect(files))
                .catch(error => console.error('获取文件列表失败:', error));
            return;
        }

        // 保存当前选中的值
        const currentValue = select.value;
        
        // 清空并重新填充选项
        select.innerHTML = '<option value="">请选择日志文件</option>';
        
        files.forEach(file => {
            const option = document.createElement('option');
            const fileName = file.fileName || file.name || 'Unknown';
            const fileSize = file.fileSize || file.size || 0;
            option.value = fileName;
            option.textContent = `${fileName} (${this.formatFileSize(fileSize)})`;
            select.appendChild(option);
        });

        // 恢复之前的选中值
        if (currentValue) {
            select.value = currentValue;
        }
    }

    /**
     * 加载历史日志 - 增强版：智能API端点切换
     */
    async loadHistoryLogs() {
        if (!this.currentFile) {
            return;
        }

        try {
            this.showLoading();
            
            // 智能端点选择：检查是否有搜索参数
            const hasSearchParams = Object.keys(this.searchParams).length > 0;
            const endpoint = hasSearchParams ? '/logs/search' : '/logs/history';
            
            console.log('智能端点选择:', {
                hasSearchParams,
                searchParams: this.searchParams,
                selectedEndpoint: endpoint
            });
            
            // 构建请求参数
            const params = new URLSearchParams({
                fileName: this.currentFile,
                page: this.currentPage,
                size: this.pageSize
            });

            console.log('历史日志请求参数:', {
                fileName: this.currentFile,
                page: this.currentPage,
                size: this.pageSize,
                endpoint: endpoint
            });

            // 只在搜索端点时添加搜索参数
            if (hasSearchParams) {
                // 过滤掉时间参数（后端暂不支持）
                const supportedParams = ['keyword', 'level'];
                supportedParams.forEach(param => {
                    if (this.searchParams[param]) {
                        params.append(param, this.searchParams[param]);
                    }
                });
                
                console.log('搜索参数传递:', {
                    keyword: this.searchParams.keyword,
                    level: this.searchParams.level,
                    startTime: this.searchParams.startTime ? '(暂不支持)' : undefined,
                    endTime: this.searchParams.endTime ? '(暂不支持)' : undefined
                });
            }

            const response = await fetch(`${window.location.origin}/api${endpoint}?${params}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            console.log(`${endpoint}端点响应:`, result);
            
            this.renderHistoryLogs(result);
            this.renderPagination(result);
            
            // 显示搜索结果统计
            if (hasSearchParams) {
                const totalElements = result.total || result.totalElements || 0;
                this.showSearchResultStats(totalElements);
            }
            
        } catch (error) {
            console.error('加载历史日志失败:', error);
            const errorMsg = hasSearchParams ? '搜索日志失败' : '加载历史日志失败';
            this.showError(`${errorMsg}: ${error.message}`);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 渲染历史日志
     */
    renderHistoryLogs(result) {
        const container = document.getElementById('historyLogContent');

        console.log('渲染历史日志数据:', result);

        // 兼容不同的数据属性名称
        const data = result.content || result.data || [];

        console.log('历史日志数据详情:', {
            totalData: data.length,
            currentPageSize: this.pageSize,
            currentPage: this.currentPage,
            resultStructure: {
                hasContent: !!result.content,
                hasData: !!result.data,
                contentLength: result.content ? result.content.length : 0,
                dataLength: result.data ? result.data.length : 0
            }
        });

        if (!data || data.length === 0) {
            container.innerHTML = '<div class="log-placeholder">暂无日志数据</div>';
            return;
        }

        container.innerHTML = '';
        data.forEach((logEntry, index) => {
            const lineNumber = this.calculateHistoryLineNumber(index);
            const logElement = this.createLogElement(logEntry, false, lineNumber);
            container.appendChild(logElement);
        });

        console.log(`历史日志渲染完成，实际显示 ${data.length} 行`);

        // 检查是否返回的数据少于请求的数据（可能受到后端限制）
        if (data.length < this.pageSize && data.length > 0) {
            const totalPages = result.totalPages || 1;
            const currentPage = (result.page !== undefined ? result.page + 1 : result.currentPage) || 1;

            // 只在不是最后一页的情况下显示警告
            if (currentPage < totalPages) {
                console.warn(`警告：请求 ${this.pageSize} 行，但只返回了 ${data.length} 行，可能受到后端maxPageSize限制`);
                this.showError(`注意：由于后端限制，每页最多显示 ${data.length} 行，而不是请求的 ${this.pageSize} 行`);
            }
        }
    }

    /**
     * 渲染分页控件
     */
    renderPagination(result) {
        const container = document.getElementById('pagination');
        
        console.log('渲染分页信息:', result);
        
        // 兼容不同的分页属性名称
        const currentPage = (result.page !== undefined ? result.page + 1 : result.currentPage) || 1;
        const totalPages = result.totalPages || 1;
        const totalElements = result.total || result.totalElements || 0;
        
        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHtml = '<div class="pagination-info">';
        paginationHtml += `第 ${currentPage} 页，共 ${totalPages} 页，共 ${totalElements} 条记录`;
        paginationHtml += '</div><div class="pagination-buttons">';

        // 首页
        if (currentPage > 1) {
            paginationHtml += '<button class="pagination-button" onclick="logViewer.goToPage(1)">首页</button>';
            paginationHtml += `<button class="pagination-button" onclick="logViewer.goToPage(${currentPage - 1})">上一页</button>`;
        }

        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? ' active' : '';
            paginationHtml += `<button class="pagination-button${activeClass}" onclick="logViewer.goToPage(${i})">${i}</button>`;
        }

        // 尾页
        if (currentPage < totalPages) {
            paginationHtml += `<button class="pagination-button" onclick="logViewer.goToPage(${currentPage + 1})">下一页</button>`;
            paginationHtml += `<button class="pagination-button" onclick="logViewer.goToPage(${totalPages})">尾页</button>`;
        }

        paginationHtml += '</div>';
        container.innerHTML = paginationHtml;
    }

    /**
     * 跳转到指定页
     */
    goToPage(page) {
        // 前端显示页码从1开始，后端期望从0开始
        this.currentPage = page - 1;
        this.loadHistoryLogs();
    }

    /**
     * 执行搜索 - 增强版：输入验证和用户反馈
     */
    performSearch() {
        // 检查是否选择了文件
        if (!this.currentFile) {
            this.showError('请先选择日志文件');
            return;
        }

        const keyword = document.getElementById('searchKeyword').value.trim();
        const level = document.getElementById('logLevel').value;
        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;

        console.log('执行搜索，原始输入:', { keyword, level, startTime, endTime });

        // 构建搜索参数（只包含有效值）
        this.searchParams = {};
        if (keyword) this.searchParams.keyword = keyword;
        if (level) this.searchParams.level = level;
        
        // 时间参数暂时保存（用于显示，但不传递给后端）
        if (startTime) this.searchParams.startTime = startTime;
        if (endTime) this.searchParams.endTime = endTime;

        // 检查是否有有效的搜索条件
        const hasValidSearchParams = keyword || level;
        if (!hasValidSearchParams && (startTime || endTime)) {
            this.showError('时间范围搜索功能暂未支持，请使用关键词或日志级别搜索');
            return;
        }

        if (!hasValidSearchParams) {
            this.showError('请输入搜索关键词或选择日志级别');
            return;
        }

        console.log('有效搜索参数:', this.searchParams);

        // 重置到第一页并执行搜索
        this.currentPage = 0;
        this.loadHistoryLogs();
    }

    /**
     * 清空搜索 - 增强版：确保正确切换到历史日志模式
     */
    clearSearch() {
        console.log('清空搜索条件');
        
        // 清空UI输入
        document.getElementById('searchKeyword').value = '';
        document.getElementById('logLevel').value = '';
        document.getElementById('startTime').value = '';
        document.getElementById('endTime').value = '';
        
        // 清空搜索参数
        this.searchParams = {};
        this.currentPage = 0;
        
        // 检查是否选择了文件
        if (!this.currentFile) {
            console.log('未选择文件，无需加载历史日志');
            return;
        }
        
        console.log('切换到历史日志模式');
        this.loadHistoryLogs();
    }

    /**
     * 下载文件
     */
    downloadFile() {
        if (!this.currentFile) {
            this.showError('请先选择日志文件');
            return;
        }

        const url = `${window.location.origin}/api/logs/download?file=${encodeURIComponent(this.currentFile)}`;
        
        // 创建临时链接下载
        const link = document.createElement('a');
        link.href = url;
        link.download = this.currentFile;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        this.showSuccess('文件下载已开始');
    }

    /**
     * 更新系统状态
     */
    async updateSystemStatus() {
        try {
            const response = await fetch(`${window.location.origin}/api/logs/status`);
            if (response.ok) {
                const status = await response.json();
                document.getElementById('connectionCount').textContent = status.connectionCount || 0;
                document.getElementById('currentFile').textContent = status.currentFile || '-';
                document.getElementById('fileSize').textContent = status.fileSize ? this.formatFileSize(status.fileSize) : '-';
                document.getElementById('lastUpdate').textContent = status.lastUpdate ? this.formatDate(status.lastUpdate) : '-';
            }
        } catch (error) {
            // 静默处理状态更新错误
            console.debug('更新系统状态失败:', error);
        }
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 格式化日期
     */
    formatDate(timestamp) {
        if (!timestamp) return '-';
        
        let date;
        if (typeof timestamp === 'number') {
            date = new Date(timestamp);
        } else {
            date = new Date(timestamp);
        }
        
        if (isNaN(date.getTime())) return '-';
        
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * 显示加载遮罩
     */
    showLoading() {
        document.getElementById('loadingOverlay').classList.remove('hidden');
    }

    /**
     * 隐藏加载遮罩
     */
    hideLoading() {
        document.getElementById('loadingOverlay').classList.add('hidden');
    }

    /**
     * 显示错误提示
     */
    showError(message) {
        const toast = document.getElementById('errorToast');
        const messageSpan = toast.querySelector('.toast-message');
        messageSpan.textContent = message;
        toast.classList.remove('hidden');
        
        // 5秒后自动隐藏
        setTimeout(() => {
            toast.classList.add('hidden');
        }, 5000);
    }

    /**
     * 显示成功提示
     */
    showSuccess(message) {
        const toast = document.getElementById('successToast');
        const messageSpan = toast.querySelector('.toast-message');
        messageSpan.textContent = message;
        toast.classList.remove('hidden');
        
        // 3秒后自动隐藏
        setTimeout(() => {
            toast.classList.add('hidden');
        }, 3000);
    }

    /**
     * 显示搜索结果统计
     */
    showSearchResultStats(totalElements) {
        const searchKeyword = this.searchParams.keyword || '';
        const searchLevel = this.searchParams.level || '';
        
        let statsMessage = `搜索完成，共找到 ${totalElements} 条记录`;
        
        if (searchKeyword && searchLevel) {
            statsMessage += ` (关键词: "${searchKeyword}", 级别: ${searchLevel})`;
        } else if (searchKeyword) {
            statsMessage += ` (关键词: "${searchKeyword}")`;
        } else if (searchLevel) {
            statsMessage += ` (级别: ${searchLevel})`;
        }
        
        if (totalElements === 0) {
            statsMessage = '未找到匹配的日志记录，请尝试调整搜索条件';
        }
        
        console.log('搜索结果统计:', statsMessage);
        this.showSuccess(statsMessage);
    }

    /**
     * 清理资源
     */
    cleanup() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }
}

// 创建全局实例
const logViewer = new LogViewer(); 