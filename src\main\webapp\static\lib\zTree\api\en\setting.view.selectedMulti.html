<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">setting.view.</span>selectedMulti</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Set whether to allow select multiple nodes.</p>
			<p>Default: true</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p> true mean: you can select multiple nodes.</p>
	<p> false mean: you can only select one node.</p>
	<p class="highlight_red">1. Press Ctrl-key or Cmd-key, you can select multiple nodes.</p>
	<p class="highlight_red">2、This attribute don't affect the feature of cancel select node. ( please see setting.view.autoCancelSelected )</p>
	</div>
	<h3>Examples of setting</h3>
	<h4>1. Only select one node.</h4>
	<pre xmlns=""><code>var setting = {
	view: {
		selectedMulti: false
	}
};
......</code></pre>
</div>
</div>