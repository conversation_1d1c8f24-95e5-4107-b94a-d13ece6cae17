package com.cirpoint.controller;

import com.cirpoint.model.Result;
import com.cirpoint.service.ExtApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @description 外部API接口控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/ext")
public class ExtApiController {

	@Autowired
	private ExtApiService extApiService;

	/**
	 * 获取表格数据文件
	 *
	 * @param tableId 表格ID
	 * @return 表格数据文件相对路径
	 */
	@PostMapping("/table/{tableId}")
	public ResponseEntity<?> getTable(@PathVariable String tableId) {
		try {
			String result = extApiService.getTableFile(tableId);
			return Result.ok("获取表格数据成功", result);
		} catch (Exception e) {
			log.error("获取表格数据失败，tableId: {}", tableId, e);
			return Result.error("获取表格数据失败：" + e.getMessage());
		}
	}

	/**
	 * 获取表格数据
	 *
	 * @param tableId 表格ID
	 * @return 表格数据文件相对路径
	 */
	@RequestMapping(value = "/tableData/{tableId}", method = {RequestMethod.GET, RequestMethod.POST})
	public ResponseEntity<?> getTableData(@PathVariable String tableId) {
		return Result.ok("获取表格数据成功", extApiService.getTableData(tableId));
	}
} 