package com.cirpoint.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.aspose.words.Document;
import com.aspose.words.License;
import com.cirpoint.util.CommonUtil;
import com.cirpoint.util.FileUploadUtil;
import com.cirpoint.util.POIReadExcelToHtml;
import com.cirpoint.util.Util;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.util.RegexUtils;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
public class TableService {

	private static final String THING = "Thing.Fn.SecondTable";

	@Value("${file.temp.path}")
	private String tempPath;

	@Value("${file.upload.path}")
	private String fileUploadPath;

	/**
	 * 处理参数映射
	 *
	 * @param map   映射对象
	 * @param param 参数配置
	 */
	private void dealMap(Map<String, Object> map, JSONObject param) {
		String format = param.getStr("FORMAT", "");
		if (StrUtil.isNotEmpty(format)) {
			map.put("format", format);
		}
		map.put("width", param.getInt("WIDTH", 80));
	}

	/**
	 * 获取二级表表头
	 *
	 * @param filePath Excel文件路径
	 * @param endY     数据起始行
	 * @param tableId  表格配置ID
	 * @return 表头JSON字符串
	 */
	public String getSecondTableHeader(String filePath, int endY, String tableId) {

		ExcelReader reader = ExcelUtil.getReader(filePath);

		Sheet sheet = reader.getSheet();
		List<CellRangeAddress> listCombineCell = sheet.getMergedRegions();

		// 获取参数配置
		JSONArray params = Util.postTwx(THING, "QueryParamsById",
				JSONUtil.createObj().set("tableId", tableId));

		List<List<Map<String, Object>>> headerRows = new ArrayList<>();

		// 处理每一行
		for (int i = 0; i < endY - 1; i++) {
			List<Map<String, Object>> rowCells = new ArrayList<>();
			Row row = sheet.getRow(i);
			if (row == null) {
				continue;
			}

			int lastCellNum = row.getLastCellNum();
			for (int j = 0; j < lastCellNum; j++) {
				Cell cell = row.getCell(j);
				if (cell == null) {
					continue;
				}

				Map<String, Object> cellMap = new HashMap<>();
				String cellValue = Util.getCellValue(cell)
						.replaceAll(" ", "<br>");

				Map<String, Object> combinedInfo = Util.isCombineCell(listCombineCell, cell);
				boolean isCombined = (Boolean) combinedInfo.get("flag");

				if (isCombined) {
					CellRangeAddress range = (CellRangeAddress) combinedInfo.get("CellRangeAddress");
					if (cell.getColumnIndex() == range.getFirstColumn() &&
							cell.getRowIndex() == range.getFirstRow()) {

						cellMap.put("title", cellValue);
						cellMap.put("colspan", combinedInfo.get("mergedCol"));
						cellMap.put("rowspan", combinedInfo.get("mergedRow"));
						cellMap.put("width", 80);
						cellMap.put("align", "center");
						cellMap.put("cellColumnIndex", cell.getColumnIndex());

						JSONObject param = Util.getParam(params, j + 1);
						if (endY > 2 && i == 0) {
							if ((int) combinedInfo.get("mergedCol") == 1 &&
									(int) combinedInfo.get("mergedRow") == endY - 1) {
								cellMap.put("field", param.getStr("COL_NAME"));
								dealMap(cellMap, param);
							} else {
								cellMap.put("field", "colspan_" + combinedInfo.get("mergedCol") +
										"_" + cell.getAddress().toString());
							}
						} else {
							if ((int) combinedInfo.get("mergedCol") == 1) {
								cellMap.put("field", param.getStr("COL_NAME"));
								dealMap(cellMap, param);
							} else {
								cellMap.put("field", "colspan_" + combinedInfo.get("mergedCol") +
										"_" + cell.getAddress().toString());
							}
						}
						rowCells.add(cellMap);
					}
				} else {
					cellMap.put("title", cellValue);
					cellMap.put("width", 80);
					cellMap.put("colspan", 1);
					cellMap.put("rowspan", 1);
					cellMap.put("align", "center");
					cellMap.put("cellColumnIndex", cell.getColumnIndex());

					if (endY > 2 && i == 0) {
						cellMap.put("field", "colspan_1_" + cell.getAddress().toString());
					} else {
						JSONObject param = Util.getParam(params, j + 1);
						cellMap.put("field", param.getStr("COL_NAME"));
						dealMap(cellMap, param);
					}
					rowCells.add(cellMap);
				}
			}
			headerRows.add(rowCells);
		}

		return JSONUtil.parse(headerRows).toString();
	}

	/**
	 * 导出二级表数据
	 */
	public File exportSecondExcel(String productTreeId, String processTreeId, String tableId,
								  String tableName, String queryType, String query, String filename, int dlwIsAll) {
		String thisTempPath = tempPath + "//" + System.currentTimeMillis() + "//";

		// 获取二级表数据
		JSONArray datas = null;

		if (queryType.equals("process")) {
			datas = Util.postTwxForArray("Thing.Fn.SecondTable", "QueryTableData",
					JSONUtil.createObj()
							.set("productTreeId", productTreeId)
							.set("processTreeId", processTreeId)
							.set("table_config_id", tableId)
							.set("table_config_name", tableName)
							.set("dlw_is_all", dlwIsAll)
							.set("query", query));
		} else if (queryType.equals("summary")) {
			datas = Util.postTwxForObject("Thing.Fn.SecondTable", "QueryQualitySummary",
							JSONUtil.createObj()
									.set("tree_id", processTreeId)
									.set("table_config_id", tableId)
									.set("username", query))
					.getJSONArray("data");
		}

		return exportExcel(tableId, thisTempPath, filename, datas);
	}

	/**
	 * 导出二级表数据
	 *
	 * @param productTreeId 产品树ID
	 * @param processTreeId 工序树ID
	 * @param tableId       表格ID
	 * @param tableName     表格名称
	 * @param query         查询条件
	 * @param dlwIsAll      是否导出全部
	 * @return 生成的Excel文件
	 */
	public File exportSecondTableExcel(String productTreeId, String processTreeId, String tableId,
									   String tableName, String query, Integer dlwIsAll) {
		// 设置默认值
		if (StrUtil.isBlank(productTreeId)) {
			productTreeId = "-1";
		}
		if (StrUtil.isBlank(processTreeId)) {
			processTreeId = "0";
		}

		String tempPath = Util.getTempPath() + "//" + System.currentTimeMillis() + "//";

		// 获取二级表数据
		JSONArray datas = Util.postTwxForArray(THING, "QueryTableData",
				JSONUtil.createObj()
						.set("productTreeId", productTreeId)
						.set("processTreeId", processTreeId)
						.set("table_config_id", tableId)
						.set("table_config_name", tableName)
						.set("dlw_is_all", dlwIsAll)
						.set("query", query));

		return exportExcel(tableId, tempPath, "", datas);
	}

	/**
	 * 导出Excel文件
	 */
	private File exportExcel(String tableId, String tempPath, String filename, JSONArray datas) {
		File excelFile = null;
		InputStream is = null;
		XSSFWorkbook workbook = null;
		try {
			// 获取表信息
			JSONArray jarr = Util.postTwx("Thing.Fn.SecondTable", "QueryTableById",
					JSONUtil.createObj().set("tableId", tableId));
			String filepath = jarr.getJSONObject(0).getStr("SECOND_FILEPATH");
			String treeName = jarr.getJSONObject(0).getStr("TREE_NAME");
			String type = jarr.getJSONObject(0).getStr("TYPE");
			if (type.equals("4")) {
				filepath = jarr.getJSONObject(0).getStr("THREE_FILEPATH");
			}
			if (StrUtil.isBlank(filename)) {
				filename = treeName + "二级表";
			}
			int second_data_rownum = jarr.getJSONObject(0).getInt("SECOND_DATA_ROWNUM");

			// 创建新文件
			excelFile = createNewFile(tempPath, filepath, filename);
			is = Files.newInputStream(excelFile.toPath());
			workbook = new XSSFWorkbook(is);

			// 获取表属性信息
			JSONArray params = Util.postTwx("Thing.Fn.SecondTable", "QueryParamsById",
					JSONUtil.createObj().set("tableId", tableId));

			XSSFSheet sheet = workbook.getSheetAt(0);
			CellStyle cellStyle = createDefaultCellStyle(workbook);

			// 填充数据
			for (int i = 0; i < datas.size(); i++) {
				JSONObject map = datas.getJSONObject(i);
				int rowIndex = i + second_data_rownum - 1;
				XSSFRow row = sheet.createRow(rowIndex);
				for (Map.Entry<String, Object> entry : map.entrySet()) {
					String key = entry.getKey();
					String value = map.getStr(key);
					// 单独处理序号列
					if (Util.getType(entry.getValue()).equals("BigDecimal")) {
						value = map.getInt(key).toString();
					}
					int cellIndex = Util.getColIndex(params, key);
					if (cellIndex > 0) {
						XSSFCell cell = row.createCell(cellIndex - 1);
						cell.setCellType(CellType.STRING);
						cell.setCellStyle(cellStyle);
						cell.setCellValue(value);
					}
				}
			}

			// 保存文件
			try (FileOutputStream fos = new FileOutputStream(excelFile)) {
				workbook.write(fos);
				fos.flush();
			}

		} catch (Exception e) {
			log.error("导出Excel失败", e);
		} finally {
			if (workbook != null) {
				try {
					workbook.close();
				} catch (IOException e) {
					log.error("导出Excel失败", e);
				}
			}
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					log.error("导出Excel失败", e);
				}
			}
		}
		return excelFile;
	}

	/**
	 * 创建默认单元格样式
	 */
	private CellStyle createDefaultCellStyle(XSSFWorkbook workbook) {
		CellStyle cellStyle = workbook.createCellStyle();
		// 设置居中
		cellStyle.setAlignment(HorizontalAlignment.CENTER);
		cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 设置边框样式
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setBorderTop(BorderStyle.THIN);
		cellStyle.setBorderRight(BorderStyle.THIN);
		return cellStyle;
	}

	/**
	 * 创建新文件
	 */
	private File createNewFile(String tempPath, String filepath, String filename) {
		File file = new File(filepath);
		FileUtil.mkdir(tempPath);
		// 写入到新的excel
		File newFile = new File(tempPath, filename + ".xlsx");
		// 复制模板到新文件
		FileUtil.copy(file, newFile, true);
		return newFile;
	}

	/**
	 * 导出质量数据确认表
	 */
	public File exportQualityExcel(String treeId, String tableConfigId, String username) {
		JSONObject res = Util.postTwxForObject("Thing.Fn.SecondTable", "QueryProcessQualityData",
				JSONUtil.createObj().set("tableConfigId", tableConfigId).set("treeId", treeId));

		File file = null;
		JSONArray lists = new JSONArray();
		int rowNum = 0;
		// 标题所在行的行数
		List<Integer> rowC = new ArrayList<>();
		// 是否存在基本信息表
		boolean isHasBase = false;
		int baseColCount = 0;
		int colCount = 0;

		if (res.getBool("success")) {
			JSONArray arr = res.getJSONArray("data");
			String statusStr = "状态";
			String indexStr = "序号";

			for (int i = 0; i < arr.size(); i++) {
				JSONObject obj = arr.getJSONObject(i);
				JSONArray nameArr = new JSONArray();
				nameArr.add(obj.getStr("name"));
				lists.add(nameArr);
				rowNum++;
				rowC.add(rowNum);

				JSONArray colArr = obj.getJSONArray("cols");
				int statusIndex = colArr.indexOf(statusStr);
				int indexIndex = colArr.indexOf(indexStr);

				if (obj.getInt("isBase") == 1) {
					isHasBase = true;
					baseColCount = colArr.size();
				} else {
					colCount = colArr.size();
				}

				lists.add(colArr);
				rowNum++;

				JSONArray datas = obj.getJSONArray("datas");
				// 移除冻结状态的数据
				for (int j = datas.size() - 1; j >= 0; j--) {
					JSONArray d = datas.getJSONArray(j);
					String status = d.getStr(statusIndex);
					if ("冻结".equals(status)) {
						datas.remove(j);
					}
				}

				// 重新给序号赋值
				for (int j = 0; j < datas.size(); j++) {
					JSONArray d = datas.getJSONArray(j);
					d.set(indexIndex, j + 1);
				}

				lists.addAll(datas);
				rowNum += datas.size();
			}

			// 首先获取质量汇总表数据
			file = exportSecondExcel("-1", treeId, tableConfigId,
					"", "summary", username, System.currentTimeMillis() + "", 2);

			// 通过工具类创建writer
			ExcelWriter writer = ExcelUtil.getWriter(file);
			writer.renameSheet("质量汇总表");
			writer.setSheet(1);

			writer.renameSheet("质量确认表");

			writer.setDefaultRowHeight(20);
			// 一次性写出内容
			writer.write(lists, false);

			// 设置列宽
			for (int i = 0; i < colCount; i++) {
				writer.setColumnWidth(i, 20);
			}

			// 设置行高
			for (int i = 0; i < rowNum; i++) {
				writer.setRowHeight(i, 26);
			}

			// 设置内容字体 加粗
			Font font = writer.createFont();
			font.setBold(true);

			// 设置标题样式
			CellStyle nameStyle = writer.createCellStyle();
			nameStyle.setFont(font);
			nameStyle.setAlignment(HorizontalAlignment.LEFT);
			nameStyle.setVerticalAlignment(VerticalAlignment.CENTER);

			CellStyle headerStyle = writer.createCellStyle();
			headerStyle.setFont(font);
			headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
			headerStyle.setAlignment(HorizontalAlignment.CENTER);
			headerStyle.setBorderBottom(BorderStyle.THIN);
			headerStyle.setBorderLeft(BorderStyle.THIN);
			headerStyle.setBorderTop(BorderStyle.THIN);
			headerStyle.setBorderRight(BorderStyle.THIN);

			for (int x = 0; x < rowC.size(); x++) {
				int rowN = rowC.get(x);
				// 设置标题样式
				writer.setStyle(nameStyle, 0, rowN - 1);

				if (isHasBase && x == 0) {
					for (int i = 0; i < baseColCount; i++) {
						writer.setStyle(headerStyle, i, rowN);
					}
					// 合并单元格后的标题行
					writer.merge(rowN - 1, rowN - 1, 0, baseColCount - 1, null, false);
				} else {
					for (int i = 0; i < colCount; i++) {
						writer.setStyle(headerStyle, i, rowN);
					}
					// 合并单元格后的标题行
					writer.merge(rowN - 1, rowN - 1, 0, colCount - 1, null, false);
				}
			}

			Workbook wb = writer.getWorkbook();
			// sheet 排序
			wb.setSheetOrder("质量汇总表", 1);
			wb.setActiveSheet(0);

			// 关闭writer，释放内存
			writer.close();
		}

		return file;
	}

	/**
	 * 导出自定义模板
	 *
	 * @param tableId 表格ID
	 * @return 生成的Excel文件
	 */
	public File exportCustomTemplate(String tableId) {
		// 查询参数配置
		JSONArray params = Util.postQuerySql("select * from PARAM_CONFIG where TABLE_ID=" + tableId + " and IS_REMOVE_REPEAT=1 ORDER BY SECOND_AREA");

		if (params.isEmpty()) {
			System.out.println("未找到格{}的参数配置");
			return null;
		}

		// 构表头
		JSONArray headers = new JSONArray();
		JSONArray columnWidths = new JSONArray();
		for (int i = 0; i < params.size(); i++) {
			String paramName = params.getJSONObject(i).getStr("PARAM_NAME");
			headers.add(paramName);
			columnWidths.add(28);
		}

		// 准备空数据
		JSONArray sheetData = new JSONArray();
		for (int i = 0; i < 100; i++) {
			JSONArray row = new JSONArray();
			for (int j = 0; j < headers.size(); j++) {
				row.add("");
			}
			sheetData.add(row);
		}

		return CommonUtil.createExcelFile(System.currentTimeMillis() + "", headers, sheetData, columnWidths, 28);
	}

	/**
	 * 处理曲线数据
	 *
	 * @param tableId        表格ID
	 * @param newDatas       新数据列表
	 * @param tempPath       临时目录路径
	 * @param fileUploadPath 文件上传路径
	 * @return 处理的曲线数量
	 */
	private int processCurveData(String tableId, JSONArray newDatas, String tempPath, String fileUploadPath) {
		// 取编号和型号参数配置
		JSONArray numberParam = Util.postQuerySql(
				"select * from PARAM_CONFIG where TABLE_ID=" + tableId + " and PARAM_NAME='编号'");
		JSONArray modelParam = Util.postQuerySql(
				"select * from PARAM_CONFIG where TABLE_ID=" + tableId + " and PARAM_NAME='型号'");

		if (numberParam.isEmpty() || modelParam.isEmpty()) {
			return 0;
		}

		String numberColName = numberParam.getJSONObject(0).getStr("COL_NAME");
		String modelColName = modelParam.getJSONObject(0).getStr("COL_NAME");

		// 获取所有曲线数据
		JSONArray allCurveData = Util.postTwxForObject("Thing.Fn.ListData", "QueryCurveData",
				JSONUtil.createObj()).getJSONArray("data");

		if (allCurveData.isEmpty()) {
			return 0;
		}

		// 按编号分组处理曲线文件
		Map<String, List<CurveFileInfo>> curveFilesByNumber = groupCurveFilesByNumber(allCurveData);

		// 处理每个数据项的曲线文件
		int hasCurveNum = 0;
		for (int i = 0; i < newDatas.size(); i++) {
			JSONObject object = newDatas.getJSONObject(i);
			String numberVal = object.getStr(numberColName);
			String modelVal = object.getStr(modelColName);

			List<CurveFileInfo> curveFiles = curveFilesByNumber.get(numberVal);
			if (curveFiles != null) {
				hasCurveNum += copyCurveFiles(curveFiles, modelVal, tempPath, fileUploadPath);
			}
		}
		return hasCurveNum;
	}

	/**
	 * 处理曲线数据 - 直接模式（使用Excel数据）
	 * 直接基于型号和编号查找匹配的曲线文件
	 *
	 * @param modelNumberPairs 型号-编号对列表
	 * @param tempPath         临时目录路径
	 * @param fileUploadPath   文件上传路径
	 * @return 处理的曲线数量
	 */
	private int processCurveDataDirect(List<Map<String, String>> modelNumberPairs, String tempPath, String fileUploadPath) {
		// 获取所有曲线数据
		JSONArray allCurveData = Util.postTwxForObject("Thing.Fn.ListData", "QueryCurveData",
				JSONUtil.createObj()).getJSONArray("data");

		if (allCurveData.isEmpty()) {
			log.warn("系统中未找到任何曲线数据");
			return 0;
		}

		// 按编号分组处理曲线文件
		Map<String, List<CurveFileInfo>> curveFilesByNumber = groupCurveFilesByNumber(allCurveData);

		// 处理每个型号-编号对的曲线文件
		int hasCurveNum = 0;
		for (Map<String, String> pair : modelNumberPairs) {
			String modelVal = pair.get("model");
			String numberVal = pair.get("number");

			if (StrUtil.isNotBlank(numberVal) && StrUtil.isNotBlank(modelVal)) {
				List<CurveFileInfo> curveFiles = curveFilesByNumber.get(numberVal);
				if (curveFiles != null) {
					hasCurveNum += copyCurveFiles(curveFiles, modelVal, tempPath, fileUploadPath);
				}
			}
		}
		
		log.info("曲线处理完成，共生成{}个文件", hasCurveNum);
		return hasCurveNum;
	}


	/**
	 * 曲线文件信息类
	 */
	private static class CurveFileInfo {
		String fileName;
		String fileFormat;
		String filePath;
		String leafName;

		CurveFileInfo(JSONObject curveData) {
			this.fileName = curveData.getStr("FILE_NAME");
			this.fileFormat = curveData.getStr("FILE_FORMAT");
			this.filePath = curveData.getStr("FILEPATH");
			this.leafName = curveData.getStr("LEAF_NAME", "");
		}

		String getCleanFileName() {
			return fileName.replace("(", "（")
					.replace(")", "）")
					.replaceAll("（公开）", "")
					.replaceAll("（内部）", "")
					.replaceAll("（秘密）", "")
					.replaceAll("（机密）", "");
		}
	}

	/**
	 * 按编号分组曲线文件
	 */
	private Map<String, List<CurveFileInfo>> groupCurveFilesByNumber(JSONArray allCurveData) {
		Map<String, List<CurveFileInfo>> curveFilesByNumber = new HashMap<>();

		for (int i = 0; i < allCurveData.size(); i++) {
			CurveFileInfo fileInfo = new CurveFileInfo(allCurveData.getJSONObject(i));
			String cleanFileName = fileInfo.getCleanFileName();

			curveFilesByNumber.computeIfAbsent(cleanFileName, k -> new ArrayList<>())
					.add(fileInfo);
		}

		return curveFilesByNumber;
	}

	/**
	 * 复制曲线文件到目标目录
	 */
	private int copyCurveFiles(List<CurveFileInfo> curveFiles, String modelVal, String tempPath, String fileUploadPath) {
		int copiedFiles = 0;
		String curveDir = tempPath + "//曲线报告//";

		// 如果只有一个文件，直接复制
		if (curveFiles.size() == 1) {
			CurveFileInfo fileInfo = curveFiles.get(0);
			String destPath = curveDir + fileInfo.fileName + modelVal + "." + fileInfo.fileFormat;
			if (copyFile(fileUploadPath + fileInfo.filePath, destPath)) {
				copiedFiles++;
			}
		}
		// 如果有多个文件，对比型号之后再复制
		else {
			for (CurveFileInfo fileInfo : curveFiles) {
				if (fileInfo.leafName.contains(modelVal)) {
					String destPath = curveDir + fileInfo.fileName + modelVal + "." + fileInfo.fileFormat;
					if (copyFile(fileUploadPath + fileInfo.filePath, destPath)) {
						copiedFiles++;
					}
				}
			}
		}

		return copiedFiles;
	}

	/**
	 * 复制单个文件
	 */
	private boolean copyFile(String srcPath, String destPath) {
		try {
			FileUtil.copy(srcPath, destPath, true);
			return true;
		} catch (Exception e) {
			log.error("复制曲线报告文件出错: {} -> {}", srcPath, destPath, e);
			return false;
		}
	}

	/**
	 * 导出自定义Excel数据
	 *
	 * @param inputStream   Excel文件输入流
	 * @param tableId       表格ID
	 * @param tableName     表格名称
	 * @param username      用户名
	 * @param isCurve       是否包含曲线
	 * @param isRelatedData 是否关联数据（仅对热敏电阻加工有效）
	 * @return 导出结果，包含文件路径和状态信息
	 */
	public JSONObject exportCustomExcel(InputStream inputStream, String tableId, String tableName,
										String username, boolean isCurve, boolean isRelatedData) {
		// 参数验证
		if (inputStream == null || StrUtil.isBlank(tableId) || StrUtil.isBlank(tableName)) {
			return JSONUtil.createObj()
					.set("success", false)
					.set("msg", "参数不能为空");
		}
		
		try {
			log.info("开始导出自定义Excel数据 - 表名: {}, 是否包含曲线: {}, 是否关联数据: {}", tableName, isCurve, isRelatedData);
			
			// 对于热敏电阻加工且不关联数据的情况，使用直接处理模式
			if ("热敏电阻加工".equals(tableName) && !isRelatedData && isCurve) {
				return exportCustomExcelDirectMode(inputStream, tableId, tableName, username);
			}
			
			// 原有的完整处理流程
			ExcelReader reader = ExcelUtil.getReader(inputStream);
			List<Object> headerList = reader.readRow(0);

			// 取需要去重的参数配
			JSONArray params = Util.postQuerySql(
					"select * from PARAM_CONFIG where TABLE_ID=" + tableId + " and IS_REMOVE_REPEAT=1");
			JSONArray indexParams = Util.postQuerySql(
					"select * from PARAM_CONFIG where TABLE_ID=" + tableId + " and FORMAT='1'");

			// 建参数射
			List<Object> paramList = new ArrayList<>();
			Map<String, String> headerAlias = new HashMap<>();
			for (int i = 0; i < params.size(); i++) {
				JSONObject param = params.getJSONObject(i);
				String paramName = param.getStr("PARAM_NAME");
				String colName = param.getStr("COL_NAME");
				headerAlias.put(paramName, colName);
				paramList.add(paramName);
			}

			// 验证上传文件的表头
			if (!CollUtil.containsAll(headerList, paramList)) {
				return JSONUtil.createObj()
						.set("success", false)
						.set("msg", "导出失败，原因：上传的文件与模板文件不一致！");
			}

			// 查询表格数据
			JSONArray datas = Util.postTwxForArray("Thing.Fn.SecondTable", "QueryTableData",
					JSONUtil.createObj()
							.set("processTreeId", -1)
							.set("table_config_id", tableId)
							.set("table_config_name", tableName)
							.set("query", JSONUtil.createObj().set("queryUser", username)));

			// 处理Excel数据
			reader.setHeaderAlias(headerAlias);
			List<Map<String, Object>> list = reader.readAll();

			JSONArray newDatas = new JSONArray();
			JSONArray extraData = new JSONArray();

			// 数据匹配和处理 筛选出不匹配的数据
			processExcelData(list, datas, newDatas, extraData);

			// 处理行号
			processRowNumbers(newDatas, indexParams);
			// 导出Excel文件
			String path = "//customExcel//" + System.currentTimeMillis() + "//";
			String thisTempPath = fileUploadPath + path;
			FileUtil.mkdir(thisTempPath);

			File file = exportExcel(tableId, thisTempPath, tableName + "自定义数据", newDatas);

			// 处理额外数据
			if (!extraData.isEmpty()) {
				processExtraData(extraData, headerAlias, file);
			}

			// 处理曲线数据
			int hasCurveNum = 0;
			if (tableName.equals("热敏电阻加工") && isCurve) {
				hasCurveNum = processCurveData(tableId, newDatas, thisTempPath, fileUploadPath);
			}

			// 返回结果
			if (hasCurveNum > 0) {
				File zip = ZipUtil.zip(thisTempPath, fileUploadPath + "//customExcel//" +
						System.currentTimeMillis() + "//" + tableName + "自定义数据（包含曲线报告）.zip");
				return JSONUtil.createObj()
						.set("success", true)
						.set("file", FileUtil.subPath(fileUploadPath, zip));
			} else {
				return JSONUtil.createObj()
						.set("success", true)
						.set("file", FileUtil.subPath(fileUploadPath, file));
			}

		} catch (Exception e) {
			log.error("导出自义Excel失败", e);
			return JSONUtil.createObj()
					.set("success", false)
					.set("msg", "导出失败，原因：" + e.getMessage());
		}
	}

	/**
	 * 导出自定义Excel数据 - 直接处理模式（跳过数据库查询）
	 * 仅针对热敏电阻加工类型，直接从Excel读取数据并生成曲线报告
	 * Excel格式：第一列为型号，第二列为编号
	 *
	 * @param inputStream Excel文件输入流
	 * @param tableId     表格ID
	 * @param tableName   表格名称
	 * @param username    用户名
	 * @return 导出结果，包含文件路径和状态信息
	 */
	private JSONObject exportCustomExcelDirectMode(InputStream inputStream, String tableId, 
													String tableName, String username) {
		try {
			log.info("开始直接处理模式导出：{}", tableName);
			
			ExcelReader reader = ExcelUtil.getReader(inputStream);
			
			// 直接读取所有数据，跳过表头（第一行）
			List<List<Object>> allRows = reader.read(1, Integer.MAX_VALUE);
			
			// 验证数据不为空
			if (allRows.isEmpty()) {
				return JSONUtil.createObj()
						.set("success", false)
						.set("msg", "Excel文件中未找到有效数据");
			}

			// 转换为简化的数据结构：型号-编号对
			List<Map<String, String>> modelNumberPairs = new ArrayList<>();
			for (List<Object> row : allRows) {
				if (row.size() >= 2) {
					String model = Convert.toStr(row.get(0), "").trim();
					String number = Convert.toStr(row.get(1), "").trim();
					
					// 跳过空行
					if (StrUtil.isNotBlank(model) && StrUtil.isNotBlank(number)) {
						Map<String, String> pair = new HashMap<>();
						pair.put("model", model);
						pair.put("number", number);
						modelNumberPairs.add(pair);
					}
				}
			}

			// 验证处理后的数据不为空
			if (modelNumberPairs.isEmpty()) {
				return JSONUtil.createObj()
						.set("success", false)
						.set("msg", "Excel文件中未找到有效的型号和编号数据");
			}

			// 创建临时目录
			String path = "//customExcel//" + System.currentTimeMillis() + "//";
			String thisTempPath = fileUploadPath + path;
			FileUtil.mkdir(thisTempPath);

			// 使用简化的数据处理曲线
			int hasCurveNum = processCurveDataDirect(modelNumberPairs, thisTempPath, fileUploadPath);

			// 返回结果
			if (hasCurveNum > 0) {
				File zip = ZipUtil.zip(thisTempPath, fileUploadPath + "//customExcel//" +
						System.currentTimeMillis() + "//" + tableName + "自定义数据（包含曲线报告）.zip");
				log.info("直接处理模式完成，生成{}个曲线报告", hasCurveNum);
				return JSONUtil.createObj()
						.set("success", true)
						.set("file", FileUtil.subPath(fileUploadPath, zip));
			} else {
				return JSONUtil.createObj()
						.set("success", false)
						.set("msg", "未找到匹配的曲线报告文件，请检查Excel中的编号和型号是否正确");
			}

		} catch (Exception e) {
			log.error("直接处理模式导出Excel失败", e);
			return JSONUtil.createObj()
					.set("success", false)
					.set("msg", "导出失败，原因：" + e.getMessage());
		}
	}

	/**
	 * 处理Excel数据
	 * 该方法的主要作用是筛选和匹配Excel数据中的有效信息，并将其分类存储
	 *
	 * @param list      Excel数据列表，每个元素是一个包含数据的Map
	 * @param datas     原始JSON数据数组，用于与Excel数据进行匹配
	 * @param newDatas  匹配成功后的数据将被添加到此数组
	 * @param extraData 未使用（即未匹配成功）的Excel数据将被添加到此数组
	 */
	private void processExcelData(List<Map<String, Object>> list, JSONArray datas,
								  JSONArray newDatas, JSONArray extraData) {
		// 遍历Excel数据列表
		for (Map<String, Object> map : list) {
			// 标记当前Map是否已被使用（匹配成功）
			boolean mapIsUse = false;

			// 只保留键以"V"开头的条目，这是为了过滤出需要的数据
			map.entrySet().removeIf(entry -> !entry.getKey().startsWith("V"));

			// 遍历原始JSON数据数组，尝试匹配每个Map条目
			for (int j = 0; j < datas.size(); j++) {
				JSONObject object = datas.getJSONObject(j);
				// 假设所有键值都匹配
				boolean isAllEquals = true;
				// 检查当前JSON对象是否与Map中的所有键值对匹配
				for (String key : map.keySet()) {
					// 如果有任何一个键值不匹配，则标记为不匹配，并中断检查
					if (!StrUtil.equals(object.getStr(key), Convert.toStr(map.get(key)))) {
						isAllEquals = false;
						break;
					}
				}
				// 如果所有键值都匹配成功
				if (isAllEquals) {
					// 标记当前Map为已使用
					mapIsUse = true;
					// 将匹配成功的JSON对象添加到新数据数组中
					newDatas.add(JSONUtil.parseObj(object.toString()));
				}
			}

			// 如果当前Map没有匹配成功，则将其添加到未使用数据数组中
			if (!mapIsUse) {
				extraData.add(map);
			}
		}
	}

	private void processRowNumbers(JSONArray newDatas, JSONArray indexParams) {
		for (int i = 0; i < newDatas.size(); i++) {
			JSONObject object = newDatas.getJSONObject(i);
			object.set("ROWNUM", i + 1).set("ROWNO", i + 1);
			if (!indexParams.isEmpty()) {
				for (int k = 0; k < indexParams.size(); k++) {
					object.set(indexParams.getJSONObject(k).getStr("COL_NAME"), i + 1);
				}
			}
		}
	}

	private void processExtraData(JSONArray extraData, Map<String, String> headerAlias, File file) {
		// 将extraData中的数据的Key值替换回中文
		for (int i = 0; i < extraData.size(); i++) {
			JSONObject object = extraData.getJSONObject(i);
			for (String key : new ArrayList<>(object.keySet())) {
				for (Map.Entry<String, String> entry : headerAlias.entrySet()) {
					if (StrUtil.equals(key, entry.getValue())) {
						object.set(entry.getKey(), object.get(key));
						object.remove(key);
					}
				}
			}
		}

		// 写入额外数据到新的sheet
		ExcelWriter writer = ExcelUtil.getWriter(file);
		writer.setSheet(1);
		writer.renameSheet("多余的数据");
		writer.write(extraData, true);
		Workbook wb = writer.getWorkbook();
		wb.setActiveSheet(0);
		writer.close();
	}

	/**
	 * 创建证书
	 *
	 * @param objsStr             证书数据JSON字符串
	 * @param certificateFilePath 证书模板文件径
	 * @return 生成的PDF文件路径
	 */
	public String createCertificate(String objsStr, String certificateFilePath) throws IOException {
		JSONArray objs = JSONUtil.parseArray(objsStr);
		Map<String, Object> map = new HashMap<>();

		// 处理证书数据
		for (int i = 0; i < objs.size(); i++) {
			JSONObject obj = objs.getJSONObject(i);
			String type = obj.getStr("type");
			String name = obj.getStr("name");
			String value = obj.getStr("value");

			if ("text".equals(type)) {
				map.put(name, value);
			} else if ("img".equals(type)) {
				String imgFormat = obj.getStr("imgFormat");
				map.put(name, new PictureRenderData(97, 42, "." + imgFormat,
						Files.newInputStream(Paths.get(fileUploadPath + value))));
			}
		}

		return createCertificateFile(map, fileUploadPath, certificateFilePath);
	}

	/**
	 * 创建证书文件
	 */
	public String createCertificateFile(Map<String, Object> map, String fileUploadPath, String tplPath) {
		String month = new SimpleDateFormat("yyyy-MM").format(new Date());
		String folder = fileUploadPath + File.separator + month;
		FileUtil.mkdir(folder);

		String docxUuid = UUID.randomUUID().toString();
		String pdfUuid = UUID.randomUUID().toString();
		String docxFile = folder + File.separator + docxUuid;
		String pdfFile = folder + File.separator + pdfUuid;

		try {
			createCertificateFile(map, tplPath, docxFile, pdfFile);
			return "//" + month + "//" + pdfUuid;
		} catch (Exception e) {
			log.error("创建证书文件失败", e);
			throw new RuntimeException("创建证书文件失败：" + e.getMessage());
		}
	}

	public void createCertificateFile(Map<String, Object> map, String tplPath, String docxFile, String pdfFile) throws IOException {
		// 配置模板
		ConfigureBuilder builder = Configure.newBuilder();
		builder.buildGrammerRegex(RegexUtils.createGeneral("{{", "}}"));
		// 渲染模板
		File tplFile = new File(tplPath);
		XWPFTemplate template = XWPFTemplate.compile(tplFile, builder.build()).render(map);

		// 保存DOCX文件
		try (FileOutputStream out = new FileOutputStream(docxFile)) {
			template.write(out);
		}
		template.close();
		// 转换为PDF
		convertDocxToPdf(docxFile, pdfFile);
	}

	/**
	 * 获取Aspose License
	 */
	private void getLicense() {
		try {
			InputStream is = getClass().getClassLoader().getResourceAsStream("license.xml");
			License license = new License();
			license.setLicense(is);
		} catch (Exception e) {
			File licenseFile = new File("resources/license.xml");
			if (!licenseFile.exists()) {
				throw new RuntimeException("License file not found at: " + licenseFile.getAbsolutePath());
			}
			License license = new License();
			try {
				license.setLicense(Files.newInputStream(licenseFile.toPath()));
			} catch (Exception ex) {
				log.error("获取Aspose License失败", e);
				throw new RuntimeException("获取Aspose License失败：" + e.getMessage());
			}
		}
	}

	/**
	 * 将DOCX转换为PDF
	 */
	private void convertDocxToPdf(String docxFile, String pdfFile) {
		getLicense();
		try {
			Document doc = new Document(docxFile);
			doc.save(pdfFile, 40); // 40是PDF保存选项
		} catch (Exception e) {
			log.error("DOCX转PDF失败", e);
			throw new RuntimeException("DOCX转PDF失败：" + e.getMessage());
		}
	}

	/**
	 * 删除照片文件
	 *
	 * @param filePath 文件路径
	 * @return JSONObject 删除结果
	 */
	public JSONObject deletePhoto(String filePath) {
		JSONObject res = new JSONObject();
		boolean success = false;
		String msg;

		try {
			// 获取完整的文件路径
			String fullPath = fileUploadPath + filePath;
			log.info("删除文件路径: {}", fullPath);

			// 创建文件对象
			File deleteFile = new File(fullPath);

			// 检查文件是否存在并删除
			if (deleteFile.exists()) {
				success = deleteFile.delete();
				msg = success ? "删除成功！" : "删除失败！";
			} else {
				msg = "该文件不存在！";
			}

		} catch (Exception e) {
			log.error("删除文件失败", e);
			msg = "删除文件报错：" + e.getMessage();
		}

		return res.set("success", success)
				.set("msg", msg);
	}

	/**
	 * 处理照片下载和打包
	 *
	 * @param photos      照片数据
	 * @param tempPath    临时目录路径
	 * @param zipFileName ZIP文件
	 * @return 打包的ZIP文件
	 */
	private File processPhotosDownload(JSONArray photos, String tempPath, String zipFileName) {
		try {
			// 创建临时目录
			String zipFilePath = tempPath + File.separator + zipFileName + ".zip";
			String photosPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
			FileUtil.mkdir(photosPath);

			// 处理每张照片
			for (int i = 0; i < photos.size(); i++) {
				JSONObject photo = photos.getJSONObject(i);
				String type = photo.getStr("TYPE");
				String path = photo.getStr("PHOTO_PATH");
				String fileName = photo.getStr("PHOTO_NUMBER")
						.replace("/", "_")
						.replace("\\", "_");

				if ("upload".equals(type)) {
					// 处理手动上传的照片
					String format = photo.getStr("PHOTO_FORMAT");
					String sourcePath = fileUploadPath + path;
					if (FileUtil.exist(sourcePath)) {
						FileUtil.copy(sourcePath,
								photosPath + fileName + "." + format, true);
					}
				} else if ("auto".equals(type)) {
					// 处理自动生成的照片
					File file = HttpUtil.downloadFileFromUrl(path, photosPath);
					FileUtil.rename(file, fileName, true, true);
				}
			}

			// 创建ZIP文件
			File zipFile = ZipUtil.zip(photosPath, zipFilePath);
			FileUtil.del(photosPath);

			return zipFile;

		} catch (Exception e) {
			log.error("处理照片下载失败", e);
			throw new RuntimeException("下载照片出错：" + e.getMessage());
		}
	}

	/**
	 * 下载单元格的图片
	 */
	public File downloadTdPhotos(String tableConfigId, String treeId, String onlyValue, String paramId) {
		// 查询照片数据
		JSONObject params = JSONUtil.createObj()
				.set("tableConfigId", tableConfigId)
				.set("treeId", treeId)
				.set("onlyValue", onlyValue)
				.set("paramId", paramId);

		JSONObject res = Util.postTwxForObject("Thing.Fn.SecondTable", "QueryTdPhoto", params);
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;

		if (!res.getBool("success")) {
			throw new RuntimeException(res.getStr("msg"));
		}

		JSONArray photos = res.getJSONArray("data");
		if (photos.isEmpty()) {
			throw new RuntimeException("没有照片可以下载！");
		}

		// 构建文件名
		JSONObject onlyObj = JSONUtil.parseObj(onlyValue);
		List<String> names = new ArrayList<>();
		for (String key : onlyObj.keySet()) {
			names.add(onlyObj.getStr(key));
		}
		String newFileName = StrUtil.join("_", names)
				.replace("/", "_")
				.replace("\\", "_");

		return processPhotosDownload(photos, thisTempPath, newFileName);
	}

	/**
	 * 下载策划表中的所有图片
	 */
	public File downloadAllPhotos(String tableConfigId, String treeId, String thisTempPath) {
		// 查询所有照片数据
		JSONObject params = JSONUtil.createObj()
				.set("tableConfigId", tableConfigId)
				.set("treeId", treeId);

		JSONObject res = Util.postTwxForObject("Thing.Fn.SecondTable", "QueryAllPhoto", params);

		if (!res.getBool("success")) {
			throw new RuntimeException(res.getStr("msg"));
		}

		JSONArray photos = res.getJSONArray("data");
		if (photos.isEmpty()) {
			throw new RuntimeException("没有照片可以下载！");
		}

		return processPhotosDownload(photos, thisTempPath, "批量照片下载");
	}

	/**
	 * 下载MES文件
	 *
	 * @param url 下载URL
	 * @return 下载结果
	 */
	public JSONObject downloadMesFile(String url) {
		return Util.downloadFromUrl(url);
	}

	/**
	 * 生成策划表中的所有图片
	 *
	 * @param downloadId    下载ID
	 * @param tableConfigId 表格配置ID
	 * @param treeId        树节点ID
	 * @param creator       创建者
	 * @param type          类型
	 */
	public void generateAllPhotos(String downloadId, String tableConfigId,
								  String treeId, String creator, String type) {
		// 创建临时目录
		String thisTempPath = fileUploadPath + File.separator + "qualityDownload"
				+ File.separator + creator + File.separator + type + File.separator;
		FileUtil.mkdir(thisTempPath);


		// 构建参数
		JSONObject param = new JSONObject()
				.set("downloadId", downloadId);

		try {
			File file = downloadAllPhotos(tableConfigId, treeId, thisTempPath);
			String fileName = file.getName();

			// 重命名文件
			File newFile = FileUtil.rename(file,
					String.valueOf(System.currentTimeMillis()), true, true);

			// 获取文件信息
			String fileSize = FileUtil.readableFileSize(newFile);
			String childPath = FileUtil.subPath(fileUploadPath, newFile.getAbsolutePath());

			// 设置成功参数
			param.set("filePath", childPath)
					.set("fileName", fileName)
					.set("fileSize", fileSize)
					.set("isComplete", 1)
					.set("msg", "文件已生成");
		} catch (Exception e) {
			// 设置失败参数
			param.set("isComplete", 2)
					.set("msg", "文件生成失败，原因：" + e);
		}

		// 通知生成完成
		Util.postTwxForObject("Thing.Fn.SecondTable", "GenerateFileComplete", param);
	}

	/**
	 * Excel转HTML
	 *
	 * @param filepath Excel文件相对路径
	 * @return HTML字符串
	 */
	public String excelToHtml(String filepath) {
		// 获取整的文件路径
		String fullPath = fileUploadPath + File.separator + filepath;

		// 调用Excel转HTML工具类
		return POIReadExcelToHtml.exceltothml(fullPath);
	}

	/**
	 * 导出质量影像记录策划表
	 *
	 * @param processTreeId 工序树ID
	 * @param tableId       表格ID
	 * @return 导出的Excel文件
	 */
	public File exportPhotoPlanExcel(String processTreeId, String tableId) {
		File resFile;
		String fileName;

		// 查询策划表数据
		String sql = "select * from quality_plan where type=2 and TABLE_CONFIG_ID=" + tableId + " and treeid=" + processTreeId;
		JSONArray objects = Util.postQuerySql(sql);

		if (objects.isEmpty()) {
			return null;
		}

		try {
			JSONObject object = objects.getJSONObject(0);
			JSONArray excelData = object.getJSONArray("DATA");
			String filePath = fileUploadPath + object.getStr("FILEPATH");
			fileName = object.getStr("FILENAME");
			// 判断文件名是否已包含.xlsx后缀
			String suffix = ".xlsx";
			if (fileName.toLowerCase().endsWith(suffix)) {
				suffix = "";
			}
			String resFilePath = tempPath + File.separator + System.currentTimeMillis() +
					File.separator + fileName + suffix;

			// 查询工序质量照片数据
			JSONObject res = Util.postTwxForObject("Thing.Fn.SecondTable", "QueryProcessQualityPhotoData",
					JSONUtil.createObj()
							.set("tableConfigId", tableId)
							.set("treeId", processTreeId));

			if (!res.getBool("success")) {
				return FileUtil.copy(filePath, resFilePath, true);
			}

			// 处理noKeys数据
			JSONArray noKeys = processNoKeys(res.getJSONArray("data"));

			if (noKeys.isEmpty()) {
				return FileUtil.copy(filePath, resFilePath, true);
			}

			// 更新Excel数据
			updateExcelData(excelData, noKeys);

			// 写入Excel文件
			ExcelWriter writer = ExcelUtil.getWriter(resFilePath);
			writer.write(excelData);

			// 设置样式
			applyExcelStyles(writer);

			writer.close();
			resFile = new File(resFilePath);

		} catch (Exception e) {
			log.error("导出影像记录策划表失败", e);
			throw new RuntimeException("导出失败：" + e.getMessage());
		}

		return resFile;
	}

	/**
	 * 处理noKeys数据
	 */
	private JSONArray processNoKeys(JSONArray data) {
		JSONArray noKeys = new JSONArray();

		for (int i = 0; i < data.size(); i++) {
			JSONObject table = data.getJSONObject(i);
			JSONArray onlyNamesValueArr = table.getJSONArray("onlyNamesValueArr");

			for (int r = 0; r < onlyNamesValueArr.size(); r++) {
				JSONObject onlyNamesValue = onlyNamesValueArr.getJSONObject(r);
				if (onlyNamesValue.toString().contains(":noKey")) {
					if (!isNoKeyExists(noKeys, onlyNamesValue)) {
						noKeys.add(onlyNamesValue);
					}
				}
			}
		}

		return noKeys;
	}

	/**
	 * 检查noKey是否已存在
	 */
	private boolean isNoKeyExists(JSONArray noKeys, JSONObject newNoKey) {
		for (int i = 0; i < noKeys.size(); i++) {
			JSONObject existObj = noKeys.getJSONObject(i);
			boolean same = true;
			for (String param : existObj.keySet()) {
				if (!existObj.getStr(param).replace(":noKey", "")
						.equals(newNoKey.getStr(param).replace(":noKey", ""))) {
					same = false;
					break;
				}
			}
			if (same) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 更新Excel数据
	 */
	private void updateExcelData(JSONArray excelData, JSONArray noKeys) {
		JSONArray headers = excelData.getJSONArray(0);

		for (int i = 1; i < excelData.size(); i++) {
			JSONArray row = excelData.getJSONArray(i);
			for (int j = 0; j < noKeys.size(); j++) {
				JSONObject noKey = noKeys.getJSONObject(j);
				if (isRowMatchNoKey(row, noKey, headers)) {
					updateRowWithNoKey(row, noKey, headers);
				}
			}
		}
	}

	/**
	 * 检查行是否匹配noKey
	 */
	private boolean isRowMatchNoKey(JSONArray row, JSONObject noKey, JSONArray headers) {
		for (String param : noKey.keySet()) {
			if (!noKey.getStr(param).replace(":noKey", "")
					.equals(row.getStr(headers.lastIndexOf(param)))) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 使用noKey更新行数据
	 */
	private void updateRowWithNoKey(JSONArray row, JSONObject noKey, JSONArray headers) {
		for (String param : noKey.keySet()) {
			row.set(headers.lastIndexOf(param), noKey.getStr(param));
		}
	}

	/**
	 * 应用Excel样式
	 */
	private void applyExcelStyles(ExcelWriter writer) {
		// 创建标题样式
		Font font = writer.createFont();
		font.setBold(true);
		CellStyle headerStyle = createHeaderStyle(writer, font);

		// 创建红色字体样式
		Font redFont = writer.createFont();
		redFont.setColor(IndexedColors.RED.getIndex());
		CellStyle redStyle = createRedStyle(writer, redFont);

		// 应用样式到单元格
		for (int r = 0; r < writer.getRowCount(); r++) {
			for (int c = 0; c < writer.getColumnCount(); c++) {
				Cell cell = writer.getCell(c, r);
				if (r == 0) {
					cell.setCellStyle(headerStyle);
				}
				String cellValue = Util.getCellValue(cell);
				if (cellValue.contains(":noKey")) {
					cell.setCellValue(cellValue.replace(":noKey", ""));
					cell.setCellStyle(redStyle);
				}
			}
		}
	}

	/**
	 * 创建标题样式
	 */
	private CellStyle createHeaderStyle(ExcelWriter writer, Font font) {
		CellStyle style = writer.createCellStyle();
		style.setFont(font);
		style.setVerticalAlignment(VerticalAlignment.CENTER);
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setBorderBottom(BorderStyle.THIN);
		style.setBorderLeft(BorderStyle.THIN);
		style.setBorderTop(BorderStyle.THIN);
		style.setBorderRight(BorderStyle.THIN);
		style.setWrapText(true);
		return style;
	}

	/**
	 * 创建红色字体样式
	 */
	private CellStyle createRedStyle(ExcelWriter writer, Font font) {
		CellStyle style = writer.createCellStyle();
		style.setFont(font);
		style.setVerticalAlignment(VerticalAlignment.CENTER);
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setBorderBottom(BorderStyle.THIN);
		style.setBorderLeft(BorderStyle.THIN);
		style.setBorderTop(BorderStyle.THIN);
		style.setBorderRight(BorderStyle.THIN);
		style.setWrapText(true);
		return style;
	}

	/**
	 * 导出质量影像记录确认表以及照片
	 *
	 * @param treeId        树节点ID
	 * @param tableConfigId 表格配置ID
	 * @param fileName      文件名称
	 * @return 打包的ZIP文件
	 */
	public File exportQualityPhotoExcel(String treeId, String tableConfigId, String fileName) {
		// 创建临时目录
		String tempFolderPath = tempPath + File.separator + System.currentTimeMillis();
		exportQualityPhotoExcel(treeId, tableConfigId, tempFolderPath, fileName);
		String zipFilePath = tempPath + File.separator + fileName + ".zip";
		ZipUtil.zip(tempFolderPath, zipFilePath);
		return FileUtil.file(zipFilePath);
	}


	/**
	 * 生成质量影像记录确认表以及照片
	 *
	 * @param downloadId    下载ID
	 * @param treeId        树节点ID
	 * @param tableConfigId 表格配置ID
	 * @param creator       创建者
	 * @param type          类型
	 * @param fileName      文件名称
	 */
	public void generateQualityPhotoExcel(String downloadId, String treeId, String tableConfigId,
										  String creator, String type, String fileName) {
		// 创建临时目录
		String tempPath = fileUploadPath + File.separator + "qualityDownload"
				+ File.separator + creator + File.separator + type + File.separator;
		FileUtil.mkdir(tempPath);

		// 构建参数
		JSONObject param = new JSONObject()
				.set("downloadId", downloadId);

		try {
			// 创建临时文件夹
			String tempFolderPath = tempPath + File.separator + System.currentTimeMillis();

			// 导出Excel文件
			exportQualityPhotoExcel(treeId, tableConfigId, tempFolderPath, fileName);

			// 创建ZIP文件
			String zipFilePath = tempPath + File.separator + fileName + ".zip";
			ZipUtil.zip(tempFolderPath, zipFilePath);

			// 处理文件
			File file = FileUtil.file(zipFilePath);
			File newFile = FileUtil.rename(file, String.valueOf(System.currentTimeMillis()), true, true);

			// 获取文件信息
			String fileSize = FileUtil.readableFileSize(newFile);
			String childPath = FileUtil.subPath(fileUploadPath, newFile.getAbsolutePath());

			// 设置成功参数
			param.set("filePath", childPath)
					.set("fileName", fileName + ".zip")
					.set("fileSize", fileSize)
					.set("isComplete", 1)
					.set("msg", "文件已生成");

		} catch (Exception e) {
			// 设置失败参数
			param.set("isComplete", 2)
					.set("msg", "文件生成失败，原因：" + e.getLocalizedMessage());
		}

		// 通知生成完成
		Util.postTwxForObject("Thing.Fn.SecondTable", "GenerateFileComplete", param);
	}


	public static void exportQualityPhotoExcel(String treeId, String tableConfigId, String tempPath, String zipFileName) {
		JSONObject res = Util.postTwxForObject("Thing.Fn.SecondTable", "QueryProcessQualityPhotoData",
				JSONUtil.createObj().set("tableConfigId", tableConfigId).set("treeId", treeId));
		JSONObject summaryRes = Util.postTwxForObject("Thing.Fn.SecondTable", "QueryQualityPhotoSummary",
				JSONUtil.createObj().set("table_config_id", tableConfigId).set("tree_id", treeId));
		JSONArray lists = new JSONArray();
		int colCount = 0;
		int rowNum = 0;
		List<Integer> rowC = new ArrayList<>();
		if (res.getBool("success")) {

			String photosPath = tempPath + File.separator;
			FileUtil.mkdir(photosPath);
			String uploadPath = Util.getFileUploadPath();

			JSONArray arr = res.getJSONArray("data");
			String statusStr = "状态";
			String indexStr = "序号";
			String actualPhotoStr = "实际照片";
			for (int i = 0; i < arr.size(); i++) {
				JSONObject obj = arr.getJSONObject(i);
				JSONArray nameArr = new JSONArray();
				nameArr.add(obj.getStr("name"));
				lists.add(nameArr);
				rowNum++;
				rowC.add(rowNum);
				JSONArray colArr = obj.getJSONArray("cols");
				int statusIndex = colArr.indexOf(statusStr);
				int indexIndex = colArr.indexOf(indexStr);
				int actualPhotoIndex = colArr.indexOf(actualPhotoStr);

				colCount = colArr.size();
				lists.add(colArr);
				rowNum++;
				JSONArray onlyValues = obj.getJSONArray("onlyValues");
				JSONArray datas = obj.getJSONArray("datas");
				for (int j = datas.size() - 1; j >= 0; j--) {
					JSONArray d = datas.getJSONArray(j);
					JSONObject onlyObj = onlyValues.getJSONObject(j);
					String status = d.getStr(statusIndex);
					if ("冻结".equals(status)) {
						datas.remove(j);
					} else {
						JSONArray photos = d.getJSONArray(actualPhotoIndex);

						List<String> names = new ArrayList<>();
						for (String key : onlyObj.keySet()) {
							names.add(onlyObj.getStr(key));
						}
						String folderName = StrUtil.join("_", names).replace("/", "_").replace("\\", "_");

						String folderPath = photosPath + File.separator + folderName + File.separator;
						FileUtil.mkdir(folderPath);
						String photoStr = "";
						int uploadNum = 0;
						int photo360Num = 0;
						int autoNum = 0;
						for (int x = 0; x < photos.size(); x++) {
							JSONObject photo = photos.getJSONObject(x);
							String photoType = photo.getStr("TYPE");

							String path = photo.getStr("PHOTO_PATH");
							String fileName = photo.getStr("PHOTO_NUMBER").replace("/", "_").replace("\\", "_");

							if (photoType.equals("auto")) {
								try {
									File file = HttpUtil.downloadFileFromUrl(path, folderPath);
									FileUtil.rename(file, fileName, true, true);
									autoNum++;
								} catch (Exception ignored) {

								}

							} else {

								//手动上传的照片 直接复制到临时文件夹
								String format = photo.getStr("PHOTO_FORMAT");
								if (FileUtil.exist(uploadPath + path)) {
									FileUtil.copy(uploadPath + path, folderPath + fileName + "." + format, true);
								}

								if (photo.getStr("PHOTO_FORMAT").equals("zip") || photo.getStr("PHOTO_FORMAT").equals("rar")) {
									photo360Num++;
								} else {
									uploadNum++;
								}

							}
						}
						if (uploadNum > 0) {
							photoStr += "\r 手动上传(" + uploadNum + ")";
						}
						if (autoNum > 0) {
							photoStr += "\r 自动采集(" + autoNum + ")";
						}
						if (photo360Num > 0) {
							photoStr += "\r 全景影像(" + photo360Num + ")";
						}
						photoStr = StrUtil.isNotEmpty(photoStr) ? photoStr.substring(2) : "";
						d.set(actualPhotoIndex, photoStr);
					}
				}
				// 重新给序号赋值
				for (int j = 0; j < datas.size(); j++) {
					JSONArray d = datas.getJSONArray(j);
					d.set(indexIndex, j + 1);
				}
				lists.addAll(datas);
				rowNum += datas.size();
			}

			String excelPath = photosPath + File.separator + zipFileName + ".xlsx";
			// 通过工具类创建writer
			ExcelWriter writer = ExcelUtil.getWriter(excelPath);

			writer.setDefaultRowHeight(20);
			writer.renameSheet("影像记录确认表");
			// 一次性写出内容
			writer.write(lists, false);
			// 设置列宽
			for (int i = 0; i < colCount; i++) {
				writer.setColumnWidth(i, 20);
			}

			// 设置行高
			for (int i = 0; i < rowNum; i++) {
				writer.setRowHeight(i, 26);
			}

			// 设置内容字体 加粗
			Font font = writer.createFont();
			font.setBold(true);

			// 设置标题样式
			CellStyle nameStyle = writer.createCellStyle();
			nameStyle.setFont(font);
			nameStyle.setAlignment(HorizontalAlignment.LEFT);
			nameStyle.setVerticalAlignment(VerticalAlignment.CENTER);
			nameStyle.setWrapText(true);

			CellStyle headerStyle = writer.createCellStyle();
			headerStyle.setFont(font);
			headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
			headerStyle.setAlignment(HorizontalAlignment.CENTER);
			headerStyle.setBorderBottom(BorderStyle.THIN);
			headerStyle.setBorderLeft(BorderStyle.THIN);
			headerStyle.setBorderTop(BorderStyle.THIN);
			headerStyle.setBorderRight(BorderStyle.THIN);
			headerStyle.setWrapText(true);
			for (Integer rowN : rowC) {
				// 设置标题样式
				writer.setStyle(nameStyle, 0, rowN - 1);
				for (int i = 0; i < colCount; i++) {
					writer.setStyle(headerStyle, i, rowN);
				}
				// 设置行样式无效 不知道什么原因
				// writer.setRowStyle(rowN, nameStyle);

				// 合并单元格后的标题行
				writer.merge(rowN - 1, rowN - 1, 0, colCount - 1, null, false);
			}

			if (summaryRes.getBool("success")) {
				writer.setSheet(1);
				writer.renameSheet("影像记录汇总表");
				JSONArray summaryData = summaryRes.getJSONObject("data").getJSONArray("excelData");
				writer.write(summaryData, false);

				for (int i = 0; i < writer.getColumnCount(); i++) {
					// 设置标题样式
					writer.setStyle(headerStyle, i, 0);
					// 设置列宽
					writer.setColumnWidth(i, 20);
				}

				// 设置行高
				for (int i = 0; i < writer.getRowCount(); i++) {
					writer.setRowHeight(i, 26);
				}

			}
			writer.setSheet(0);
			Font redFont = writer.createFont();
			redFont.setColor(IndexedColors.RED.getIndex());
			CellStyle redStyle = writer.createCellStyle();
			redStyle.setFont(redFont);
			redStyle.setVerticalAlignment(VerticalAlignment.CENTER);
			redStyle.setAlignment(HorizontalAlignment.CENTER);
			redStyle.setBorderBottom(BorderStyle.THIN);
			redStyle.setBorderLeft(BorderStyle.THIN);
			redStyle.setBorderTop(BorderStyle.THIN);
			redStyle.setBorderRight(BorderStyle.THIN);
			redStyle.setWrapText(true);

			//处理noKeys
			for (int r = 0; r < writer.getRowCount(); r++) {
				for (int c = 0; c < writer.getColumnCount(); c++) {
					Cell cell = writer.getCell(c, r);
					String cellValue = Util.getCellValue(cell);
					if (cellValue.contains(":noKey")) {
						cell.setCellValue(cellValue.replace(":noKey", ""));
						cell.setCellStyle(redStyle);
					}
				}
			}
			// 关闭writer，释放内存
			writer.close();
		}
	}

	/**
	 * 导入MES三级表数据
	 *
	 * @param type     数据来源类型 类型  2：二级表数据来源为三级表 3：二级表与三级表完全相同 4：二级表与三级表表头相同
	 * @param fileType 文件类型(2或3)
	 * @param tableId  配置表ID
	 * @param treeId   树节点ID
	 * @param creator  创建者
	 * @param endY     数据起始行
	 * @param file     上传的文件
	 * @param filename 文件名称
	 * @return 导入结果
	 */
	public JSONObject importThreeExcel(String type, String fileType, String tableId,
									   String treeId, String creator, int endY,
									   MultipartFile file, String filename) {
		try {
			// 创建月份目录
			String month = new SimpleDateFormat("yyyy-MM").format(new Date());
			String monthPath = fileUploadPath + File.separator + month;
			FileUtil.mkdir(monthPath);

			// 生成唯一文件名
			String uuid = UUID.randomUUID().toString();
			String fullPath = monthPath + File.separator + uuid;
			String relativePath = "//" + month + "//" + uuid;

			// 保存上传的文件
			try (InputStream input = file.getInputStream();
				 FileOutputStream output = new FileOutputStream(fullPath)) {
				byte[] buffer = new byte[102400];
				int length;
				while ((length = input.read(buffer)) != -1) {
					output.write(buffer, 0, length);
				}
			}

			// 构建参数对象
			JSONObject fields = new JSONObject()
					.set("fullPath", fullPath)
					.set("relativePath", relativePath)
					.set("treeId", treeId)
					.set("creator", creator)
					.set("filename", filename)
					.set("type", type)
					.set("tableId", tableId)
					.set("endY", endY)
					.set("productId", "")
					.set("mes", false)
					.set("fileType", fileType);

			// 调用解析方法
			return parsingThreeExcel(fields);

		} catch (Exception e) {
			log.error("导入三级表数据失败", e);
			return new JSONObject()
					.set("success", false)
					.set("msg", e.toString())
					.set("result", e.toString());
		}
	}

	/**
	 * 解析上传数据来源为三级表的文件
	 */
	public static JSONObject parsing1(JSONObject fields, String tplPath, JSONArray params) {

		String fullPath = fields.getStr("fullPath");
		String relativePath = fields.getStr("relativePath");
		String treeId = fields.getStr("treeId");
		String creator = fields.getStr("creator");
		String filename = fields.getStr("filename");
		String tableId = fields.getStr("tableId");
		String productId = fields.getStr("productId");
		boolean mes = fields.getBool("mes");
		JSONObject res = new JSONObject();
		boolean success = true;
		int result = 0;
		String msg = "";
		JSONArray readAll = Util.readAll(fullPath);
		if (Util.compareThreeExcelTpl(tplPath, fullPath) || mes) {
			JSONObject obj = new JSONObject();
			for (int i = 0; i < params.size(); i++) {
				JSONObject param = params.getJSONObject(i);
				String threeArea = param.getStr("THREE_AREA", "");
				String secondArea = param.getStr("SECOND_AREA", "");
				String colName = param.getStr("COL_NAME", "");
				String colFormat = param.getStr("FORMAT", "0");
				String value = "";
				if (Util.isCellAddress(threeArea)) {
					int[] rowCol = Util.convertExcelPosToRowCol(threeArea);
					value = readAll.getJSONArray(rowCol[0]).getStr(rowCol[1]);
				}
				// 判断是否为纯大写字母并且列名不为空
				if (Util.isCapital(secondArea) && StrUtil.isNotEmpty(colName)) {
					if (colFormat.equals("3") || colFormat.equals("4")) {
						if (StrUtil.isNotBlank(value)) {
							value = Util.strToClobSql(value);
						}
					}
					obj.set(colName, value);
				} else {
					obj.set("col" + secondArea, value);
				}
			}
			JSONObject twxParams = new JSONObject();
			twxParams.set("TABLE_CONFIG_ID", tableId);
			twxParams.set("TREE_ID", treeId);
			twxParams.set("FILEPATH", relativePath);
			twxParams.set("FILENAME", filename);
			twxParams.set("data", obj.toString());
			twxParams.set("creator", creator);
			twxParams.set("productId", productId);
			JSONArray jarr = Util.postTwx(THING, "AddTableData", twxParams);
			result = jarr.getJSONObject(0).getInt("result", 0);
		} else {
			success = false;
			msg = "上传的文件与模板文件不一致，请校验后重新上传！";
		}
		res.set("success", success);
		res.set("msg", msg);
		res.set("result", result);
		return res;
	}

	/**
	 * 解析上传数据 二级表与三级表表头相同
	 */
	public static JSONObject parsing2(JSONObject fields, JSONArray params) throws Exception {
		String fullPath = fields.getStr("fullPath");
		String relativePath = fields.getStr("relativePath");
		String treeId = fields.getStr("treeId");
		String creator = fields.getStr("creator");
		String filename = fields.getStr("filename");
		String tableId = fields.getStr("tableId");
		int endY = fields.getInt("endY");
		String productId = fields.getStr("productId");
		JSONObject res = new JSONObject();
		boolean success = true;
		int result = 0;
		String msg = "";
		// 二级表与三级表表头相同
		Sheet sheet = Util.getSheet(fullPath);
		// 获取合并单元格的坐标集合
		List<CellRangeAddress> listCombineCell = sheet.getMergedRegions();
		int lastRowNum = sheet.getLastRowNum();
		for (int i = endY - 1; i <= lastRowNum; i++) {
			Row row = sheet.getRow(i);
			JSONObject obj = new JSONObject();
			int currentRowMaxRowspan = 1;// 这一行数据最大的行合并数
			StringBuilder mergedInfo = new StringBuilder();// 这一行的合并信息

			for (int x = 0; x < params.size(); x++) {
				JSONObject param = params.getJSONObject(x);
				String secondArea = param.getStr("SECOND_AREA", "");
				String colName = param.getStr("COL_NAME", "");
				String colFormat = param.getStr("FORMAT", "0");
				String v;
				if (Util.isCapital(secondArea)) {// 判断是否为纯大写字母
					int cellIndex = Util.transferase_10(secondArea);
					Cell cell = row.getCell(cellIndex - 1);
					if (cell == null) {
						continue;
					}
					v = Util.getCellValue(cell);
					Map<String, Object> isCombined = Util.isCombineCell(listCombineCell, cell);
					// 如果是，则判断是否有值，有值的才添加到list中
					if ((Boolean) isCombined.get("flag")) {
						CellRangeAddress ca = (CellRangeAddress) isCombined.get("CellRangeAddress");
						int c = ca.getFirstColumn();
						int r = ca.getFirstRow();
						if ((v != null && !v.isEmpty())
								|| cell.getColumnIndex() == c && cell.getRowIndex() == r) {
							// int colspan = (int)
							// isCombined.get("mergedCol");
							int rowspan = (int) isCombined.get("mergedRow");
							if (rowspan > 1) {
								mergedInfo.append(colName).append(":").append(rowspan).append(",");
							}
							if (rowspan > currentRowMaxRowspan) {
								currentRowMaxRowspan = rowspan;
							}
						}
						// 如果不是，则直接插入
					}
					if (colFormat.equals("3") || colFormat.equals("4")) {
						if (StrUtil.isNotBlank(v)) {
							v = Util.strToClobSql(v);
						}
					}
					obj.set(colName, v);

				} else {
					obj.set("col" + secondArea, Util.getAddressValue(fullPath, 0, secondArea));
				}
			}
			if (!mergedInfo.toString().isEmpty()) {
				obj.set("mergedInfo", mergedInfo.substring(0, mergedInfo.length() - 1));
			}
			if (currentRowMaxRowspan > 1) {
				for (int j = i + 1; j < i + currentRowMaxRowspan; j++) {
					StringBuilder mergedInfo1 = new StringBuilder();// 这一行的合并信息
					Row row1 = sheet.getRow(j);
					obj = new JSONObject();
					for (int x = 0; x < params.size(); x++) {
						JSONObject param = params.getJSONObject(x);
						String secondArea = param.getStr("SECOND_AREA", "");
						String colName = param.getStr("COL_NAME", "");
						String colFormat = param.getStr("FORMAT", "0");
						String v;
						if (Util.isCapital(secondArea)) {// 判断是否为纯大写字母
							int cellIndex = Util.transferase_10(secondArea);
							Cell cell = row1.getCell(cellIndex - 1);
							if (cell == null) {
								continue;
							}
							v = Util.getCellValue(cell);
							Map<String, Object> isCombined = Util.isCombineCell(listCombineCell, cell);
							// 如果是，则判断是否有值，有值的才添加到list中
							if ((Boolean) isCombined.get("flag")) {
								CellRangeAddress ca = (CellRangeAddress) isCombined.get("CellRangeAddress");
								int c = ca.getFirstColumn();
								int r = ca.getFirstRow();
								if ((v != null && !v.isEmpty())
										|| cell.getColumnIndex() == c && cell.getRowIndex() == r) {
									// int colspan = (int)
									// isCombined.get("mergedCol");
									int rowspan = (int) isCombined.get("mergedRow");
									if (rowspan > 1) {
										mergedInfo1.append(colName).append(":").append(rowspan).append(",");
									}
								}
								// 如果不是，则直接插入
							}
							if (colFormat.equals("3") || colFormat.equals("4")) {
								if (StrUtil.isNotBlank(v)) {
									v = Util.strToClobSql(v);
								}
							}
							obj.set(colName, v);
						} else {
							obj.set("col" + secondArea, Util.getAddressValue(fullPath, 0, secondArea));
						}
					}
					obj.set("mergedInfo",
							!mergedInfo1.toString().isEmpty() ? mergedInfo1.substring(0, mergedInfo1.length() - 1) : "");
				}
			}

			i += currentRowMaxRowspan - 1;
			JSONObject twxParams = new JSONObject();
			twxParams.set("TABLE_CONFIG_ID", tableId);
			twxParams.set("TREE_ID", treeId);
			twxParams.set("FILEPATH", relativePath);
			twxParams.set("FILENAME", filename);
			twxParams.set("data", obj.toString());
			twxParams.set("creator", creator);
			twxParams.set("productId", productId);
			JSONArray jarr = Util.postTwx(THING, "AddTableData", twxParams);
			result += jarr.getJSONObject(0).getInt("result", 0);
		}

		res.set("success", success);
		res.set("msg", msg);
		res.set("result", result);
		return res;
	}

	/**
	 * 解析上传数据 结构化三级表
	 */
	public static JSONObject parsing3(JSONObject fields, JSONArray params, JSONObject table) throws Exception {

		String fullPath = fields.getStr("fullPath");
		String relativePath = fields.getStr("relativePath");
		String treeId = fields.getStr("treeId");
		String creator = fields.getStr("creator");
		String filename = fields.getStr("filename");
		String tableId = fields.getStr("tableId");
		String productId = fields.getStr("productId");
		JSONObject res = new JSONObject();
		boolean success = true;
		int result = 0;
		String msg = "";
		Sheet sheet = Util.getSheet(fullPath);
		// 根据tableId 获取二级表配置的信息
		int threeDataRownum = table.getInt("THREE_DATA_ROWNUM", 2);
		int lastRowNum = sheet.getLastRowNum();
		for (int i = threeDataRownum - 1; i <= lastRowNum; i++) {
			Row row = sheet.getRow(i);
			JSONObject obj = new JSONObject();

			for (int x = 0; x < params.size(); x++) {
				JSONObject param = params.getJSONObject(x);
				String threeArea = param.getStr("THREE_AREA", "");
				String colName = param.getStr("COL_NAME", "");
				String colFormat = param.getStr("FORMAT", "0");
				String v;
				if (Util.isCapital(threeArea)) {// 判断是否为纯大写字母
					int cellIndex = Util.transferase_10(threeArea);
					Cell cell = row.getCell(cellIndex - 1);
					v = Util.getCellValue(cell);
					// 如果是，则判断是否有值，有值的才添加到list中
					if (colFormat.equals("3") || colFormat.equals("4")) {
						if (StrUtil.isNotBlank(v)) {
							v = Util.strToClobSql(v);
						}
					}
					obj.set(colName, v);
				}
			}

			JSONObject twxParams = new JSONObject();
			twxParams.set("TABLE_CONFIG_ID", tableId);
			twxParams.set("TREE_ID", treeId);
			twxParams.set("FILEPATH", relativePath);
			twxParams.set("FILENAME", filename);
			twxParams.set("data", obj.toString());
			twxParams.set("creator", creator);
			twxParams.set("productId", productId);
			System.out.println("twxParams:" + twxParams);
			JSONArray jarr = Util.postTwx(THING, "AddTableData", twxParams);
			result += jarr.getJSONObject(0).getInt("result", 0);
		}

		res.set("success", success);
		res.set("msg", msg);
		res.set("result", result);
		return res;
	}

	public static JSONObject parsingThreeExcel(JSONObject fields) throws Exception {
		String fullPath = fields.getStr("fullPath");
		int endY = fields.getInt("endY", 2);
		fullPath = CommonUtil.processMesExcel(fullPath, endY);
		fields.set("fullPath", fullPath);
		String type = fields.getStr("type");
		String tableId = fields.getStr("tableId");
		boolean mes = fields.getBool("mes");
		String fileType = fields.getStr("fileType");

		JSONObject res = new JSONObject();
		int result = 0;
		String msg;
		// 根据tableId 获取二级表配置的参数
		JSONArray params = Util.postTwx(THING, "QueryParamsById", JSONUtil.createObj().set("tableId", tableId));
		// 查询配置表的信息
		JSONArray tables = Util.postTwx(THING, "QueryTableById", JSONUtil.createObj().set("tableId", tableId));
		JSONObject table = tables.getJSONObject(0);
		String tplPath;
		if (fileType.equals("3")) {
			tplPath = table.getStr("THREE_FILEPATH");
			if (type.equals("2") || type.equals("3")) {// 数据来源于三级表
				// 需要做数据抓取
				if (type.equals("2")) {
					res = parsing1(fields, tplPath, params);
				}
			} else if (type.equals("4") || type.equals("5")) {
				if (Util.compareExcelHeader(tplPath, fullPath) || mes) {
					if (type.equals("4")) {
						res = parsing2(fields, params);
					} else {
						res = parsing3(fields, params, table);
					}
				} else {
					msg = "上传的文件与模板文件不一致，请校验后重新上传！";
					res.set("success", false);
					res.set("msg", msg);
					res.set("result", result);
				}
			}

		} else if (fileType.equals("2")) {
			if (type.equals("4")) {
				tplPath = table.getStr("THREE_FILEPATH");
			} else {
				tplPath = table.getStr("SECOND_FILEPATH", "");
			}
			if (Util.compareExcelHeader(tplPath, fullPath) || mes) {
				res = parsing2(fields, params);
			} else {
				msg = "上传的文件与模板文件不一致，请校验后重新上传！";
				res.set("success", false);
				res.set("msg", msg);
				res.set("result", result);
			}
		}
		return res;
	}

	/**
	 * 导入MES三级表数据
	 *
	 * @param type         数据来源类型
	 * @param tableId      配置表ID
	 * @param treeId       树节点ID
	 * @param creator      创建者
	 * @param endY         数据起始行
	 * @param productId    产品ID
	 * @param relativePath 相对路径
	 * @param filename     文件名称
	 * @return 导入结果
	 */
	public JSONObject importMesThreeExcel(String type, String tableId, String treeId,
										  String creator, int endY, String productId,
										  String relativePath, String filename) {
		try {
			// 构建完整路径
			String fullPath = fileUploadPath + File.separator + relativePath;
			FileUtil.mkdir(fullPath);
			// 构建参数对象
			JSONObject fields = new JSONObject()
					.set("fullPath", fullPath)
					.set("relativePath", relativePath)
					.set("treeId", treeId)
					.set("creator", creator)
					.set("filename", filename)
					.set("type", type)
					.set("tableId", tableId)
					.set("endY", endY)
					.set("productId", productId)
					.set("mes", true)
					.set("fileType", "3");

			// 调用解析方法
			return parsingThreeExcel(fields);

		} catch (Exception e) {
			log.error("导入MES三级表数据失败", e);
			return new JSONObject()
					.set("success", false)
					.set("result", e.toString());
		}
	}

	/**
	 * 导入质量数据策划表
	 *
	 * @param type          策划表类型(1:质量数据 2:影像记录)
	 * @param tableConfigId 配置表ID
	 * @param treeId        树节点ID
	 * @param creator       创建者
	 * @param file          上传的文件
	 * @param filename      文件名称
	 * @return 导入结果
	 */
	public JSONObject importPlanTable(String type, String tableConfigId, String treeId,
									  String creator, MultipartFile file, String filename) {
		try {
			// 创建月份目录
			String month = new SimpleDateFormat("yyyy-MM").format(new Date());
			String monthPath = fileUploadPath + File.separator + month;
			FileUtil.mkdir(monthPath);

			// 生成唯一文件名
			String uuid = UUID.randomUUID().toString();
			String fullPath = monthPath + File.separator + uuid;
			String relativePath = "//" + month + "//" + uuid;

			// 保存上传的文件
			try (InputStream input = file.getInputStream();
				 FileOutputStream output = new FileOutputStream(fullPath)) {
				byte[] buffer = new byte[102400];
				int length;
				while ((length = input.read(buffer)) != -1) {
					output.write(buffer, 0, length);
				}
			}

			// 调用解析方法
			return parsingPlanExcel(fullPath, relativePath, treeId, creator,
					filename, type, tableConfigId);

		} catch (Exception e) {
			log.error("导入质量数据策划表失败", e);
			return new JSONObject()
					.set("success", false)
					.set("msg", "上传失败：原因：" + e);
		}
	}

	/**
	 * 解析质量数据策划表Excel
	 *
	 * @param fullPath      文件绝对路径
	 * @param relativePath  文件相对路径
	 * @param treeId        树节点ID
	 * @param creator       创建者
	 * @param filename      文件名称
	 * @param type          策划表类型
	 * @param tableConfigId 配置表ID
	 * @return 解析结果
	 */
	private JSONObject parsingPlanExcel(String fullPath, String relativePath, String treeId,
										String creator, String filename, String type,
										String tableConfigId) {
		// 查询配置表信息
		JSONArray tables = Util.postTwx(THING, "QueryTableById",
				JSONUtil.createObj().set("tableId", tableConfigId));
		JSONObject table = tables.getJSONObject(0);

		// 获取模板路径
		String planTplPath = table.getStr("PLAN_FILEPATH");
		if (type.equals("2")) {
			planTplPath = table.getStr("PHOTO_FILEPATH");
		}

		// 校验模板是否存在
		if (StrUtil.isBlank(planTplPath)) {
			return new JSONObject()
					.set("success", false)
					.set("msg", "该质量数据类型暂无策划表模板，请先上传！");
		}

		log.info("planTplPath: {}", planTplPath);
		log.info("fullPath: {}", fullPath);

		// 比较Excel表头
		if (Util.compareExcelHeader(planTplPath, fullPath)) {
			String originalData = Util.readAll(fullPath).toString();
			String data = Util.strToClobSql(originalData);

			// 构建参数
			JSONObject twxParams = new JSONObject()
					.set("tableConfigId", tableConfigId)
					.set("treeId", treeId)
					.set("filepath", relativePath)
					.set("type", type)
					.set("filename", filename)
					.set("data", data)
					.set("originalData", originalData)
					.set("creator", creator);

			return Util.postTwxForObject(THING, "AddQualityPlan", twxParams);
		} else {
			return new JSONObject()
					.set("success", false)
					.set("msg", "上传的文件与模板文件不一致，请校验后重新上传！");
		}
	}

	/**
	 * 处理360度全景照片
	 *
	 * @param tempFilePath 临时文件路径
	 * @param fileName     文件名
	 * @return 处理结果
	 */
	public JSONObject handle360Photo(String tempFilePath, String fileName) {
		try {
			File uploadedFile = new File(tempFilePath);

			// 获取文件信息
			String photoSize = FileUtil.readableFileSize(uploadedFile);
			fileName = fileName != null ? fileName : uploadedFile.getName();
			String fileFormat = FileNameUtil.extName(fileName);
			String uuid = UUID.randomUUID().toString();

			// 构建存储路径
			String month = new SimpleDateFormat("yyyy-MM").format(new Date());
			String photoPath = "//" + month + "//" + uuid;

			// 复制文件到目标目录
			FileUtil.copy(tempFilePath, fileUploadPath + photoPath, true);

			// 构建返回结果
			JSONObject photoInfo = new JSONObject()
					.set("photoSize", photoSize)
					.set("photoFormat", fileFormat)
					.set("photoName", fileName)
					.set("photoPath", photoPath);

			return new JSONObject()
					.set("success", true)
					.set("isAll", true)
					.set("msg", "上传成功！")
					.set("data", photoInfo);

		} catch (Exception e) {
			log.error("处理360度全景照片失败", e);
			return new JSONObject()
					.set("success", false)
					.set("msg", "处理失败：" + e.getMessage());
		}
	}

	/**
	 * 上传照片文件
	 *
	 * @param photos 上传的照片文件数组
	 * @return 上传结果
	 */
	public JSONObject uploadPhoto(MultipartFile[] photos) {
		try {
			// 创建月份目录
			String month = new SimpleDateFormat("yyyy-MM").format(new Date());
			String monthPath = fileUploadPath + File.separator + month;
			FileUtil.mkdir(monthPath);

			// 处理每个照片文件
			JSONArray photoInfoArray = new JSONArray();
			for (MultipartFile photo : photos) {
				// 获取原始文件名和格式
				String originalFilename = photo.getOriginalFilename();
				String fileFormat = FileNameUtil.extName(originalFilename);

				// 生成唯一文件名
				String uuid = UUID.randomUUID().toString();
				String filePath = monthPath + File.separator + uuid;

				// 保存文件
				File destFile = new File(filePath);
				photo.transferTo(destFile);

				// 获取文件大小
				String photoSize = FileUtil.readableFileSize(destFile);

				// 构建照片信息
				JSONObject photoInfo = new JSONObject()
						.set("photoSize", photoSize)
						.set("photoFormat", fileFormat)
						.set("photoName", originalFilename)
						.set("photoPath", "//" + month + "//" + uuid);

				photoInfoArray.add(photoInfo);
			}

			// 构建返回结果
			return new JSONObject()
					.set("success", true)
					.set("msg", "上传成功！")
					.set("data", photoInfoArray);

		} catch (Exception e) {
			log.error("上传照片失败", e);
			return new JSONObject()
					.set("success", false)
					.set("msg", "上传照片失败：" + e.getMessage());
		}
	}

	/**
	 * Web上传照片文件
	 *
	 * @param file 上传的文件
	 * @return 上传结果
	 */
	public JSONObject webUploadPhoto(MultipartFile file, Integer chunk, Integer chunks,
									 String fileName, String reqIdent, String extraData) {
		try {
			// 处理文件上传
			JSONObject uploadResult = FileUploadUtil.handleFileUpload(file, chunk, chunks, fileName, reqIdent, extraData);

			if (uploadResult.getBool("success")) {
				if (uploadResult.getBool("isAll")) {
					// 获取临时文件路径
					String tempFilePath = uploadResult.getStr("file");

					// 创建月份目录
					String month = new SimpleDateFormat("yyyy-MM").format(new Date());
					String monthPath = fileUploadPath + File.separator + month;
					FileUtil.mkdir(monthPath);

					// 生成新的文件名
					String uuid = UUID.randomUUID().toString();
					String filePath = monthPath + File.separator + uuid;

					// 复制文件到目标目录
					FileUtil.copy(tempFilePath, filePath, true);

					// 获取文件信息
					File destFile = new File(filePath);
					String fileFormat = FileNameUtil.extName(tempFilePath);
					String fileSize = FileUtil.readableFileSize(destFile);

					// 构建文件信息
					JSONObject fileInfo = new JSONObject()
							.set("size", fileSize)
							.set("format", fileFormat)
							.set("name", FileNameUtil.getName(destFile))
							.set("path", "//" + month + "//" + uuid)
							.set("fullPath", filePath);

					return new JSONObject()
							.set("success", true)
							.set("msg", "上传成功！")
							.set("data", fileInfo);
				}
				return uploadResult;
			}
			return uploadResult;

		} catch (Exception e) {
			log.error("Web上传照片失败", e);
			return new JSONObject()
					.set("success", false)
					.set("msg", "上传失败：" + e.getMessage());
		}
	}
}