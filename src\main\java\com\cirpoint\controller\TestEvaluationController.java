package com.cirpoint.controller;

import cn.hutool.json.JSONObject;
import com.cirpoint.service.FileService;
import com.cirpoint.service.TestEvaluationService;
import com.cirpoint.util.FileDownloadUtil;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 实验鉴定相关操作的控制器
 */
@Slf4j
@RestController
@RequestMapping("/test/evaluation")
public class TestEvaluationController {

	private final TestEvaluationService testEvaluationService;
	private final FileService fileService;

	@Autowired
	public TestEvaluationController(TestEvaluationService testEvaluationService, FileService fileService) {
		this.testEvaluationService = testEvaluationService;
		this.fileService = fileService;
	}

	/**
	 * 导出数据包
	 *
	 * @param treeId 树节点ID
	 * @return ResponseEntity
	 */
	@PostMapping("/export/pkg")
	public ResponseEntity<?> exportDataPackage(@RequestParam String treeId) {
		return FileDownloadUtil.fileResponseAndDelete(testEvaluationService.exportDataPackage(treeId));
	}

	/**
	 * 上传测试文件
	 *
	 * @param file 上传的文件数组
	 * @return ResponseEntity
	 */
	@PostMapping("/upload")
	public ResponseEntity<JSONObject> uploadTestFile(@RequestParam("file") MultipartFile file) throws IOException {
		// 处理文件上传
		JSONObject result = fileService.uploadFile(file);
		return ResponseEntity.ok(result);
	}

} 