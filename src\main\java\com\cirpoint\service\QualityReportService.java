package com.cirpoint.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.cirpoint.model.PdfOptions;
import com.cirpoint.model.ReportTreeNode;
import com.cirpoint.util.*;
import com.deepoove.poi.data.PictureRenderData;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.*;
import com.itextpdf.kernel.pdf.action.PdfAction;
import com.itextpdf.kernel.pdf.navigation.PdfExplicitDestination;
import com.itextpdf.kernel.pdf.xobject.PdfFormXObject;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.*;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.Writer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
public class QualityReportService extends ApplicationConfig {

	private final TableService tableService;

	@Value("${table.column.design-value}")
	private int designValueColumn;

	@Value("${table.column.tolerance}")
	private int toleranceColumn;

	@Value("${table.column.measured-value}")
	private int measuredValueColumn;

	@Value("${table.column.result}")
	private int resultColumn;

	@Value("${table.row.data-start}")
	private int dataStartRow;

	@Autowired
	public QualityReportService(TableService tableService) {
		this.tableService = tableService;
	}


	private final static String THING = "Thing.Fn.QualityReport";

	/**
	 * 导出单张表的Excel
	 *
	 * @param id 节点ID
	 * @return 生成的Excel文件
	 */
	public File exportTableExcel(String id) {
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
		FileUtil.mkdir(thisTempPath);
		try {
			// 查询节点数据
			JSONObject obj = Util.postTwxForObject(THING, "QueryNodeById",
					JSONUtil.createObj().set("id", id));
			JSONObject data = obj.getJSONObject("data");

			// 转换为Excel文件
			return Util.tableData2Excel(data, thisTempPath);
		} catch (IllegalArgumentException e) {
			log.error("导出Excel失败: 合并单元格错误", e);
			throw new IllegalArgumentException("导出Excel失败：表格结构不正确，请检查合并单元格设置");
		} catch (Exception e) {
			log.error("导出Excel失败", e);
			throw new IllegalArgumentException("导出Excel失败：" + e.getMessage());
		}
	}

	/**
	 * 导出单张表的PDF（使用默认PDF选项）
	 *
	 * @param id 节点ID
	 * @return 包含PDF文件信息的JSONObject
	 */
	public JSONObject exportTablePdf(String id) {
		// 使用默认选项调用带PdfOptions参数的重载方法
		return exportTablePdf(id, new PdfOptions());
	}

	/**
	 * 导出单张表的PDF（支持自定义PDF选项）
	 *
	 * @param id      节点ID
	 * @param options PDF导出选项对象，包含页面大小和方向等配置
	 * @return 包含PDF文件信息的JSONObject
	 */
	public JSONObject exportTablePdf(String id, PdfOptions options) {
		try {
			// 查询节点数据
			JSONObject obj = Util.postTwxForObject(THING, "QueryNodeById",
					JSONUtil.createObj().set("id", id));
			JSONObject data = obj.getJSONObject("data");

			// 转换为PDF文件，传入自定义选项
			return Util.tableData2Pdf(data, tempPath, pdfFontPath, options);
		} catch (Exception e) {
			log.error("导出PDF失败: {}", e.getMessage(), e);
			return JSONUtil.createObj()
					.set("success", false)
					.set("msg", "导出PDF失败：" + e.getMessage());
		}
	}

	/**
	 * 导出多个表的Excel（结构树）
	 *
	 * @param id  节点ID
	 * @param pid 父节点ID
	 * @return ZIP压缩文件
	 */
	public File exportMoreExcel(String id, String pid) {
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
		FileUtil.mkdir(thisTempPath);
		// 查询树形结构数据
		JSONObject obj = Util.postTwxForObject(THING, "QueryTreeById",
				JSONUtil.createObj().set("id", id));

		if (!obj.getBool("success")) {
			log.error("查询树形结构失败: {}", obj.getStr("msg"));
			throw new IllegalArgumentException("查询树形结构失败：" + obj.getStr("msg"));
		}

		JSONArray data = obj.getJSONArray("data");
		List<ReportTreeNode> nodes = CollUtil.newArrayList();

		// 构建节点列表
		for (int i = 0; i < data.size(); i++) {
			JSONObject node = data.getJSONObject(i);
			ReportTreeNode treeNode = new ReportTreeNode();
			treeNode.setId(node.getStr("ID"));
			treeNode.setPid(node.getStr("PID"));
			treeNode.setName(node.getStr("NAME"));
			treeNode.setSecurity(node.getStr("SECURITY_NAME", ""));
			treeNode.setSaveData(node.getStr("SAVE_DATA", ""));
			treeNode.setTableNum(node.getStr("TABLE_NUM", ""));
			treeNode.setSignHtml(node.getStr("HTML_DATA", ""));
			treeNode.setHtmlData(node.getStr("HTML_DATA", ""));
			treeNode.setLevelNum(node.getInt("LEVEL_NUM"));
			treeNode.setSort(node.getInt("SORT", 1));
			treeNode.setTableHeader(node.getStr("TABLE_HEADER", "0"));
			treeNode.setType(node.getStr("TYPE"));
			treeNode.setTableStatus(node.getStr("TABLE_STATUS", "edit"));
			nodes.add(treeNode);
		}

		// 创建临时目录
		String folderName = System.currentTimeMillis() + "";
		String tempFolderPath = thisTempPath + File.separator + folderName;

		// 构建树形结构并处理节点
		List<ReportTreeNode> nodeList = ReportTreeUtil.build(nodes, pid);
		dealExcelNodes(tempFolderPath, nodeList);

		// 压缩文件
		File zipFile = ZipUtil.zip(tempFolderPath, Charset.forName("GBK"));

		// 重命名压缩文件
		if (!nodes.isEmpty()) {
			ReportTreeNode firstNode = nodes.get(0);
			String tableNum = firstNode.getTableNum();
			String tableName = StrUtil.isBlank(tableNum) ?
					firstNode.getName() :
					tableNum + "：" + firstNode.getName();
			tableName += "（" + firstNode.getSecurity() + "）";
			zipFile = FileUtil.rename(zipFile, tableName, true, true);
		}

		return zipFile;
	}

	/**
	 * 处理节点列表，递归生成文件结构
	 */
	private void dealExcelNodes(String folder, List<ReportTreeNode> nodes) {
		for (ReportTreeNode node : nodes) {
			if (node.getType().equals("leaf") || node.getType().equals("dir")) {
				String path = folder + File.separator + node.getName();
				File file = new File(path);
				FileUtil.mkdir(file);
				if (!node.getChildren().isEmpty()) {
					dealExcelNodes(path, node.getChildren());
				}
			} else if (node.getType().equals("report") ||
					(StrUtil.contains(node.getType(), "table") && !node.getType().equals("table_3"))) {
				String name = node.getTableNum() + "：" + node.getName();
				name += "（" + node.getSecurity() + "）";
				String path = folder + File.separator + name;
				File file = new File(path);
				FileUtil.mkdir(file);
				exportNodeToExcel(path, name, node);
				if (!node.getChildren().isEmpty()) {
					dealExcelNodes(path, node.getChildren());
				}
			} else if (node.getType().equals("table_3")) {
				String name = node.getTableNum() + "：" + node.getName();
				name += "（" + node.getSecurity() + "）";
				exportNodeToExcel(folder, name, node);
			}
		}
	}

	/**
	 * 将节点数据导出为Excel文件
	 *
	 * @param folder   目标文件夹
	 * @param fileName 文件名
	 * @param node     节点数据
	 */
	private void exportNodeToExcel(String folder, String fileName, ReportTreeNode node) {
		String saveDataStr = node.getSaveData();
		if (StrUtil.isNotBlank(saveDataStr)) {
			try {
				String baseFileName = fileName;
				String path = folder + File.separator + baseFileName + ".xlsx";
				File file = new File(path);

				// 如果文件已存在，在文件名后添加序号
				int counter = 1;
				while (file.exists()) {
					baseFileName = fileName + "(" + counter + ")";
					path = folder + File.separator + baseFileName + ".xlsx";
					file = new File(path);
					counter++;
				}

				String headerRow = node.getTableHeader();
				try {
					Util.tableData2Excel(saveDataStr, path, headerRow, baseFileName);
				} catch (IllegalArgumentException e) {
					log.error("导出Excel失败: 合并单元格错误 - 节点[{}:{}]", node.getTableNum(), node.getName(), e);
					throw new IllegalArgumentException(String.format(
							"导出节点[%s:%s]失败：表格结构不正确，请检查合并单元格设置",
							node.getTableNum(), node.getName()));
				}
			} catch (Exception e) {
				String errorMsg = String.format("导出节点[%s:%s]失败: %s",
						node.getTableNum(), node.getName(), e.getMessage());
				throw new IllegalArgumentException(errorMsg, e);
			}
		}
	}


	/**
	 * 导入Excel文件
	 *
	 * @param file     Excel文件
	 * @param postId   节点ID
	 * @param saveUser 保存用户
	 * @return 导入结果
	 */
	public JSONObject importExcel(MultipartFile file, String postId, String saveUser) {
		FileUploadUtil.UploadResult uploadResult = FileUploadUtil.uploadToTemp(file);
		try {
			// 转换Excel为HandsonTable格式
			JSONObject res = Util.excel2HandsonTable(uploadResult.getAbsolutePath());
			if (res.getBool("success")) {
				String tableData = res.getJSONObject("data").toString();
				// 验证是否可以导出PDF
				if (Util.isExportPdf(tableData, tempPath, pdfFontPath)) {
					res = Util.postTwxForObject(THING, "SaveTableData",
							JSONUtil.createObj().set("id", postId).set("tableData", tableData).set("saveUser", saveUser));
				} else {
					res.set("success", false);
					res.set("msg", "上传的excel存在多余的合并单元格，请调整后重新上传！");
				}
			}
			return res;
		} finally {
			// 清理临时文件
			FileUploadUtil.cleanupTemp(uploadResult.getAbsolutePath());
		}
	}

	/**
	 * 导入PDF文件
	 *
	 * @param file PDF文件
	 * @param id   节点ID
	 * @return 导入结果
	 */
	public JSONObject importPdf(MultipartFile file, String id) {
		FileUploadUtil.UploadResult uploadResult = FileUploadUtil.uploadToStorage(file);

		// 调用TWX保存PDF信息
		return Util.postTwxForObject(THING, "ImportPdf",
				JSONUtil.createObj()
						.set("id", id)
						.set("fileName", uploadResult.getOriginalFilename())
						.set("filePath", uploadResult.getStoragePath())
						.set("fileFormat", uploadResult.getFileFormat()));
	}

	/**
	 * 导出多个表格为PDF（结构树）（使用默认PDF选项）
	 *
	 * @param postId   节点ID
	 * @param postPId  父节点ID
	 * @param tempPath 临时路径
	 * @param creator  创建者
	 * @return 导出结果
	 */
	public JSONObject exportMorePdf(String postId, String postPId, String tempPath, String creator) {
		return exportMorePdf(postId, postPId, tempPath, creator, true);
	}

	/**
	 * 导出多个表格为PDF（结构树）（使用默认PDF选项）
	 *
	 * @param postId   节点ID
	 * @param postPId  父节点ID
	 * @param tempPath 临时路径
	 * @param creator  创建者
	 * @param isOutImg 是否输出图片
	 * @return 导出结果
	 */
	public JSONObject exportMorePdf(String postId, String postPId, String tempPath, String creator, boolean isOutImg) {
		// 使用默认选项调用带PdfOptions参数的重载方法
		return exportMorePdf(postId, postPId, tempPath, creator, isOutImg, new PdfOptions());
	}

	/**
	 * 导出多个表格为PDF（结构树）（支持自定义PDF选项）
	 *
	 * @param postId   节点ID
	 * @param postPId  父节点ID
	 * @param tempPath 临时路径
	 * @param creator  创建者
	 * @param isOutImg 是否输出图片
	 * @param options  PDF导出选项对象，包含页面大小和方向等配置
	 * @return 导出结果
	 */
	public JSONObject exportMorePdf(String postId, String postPId, String tempPath, String creator, boolean isOutImg, PdfOptions options) {
		JSONObject res = new JSONObject();
		try {
			String path = tempPath + "\\" + System.currentTimeMillis() + ".pdf";
			File f = new File(path);
			if (!f.exists()) {
				f.createNewFile();
			}

			JSONObject obj = Util.postTwxForObject(THING, "QueryTreeById",
					JSONUtil.createObj().set("id", postId));
			JSONArray errorNode = new JSONArray();
			boolean success = obj.getBool("success");
			if (success) {

				PdfDocument pdfDoc = new PdfDocument(new PdfWriter(path));
				PdfOutline rootOutline = pdfDoc.getOutlines(false);

				// 使用PdfOptions创建Document
				Document doc = options.createDocument(pdfDoc);

				PdfFont font = PdfFontFactory.createFont(pdfFontPath + "\\simhei.ttf", PdfEncodings.IDENTITY_H);
				pdfDoc.addEventHandler(PdfDocumentEvent.END_PAGE, new PageMarker(font));
				JSONArray data = obj.getJSONArray("data");

				// 构建node列表
				List<ReportTreeNode> nodes = CollUtil.newArrayList();
				for (int i = 0; i < data.size(); i++) {
					JSONObject node = data.getJSONObject(i);
					ReportTreeNode treeNode = new ReportTreeNode();
					treeNode.setId(formatId(node.getStr("ID")));
					treeNode.setPid(node.getStr("PID"));
					treeNode.setName(node.getStr("NAME"));
					treeNode.setSecurity(node.getStr("SECURITY_NAME"));
					treeNode.setSaveData(node.getStr("SAVE_DATA", ""));
					treeNode.setTableNum(node.getStr("TABLE_NUM", ""));
					treeNode.setSignHtml(node.getStr("HTML_DATA", ""));
					treeNode.setHtmlData(node.getStr("HTML_DATA", ""));
					treeNode.setLevelNum(node.getInt("LEVEL_NUM"));
					treeNode.setSort(node.getInt("SORT", 1));
					treeNode.setTableHeader(node.getStr("TABLE_HEADER", "0"));
					treeNode.setType(node.getStr("TYPE"));
					treeNode.setTableStatus(node.getStr("TABLE_STATUS", "edit"));
					treeNode.setDataSource(node.getStr("DATA_SOURCE", ""));
					treeNode.setReportType(node.getStr("REPORT_TYPE", ""));
					treeNode.setDataType(node.getStr("DATA_TYPE", ""));
					treeNode.setFileName(node.getStr("FILE_NAME", ""));
					treeNode.setFilePath(node.getStr("FILE_PATH", ""));
					treeNode.setFileFormat(node.getStr("FILE_FORMAT", ""));
					nodes.add(treeNode);
				}
				List<ReportTreeNode> nodeList = ReportTreeUtil.build(nodes, postPId);
				dealPdfNodes(errorNode, pdfDoc, doc, rootOutline, nodeList, font, creator, isOutImg);
				// 在文档有页面时才删除最后一页
				if (pdfDoc.getNumberOfPages() > 0) {
					pdfDoc.removePage(pdfDoc.getNumberOfPages());
				}

				doc.close();
				pdfDoc.close();

				if (errorNode.isEmpty()) {
					res.set("success", true);
					if (!nodes.isEmpty()) {
						String tableNum = nodes.get(0).getTableNum();
						String tableName = tableNum + "：" + nodes.get(0).getName();
						if (StrUtil.isBlank(tableNum)) {
							tableName = nodes.get(0).getName();
						}
						tableName += "（" + nodes.get(0).getSecurity() + "）";
						f = FileUtil.rename(f, tableName, true, true);
					}

					res.set("data", f.getAbsolutePath());
				} else {
					res.set("success", false);
					res.set("msg", HandSonTableUtil.exportPdfErrorMsg(errorNode));
				}
			} else {
				String msg = obj.getStr("msg");
				res.set("success", false);
				res.set("msg", msg);
			}
		} catch (Exception e) {
			log.error("生成PDF文件失败: {}", e.getMessage(), e);
			res.set("success", false);
			res.set("msg", e.getLocalizedMessage());
		}
		return res;
	}

	public void dealPdfNodes(JSONArray errorNode, PdfDocument pdfDoc, Document doc, PdfOutline rootOutline, List<ReportTreeNode> nodes,
							 PdfFont font, String creator, boolean isOutImg) {
		for (ReportTreeNode node : nodes) {
			if (node.getType().equals("leaf") || node.getType().equals("dir")) {
				String title = node.getName();
				doc.add(new Paragraph(title).setMultipliedLeading(1.2f).setFont(font).setBold().setFontSize(20))
						.setTextAlignment(TextAlignment.LEFT).setHorizontalAlignment(HorizontalAlignment.CENTER);
				PdfOutline projectOutLine = rootOutline.addOutline(title);
				projectOutLine.addAction(PdfAction.createGoTo(PdfExplicitDestination.createFitH(pdfDoc.getLastPage(),
						pdfDoc.getLastPage().getPageSize().getTop())));
				if (!node.getChildren().isEmpty()) {
					dealPdfNodes(errorNode, pdfDoc, doc, rootOutline, node.getChildren(), font, creator, isOutImg);
				}

			} else if (node.getType().equals("report") || (StrUtil.contains(node.getType(), "table") && (!node.getType().equals("table_3")))) {
				String title = node.getTableNum() + ":" + node.getName();
				title += "（" + node.getSecurity() + "）";
				doc.add(new Paragraph(title).setFirstLineIndent(0).setMultipliedLeading(1.2f).setFont(font).setBold()
						.setFontSize(15)).setTextAlignment(TextAlignment.LEFT);
				PdfOutline projectOutLine = rootOutline.addOutline(title);
				projectOutLine.addAction(PdfAction.createGoTo(PdfExplicitDestination.createFitH(pdfDoc.getLastPage(),
						pdfDoc.getLastPage().getPageSize().getTop())));

				boolean flag = addPdfTable(doc, font, node, creator, isOutImg);
				if (!flag) {
					errorNode.add(title);
				}
				if (!node.getChildren().isEmpty()) {
					dealPdfNodes(errorNode, pdfDoc, doc, projectOutLine, node.getChildren(), font, creator, isOutImg);
				}

			} else if (node.getType().equals("table_3")) {
				String title = node.getTableNum() + ":" + node.getName();
				title += "（" + node.getSecurity() + "）";
				doc.add(new Paragraph(title).setFirstLineIndent(16).setMultipliedLeading(1.2f).setFont(font).setBold()
						.setFontSize(15)).setTextAlignment(TextAlignment.LEFT);
				PdfOutline bOutLine = rootOutline.addOutline(title);
				bOutLine.addAction(PdfAction.createGoTo(PdfExplicitDestination.createFitH(pdfDoc.getLastPage(),
						pdfDoc.getLastPage().getPageSize().getTop())));
				boolean flag;
				if (node.getFileFormat().equalsIgnoreCase("pdf") && StrUtil.isNotBlank(node.getFilePath())) {
					String filePath = fileUploadPath + node.getFilePath();
					try {
						PdfDocument pdfToInsert = new PdfDocument(new PdfReader(filePath));

						Console.log(pdfToInsert.getNumberOfPages());
						for (int pageNumber = 1; pageNumber <= pdfToInsert.getNumberOfPages(); pageNumber++) {
							PdfPage page = pdfToInsert.getPage(pageNumber);

							PdfFormXObject pdfFormXObject = page.copyAsFormXObject(pdfDoc);
							Image image = new Image(pdfFormXObject);
							image.setHorizontalAlignment(HorizontalAlignment.CENTER);
							if (image.getImageHeight() > image.getImageWidth()) {
								image.setRotationAngle(Math.toRadians(90));
							}
							doc.add(image);
						}
						pdfToInsert.close();
						doc.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
						flag = true;
					} catch (IOException e) {
						log.error("处理PDF文件失败: {}", filePath, e);
						flag = false;
					}
				} else {
					flag = addPdfTable(doc, font, node, creator, isOutImg);
				}
				if (!flag) {
					errorNode.add(title);
				}
				if (!node.getChildren().isEmpty()) {
					dealPdfNodes(errorNode, pdfDoc, doc, bOutLine, node.getChildren(), font, creator, isOutImg);
				}
			}
		}
	}

	public boolean addPdfTable(Document doc, PdfFont font, ReportTreeNode node, String creator, boolean isOutImg) {
		boolean flag = true;
		try {
			Color bgColour = new DeviceRgb(230, 230, 230);
			String dataSource = node.getDataSource();
			if (dataSource.equals("auto")) {
				String dataType = node.getDataType();
				String reportType = node.getReportType();
				String nodeId = node.getId();
				String tableConfigId = node.getDataType();
				if (StrUtil.isNotBlank(dataType) && StrUtil.isNotBlank(reportType)) {
					JSONObject autoTableRes = Util.postTwxForObject(THING, "QueryAutoTable",
							JSONUtil.createObj().set("id", nodeId).set("username", creator));
					if (autoTableRes.getBool("success")) {
						if (reportType.equals("confirm") || reportType.equals("photo")) {
							JSONArray tables = autoTableRes.getJSONArray("data");
							for (int i = 0; i < tables.size(); i++) {
								JSONObject table = tables.getJSONObject(i);
								String tableName = table.getStr("name");
								JSONArray cols = table.getJSONArray("cols");
								JSONArray datas = table.getJSONArray("datas");
								float[] pointColumnWidths = new float[cols.size()];
								Arrays.fill(pointColumnWidths, 1);
								Table docTable = new Table(pointColumnWidths);
								docTable.useAllAvailableWidth();
								Cell tableNameCell = new Cell(1, cols.size());
								tableNameCell.add(new Paragraph(tableName).setMultipliedLeading(1.2f).setFont(font))
										.setTextAlignment(TextAlignment.CENTER);
								tableNameCell.setBold().setBackgroundColor(bgColour);
								docTable.addHeaderCell(tableNameCell);

								for (int x = 0; x < cols.size(); x++) {
									Cell colCell = new Cell();
									colCell.add(new Paragraph(cols.getStr(x)).setMultipliedLeading(1.2f).setFont(font))
											.setTextAlignment(TextAlignment.CENTER);
									colCell.setBold().setBackgroundColor(bgColour);
									docTable.addHeaderCell(colCell);
								}

								for (int y = 0; y < datas.size(); y++) {
									JSONArray row = datas.getJSONArray(y);
									for (int x = 0; x < row.size(); x++) {
										Cell colCell = new Cell();
										String cellValue = row.getStr(x);
										if (reportType.equals("photo") && x == (row.size() - 4)) {
											JSONArray photos = JSONUtil.parseArray(cellValue);
											StringBuilder tempStr = new StringBuilder();
											for (int p = 0; p < photos.size(); p++) {
												JSONObject photo = photos.getJSONObject(p);
												String photoNumber = photo.getStr("PHOTO_NUMBER");
												tempStr.append("\n").append(photoNumber);
											}
											cellValue = tempStr.toString();
											if (!photos.isEmpty()) {
												cellValue = cellValue.substring(2);
											}
										}
										if (cellValue.contains(":noKey")) {
											cellValue = cellValue.replace(":noKey", "");
											colCell.add(new Paragraph(cellValue).setMultipliedLeading(1.2f).setFont(font)).setFontColor(new DeviceRgb(255, 0, 0))
													.setTextAlignment(TextAlignment.CENTER);
										} else {
											colCell.add(new Paragraph(cellValue).setMultipliedLeading(1.2f).setFont(font))
													.setTextAlignment(TextAlignment.CENTER);
										}

										docTable.addCell(colCell);
									}

								}

								doc.add(docTable);
							}
						} else {
							JSONObject tableHeaderRes = Util.postTwxForObject("Thing.Fn.SecondTable",
									"GetSecondTableHeader", JSONUtil.createObj().set("id", Convert.toInt(tableConfigId)));
							if (tableHeaderRes.getBool("success")) {
								JSONArray headerArray = tableHeaderRes.getJSONArray("result");
								int tableColNum = 0;
								for (int h = 0; h < headerArray.size(); h++) {
									JSONArray header = headerArray.getJSONArray(h);
									for (int c = 0; c < header.size(); c++) {
										JSONObject headerObj = header.getJSONObject(c);
										int cellColumnIndex = headerObj.getInt("cellColumnIndex");
										if (tableColNum < cellColumnIndex) {
											tableColNum = cellColumnIndex;
										}
									}
								}
								tableColNum = tableColNum + 1;
								float[] pointColumnWidths = new float[tableColNum];
								Arrays.fill(pointColumnWidths, 1);

								Table docTable = new Table(pointColumnWidths);
								docTable.useAllAvailableWidth();
								JSONArray tempArray = new JSONArray();
								for (int h = 0; h < headerArray.size(); h++) {
									JSONArray header = headerArray.getJSONArray(h);
									for (int c = 0; c < header.size(); c++) {
										JSONObject headerObj = header.getJSONObject(c);
										String title = headerObj.getStr("title");
										String field = headerObj.getStr("field");
										if (field.startsWith("V")) {
											tempArray.add(headerObj);
										}
										int colspan = headerObj.getInt("colspan");
										int rowspan = headerObj.getInt("rowspan");
										Cell colCell;
										if (colspan > 1 || rowspan > 1) {
											colCell = new Cell(rowspan, colspan);
										} else {
											colCell = new Cell();
										}
										title = title.replaceAll("<br>", "");
										colCell.add(new Paragraph(title).setMultipliedLeading(1.2f).setFont(font))
												.setTextAlignment(TextAlignment.CENTER).setBold()
												.setBackgroundColor(bgColour);
										docTable.addHeaderCell(colCell);
									}
								}
								tempArray
										.sort(Comparator.comparingInt(obj -> ((JSONObject) obj).getInt("cellColumnIndex")));
								JSONArray tables = autoTableRes.getJSONArray("data");
								for (int r = 0; r < tables.size(); r++) {
									JSONObject row = tables.getJSONObject(r);
									for (int h = 0; h < tempArray.size(); h++) {
										JSONObject headerObj = tempArray.getJSONObject(h);
										String field = headerObj.getStr("field");
										String value = row.getStr(field);
										Cell colCell = new Cell();
										colCell.add(new Paragraph(value).setMultipliedLeading(1.2f).setFont(font))
												.setTextAlignment(TextAlignment.CENTER);
										docTable.addCell(colCell);
									}
								}
								doc.add(docTable);
							} else {
								doc.add(new Paragraph(tableHeaderRes.getStr("result")).setFirstLineIndent(16)
										.setMultipliedLeading(1.2f).setFont(font).setFontSize(15)
										.setFontColor(new DeviceRgb(255, 0, 0))).setTextAlignment(TextAlignment.LEFT);
							}

						}
					} else {
						doc.add(new Paragraph(autoTableRes.getStr("msg")).setFirstLineIndent(16).setMultipliedLeading(1.2f)
										.setFont(font).setFontSize(15).setFontColor(new DeviceRgb(255, 0, 0)))
								.setTextAlignment(TextAlignment.LEFT);
					}
					doc.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
			} else {
				String saveDataStr = node.getSaveData();
				if (StrUtil.isNotBlank(saveDataStr)) {
					String tableStr = node.getSignHtml();
					String headerRow = node.getTableHeader();
					Util.tableDataToPdfTable(doc, tableStr, saveDataStr, headerRow, font, isOutImg);
					doc.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}

				doc.add(new Paragraph());
			}
		} catch (Exception e) {
			flag = false;
		}
		return flag;
	}

	private String formatId(String id) {
		if (id.contains(".")) {
			id = String.valueOf(Integer.parseInt(id));
		}
		return id;
	}

	/**
	 * 生成多个表格的PDF（结构树）（使用默认PDF选项）
	 *
	 * @param downloadId 下载ID
	 * @param id         节点ID
	 * @param pid        父节点ID
	 * @param creator    创建者
	 * @param module     模块
	 * @return 生成结果
	 */
	public JSONObject generateMorePdf(String downloadId, String id, String pid, String creator, String module) {
		// 使用默认选项调用带PdfOptions参数的重载方法
		return generateMorePdf(downloadId, id, pid, creator, module, new PdfOptions());
	}

	/**
	 * 生成多个表格的PDF（结构树）（支持自定义PDF选项）
	 *
	 * @param downloadId 下载ID
	 * @param id         节点ID
	 * @param pid        父节点ID
	 * @param creator    创建者
	 * @param module     模块
	 * @param options    PDF导出选项对象，包含页面大小和方向等配置
	 * @return 生成结果
	 */
	public JSONObject generateMorePdf(String downloadId, String id, String pid, String creator, String module, PdfOptions options) {
		// 创建临时目录
		String tempPath = fileUploadPath + File.separator + "confirmDownload" + File.separator + creator + File.separator + module + File.separator;
		FileUtil.mkdir(tempPath);

		// 生成PDF，传入自定义选项
		JSONObject result = exportMorePdf(id, pid, tempPath, creator, true, options);

		// 处理生成结果
		HandSonTableUtil.GenerateFileComplete(downloadId, fileUploadPath, result);

		return result;
	}

	/**
	 * 导出PDF并打包成ZIP（使用默认PDF选项）
	 *
	 * @param id 节点ID
	 * @return 包含ZIP文件信息的JSONObject
	 */
	public JSONObject exportPdfZip(String id) {
		// 使用默认选项调用带PdfOptions参数的重载方法
		return exportPdfZip(id, new PdfOptions());
	}

	/**
	 * 导出PDF并打包成ZIP（支持自定义PDF选项）
	 *
	 * @param id      节点ID
	 * @param options PDF导出选项对象，包含页面大小和方向等配置
	 * @return 包含ZIP文件信息的JSONObject
	 */
	public JSONObject exportPdfZip(String id, PdfOptions options) {
		return HandSonTableUtil.exportPdfZip(id, tempPath, pdfFontPath, THING, options);
	}

	/**
	 * 导出ZIP文件
	 *
	 * @param postId 节点ID
	 * @return 包含ZIP文件信息的JSONObject
	 */
	public JSONObject exportZip(String postId) {
		log.info("开始导出ZIP文件: postId={}", postId);
		JSONObject res = new JSONObject();
		String folderName = System.currentTimeMillis() + "";
		String thisTempPath = tempPath + File.separator + folderName + File.separator;
		log.debug("创建临时目录: {}", thisTempPath);
		FileUtil.mkdir(thisTempPath);
		File zipFile;
		try {
			log.info("调用TWX查询树形结构数据: id={}", postId);
			JSONObject obj = Util.postTwxForObject(THING, "QueryTreeById",
					JSONUtil.createObj().set("id", postId));


			boolean success = obj.getBool("success");

			if (success) {
				log.info("树形结构数据查询成功");
				JSONArray data = obj.getJSONArray("data");
				log.debug("获取需要复制的文件列表");
				List<String> copyFiles = Util.getCopyFiles(data);
				log.info("需要复制的文件数量: {}", copyFiles.size());

				log.debug("保存数据JSON文件");
				String dataPath = thisTempPath + File.separator + "data.json";
				Writer writer = FileUtil.getWriter(dataPath, StandardCharsets.UTF_8, false);
				data.write(writer);
				writer.close();
				log.info("数据JSON文件保存成功: {}", dataPath);

				String uploadPath = Util.getFileUploadPath();
				log.debug("文件上传路径: {}", uploadPath);

				//日志查询
				log.info("调用TWX查询日志数据: id={}", postId);
				JSONObject logObj = Util.postTwxForObject(THING, "QueryLogById",
						JSONUtil.createObj().set("id", postId));

				if (logObj.getBool("success")) {
					log.info("日志数据查询成功");
					JSONArray signData = logObj.getJSONArray("data");
					String signPath = thisTempPath + File.separator + "log.json";
					log.debug("保存日志JSON文件: {}", signPath);
					Writer signWriter = FileUtil.getWriter(signPath, StandardCharsets.UTF_8, false);
					signData.write(signWriter);
					signWriter.close();
					log.info("日志JSON文件保存成功");
				} else {
					log.warn("日志数据查询失败,原因：{}", logObj.getStr("msg"));
				}

				log.info("调用TWX查询签名数据: id={}", postId);
				JSONObject signObj = Util.postTwxForObject(THING, "QuerySignById",
						JSONUtil.createObj().set("id", postId));


				if (signObj.getBool("success")) {
					log.info("签名数据查询成功");
					JSONArray signData = signObj.getJSONArray("data");
					String signPath = thisTempPath + File.separator + "sign.json";
					log.debug("保存签名JSON文件: {}", signPath);
					Writer signWriter = FileUtil.getWriter(signPath, StandardCharsets.UTF_8, false);
					signData.write(signWriter);
					signWriter.close();
					log.info("签名JSON文件保存成功");
				} else {
					log.warn("签名数据查询失败,原因：{}", logObj.getStr("msg"));
				}

				log.info("调用TWX查询照片数据: id={}", postId);
				JSONObject photoObj = Util.postTwxForObject(THING, "QueryPhotoById",
						JSONUtil.createObj().set("id", postId));
				if (photoObj.getBool("success")) {
					log.info("照片数据查询成功");
					JSONArray photoData = photoObj.getJSONArray("data");
					String photoPath = thisTempPath + File.separator + "photo.json";
					log.debug("保存照片JSON文件: {}", photoPath);
					Writer photoWriter = FileUtil.getWriter(photoPath, StandardCharsets.UTF_8, false);
					photoData.write(photoWriter);
					photoWriter.close();
					log.info("照片JSON文件保存成功");
				} else {
					log.warn("照片数据查询失败,原因：{}", logObj.getStr("msg"));
				}

				// 优化的文件复制逻辑
				log.info("开始复制文件，总数: {}", copyFiles.size());
				int successCount = 0;
				int failCount = 0;
				for (String src : copyFiles) {
					String srcFilepath = uploadPath + src;
					String descFilepath = thisTempPath + File.separator + "photo" + src;

					// 添加日志记录
					log.debug("准备复制文件: {} -> {}", srcFilepath, descFilepath);

					// 检查源文件是否存在
					if (!FileUtil.exist(srcFilepath)) {
						log.warn("源文件不存在，跳过复制: {}", srcFilepath);
						failCount++;
						continue;
					}

					// 确保目标目录存在
					File descDir = FileUtil.file(FileUtil.getParent(descFilepath, 1));
					if (!descDir.exists()) {
						log.debug("创建目标目录: {}", descDir.getAbsolutePath());
						FileUtil.mkdir(descDir);
					}

					// 重试机制
					boolean copied = false;
					int retryCount = 3;
					int retryDelay = 1000; // 1秒

					for (int i = 0; i < retryCount && !copied; i++) {
						try {
							if (i > 0) {
								log.info("第{}次尝试复制文件: {}", i + 1, srcFilepath);
							}

							FileUtil.copy(srcFilepath, descFilepath, true);
							log.debug("文件复制成功: {}", descFilepath);
							copied = true;
							successCount++;
						} catch (Exception e) {
							if (i == retryCount - 1) {
								// 最后一次尝试失败
								log.error("复制文件失败(尝试{}次): {} -> {}, 错误: {}", retryCount, srcFilepath, descFilepath, e.getMessage());

								// 尝试备用复制方法
								try {
									log.info("尝试使用备用方法复制文件: {}", srcFilepath);
									if (tryAlternativeCopy(srcFilepath, descFilepath)) {
										log.info("使用备用方法复制文件成功: {}", descFilepath);
										copied = true;
										successCount++;
									} else {
										failCount++;
									}
								} catch (Exception ex) {
									log.error("备用复制方法也失败: {}, 错误: {}", srcFilepath, ex.getMessage());
									failCount++;
								}
							} else {
								// 还有重试机会
								log.warn("复制文件失败，将重试: {}, 错误: {}", srcFilepath, e.getMessage());
								try {
									Thread.sleep(retryDelay);
								} catch (InterruptedException ie) {
									Thread.currentThread().interrupt();
									log.warn("复制文件延迟被中断", ie);
								}
							}
						}
					}
				}
				log.info("文件复制完成: 成功={}, 失败={}", successCount, failCount);

				log.info("开始压缩文件: {}", thisTempPath);
				zipFile = ZipUtil.zip(thisTempPath, Charset.forName("GBK"));
				log.info("文件压缩成功: {}", zipFile.getAbsolutePath());

				// 重命名压缩文件
				if (!data.isEmpty()) {
					JSONObject firstNode = data.getJSONObject(0);
					String tableNum = firstNode.getStr("TABLE_NUM");
					String tableName = StrUtil.isBlank(tableNum) ?
							firstNode.getStr("NAME") :
							tableNum + "：" + firstNode.getStr("NAME");
					tableName += "（" + firstNode.getStr("SECURITY_NAME") + "）";
					log.info("重命名ZIP文件: {} -> {}", zipFile.getName(), tableName);
					zipFile = FileUtil.rename(zipFile, tableName, true, true);
					log.info("ZIP文件重命名成功: {}", zipFile.getAbsolutePath());

					// 加密ZIP文件
					log.info("开始加密ZIP文件: {}", zipFile.getAbsolutePath());
					File encryptFile = new File(zipFile.getParent() + File.separator + System.currentTimeMillis() + ".dat");
					Util.encryptFile(zipFile, encryptFile);
					log.info("ZIP文件加密成功: {}, 文件大小: {} 字节", encryptFile.getAbsolutePath(), encryptFile.length());

					res.set("success", true);
					res.set("data", encryptFile.getAbsolutePath());
					log.info("ZIP文件导出成功（已加密）: {}", encryptFile.getAbsolutePath());
				} else {
					String msg = obj.getStr("msg");
					log.error("树形结构数据查询失败: {}", msg);
					res.set("success", false);
					res.set("msg", msg);
				}
			} else {
				String msg = obj.getStr("msg");
				log.error("树形结构数据查询失败: {}", msg);
				res.set("success", false);
				res.set("msg", msg);
			}
		} catch (Exception e) {
			log.error("导出ZIP文件失败: {}", e.getMessage(), e);
			res.set("success", false)
					.set("msg", e.getMessage());
		}
		return res;
	}

	/**
	 * 尝试使用备用方法复制文件
	 *
	 * @param srcPath  源文件路径
	 * @param destPath 目标文件路径
	 * @return 是否复制成功
	 */
	private boolean tryAlternativeCopy(String srcPath, String destPath) {
		try {
			// 备用方法1: 使用Java NIO
			File srcFile = new File(srcPath);
			File destFile = new File(destPath);

			if (!srcFile.exists()) {
				return false;
			}

			// 确保目标目录存在
			if (!destFile.getParentFile().exists()) {
				destFile.getParentFile().mkdirs();
			}

			java.nio.file.Files.copy(
					srcFile.toPath(),
					destFile.toPath(),
					java.nio.file.StandardCopyOption.REPLACE_EXISTING
			);

			return true;
		} catch (Exception e) {
			log.warn("备用方法1复制失败: {}", e.getMessage());

			// 备用方法2: 尝试读入内存再写出
			try {
				byte[] bytes = FileUtil.readBytes(srcPath);
				FileUtil.writeBytes(bytes, destPath);
				return true;
			} catch (Exception e2) {
				log.warn("备用方法2复制失败: {}", e2.getMessage());
				return false;
			}
		}
	}

	public JSONObject importBigZip(String filePath, String postId, String postPId, String treeId, String type) {
		log.info("开始导入大型ZIP文件: filePath={}, postId={}, postPId={}, treeId={}, type={}", filePath, postId, postPId, treeId, type);
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis();
		log.debug("创建临时解压目录: {}", thisTempPath);
		JSONObject result = new JSONObject();
		result.set("success", true);

		try {
			// 尝试解压文件
			try {
				log.info("尝试解压ZIP文件到临时目录: {} -> {}", filePath, thisTempPath);
				CommonUtil.customUnzip(filePath, thisTempPath);
				log.info("ZIP文件解压成功");
			} catch (Exception e) {
				// 如果普通解压失败，尝解密后解压
				log.warn("普通解压失败，尝试解密后解压: {}", e.getMessage());
				String decryptFile = filePath + "decrypt";
				log.info("开始解密文件: {} -> {}", filePath, decryptFile);
				Util.decryptFile(FileUtil.file(filePath), FileUtil.file(decryptFile));
				log.info("文件解密成功，开始解压解密后的文件");
				CommonUtil.customUnzip(decryptFile, thisTempPath);
				log.info("解密后的ZIP文件解压成功");
			}

			// 处理照片文件
			String srcPhotoPath = thisTempPath + File.separator + "photo";
			log.info("开始处理照片文件，照片目录: {}", srcPhotoPath);
			if (FileUtil.exist(srcPhotoPath)) {
				log.debug("照片目录存在，准备复制照片文件");
				File[] photos = new File(srcPhotoPath).listFiles();
				if (photos != null) {
					log.info("发现{}个照片文件/目录", photos.length);
					for (File f : photos) {
						if (FileUtil.isDirectory(f)) {
							try {
								log.debug("复制照片目录: {} -> {}", f.getAbsolutePath(), fileUploadPath);
								FileUtil.copy(f.getAbsolutePath(), fileUploadPath, true);
								log.debug("照片目录复制成功: {}", f.getName());
							} catch (Exception e) {
								// 记录错误但继续处理
								log.error("复制文件失败: {}, 错误: {}", f.getAbsolutePath(), e.getMessage(), e);
							}
						}
					}
					log.info("照片文件处理完成");
				} else {
					log.info("照片目录为空");
				}
			} else {
				log.info("照片目录不存在，跳过照片处理");
			}

			// 导入数据JSON
			String dataPath = thisTempPath + File.separator + "data.json";
			log.info("开始导入数据JSON: {}", dataPath);
			String relationIds;
			if (FileUtil.exist(dataPath)) {
				log.debug("数据JSON文件存在，开始读取");
				String dataStr = FileUtil.readString(dataPath, StandardCharsets.UTF_8);
				log.info("调用TWX导入数据: id={}, pid={}, treeId={}, type={}", postId, postPId, treeId, type);
				JSONObject obj = Util.postTwxForObject(THING, "ImportData",
						JSONUtil.createObj().set("id", postId).set("pid", postPId).set("treeId", treeId).set("type", type).set("dataStr", dataStr));
				if (obj.getBool("success")) {
					relationIds = obj.getJSONArray("data").toString();
					log.info("数据JSON导入成功，获取关联ID: {}", relationIds);
				} else {
					log.error("数据JSON导入失败: {}", obj.getStr("msg"));
					result.set("success", false);
					result.set("msg", obj.getStr("msg"));
					return result;
				}
			} else {
				log.error("未发现数据JSON文件: {}", dataPath);
				return JSONUtil.createObj()
						.set("success", false)
						.set("msg", "导入失败，原因：未发现数据JSON文件！");
			}

			// 导入照片数据
			String photoPath = thisTempPath + File.separator + "photo.json";
			log.info("开始导入照片数据: {}", photoPath);
			if (FileUtil.exist(photoPath)) {
				log.debug("照片数据文件存在，开始读取");
				String photoStr = FileUtil.readString(photoPath, StandardCharsets.UTF_8);
				log.info("调用TWX导入照片数据");
				JSONObject obj = Util.postTwxForObject(THING, "ImportPhotoData",
						JSONUtil.createObj().set("relationIds", relationIds).set("dataStr", photoStr));
				if (!obj.getBool("success")) {
					log.error("照片数据导入失败: {}", obj.getStr("msg"));
					result.set("success", false);
					result.set("msg", obj.getStr("msg"));
					return result;
				}
				log.info("照片数据导入成功");
			} else {
				log.info("照片数据文件不存在，跳过照片数据导入");
			}

			// 导入签名数据
			String signPath = thisTempPath + File.separator + "sign.json";
			log.info("开始导入签名数据: {}", signPath);
			if (FileUtil.exist(signPath)) {
				log.debug("签名数据文件存在，开始读取");
				String signStr = FileUtil.readString(signPath, StandardCharsets.UTF_8);
				log.info("调用TWX导入签名数据");
				JSONObject obj = Util.postTwxForObject(THING, "ImportSignData",
						JSONUtil.createObj().set("relationIds", relationIds).set("dataStr", signStr));
				if (!obj.getBool("success")) {
					log.error("签名数据导入失败: {}", obj.getStr("msg"));
					result.set("success", false);
					result.set("msg", obj.getStr("msg"));
					System.out.println(result);
				} else {
					log.info("签名数据导入成功");
				}
			} else {
				log.info("签名数据文件不存在，跳过签名数据导入");
			}

			// 导入日志数据
			String logPath = thisTempPath + File.separator + "log.json";
			log.info("开始导入日志数据: {}", logPath);
			if (FileUtil.exist(logPath)) {
				log.debug("日志数据文件存在，开始读取");
				String logStr = FileUtil.readString(logPath, StandardCharsets.UTF_8);
				log.info("调用TWX导入日志数据");
				JSONObject obj = Util.postTwxForObject(THING, "ImportLogData",
						JSONUtil.createObj().set("relationIds", relationIds).set("dataStr", logStr));
				if (!obj.getBool("success")) {
					log.error("日志数据导入失败: {}", obj.getStr("msg"));
					result.set("success", false);
					result.set("msg", obj.getStr("msg"));
					System.out.println(result);
				} else {
					log.info("日志数据导入成功");
				}
			} else {
				log.info("日志数据文件不存在，跳过日志数据导入");
			}

			log.info("大型ZIP文件导入完成: {}", filePath);
			return result;
		} catch (Exception e) {
			log.error("导入大型ZIP文件失败: {}", e.getMessage(), e);
			return JSONUtil.createObj()
					.set("success", false)
					.set("msg", "导入失败：" + e.getMessage());
		} finally {
			// 清理临时文件
			try {
				log.info("开始清理临时文件: {}", thisTempPath);
				FileUtil.del(thisTempPath);
				log.info("临时文件清理完成");
			} catch (Exception e) {
				log.warn("删除临时文件夹失败: {}, 错误: {}", thisTempPath, e.getMessage(), e);
			}
		}
	}

	/**
	 * 导入Excel模板
	 *
	 * @param file     Excel文件
	 * @param id       节点ID
	 * @param saveUser 保存用户
	 * @return 导入结果
	 */
	public JSONObject importTplExcel(MultipartFile file, String id, String saveUser) {
		FileUploadUtil.UploadResult uploadResult = FileUploadUtil.uploadToTemp(file);
		try {
			// 转换Excel为HandsonTable格式
			JSONObject res = Util.excel2HandsonTable(uploadResult.getAbsolutePath());
			if (res.getBool("success")) {
				String tableData = res.getJSONObject("data").toString();
				// 保存数据到TWX
				return Util.postTwxForObject("Thing.Fn.QualityReportTpl", "SaveTableData",
						JSONUtil.createObj()
								.set("id", id)
								.set("tableData", tableData)
								.set("saveUser", saveUser));
			}
			return res;
		} finally {
			// 处理临时文件
			FileUploadUtil.cleanupTemp(uploadResult.getAbsolutePath());
		}
	}

	/**
	 * 处理Excel文件并导入照片数据
	 *
	 * @param excelPath Excel文件相对路径
	 * @param photosStr 照片或签名JSON数组字符串
	 * @return 处理结果
	 */
	public JSONObject importTestExcel(String excelPath, String photosStr) {
		JSONObject result;
		try {
			// 获取文件上传路径
			String path = Util.getFileUploadPath();

			// 创建文件上传目录
			FileUtil.mkdir(path);

			// 构建完整的文件路径
			String fullPath = path + File.separator + excelPath;

			// 转换Excel为HandsonTable格式
			result = Util.excel2HandsonTable(fullPath);

			if (result.getBool("success")) {
				JSONObject tableData = result.getJSONObject("data");
				JSONArray metas = tableData.getJSONArray("meta");
				JSONArray photos = JSONUtil.parseArray(photosStr);

				int photoIndex = 0;
				for (int i = 0; i < photos.size(); i++) {
					JSONObject photo = photos.getJSONObject(i);
					String photoDownLoadURL = photo.getStr("photoDownLoadURL");
					JSONObject downRes = JSONUtil.parseObj(Util.downloadFromUrl(photoDownLoadURL));

					if (downRes.getBool("success")) {
						String filePath = downRes.getStr("filePath");
						String type = photo.getStr("type");
						String date = photo.getStr("date");
						int row = photo.getInt("row");
						int col = photo.getInt("col");

						// 更新元数据
						for (int j = 0; j < metas.size(); j++) {
							JSONObject meta = metas.getJSONObject(j);
							if (meta.getInt("row") == row && meta.getInt("col") == col) {
								JSONArray eles = meta.getJSONArray("eles");
								JSONObject ele = new JSONObject();

								if (type.equals("sign")) {
									ele.set("type", "sign")
											.set("src", filePath)
											.set("date", date);
								} else if (type.equals("photo")) {
									ele.set("type", "photo")
											.set("src", "/File" + filePath)
											.set("photoPath", filePath)
											.set("date", date)
											.set("photoShowNum", "自动同步图片-图" + (++photoIndex))
											.set("class", "sign-img photo");
								}
								eles.add(ele);
							}
						}
					}
				}
			}

			return result;
		} catch (Exception e) {
			log.error("处理Excel文件失败", e);
			return JSONUtil.createObj()
					.set("success", false)
					.set("result", e.toString());
		}
	}

	private JSONArray getCertificateMap() {
		return Util.postTwxForArray(THING, "GetCertificateMap");
	}

	private static final String INTERNAL_TYPE = "internal";
	private static final String PUBLIC_TEMPLATE_PATH = "classpath:static/templates/公开合格证模板.docx";
	private static final String INTERNAL_TEMPLATE_PATH = "classpath:static/templates/内部合格证模板.docx";
	private static final String MODEL_KEY = "型号";
	private static final String PHASE_KEY = "阶段";
	private static final String INSPECTOR_KEY = "检验人员";
	private static final String MODEL_PHASE_KEY1 = "型号（阶段）";
	private static final String MODEL_PHASE_KEY2 = "型号(阶段)";

	/**
	 * 批量生成合格证
	 *
	 * @param id               A表节点ID
	 * @param certificateTypes 证书类型（public/internal/internal,public）
	 * @param selectedIds      选中的B表ID列表，格式如："2059,2062,2065"
	 * @return 生成的ZIP文件路径
	 */
	public String batchCreateCertificates(String id, String certificateTypes, String selectedIds) {
		try {
			// 获取证书数据
			String fileName = FileNameUtil.getPrefix(generateFileName(getNodeData(id)));
			// 解析证书类型
			List<String> types = StrUtil.split(certificateTypes, ',');
			if (CollUtil.isEmpty(types)) {
				throw new RuntimeException("证书类型不能为空");
			}

			// 解析选中的B表ID列表
			List<String> selectedIdList = StrUtil.split(selectedIds, ',');

			// 获取B表节点数据
			JSONArray treeData = Util.postTwxForObject(THING, "AsyncQueryTree", JSONUtil.createObj().set("ID", id)).getJSONArray("data");
			if (treeData == null || treeData.isEmpty()) {
				throw new RuntimeException("未找到节点数据");
			}

			// 创建临时目录
			String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
			if (types.size() > 1) {
				// 如果同时需要公开和内部证书，创建子目录
				FileUtil.mkdir(thisTempPath + "公开");
				FileUtil.mkdir(thisTempPath + "内部");
			}
			FileUtil.mkdir(thisTempPath);

			try {
				// 处理所有通用件节点生成合格证
				for (int i = 0; i < treeData.size(); i++) {
					JSONObject nodeData = treeData.getJSONObject(i);
					int isGeneralComponent = nodeData.getInt("IS_GENERAL_COMPONENT");
					String nodeId = nodeData.getStr("ID");

					// 检查是否在选中的ID列表中
					if (isGeneralComponent == 1 && (CollUtil.isEmpty(selectedIdList) || selectedIdList.contains(nodeId))) {
						for (String type : types) {
							boolean isInternal = INTERNAL_TYPE.equals(type);
							String certificatePath = generateCertificate(nodeId, isInternal);

							// 确定目标路径
							String targetDir = thisTempPath;
							if (types.size() > 1) {
								// 如果同时需要公开和内部证书，放入对应子目录
								targetDir = thisTempPath + (isInternal ? "内部" : "公开") + File.separator;
							}

							// 复制文件到目标目录
							File certificateFile = FileUtil.file(certificatePath);
							String targetPath = targetDir + FileUtil.getName(certificatePath);
							FileUtil.copy(certificateFile, FileUtil.file(targetPath), true);
						}
					}
				}

				// 创建ZIP文件目录
				String zipDir = tempPath + File.separator + System.currentTimeMillis() + "_zip" + File.separator;
				FileUtil.mkdir(zipDir);
				String zipPath = zipDir + fileName + ".zip";

				// 压缩文件
				ZipUtil.zip(thisTempPath, zipPath);

				return zipPath;
			} finally {
				// 清理临时文件
				FileUtil.del(thisTempPath);
			}
		} catch (Exception e) {
			log.error("批量生成合格证失败", e);
			throw new RuntimeException("批量生成合格证失败：" + e.getMessage());
		}
	}

	/**
	 * 生成PDF文件
	 */
	private String generatePdfFile(Map<String, Object> certificateData, String fileName, boolean isInternal) throws IOException {
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
		FileUtil.mkdir(thisTempPath);

		String docxPath = thisTempPath + UUID.randomUUID() + ".docx";
		String pdfPath = thisTempPath + fileName;

		// 根据类型选择模板
		String templatePath = isInternal ? INTERNAL_TEMPLATE_PATH : PUBLIC_TEMPLATE_PATH;
		File templateFile = ResourceUtils.getFile(templatePath);
		try {
			tableService.createCertificateFile(certificateData, templateFile.getAbsolutePath(), docxPath, pdfPath);
		} finally {
			FileUtil.del(docxPath);
		}

		return pdfPath;
	}

	/**
	 * 生成合格证PDF文件
	 */
	private String generateCertificate(String id, boolean isInternal) throws IOException {
		// 获取证书数据
		JSONObject nodeData = getNodeData(id);
		String fileName = generateFileName(nodeData);

		// 构建证书内容
		Map<String, Object> certificateData = buildCertificateData(nodeData, isInternal);

		// 生成PDF文件
		return generatePdfFile(certificateData, fileName, isInternal);
	}

	/**
	 * 获取节点数据
	 */
	private JSONObject getNodeData(String id) {
		JSONObject obj = Util.postTwxForObject(THING, "QueryNodeById",
				JSONUtil.createObj().set("id", id));
		return obj.getJSONObject("data");
	}

	/**
	 * 生成文件名
	 */
	private String generateFileName(JSONObject nodeData) {
		String name = nodeData.getStr("NAME");
		String tableNum = nodeData.getStr("TABLE_NUM");
		return tableNum + "：" + name + "-合格证.pdf";
	}

	/**
	 * 构建证书数据
	 */
	private Map<String, Object> buildCertificateData(JSONObject nodeData, boolean isInternal) {
		JSONArray certificateMaps = getCertificateMap();
		JSONArray tableData = nodeData.getJSONObject("SAVE_DATA").getJSONArray("tableData");
		Map<String, Object> map = new HashMap<>();
		String[] modelPhase = new String[2];

		certificateMaps.forEach(item -> {
			JSONObject certificateMap = (JSONObject) item;
			String tplName = certificateMap.getStr("tplName");
			String value = getTableValue(tableData, certificateMap.getStr("location"));

			if (INSPECTOR_KEY.equals(tplName) && isInternal) {
				processInspectorSignature(tplName, value, map);
			} else {
				map.put(tplName, value);
				updateModelPhase(tplName, value, modelPhase);
			}
		});

		// 添加型号（阶段）组合值
		String combinedValue = String.format("%s（%s）", modelPhase[0], modelPhase[1]);
		map.put(MODEL_PHASE_KEY1, combinedValue);
		map.put(MODEL_PHASE_KEY2, combinedValue.replace("（", "(").replace("）", ")"));

		return map;
	}

	/**
	 * 获取表格单元格的值
	 */
	private String getTableValue(JSONArray tableData, String location) {
		try {
			int[] rowCol = Util.convertExcelPosToRowCol(location);
			return tableData.getJSONArray(rowCol[0]).getStr(rowCol[1], "");
		} catch (Exception e) {
			return "";
		}
	}

	/**
	 * 处理检验人员签名
	 */
	private void processInspectorSignature(String tplName, String value, Map<String, Object> map) {
		if (StrUtil.isBlank(value)) {
			return;
		}

		String userSql = "select USER_SIGN,USER_SIGN_FORMAT from SYS_USER where USER_FULLNAME='" + value + "'";
		JSONArray users = Util.postQuerySql(userSql);

		Optional.ofNullable(users)
				.filter(arr -> !arr.isEmpty())
				.map(arr -> arr.getJSONObject(0))
				.ifPresent(user -> {
					String signImgPath = user.getStr("USER_SIGN", "");
					String signImgFormat = user.getStr("USER_SIGN_FORMAT", "");
					if (StrUtil.isNotBlank(signImgPath) && StrUtil.isNotBlank(signImgFormat)) {
						try {
							map.put(tplName, new PictureRenderData(97, 42,
									"." + signImgFormat,
									FileUtil.getInputStream(fileUploadPath + signImgPath)));
						} catch (Exception e) {
							log.error("处理检验人员签名失败", e);
						}
					}
				});
	}

	/**
	 * 更新型号和阶段值
	 */
	private void updateModelPhase(String tplName, String value, String[] modelPhase) {
		if (MODEL_KEY.equals(tplName)) {
			modelPhase[0] = value;
		} else if (PHASE_KEY.equals(tplName)) {
			modelPhase[1] = value;
		}
	}

	/**
	 * 创建证书文件
	 */
	public String createCertificate(String id, String certificateTypes) throws IOException {
		if (StrUtil.isBlank(certificateTypes)) {
			throw new IllegalArgumentException("证书类型不能为空");
		}

		// 处理证书类型
		List<String> types = StrUtil.split(certificateTypes, ',');
		if (types.size() == 1) {
			// 单个证书
			String type = types.get(0);
			boolean isInternal = INTERNAL_TYPE.equals(type);
			return generateCertificate(id, isInternal);
		} else {
			// 多个证书，需要压缩
			String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
			FileUtil.mkdir(thisTempPath);

			try {
				// 生成内部证书
				String internalPath = generateCertificate(id, true);
				File internalFile = FileUtil.file(internalPath);
				String fileName = FileNameUtil.getPrefix(internalFile);
				String internalNewPath = thisTempPath + "内部-" + FileUtil.getName(internalPath);
				FileUtil.copy(internalFile, FileUtil.file(internalNewPath), true);

				// 生成公开证书
				String publicPath = generateCertificate(id, false);
				File publicFile = FileUtil.file(publicPath);
				String publicNewPath = thisTempPath + "公开-" + FileUtil.getName(publicPath);
				FileUtil.copy(publicFile, FileUtil.file(publicNewPath), true);

				// 创建一个新的目录用于存放zip文件
				String zipDir = tempPath + File.separator + System.currentTimeMillis() + "_zip" + File.separator;
				FileUtil.mkdir(zipDir);
				String zipPath = zipDir + fileName + ".zip";

				// 压缩文件
				ZipUtil.zip(thisTempPath, zipPath);

				return zipPath;
			} finally {
				// 清理临时文件，保留zip文件
				File[] files = FileUtil.ls(thisTempPath);
				for (File file : files) {
					if (!file.getName().endsWith(".zip")) {
						FileUtil.del(file);
					}
				}
			}
		}
	}

	/**
	 * 对整表数据进行数据比对
	 * <p>
	 * 方法执行流程：
	 * 1. 获取表格数据：
	 * - 通过表ID请求QueryNodeById获取表格数据
	 * - 解析SAVE_DATA对象获取tableData、meta等信息
	 * <p>
	 * 2. 检查实测值：
	 * - 从第6行开始遍历数据区域
	 * - 检查是否所有实测值都为空
	 * <p>
	 * 3. 数据比对：
	 * - 获取每行的设计值(第3列)、公差值(第5列)和实测值(第8列)
	 * - 使用compareValues方法进行比对
	 * - 根据比对结果更新是否符合(第10列)和单元格样式
	 * <p>
	 * 4. 保存数据：
	 * - 调用SaveTableData方法保存更新后的数据
	 *
	 * @param id 表格ID
	 */
	public JSONObject compareTableData(String id, String saveUser) {
		// 1. 获取表格数据
		JSONObject response = Util.postTwxForObject(THING, "QueryNodeById",
				JSONUtil.createObj().set("id", id));
		JSONObject data = response.getJSONObject("data");
		JSONObject saveData = data.getJSONObject("SAVE_DATA");

		// 获取表格数据和元数据
		JSONArray tableData = saveData.getJSONArray("tableData");
		JSONArray meta = saveData.getJSONArray("meta");

		// 2. 检查是否所有实测值都为空
		boolean hasValue = false;
		for (int i = dataStartRow; i < tableData.size(); i++) {
			JSONArray row = tableData.getJSONArray(i);
			String measuredValue = row.getStr(measuredValueColumn);
			if (StrUtil.isNotEmpty(measuredValue)) {
				hasValue = true;
				break;
			}
		}

		// 如果所有实测值都为空，清空所有单元格样式并设置待定
		if (!hasValue) {
			for (int i = dataStartRow; i < tableData.size(); i++) {
				JSONArray row = tableData.getJSONArray(i);
				// 设置是否符合为待定
				row.set(resultColumn, "待定");
				// 清空单元格样式
				updateCellStyle(meta, i, measuredValueColumn, "htMiddle htCenter");
				updateCellStyle(meta, i, resultColumn, "htMiddle htCenter");
			}
			return Util.postTwxForObject("Thing.Util.HandsonTable", "SaveTableData",
					JSONUtil.createObj()
							.set("id", id)
							.set("tableData", saveData)
							.set("saveUser", saveUser)
							.set("tableName", Util.getThingProperty(THING, "tableName")));
		}

		// 3. 数据比对
		for (int i = dataStartRow; i < tableData.size(); i++) {
			JSONArray row = tableData.getJSONArray(i);

			// 获取设计值、公差值和实测值
			String designValue = row.getStr(designValueColumn);  // 第3列
			String tolerance = row.getStr(toleranceColumn);    // 第5列
			String measuredValue = row.getStr(measuredValueColumn); // 第8列

			boolean isConform = CommonUtil.compareValues(designValue, tolerance, measuredValue);

			// 更新是否符合列
			row.set(resultColumn, isConform ? "符合" : "不符合");

			// 更新单元格样式
			if (!isConform) {
				// 如果不符合，设置橙色样式
				updateCellStyle(meta, i, measuredValueColumn, "htMiddle htCenter font-color-orange");
				updateCellStyle(meta, i, resultColumn, "htMiddle htCenter font-color-orange");
			} else {
				// 如果符合，清除样式
				updateCellStyle(meta, i, measuredValueColumn, "htMiddle htCenter");
				updateCellStyle(meta, i, resultColumn, "htMiddle htCenter");
			}
		}

		// 4. 保存更新后的数据
		return Util.postTwxForObject("Thing.Util.HandsonTable", "SaveTableData",
				JSONUtil.createObj()
						.set("id", id)
						.set("tableData", saveData)
						.set("saveUser", saveUser)
						.set("tableName", Util.getThingProperty(THING, "tableName")));
	}

	/**
	 * 锁定表格时更新数据
	 * <p>
	 * 方法执行流程：
	 * 1. 获取表格数据：
	 * - 通过表ID请求QueryNodeById获取表格数据
	 * - 解析SAVE_DATA对象获取tableData、meta等信息
	 * <p>
	 * 2. 更新数据：
	 * - 将所有行的"是否符合"列更新为"符合"
	 * - 清除实测值和是否符合列的样式标记
	 * <p>
	 * 3. 保存数据：
	 * - 调用SaveTableData方法保存更新后的数据
	 *
	 * @param id       表格ID
	 * @param saveUser 保存用户
	 * @return 保存结果
	 */
	public JSONObject updateTableDataWhenLock(String id, String saveUser) {
		// 1. 获取表格数据
		JSONObject response = Util.postTwxForObject(THING, "QueryNodeById",
				JSONUtil.createObj().set("id", id));
		JSONObject data = response.getJSONObject("data");
		JSONObject saveData = data.getJSONObject("SAVE_DATA");

		// 获取表格数据和元数据
		JSONArray tableData = saveData.getJSONArray("tableData");
		JSONArray meta = saveData.getJSONArray("meta");

		// 2. 更新数据
		for (int i = dataStartRow; i < tableData.size(); i++) {
			JSONArray row = tableData.getJSONArray(i);

			// 设置是否符合为"符合"
			row.set(resultColumn, "符合");

			// 清除实测值和是否符合列的样式标记
			updateCellStyle(meta, i, measuredValueColumn, "htMiddle htCenter");
			updateCellStyle(meta, i, resultColumn, "htMiddle htCenter");
		}

		// 3. 保存数据
		return Util.postTwxForObject("Thing.Util.HandsonTable", "SaveTableData",
				JSONUtil.createObj()
						.set("id", id)
						.set("tableData", saveData)
						.set("saveUser", saveUser)
						.set("tableName", Util.getThingProperty(THING, "tableName")));
	}

	/**
	 * 更新单元格样式
	 *
	 * @param meta      元数据数组
	 * @param row       行号
	 * @param col       列号
	 * @param className 样式类名
	 */
	private void updateCellStyle(JSONArray meta, int row, int col, String className) {
		// 查找对应的单元格样式
		for (int i = 0; i < meta.size(); i++) {
			JSONObject cell = meta.getJSONObject(i);
			if (cell.getInt("row") == row && cell.getInt("col") == col) {
				cell.set("className", className);
				break;
			}
		}
	}

	/**
	 * 创建证书文件 - 用于打印
	 * 与createCertificate类似，但返回文件而不是路径
	 */
	public File createCertificateForPrint(String id, String certificateType) throws IOException {
		if (StrUtil.isBlank(certificateType)) {
			throw new IllegalArgumentException("证书类型不能为空");
		}

		// 只支持一种证书类型
		boolean isInternal = INTERNAL_TYPE.equals(certificateType);

		// 生成PDF文件
		String pdfPath = generateCertificate(id, isInternal);

		// 创建pdf-print目录
		String pdfPrintDir = fileUploadPath + File.separator + "pdf-print";
		FileUtil.mkdir(pdfPrintDir);

		// 生成唯一文件名
		String uniqueFileName = "certificate_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8) + ".pdf";
		String targetPath = pdfPrintDir + File.separator + uniqueFileName;

		// 复制文件到目标目录
		FileUtil.copy(pdfPath, targetPath, true);

		// 返回文件对象
		return FileUtil.file(targetPath);
	}

	/**
	 * 批量创建证书文件并合并为单个PDF - 用于打印
	 *
	 * @param id              A表节点ID
	 * @param certificateType 证书类型（public/internal，打印模式下只能选一种）
	 * @param selectedIds     选中的B表ID列表，格式如："2059,2062,2065"
	 * @return 合并后的PDF文件
	 */
	public File batchCreateCertificatesForPrint(String id, String certificateType, String selectedIds) {
		try {
			// 解析证书类型 - 打印模式下只允许一种类型
			boolean isInternal = INTERNAL_TYPE.equals(certificateType);

			// 解析选中的B表ID列表
			List<String> selectedIdList = StrUtil.split(selectedIds, ',');

			// 获取B表节点数据
			JSONArray treeData = Util.postTwxForObject(THING, "AsyncQueryTree", JSONUtil.createObj().set("ID", id)).getJSONArray("data");
			if (treeData == null || treeData.isEmpty()) {
				throw new RuntimeException("未找到节点数据");
			}

			// 创建临时目录
			String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
			FileUtil.mkdir(thisTempPath);

			// 收集所有生成的PDF文件路径
			List<String> pdfFiles = new ArrayList<>();

			try {
				// 处理所有通用件节点生成合格证
				for (int i = 0; i < treeData.size(); i++) {
					JSONObject nodeData = treeData.getJSONObject(i);
					int isGeneralComponent = nodeData.getInt("IS_GENERAL_COMPONENT");
					String nodeId = nodeData.getStr("ID");

					// 检查是否在选中的ID列表中
					if (isGeneralComponent == 1 && (CollUtil.isEmpty(selectedIdList) || selectedIdList.contains(nodeId))) {
						String certificatePath = generateCertificate(nodeId, isInternal);
						pdfFiles.add(certificatePath);
					}
				}

				// 如果没有生成任何PDF文件
				if (pdfFiles.isEmpty()) {
					throw new RuntimeException("未生成任何合格证");
				}

				// 创建pdf-print目录
				String pdfPrintDir = fileUploadPath + File.separator + "pdf-print";
				FileUtil.mkdir(pdfPrintDir);

				// 生成唯一文件名
				String uniqueFileName = "certificates_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8) + ".pdf";
				String targetPath = pdfPrintDir + File.separator + uniqueFileName;

				// 合并PDF文件
				mergePdfFiles(pdfFiles, targetPath);

				return FileUtil.file(targetPath);
			} finally {
				// 删除中间生成的PDF文件，但保留合并后的文件
				pdfFiles.forEach(FileUtil::del);
			}
		} catch (Exception e) {
			log.error("批量生成合格证打印失败", e);
			throw new RuntimeException("批量生成合格证打印失败：" + e.getMessage());
		}
	}

	/**
	 * 合并多个PDF文件为一个文件
	 *
	 * @param inputFiles 输入PDF文件路径列表
	 * @param outputPath 输出PDF文件路径
	 */
	private void mergePdfFiles(List<String> inputFiles, String outputPath) {
		log.debug("开始合并{}个PDF文件到: {}", inputFiles.size(), outputPath);
		try {
			PdfDocument pdfDocument = new PdfDocument(new PdfWriter(outputPath));

			int successCount = 0;
			for (String inputFile : inputFiles) {
				try {
					PdfDocument srcDoc = new PdfDocument(new PdfReader(inputFile));
					srcDoc.copyPagesTo(1, srcDoc.getNumberOfPages(), pdfDocument);
					srcDoc.close();
					successCount++;
					log.debug("成功合并文件: {}", inputFile);
				} catch (Exception e) {
					log.error("合并PDF文件失败: {}, 错误: {}", inputFile, e.getMessage());
				}
			}

			pdfDocument.close();
			log.info("PDF文件合并完成: 成功合并{}个文件(共{}个)", successCount, inputFiles.size());

			// 验证合并后的文件
			try {
				// 尝试打开文件验证其完整性
				PdfDocument checkDoc = new PdfDocument(new PdfReader(outputPath));
				int pageCount = checkDoc.getNumberOfPages();
				checkDoc.close();
				log.info("合并后的PDF文件有效，共{}页", pageCount);
			} catch (Exception e) {
				log.error("合并后的PDF文件无效: {}", e.getMessage());
				throw new RuntimeException("生成的PDF文件无效，请重试");
			}
		} catch (Exception e) {
			log.error("PDF合并过程出错: {}", e.getMessage(), e);
			throw new RuntimeException("合并PDF文件失败: " + e.getMessage());
		}
	}

	/**
	 * 创建二维码内容文本
	 *
	 * @param certificateData 证书数据Map
	 * @return 格式化的二维码内容文本
	 */
	private String createQrCodeContent(Map<String, Object> certificateData) {
		// 创建带默认值的获取方法
		java.util.function.Function<String, String> getValue = key -> {
			Object value = certificateData.getOrDefault(key, "");
			return value == null ? "" : value.toString().trim();
		};

		// 按照需求格式添加信息，并处理可能的空值
		String sb = "型号：" + getValue.apply(MODEL_KEY) + "\n" +
				"阶段：" + getValue.apply(PHASE_KEY) + "\n" +
				"图件号：" + getValue.apply("图件号") + "\n" +
				"零部件名称：" + getValue.apply("零部件名称") + "\n" +
				"合格件数：" + getValue.apply("合格件数") + "\n" +
				"检验日期：" + getValue.apply("检验日期") + "\n" +
				"备注：" + getValue.apply("备注");

		return sb;
	}

	/**
	 * 生成二维码图像
	 *
	 * @param content 二维码内容
	 * @return 二维码图像字节数组
	 * @throws WriterException 编码异常
	 * @throws IOException     IO异常
	 */
	private byte[] generateQrCodeImage(String content) throws WriterException, IOException {
		Map<EncodeHintType, Object> hints = new HashMap<>();
		hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H); // 使用最高纠错级别
		hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
		hints.put(EncodeHintType.MARGIN, 0); // 减小边距，使二维码更大

		// 增加二维码尺寸，提高清晰度
		BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, 600, 600, hints);
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		MatrixToImageWriter.writeToStream(bitMatrix, "PNG", outputStream);
		return outputStream.toByteArray();
	}

	/**
	 * 创建二维码PDF文档
	 *
	 * @param qrCodeImage   二维码图像字节数组
	 * @param componentName 零部件名称
	 * @param pdfPath       PDF文件路径
	 * @throws IOException IO异常
	 */
	private void createQrCodePdf(byte[] qrCodeImage, String componentName, String pdfPath) throws IOException {
		// 创建4cm*6cm的PDF文档
		PdfWriter writer = new PdfWriter(pdfPath);
		PdfDocument pdfDocument = new PdfDocument(writer);

		// 设置页面大小为4cm*6cm（转换为磅）
		float cmToPoints = 28.35f; // 1cm约等于28.35磅
		float width = 4 * cmToPoints;
		float height = 6 * cmToPoints;
		PageSize pageSize = new PageSize(width, height);
		pdfDocument.setDefaultPageSize(pageSize);

		Document document = new Document(pdfDocument);
		document.setMargins(5, 5, 5, 5); // 设置较小的边距以充分利用空间

		// 创建二维码图像
		Image qrImage = new Image(ImageDataFactory.create(qrCodeImage));
		qrImage.setHorizontalAlignment(HorizontalAlignment.CENTER);

		// 调整图像大小，使其适合页面但留出足够空间显示名称
		float imageWidth = width * 0.85f; // 使用页面宽度的85%
		qrImage.setWidth(imageWidth);
		qrImage.setAutoScaleHeight(true);

		// 创建表格布局，确保内容垂直居中
		Table table = new Table(UnitValue.createPercentArray(1)).useAllAvailableWidth();

		// 添加二维码图像
		Cell imageCell = new Cell()
				.add(qrImage)
				.setBorder(null)
				.setHorizontalAlignment(HorizontalAlignment.CENTER)
				.setVerticalAlignment(VerticalAlignment.MIDDLE)
				.setPadding(0)
				.setMargin(0);
		table.addCell(imageCell);

		// 添加零部件名称，文字过长时自动换行显示
		PdfFont font = PdfFontFactory.createFont(pdfFontPath + "\\simhei.ttf", PdfEncodings.IDENTITY_H);
		Paragraph nameParagraph = new Paragraph(componentName)
				.setFont(font)
				.setFontSize(7)                  // 缩小字体以确保适应空间
				.setTextAlignment(TextAlignment.CENTER)
				.setMultipliedLeading(1.0f);     // 行间距

		Cell nameCell = new Cell()
				.add(nameParagraph)
				.setBorder(null)
				.setHorizontalAlignment(HorizontalAlignment.CENTER)
				.setVerticalAlignment(VerticalAlignment.MIDDLE)
				.setPadding(0)
				.setMargin(0);
		table.addCell(nameCell);

		document.add(table);
		document.close();
	}

	/**
	 * 生成单个结构件二维码
	 *
	 * @param id 节点ID
	 * @return 生成的二维码PDF文件路径
	 * @throws IOException     IO异常
	 * @throws WriterException 二维码生成异常
	 */
	public String createQrCode(String id) throws IOException, WriterException {
		// 获取节点数据
		JSONObject nodeData = getNodeData(id);
		String name = nodeData.getStr("NAME");
		String tableNum = nodeData.getStr("TABLE_NUM");
		String fileName = tableNum + "：" + name + "-二维码.pdf";

		// 构建证书数据（与合格证使用相同的数据）
		Map<String, Object> certificateData = buildCertificateData(nodeData, false);

		// 创建二维码内容
		String qrCodeContent = createQrCodeContent(certificateData);

		// 生成二维码图像
		byte[] qrCodeImage = generateQrCodeImage(qrCodeContent);

		// 创建临时目录和文件路径
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
		FileUtil.mkdir(thisTempPath);
		String pdfPath = thisTempPath + fileName;

		// 创建二维码PDF
		createQrCodePdf(qrCodeImage, name, pdfPath);

		return pdfPath;
	}

	/**
	 * 批量生成结构件二维码
	 *
	 * @param id          A表节点ID
	 * @param selectedIds 选中的B表ID列表，格式如："2059,2062,2065"
	 * @return 生成的ZIP文件路径
	 */
	public String batchCreateQrCodes(String id, String selectedIds) {
		log.info("开始批量生成二维码: id={}, selectedIds={}", id, selectedIds);
		String thisTempPath = null;
		try {
			// 获取主节点数据
			JSONObject mainNodeData = getNodeData(id);
			String fileName = FileNameUtil.getPrefix(mainNodeData.getStr("TABLE_NUM") + "：" + mainNodeData.getStr("NAME"));
			log.debug("根节点名称: {}", fileName);

			// 解析选中的B表ID列表
			List<String> selectedIdList = StrUtil.split(selectedIds, ',');
			log.debug("选中节点数量: {}", selectedIdList.size());

			// 获取B表节点数据
			log.info("查询子节点数据");
			JSONArray treeData = Util.postTwxForObject(THING, "AsyncQueryTree", JSONUtil.createObj().set("ID", id)).getJSONArray("data");
			if (treeData == null || treeData.isEmpty()) {
				log.error("未找到节点数据");
				throw new RuntimeException("未找到节点数据");
			}
			log.debug("查询到节点数量: {}", treeData.size());

			// 创建临时目录
			thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
			FileUtil.mkdir(thisTempPath);
			log.debug("创建临时目录: {}", thisTempPath);

			int successCount = 0;
			int failCount = 0;

			try {
				// 处理所有通用件节点生成二维码
				for (int i = 0; i < treeData.size(); i++) {
					JSONObject nodeData = treeData.getJSONObject(i);
					int isGeneralComponent = nodeData.getInt("IS_GENERAL_COMPONENT");
					String nodeId = nodeData.getStr("ID");

					// 检查是否在选中的ID列表中
					if (isGeneralComponent == 1 && (CollUtil.isEmpty(selectedIdList) || selectedIdList.contains(nodeId))) {
						try {
							String qrCodePath = createQrCode(nodeId);

							// 复制文件到目标目录
							File qrCodeFile = FileUtil.file(qrCodePath);
							String targetPath = thisTempPath + FileUtil.getName(qrCodePath);
							FileUtil.copy(qrCodeFile, FileUtil.file(targetPath), true);
							successCount++;

							// 清理源文件
							FileUtil.del(qrCodeFile);
						} catch (Exception e) {
							log.error("生成节点[{}]二维码失败: {}", nodeId, e.getMessage(), e);
							failCount++;
						}
					}
				}

				log.info("二维码生成完成: 成功={}, 失败={}", successCount, failCount);

				// 创建ZIP文件目录
				String zipDir = tempPath + File.separator + System.currentTimeMillis() + "_zip" + File.separator;
				FileUtil.mkdir(zipDir);
				String zipPath = zipDir + fileName + "-二维码.zip";
				log.debug("创建ZIP文件: {}", zipPath);

				// 压缩文件
				ZipUtil.zip(thisTempPath, zipPath);
				log.info("ZIP文件生成成功: {}", zipPath);

				return zipPath;
			} finally {
				// 清理临时文件
				try {
					log.debug("清理临时目录: {}", thisTempPath);
					FileUtil.del(thisTempPath);
				} catch (Exception e) {
					log.warn("清理临时目录失败: {}", e.getMessage());
				}
			}
		} catch (Exception e) {
			log.error("批量生成二维码失败: {}", e.getMessage(), e);
			// 确保临时目录被清理
			if (thisTempPath != null) {
				try {
					FileUtil.del(thisTempPath);
				} catch (Exception ex) {
					// 忽略清理异常
				}
			}
			throw new RuntimeException("批量生成二维码失败：" + e.getMessage());
		}
	}

	/**
	 * 创建二维码文件 - 用于打印
	 *
	 * @param id 节点ID
	 * @return 二维码PDF文件
	 * @throws IOException     IO异常
	 * @throws WriterException 二维码生成异常
	 */
	public File createQrCodeForPrint(String id) throws IOException, WriterException {
		log.info("开始生成用于打印的二维码: id={}", id);

		// 生成二维码PDF文件
		String pdfPath = createQrCode(id);
		log.debug("二维码PDF生成成功: {}", pdfPath);

		// 创建pdf-print目录
		String pdfPrintDir = fileUploadPath + File.separator + "pdf-print";
		FileUtil.mkdir(pdfPrintDir);
		log.debug("确保打印目录存在: {}", pdfPrintDir);

		// 生成唯一文件名
		String uniqueFileName = "qrcode_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8) + ".pdf";
		String targetPath = pdfPrintDir + File.separator + uniqueFileName;
		log.debug("目标打印文件路径: {}", targetPath);

		// 复制文件到目标目录
		FileUtil.copy(pdfPath, targetPath, true);

		// 删除临时文件
		FileUtil.del(pdfPath);
		log.info("打印文件准备完成: {}", targetPath);

		// 返回文件对象
		return FileUtil.file(targetPath);
	}

	/**
	 * 批量创建二维码文件并合并为单个PDF - 用于打印
	 *
	 * @param id          A表节点ID
	 * @param selectedIds 选中的B表ID列表，格式如："2059,2062,2065"
	 * @return 合并后的PDF文件
	 */
	public File batchCreateQrCodesForPrint(String id, String selectedIds) {
		log.info("开始批量生成用于打印的二维码: id={}, selectedIds={}", id, selectedIds);

		// 创建临时目录
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
		FileUtil.mkdir(thisTempPath);
		log.debug("创建临时目录: {}", thisTempPath);

		// 收集所有生成的PDF文件路径
		List<String> pdfFiles = new ArrayList<>();

		try {
			// 解析选中的B表ID列表
			List<String> selectedIdList = StrUtil.split(selectedIds, ',');
			log.debug("选中节点数量: {}", selectedIdList.size());

			// 获取B表节点数据
			JSONArray treeData = Util.postTwxForObject(THING, "AsyncQueryTree", JSONUtil.createObj().set("ID", id)).getJSONArray("data");
			if (treeData == null || treeData.isEmpty()) {
				log.error("未找到节点数据");
				throw new RuntimeException("未找到节点数据");
			}
			log.debug("查询到节点数量: {}", treeData.size());

			int successCount = 0;
			int failCount = 0;

			// 处理所有通用件节点生成二维码
			for (int i = 0; i < treeData.size(); i++) {
				JSONObject nodeData = treeData.getJSONObject(i);
				int isGeneralComponent = nodeData.getInt("IS_GENERAL_COMPONENT");
				String nodeId = nodeData.getStr("ID");

				// 检查是否在选中的ID列表中
				if (isGeneralComponent == 1 && (CollUtil.isEmpty(selectedIdList) || selectedIdList.contains(nodeId))) {
					try {
						String qrCodePath = createQrCode(nodeId);
						pdfFiles.add(qrCodePath);
						successCount++;
					} catch (Exception e) {
						log.error("生成节点[{}]二维码失败: {}", nodeId, e.getMessage(), e);
						failCount++;
					}
				}
			}

			log.info("二维码生成完成: 成功={}, 失败={}", successCount, failCount);

			// 如果没有生成任何PDF文件
			if (pdfFiles.isEmpty()) {
				log.error("未生成任何二维码");
				throw new RuntimeException("未生成任何二维码");
			}

			// 创建pdf-print目录
			String pdfPrintDir = fileUploadPath + File.separator + "pdf-print";
			FileUtil.mkdir(pdfPrintDir);
			log.debug("确保打印目录存在: {}", pdfPrintDir);

			// 生成唯一文件名
			String uniqueFileName = "qrcodes_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8) + ".pdf";
			String targetPath = pdfPrintDir + File.separator + uniqueFileName;
			log.debug("目标打印文件路径: {}", targetPath);

			// 合并PDF文件
			mergePdfFiles(pdfFiles, targetPath);
			log.info("PDF文件合并成功: {}", targetPath);

			return FileUtil.file(targetPath);
		} catch (Exception e) {
			log.error("批量生成二维码打印失败: {}", e.getMessage(), e);
			throw new RuntimeException("批量生成二维码打印失败：" + e.getMessage());
		} finally {
			// 删除所有临时生成的PDF文件
			for (String pdfFile : pdfFiles) {
				try {
					FileUtil.del(pdfFile);
				} catch (Exception e) {
					log.warn("删除临时PDF文件失败: {}", pdfFile);
				}
			}
			// 清理临时目录
			try {
				FileUtil.del(thisTempPath);
			} catch (Exception e) {
				log.warn("清理临时目录失败: {}", e.getMessage());
			}
		}
	}

	/**
	 * 导出确认表锁定统计Excel
	 *
	 * @param nodeId 节点ID
	 * @return 生成的Excel文件
	 */
	public File exportUnlockedTablesExcel(String nodeId) {
		log.info("开始导出未锁定确认表统计Excel: nodeId={}", nodeId);

		try {
			// 调用Thingworx服务获取未锁定表数据
			JSONObject result = Util.postTwxForObject(THING, "QueryUnlockedTables",
					JSONUtil.createObj().set("nodeId", nodeId));

			if (!result.getBool("success", false)) {
				log.error("查询未锁定确认表失败: {}", result.getStr("msg"));
				throw new RuntimeException("查询未锁定确认表失败: " + result.getStr("msg"));
			}

			JSONArray data = result.getJSONArray("data");
			if (data == null || data.isEmpty()) {
				log.warn("未找到未锁定的确认表");
				throw new RuntimeException("未找到未锁定的确认表");
			}
			log.debug("获取到{}条未锁定确认表数据", data.size());

			// 查询节点信息获取名称
			JSONObject nodeInfo = Util.postTwxForObject(THING, "QueryNodeById",
					JSONUtil.createObj().set("id", nodeId));

			String nodeName = "确认表锁定统计";
			if (nodeInfo.getBool("success", false)) {
				JSONObject nodeData = nodeInfo.getJSONObject("data");
				nodeName = nodeData.getStr("NAME", "确认表锁定统计");
			}
			log.debug("节点名称: {}", nodeName);

			// 构建表头数据
			JSONArray headers = new JSONArray();
			headers.add("序号");
			headers.add("型号分类");
			headers.add("型号");
			headers.add("阶段");
			headers.add("专业");
			headers.add("过程");
			headers.add("A表");
			headers.add("类型");
			headers.add("表名称");

			// 构建表格数据
			JSONArray tableData = new JSONArray();

			// 添加数据行
			for (int i = 0; i < data.size(); i++) {
				JSONObject item = data.getJSONObject(i);
				JSONArray row = new JSONArray();

				// 序号
				row.add(String.valueOf(i + 1));

				// 其他字段映射
				row.add(item.getStr("FOLDER_NAME", "-"));
				row.add(item.getStr("MODEL_NAME", "-"));
				row.add(item.getStr("PHASE_NAME", "-"));
				row.add(item.getStr("DIR_NAME", "-"));
				row.add(item.getStr("LEAF_NAME", "-"));
				row.add(item.getStr("REPORT_NAME", "-"));
				// A表/B表处理
				String type = item.getStr("TYPE", "");
				if ("report".equals(type)) {
					row.add("A表");
				} else if (type.contains("table")) {
					row.add("B表");
				} else {
					row.add(type);
				}

				// 表名称
				row.add(item.getStr("NAME", "-"));

				tableData.add(row);
			}

			// 设置列宽
			JSONArray columnWidths = new JSONArray();
			columnWidths.add(10);  // 序号
			columnWidths.add(20);  // 型号分类
			columnWidths.add(20);  // 型号
			columnWidths.add(20);  // 阶段
			columnWidths.add(20);  // 专业
			columnWidths.add(20);  // 过程
			columnWidths.add(60);  // A表
			columnWidths.add(15);  // 类型
			columnWidths.add(60);  // 表名称

			// 设置行高
			int rowHeight = 25;

			// 创建Excel文件
			String fileName = nodeName + "-未锁定确认表统计";

			// 使用CommonUtil.createExcelFile创建Excel文件
			File excelFile = CommonUtil.createExcelFile(fileName, headers, tableData, columnWidths, rowHeight);

			log.info("确认表锁定统计Excel生成成功: {}", excelFile.getAbsolutePath());

			return excelFile;
		} catch (Exception e) {
			log.error("导出确认表锁定统计Excel失败: {}", e.getMessage(), e);
			throw new RuntimeException("导出确认表锁定统计Excel失败: " + e.getMessage());
		}
	}

	/**
	 * 导出确认表签署统计Excel
	 *
	 * @param nodeId 节点ID
	 * @return 生成的Excel文件
	 */
	public File exportUnsignedTablesExcel(String nodeId) {
		log.info("开始导出应签未签确认表统计Excel: nodeId={}", nodeId);

		try {
			// 调用Thingworx服务获取应签未签表数据
			JSONObject result = Util.postTwxForObject(THING, "QueryUnsignedTables",
					JSONUtil.createObj().set("nodeId", nodeId));

			if (!result.getBool("success", false)) {
				log.error("查询应签未签确认表失败: {}", result.getStr("msg"));
				throw new RuntimeException("查询应签未签确认表失败: " + result.getStr("msg"));
			}

			JSONArray data = result.getJSONArray("data");
			if (data == null || data.isEmpty()) {
				log.warn("未找到应签未签的确认表");
				throw new RuntimeException("未找到应签未签的确认表");
			}
			log.debug("获取到{}条应签未签确认表数据", data.size());

			// 查询节点信息获取名称
			JSONObject nodeInfo = Util.postTwxForObject(THING, "QueryNodeById",
					JSONUtil.createObj().set("id", nodeId));

			String nodeName = "确认表签署统计";
			if (nodeInfo.getBool("success", false)) {
				JSONObject nodeData = nodeInfo.getJSONObject("data");
				nodeName = nodeData.getStr("NAME", "确认表签署统计");
			}
			log.debug("节点名称: {}", nodeName);

			// 构建表头数据
			JSONArray headers = new JSONArray();
			headers.add("序号");
			headers.add("型号分类");
			headers.add("型号");
			headers.add("阶段");
			headers.add("专业");
			headers.add("过程");
			headers.add("A表");
			headers.add("类型");
			headers.add("表名称");
			headers.add("应签未签位置");

			// 构建表格数据
			JSONArray tableData = new JSONArray();

			// 添加数据行
			for (int i = 0; i < data.size(); i++) {
				JSONObject item = data.getJSONObject(i);
				JSONArray row = new JSONArray();

				// 序号
				row.add(String.valueOf(i + 1));

				// 其他字段映射
				row.add(item.getStr("FOLDER_NAME", "-"));
				row.add(item.getStr("MODEL_NAME", "-"));
				row.add(item.getStr("PHASE_NAME", "-"));
				row.add(item.getStr("DIR_NAME", "-"));
				row.add(item.getStr("LEAF_NAME", "-"));
				row.add(item.getStr("REPORT_NAME", "-"));

				// 类型处理
				String type = item.getStr("TYPE", "");
				if ("report".equals(type)) {
					row.add("A表");
				} else if (type.contains("table")) {
					row.add("B表");
				} else {
					row.add(type);
				}

				// 表名称
				row.add(item.getStr("NAME", "-"));

				// 应签未签位置处理
				String positions = "";
				if (item.containsKey("unsignedCells") && item.getJSONArray("unsignedCells") != null && !item.getJSONArray("unsignedCells").isEmpty()) {
					JSONArray posArr = item.getJSONArray("unsignedCells");
					List<String> posList = new ArrayList<>();

					for (int j = 0; j < posArr.size(); j++) {
						JSONObject pos = posArr.getJSONObject(j);

						posList.add("行" + pos.getInt("row") + " 列" + pos.getInt("col"));
					}

					// 每3个元素换行一次
					StringBuilder sb = new StringBuilder();
					for (int j = 0; j < posList.size(); j++) {
						sb.append(posList.get(j));
						if (j < posList.size() - 1) {
							// 如果不是最后一个元素，添加分隔符
							if ((j + 1) % 3 == 0) {
								// 每3个元素后添加换行
								sb.append("\n");
							} else {
								// 同组内元素用分号分隔
								sb.append("; ");
							}
						}
					}
					positions = sb.toString();
				} else if (item.containsKey("UNSIGNED_POSITIONS") && StrUtil.isNotBlank(item.getStr("UNSIGNED_POSITIONS"))) {
					try {
						JSONArray posArr = JSONUtil.parseArray(item.getStr("UNSIGNED_POSITIONS"));
						List<String> posList = new ArrayList<>();

						for (int j = 0; j < posArr.size(); j++) {
							JSONObject pos = posArr.getJSONObject(j);

							posList.add("行" + pos.getInt("row") + " 列" + pos.getInt("col"));
						}

						// 每3个元素换行一次
						StringBuilder sb = new StringBuilder();
						for (int j = 0; j < posList.size(); j++) {
							sb.append(posList.get(j));
							if (j < posList.size() - 1) {
								// 如果不是最后一个元素，添加分隔符
								if ((j + 1) % 3 == 0) {
									// 每3个元素后添加换行
									sb.append("\n");
								} else {
									// 同组内元素用分号分隔
									sb.append("; ");
								}
							}
						}
						positions = sb.toString();
					} catch (Exception e) {
						log.warn("解析应签未签位置异常: {}", e.getMessage());
						positions = "解析异常";
					}
				} else {
					positions = "无详细信息";
				}
				row.add(positions);

				tableData.add(row);
			}

			// 设置列宽
			JSONArray columnWidths = new JSONArray();
			columnWidths.add(8);    // 序号
			columnWidths.add(15);   // 型号分类
			columnWidths.add(15);   // 型号
			columnWidths.add(15);   // 阶段
			columnWidths.add(20);   // 专业
			columnWidths.add(20);   // 过程
			columnWidths.add(50);   // A表
			columnWidths.add(10);   // 类型
			columnWidths.add(50);   // 表名称
			columnWidths.add(60);   // 应签未签位置

			// 设置行高
			int rowHeight = 25;

			// 创建Excel文件
			String fileName = nodeName + "-应签未签确认表统计";

			// 使用CommonUtil.createExcelFile创建Excel文件
			File excelFile = CommonUtil.createExcelFile(fileName, headers, tableData, columnWidths, rowHeight);

			log.info("确认表签署统计Excel生成成功: {}", excelFile.getAbsolutePath());

			return excelFile;
		} catch (Exception e) {
			log.error("导出确认表签署统计Excel失败: {}", e.getMessage(), e);
			throw new RuntimeException("导出确认表签署统计Excel失败: " + e.getMessage());
		}
	}

	/**
	 * 导入AIT映射模板
	 *
	 * @param file     Excel文件
	 * @param username 导入用户
	 * @return 导入结果
	 */
	public JSONObject importAitMappingTemplate(MultipartFile file, String username) throws IOException {
		ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
		String batchNo = System.currentTimeMillis() + "";

		// 读取Excel数据
		List<Map<String, Object>> rows = reader.readAll();

		// 校验数据
		if (rows.isEmpty()) {
			throw new IllegalArgumentException("Excel文件为空");
		}

		// 校验表头
		Map<String, Object> firstRow = rows.get(0);
		if (!firstRow.containsKey("来源专业节点") ||
				!firstRow.containsKey("来源过程节点") ||
				!firstRow.containsKey("来源确认表名称") ||
				!firstRow.containsKey("目标专业节点") ||
				!firstRow.containsKey("目标过程节点") ||
				!firstRow.containsKey("目标确认表名称")) {
			throw new IllegalArgumentException("Excel表头不符合要求，应包含：来源专业节点、来源过程节点、来源确认表名称、目标专业节点、目标过程节点、目标确认表名称");
		}

		// 删除旧数据并插入新数据
		String deleteSql = "DELETE FROM AIT_CONFIRM_MAPPING";
		Util.postCommandSql(deleteSql);

		// 构建批量插入SQL
		StringBuilder insertSql = new StringBuilder();
		insertSql.append("INSERT ALL ");

		for (Map<String, Object> row : rows) {
			String sourceProfessionalNode = String.valueOf(row.get("来源专业节点"));
			String sourceProcessNode = row.get("来源过程节点") == null ? "" :
					String.valueOf(row.get("来源过程节点"));
			String sourceConfirmTable = String.valueOf(row.get("来源确认表名称"));
			String targetProfessionalNode = String.valueOf(row.get("目标专业节点"));
			String targetProcessNode = row.get("目标过程节点") == null ? "" :
					String.valueOf(row.get("目标过程节点"));
			String targetConfirmTable = String.valueOf(row.get("目标确认表名称"));

			// 校验必填字段
			if (StrUtil.hasBlank(sourceProfessionalNode, sourceConfirmTable,
					targetProfessionalNode, targetConfirmTable)) {
				continue;
			}

			insertSql.append("INTO AIT_CONFIRM_MAPPING (")
					.append("BATCH_NO, SOURCE_PROFESSIONAL_NODE, SOURCE_PROCESS_NODE, ")
					.append("SOURCE_CONFIRM_TABLE, TARGET_PROFESSIONAL_NODE, TARGET_PROCESS_NODE, ")
					.append("TARGET_CONFIRM_TABLE, IMPORT_TIME, IMPORT_USER) VALUES ('")
					.append(batchNo).append("', '")
					.append(sourceProfessionalNode).append("', '")
					.append(sourceProcessNode).append("', '")
					.append(sourceConfirmTable).append("', '")
					.append(targetProfessionalNode).append("', '")
					.append(targetProcessNode).append("', '")
					.append(targetConfirmTable).append("', ")
					.append("SYSDATE, '")
					.append(username).append("') ");
		}

		insertSql.append("SELECT 1 FROM DUAL");
		Util.postCommandSql(insertSql.toString());

		log.info("AIT映射模板导入成功，批次号：{}", batchNo);
		return JSONUtil.createObj().set("batchNo", batchNo);
	}

	/**
	 * 导出AIT映射数据到Excel
	 */
	public File exportAitMappingData() {
		String sql = "SELECT SOURCE_PROFESSIONAL_NODE, " +
				"SOURCE_PROCESS_NODE, " +
				"SOURCE_CONFIRM_TABLE, " +
				"TARGET_PROFESSIONAL_NODE, " +
				"TARGET_PROCESS_NODE, " +
				"TARGET_CONFIRM_TABLE " +
				"FROM AIT_CONFIRM_MAPPING";

		JSONArray dataList = Util.postQuerySql(sql);
		// 准备数据
		JSONArray data = new JSONArray();
		for (int i = 0; i < dataList.size(); i++) {
			JSONObject obj = dataList.getJSONObject(i);
			JSONArray row = new JSONArray();
			row.add(obj.getStr("SOURCE_PROFESSIONAL_NODE", ""));
			row.add(obj.getStr("SOURCE_PROCESS_NODE", ""));
			row.add(obj.getStr("SOURCE_CONFIRM_TABLE", ""));
			row.add(obj.getStr("TARGET_PROFESSIONAL_NODE", ""));
			row.add(obj.getStr("TARGET_PROCESS_NODE", ""));
			row.add(obj.getStr("TARGET_CONFIRM_TABLE", ""));
			data.add(row);
		}
		// 设置表头和列宽
		JSONArray headers = JSONUtil.parseArray(Arrays.asList(
				"来源专业节点", "来源过程节点", "来源确认表名称",
				"目标专业节点", "目标过程节点", "目标确认表名称"));
		JSONArray columnWidths = JSONUtil.parseArray(Arrays.asList(25, 25, 40, 25, 25, 40));

		log.info("AIT映射数据导出成功，共{}条记录", data.size());
		return CommonUtil.createExcelFile("AIT确认表映射数据", headers, data, columnWidths, 25);
	}

	/**
	 * 导出确认表列表
	 *
	 * @param nodeId   节点ID
	 * @param nodeType 节点类型
	 * @return Excel文件
	 */
	public File exportConfirmTableList(String nodeName, String nodeId, String nodeType) {
		JSONObject result = Util.postTwxForObject(THING, "QueryTableListById", JSONUtil.createObj().set("nodeId", nodeId).set("nodeType", nodeType));
		JSONArray dataList = result.getJSONArray("data");
		JSONArray data = new JSONArray();
		if (dataList != null) {
			for (int i = 0; i < dataList.size(); i++) {
				JSONObject obj = dataList.getJSONObject(i);
				JSONArray row = new JSONArray();
				row.add(obj.getStr("DIR_NAME", ""));
				row.add(obj.getStr("LEAF_NAME", ""));
				row.add(obj.getStr("NAME", ""));
				data.add(row);
			}
		}
		JSONArray headers = JSONUtil.parseArray(Arrays.asList("专业节点", "过程节点", "确认表名称"));
		JSONArray columnWidths = JSONUtil.parseArray(Arrays.asList(25, 25, 70));
		return CommonUtil.createExcelFile(nodeName + "确认表列表", headers, data, columnWidths, 25);
	}
}
