<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Word文档转PDF工具</title>
    <link rel="shortcut icon" href="images/docx2pdf.png" type="image/x-icon" />
    <link rel="stylesheet" href="../static/lib/layui/css/layui.css" />
    <link rel="stylesheet" href="../tools/css/tool.css" />
    <link rel="stylesheet" href="css/docx2pdf.css" />
</head>
<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 顶部导航 -->
        <div class="layui-header">
            <div class="layui-logo">工具集合</div>
            <ul class="layui-nav layui-layout-left">
                <li class="layui-nav-item"><a href="../tools.html">返回工具列表</a></li>
            </ul>
        </div>
        
        <!-- 主体内容区域 -->
        <div class="layui-body">
            <div class="docx2pdf-container">
                <div class="tool-header">
                    <h2>Word文档转PDF工具</h2>
                    <p>支持将包含多个Word文档(DOC/DOCX)的ZIP压缩包批量转换为PDF格式</p>
                </div>
                
                <!-- 主要功能区域 -->
                <div class="layui-row layui-col-space20">
                    <!-- 上传区域 -->
                    <div class="layui-col-md12">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <i class="layui-icon layui-icon-upload"></i> 文件上传
                            </div>
                            <div class="layui-card-body">
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-drag-area" id="uploadDragArea">
                                        <i class="layui-icon layui-icon-upload-drag"></i>
                                        <p>点击或拖拽ZIP文件到此处上传</p>
                                        <p class="upload-tip">支持包含.doc或.docx文件的ZIP压缩包，大小不超过500MB</p>
                                    </div>
                                    <input type="file" id="fileInput" accept=".zip" class="upload-input" />
                                    <div class="upload-actions">
                                        <button type="button" id="selectFileBtn" class="layui-btn">
                                            <i class="layui-icon layui-icon-file"></i> 选择文件
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 状态/进度区域 -->
                    <div class="layui-col-md12">
                        <div class="layui-card" id="statusCard" style="display: none;">
                            <div class="layui-card-header">
                                <i class="layui-icon layui-icon-list"></i> 文件状态
                            </div>
                            <div class="layui-card-body">
                                <!-- 操作按钮 -->
                                <div class="operation-buttons" style="margin-bottom: 15px;">
                                    <button type="button" id="startConvertBtn" class="layui-btn layui-btn-normal">
                                        <i class="layui-icon layui-icon-play"></i> 开始转换
                                    </button>
                                </div>
                                
                                <!-- 进度信息 -->
                                <div class="progress-info" id="progressInfo" style="display: none; margin-bottom: 15px;">
                                    <div class="layui-progress layui-progress-big" lay-showpercent="true" lay-filter="conversionProgress">
                                        <div class="layui-progress-bar layui-bg-blue" lay-percent="0%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span id="processedCount">0</span>/<span id="totalCount">0</span> 个文件已处理
                                    </div>
                                </div>
                                
                                <!-- 已上传文件信息 -->
                                <div class="uploaded-file-info" id="uploadedFileInfo">
                                    <table class="layui-table" lay-skin="line">
                                        <colgroup>
                                            <col width="60">
                                            <col>
                                            <col width="120">
                                            <col width="100">
                                        </colgroup>
                                        <thead>
                                            <tr>
                                                <th>序号</th>
                                                <th>文件名</th>
                                                <th>大小</th>
                                                <th>状态</th>
                                            </tr>
                                        </thead>
                                        <tbody id="fileListBody">
                                            <!-- 文件列表将动态添加 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 结果区域 -->
                    <div class="layui-col-md12">
                        <div class="layui-card" id="resultCard" style="display: none;">
                            <div class="layui-card-header">
                                <i class="layui-icon layui-icon-ok-circle"></i> 转换结果
                            </div>
                            <div class="layui-card-body">
                                <div class="result-summary">
                                    <div class="result-item">
                                        <span class="result-label">总文件数：</span>
                                        <span class="result-value" id="totalFiles">0</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="result-label">成功转换：</span>
                                        <span class="result-value success" id="successFiles">0</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="result-label">转换失败：</span>
                                        <span class="result-value error" id="failedFiles">0</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="result-label">总用时：</span>
                                        <span class="result-value" id="totalTime">0秒</span>
                                    </div>
                                </div>
                                
                                <div class="download-area" id="downloadArea" style="display: none;">
                                    <a href="javascript:void(0);" target="_blank" id="downloadBtn" class="layui-btn layui-btn-normal">
                                        <i class="layui-icon layui-icon-download-circle"></i> 下载转换后的PDF文件
                                    </a>
                                </div>
                                
                                <!-- 错误信息区域 -->
                                <div class="error-info" id="errorInfo" style="display: none;">
                                    <div class="layui-collapse">
                                        <div class="layui-colla-item">
                                            <h2 class="layui-colla-title">查看错误详情</h2>
                                            <div class="layui-colla-content">
                                                <div class="error-list" id="errorList">
                                                    <!-- 错误信息将动态添加 -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../static/lib/layui/layui.js"></script>
    <script src="js/docx2pdf.js"></script>
    <script>
        layui.use(['element'], function(){
            var element = layui.element;
            // 手动初始化导航元素，解决样式问题
            element.render('nav');
        });
    </script>
</body>
</html> 