<div class="apiDetail">
<div>
	<h2><span>Function(treeNode, callbackFlag)</span><span class="path">zTreeObj.</span>removeNode</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Remove a node</p>
			<p class="highlight_red">Use removeNode() method of zTree v3.x can trigger 'beforeRemove / onRemove' callback function. for reduce redundant code.</p>
			<p class="highlight_red">Please use zTree object to executing the method.</p>
		</div>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>treeNode</b><span>JSON</span></h4>
	<p>JSON data object of the node to be removed.</p>
	<p class="highlight_red">Please ensure that this data object is an internal node data object in zTree.</p>
	<h4 class="topLine"><b>callbackFlag</b><span>Boolean</span></h4>
	<p>callbackFlag = true means: call this method, will trigger 'beforeRemove' & 'onRemove' callback.</p>
	<p>callbackFlag = false means: call this method, will not trigger callback.</p>
	<p class="highlight_red">If this parameter is omitted, it is same as 'callbackFlag = false'</p>
	<h4 class="topLine"><b>Return </b><span>none</span></h4>
	<p>no return value</p>
	</div>
	<h3>Examples of function</h3>
	<h4>1. Remove all of the selected nodes.</h4>
	<pre xmlns=""><code>var treeObj = $.fn.zTree.getZTreeObj("tree");
var nodes = treeObj.getSelectedNodes();
for (var i=0, l=nodes.length; i < l; i++) {
	treeObj.removeNode(nodes[i]);
}
</code></pre>
</div>
</div>