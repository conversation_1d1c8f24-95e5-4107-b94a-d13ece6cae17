package com.cirpoint.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.Contact;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenAPI配置类
 */
@Configuration
public class OpenAPIConfig {
    
    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("文件处理系统API文档")
                        .description("提供文件上传、下载等功能的API接口文档")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("系统管理员")
                                .email("<EMAIL>")));
    }
}
