package com.cirpoint.config;

import com.cirpoint.exception.WorkTimeProcessException;
import com.cirpoint.model.Result;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionConfig {

	/**
	 * 处理工时统计处理异常
	 */
	@ExceptionHandler(WorkTimeProcessException.class)
	public ResponseEntity<?> handleWorkTimeProcessException(WorkTimeProcessException e) {
		log.warn("工时统计处理异常: {}", e.getUserMessage(), e);
		return Result.error(e.getUserMessage());
	}

	/**
	 * 处理IllegalArgumentException异常
	 */
	@ExceptionHandler(IllegalArgumentException.class)
	public ResponseEntity<?> handleIllegalArgumentException(IllegalArgumentException e) {
		log.warn("主动异常: ", e);
		return Result.error(e.getLocalizedMessage());
	}

	/**
	 * 处理其他异常
	 */
	@ExceptionHandler(Exception.class)
	public ResponseEntity<?> handleException(Exception e) {
		log.error("系统异常", e);
		return Result.error("系统异常：" + e.getLocalizedMessage());
	}
}
