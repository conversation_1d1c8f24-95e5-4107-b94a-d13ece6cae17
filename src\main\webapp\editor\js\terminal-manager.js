// terminal-manager.js - 终端管理模块

// 终端管理器
const terminalManager = (function () {
    // 私有变量
    let terminal = null;
    let currentSessionId = null;
    let currentWorkingDirectory = null;
    let isTerminalOpen = false;
    let commandHistory = [];
    let historyIndex = -1;
    let terminalContainer = null;
    let terminalOutput = null;
    let terminalInput = null;
    let terminalPrompt = null;
    
    // 初始化终端
    function initTerminal() {
        // 创建终端容器
        terminalContainer = $('#terminalContainer');
        terminalOutput = $('#terminalOutput');
        terminalInput = $('#terminalInput');
        terminalPrompt = $('.terminal-prompt');
        
        // 绑定终端事件
        bindTerminalEvents();
    }
    
    // 更新终端提示符
    function updatePrompt(workingDirectory) {
        if (workingDirectory) {
            currentWorkingDirectory = workingDirectory;
            terminalPrompt.text(workingDirectory + ' $');
        }
    }
    
    // 绑定终端事件
    function bindTerminalEvents() {
        // 输入框回车事件
        terminalInput.on('keydown', function(e) {
            if (e.keyCode === 13) { // 回车键
                e.preventDefault();
                const command = terminalInput.val().trim();
                if (command) {
                    executeCommand(command);
                    terminalInput.val('');
                    
                    // 添加到历史记录
                    commandHistory.unshift(command);
                    if (commandHistory.length > 50) {
                        commandHistory.pop();
                    }
                    historyIndex = -1;
                }
            } else if (e.keyCode === 38) { // 上箭头
                e.preventDefault();
                navigateHistory(1);
            } else if (e.keyCode === 40) { // 下箭头
                e.preventDefault();
                navigateHistory(-1);
            } else if (e.keyCode === 9) { // Tab键
                e.preventDefault();
                // 可以在这里实现命令补全功能
            }
        });
        
        // 终止按钮事件
        $('#terminateCommand').on('click', function() {
            if (currentSessionId) {
                terminateCommand(currentSessionId);
            }
        });
        
        // 清空按钮事件
        $('#clearTerminal').on('click', function() {
            clearTerminal();
        });
        
        // 关闭终端按钮事件
        $('#closeTerminal').on('click', function() {
            toggleTerminal(false);
        });
    }
    
    // 导航命令历史
    function navigateHistory(direction) {
        if (commandHistory.length === 0) return;
        
        historyIndex += direction;
        
        if (historyIndex >= commandHistory.length) {
            historyIndex = commandHistory.length - 1;
        } else if (historyIndex < -1) {
            historyIndex = -1;
        }
        
        if (historyIndex === -1) {
            terminalInput.val('');
        } else {
            terminalInput.val(commandHistory[historyIndex]);
        }
    }
    
    // 执行命令
    function executeCommand(command) {
        // 在输出区域显示命令
        appendToTerminal(currentWorkingDirectory + ' $ ' + command, 'command');
        
        // 发送命令到后端
        $.ajax({
            url: 'terminal/execute',
            type: 'POST',
            data: {
                command: command,
                sessionId: currentSessionId,
                workingDirectory: currentWorkingDirectory
            },
            success: function(response) {
                // 显示输出
                if (response && response.length > 0) {
                    response.forEach(function(line) {
                        // 如果是cd命令，检查输出中的工作目录信息
                        if (command.trim().startsWith('cd ') && line.startsWith('当前工作目录: ')) {
                            const newWorkingDirectory = line.substring('当前工作目录: '.length);
                            updatePrompt(newWorkingDirectory);
                            // 同步更新后端的工作目录
                            $.ajax({
                                url: 'terminal/working-directory',
                                type: 'GET',
                                data: { sessionId: currentSessionId },
                                success: function(workingDir) {
                                    if (workingDir) {
                                        currentWorkingDirectory = workingDir;
                                    }
                                }
                            });
                        }
                        appendToTerminal(line, 'output');
                    });
                } else {
                    appendToTerminal('(命令执行完毕，无输出)', 'info');
                }
                
                // 滚动到底部
                scrollToBottom();
            },
            error: function(xhr, status, error) {
                appendToTerminal('请求出错: ' + error, 'error');
                scrollToBottom();
            }
        });
    }
    
    // 终止命令
    function terminateCommand(sessionId) {
        $.ajax({
            url: 'terminal/terminate',
            type: 'POST',
            data: {
                sessionId: sessionId
            },
            success: function(response) {
                appendToTerminal(response ? '命令已终止' : '没有找到正在运行的命令或命令已结束', response ? 'info' : 'error');
                scrollToBottom();
            },
            error: function(xhr, status, error) {
                appendToTerminal('终止命令请求出错: ' + error, 'error');
                scrollToBottom();
            }
        });
    }
    
    // 添加内容到终端
    function appendToTerminal(text, className) {
        const line = $('<div>').addClass('terminal-line').addClass(className);
        
        // 处理ANSI转义序列（简化版）
        text = text.replace(/\033\[(\d+)m/g, function(match, p1) {
            switch(p1) {
                case '31': return '<span class="ansi-red">';
                case '32': return '<span class="ansi-green">';
                case '33': return '<span class="ansi-yellow">';
                case '34': return '<span class="ansi-blue">';
                case '35': return '<span class="ansi-magenta">';
                case '36': return '<span class="ansi-cyan">';
                case '0': return '</span>';
                default: return '';
            }
        });
        
        line.html(text);
        terminalOutput.append(line);
    }
    
    // 滚动到底部
    function scrollToBottom() {
        terminalOutput.scrollTop(terminalOutput[0].scrollHeight);
    }
    
    // 清空终端
    function clearTerminal() {
        terminalOutput.empty();
        appendToTerminal('终端已清空', 'info');
    }
    
    // 切换终端显示状态
    function toggleTerminal(show) {
        if (show === undefined) {
            show = !isTerminalOpen;
        }
        
        isTerminalOpen = show;
        
        if (show) {
            terminalContainer.show();
            // 初始化终端
            if (!terminalOutput.children().length) {
                appendToTerminal('欢迎使用终端，输入命令并按回车执行', 'info');
                appendToTerminal('提示: 使用上下箭头可以浏览命令历史', 'info');
                
                // 生成新的会话ID
                currentSessionId = generateSessionId();
                
                // 初始化终端，使用当前编辑器路径
                $.ajax({
                    url: 'terminal/init',
                    type: 'POST',
                    data: {
                        sessionId: currentSessionId
                    },
                    success: function(response) {
                        if (response) {
                            currentWorkingDirectory = response;
                            updatePrompt(response);
                        }
                    }
                });
            } else {
                // 如果终端已经存在，获取当前工作目录
                $.ajax({
                    url: 'terminal/working-directory',
                    type: 'GET',
                    data: { sessionId: currentSessionId },
                    success: function(workingDir) {
                        if (workingDir) {
                            currentWorkingDirectory = workingDir;
                            updatePrompt(workingDir);
                        }
                    }
                });
            }
            terminalInput.focus();
        } else {
            terminalContainer.hide();
        }
    }
    
    // 生成会话ID
    function generateSessionId() {
        return 'terminal-' + Math.random().toString(36).substr(2, 9);
    }
    
    // 设置工作目录
    function setWorkingDirectory(directory) {
        currentWorkingDirectory = directory;
        updatePrompt(directory);
    }
    
    // 公开API
    return {
        init: initTerminal,
        toggle: toggleTerminal,
        execute: executeCommand,
        clear: clearTerminal,
        setWorkingDirectory: setWorkingDirectory
    };
})();

// 导出全局变量
window.terminalManager = terminalManager;
