package com.cirpoint.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * 日志查看器启动验证器
 * 在应用启动时验证配置并输出关键信息
 */
@Component
public class LogViewerStartupValidator implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(LogViewerStartupValidator.class);
    
    @Autowired
    private LogViewerConfig config;
    
    @Override
    public void run(String... args) throws Exception {
        // 验证配置
        try {
            config.validate();
        } catch (Exception e) {
            logger.error("配置验证失败: {}", e.getMessage());
            return;
        }
        
        // 检查日志文件和目录
        File logFile = new File(config.getCurrentLogFilePath());
        File logDir = logFile.getParentFile();
        File archiveDir = new File(config.getArchivedLogPath());
        
        if (!logDir.exists()) {
            if (!logDir.mkdirs()) {
                logger.error("日志目录创建失败");
            }
        }
        
        if (!archiveDir.exists()) {
            if (!archiveDir.mkdirs()) {
                logger.error("归档目录创建失败");
            }
        }
    }
} 