package com.cirpoint.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import lombok.extern.slf4j.Slf4j;

import java.io.FileOutputStream;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Access数据库导出Excel工具类
 */
@Slf4j
public class AccessToExcelUtil {

    /**
     * 将Access数据库中的表导出到Excel文件
     *
     * @param dbPath Access数据库路径
     * @param excelPath 导出的Excel文件路径
     * @return boolean 是否导出成功
     */
    public static boolean exportToExcel(String dbPath, String excelPath) {
        try (Connection conn = getConnection(dbPath);
             Workbook workbook = new XSSFWorkbook()) {

            // 获取所有表名
            List<String> tableNames = getTableNames(conn);
            
            // 为每个表创建一个工作表
            for (String tableName : tableNames) {
                createSheet(workbook, conn, tableName);
            }

            // 保存Excel文件
            try (FileOutputStream fileOut = new FileOutputStream(excelPath)) {
                workbook.write(fileOut);
                log.info("成功导出Excel文件: {}", excelPath);
                return true;
            }

        } catch (Exception e) {
            log.error("导出Excel失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取数据库连接
     */
    private static Connection getConnection(String dbPath) throws SQLException {
        try {
            Class.forName("net.ucanaccess.jdbc.UcanaccessDriver");
            String url = "jdbc:ucanaccess://" + dbPath;
            return DriverManager.getConnection(url);
        } catch (ClassNotFoundException e) {
            throw new SQLException("UCanAccess驱动程序未找到", e);
        }
    }

    /**
     * 获取数据库中所有表名
     */
    private static List<String> getTableNames(Connection conn) throws SQLException {
        List<String> tableNames = new ArrayList<>();
        DatabaseMetaData metaData = conn.getMetaData();
        ResultSet rs = metaData.getTables(null, null, "%", new String[]{"TABLE"});
        
        while (rs.next()) {
            String tableName = rs.getString("TABLE_NAME");
            // 排除系统表
            if (!tableName.startsWith("MSys")) {
                tableNames.add(tableName);
            }
        }
        return tableNames;
    }

    /**
     * 为指定表创建工作表并填充数据
     */
    private static void createSheet(Workbook workbook, Connection conn, String tableName) throws SQLException {
        Sheet sheet = workbook.createSheet(tableName);
        
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT * FROM " + tableName)) {

            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            // 创建标题行样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            for (int i = 1; i <= columnCount; i++) {
                Cell cell = headerRow.createCell(i - 1);
                cell.setCellValue(metaData.getColumnName(i));
                cell.setCellStyle(headerStyle);
            }

            // 填充数据
            int rowNum = 1;
            while (rs.next()) {
                Row row = sheet.createRow(rowNum++);
                for (int i = 1; i <= columnCount; i++) {
                    Cell cell = row.createCell(i - 1);
                    Object value = rs.getObject(i);
                    if (value != null) {
                        if (value instanceof Number) {
                            cell.setCellValue(((Number) value).doubleValue());
                        } else if (value instanceof java.util.Date) {
                            cell.setCellValue((java.util.Date) value);
                        } else if (value instanceof Boolean) {
                            cell.setCellValue((Boolean) value);
                        } else {
                            cell.setCellValue(value.toString());
                        }
                    }
                }
            }

            // 自动调整列宽
            for (int i = 0; i < columnCount; i++) {
                sheet.autoSizeColumn(i);
            }
        }
    }
}
