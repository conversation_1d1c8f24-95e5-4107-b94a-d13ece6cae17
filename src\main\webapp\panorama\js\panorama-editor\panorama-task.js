/**
 * 全景图热点编辑系统 - 任务管理模块
 * 
 * <AUTHOR>
 * @date 2025-06-10
 * @description 处理任务的创建、编辑、删除、选择等功能
 * @requires panorama-core.js, panorama-ui.js
 */

/**
 * 全景图编辑器任务管理模块
 */
var PanoramaTask = (function() {
    'use strict';
    
    // ==================== 任务列表管理 ====================
    
    /**
     * 加载任务列表
     * @param {Function} callback 回调函数
     */
    function loadTaskList(callback) {
        // 获取当前用户名，开发阶段默认为adm
        var username = PanoramaCore.getCurrentUsername();
        if (!username) {
            if (typeof layui !== 'undefined' && layui.layer) {
                layui.layer.msg('用户未登录，请重新登录', { icon: 2 });
            }
            return;
        }

        var $ = layui.$;
        $.get('/panorama/task/list?username=' + encodeURIComponent(username), function (res) {
            if (res.success) {
                var options = '<option value="">请选择任务</option>';
                if (res.data && res.data.length > 0) {
                    for (var i = 0; i < res.data.length; i++) {
                        var task = res.data[i];
                        var statusText = PanoramaCore.getStatusText(task.STATUS);
                        options += '<option value="' + task.TASK_ID + '">' +
                            task.TASK_NAME + ' (' + statusText + ')</option>';
                    }
                }
                
                var taskSelect = document.getElementById('taskSelect');
                if (taskSelect) {
                    taskSelect.innerHTML = options;
                    layui.form.render('select');
                }

                // 如果有回调函数，执行它
                if (typeof callback === 'function') {
                    callback();
                }
            } else {
                if (typeof layui !== 'undefined' && layui.layer) {
                    layui.layer.msg('加载任务列表失败: ' + res.msg, { icon: 2 });
                }
            }
        }).fail(function () {
            if (typeof layui !== 'undefined' && layui.layer) {
                layui.layer.msg('网络请求失败', { icon: 2 });
            }
        });
    }
    
    /**
     * 选择任务
     * @param {string} taskId 任务ID
     */
    function selectTask(taskId) {
        PanoramaCore.setCurrentTaskId(taskId);

        // 隐藏任务选择蒙版
        PanoramaUI.hideTaskSelectionMask();

        // 加载任务详情
        var $ = layui.$;
        $.get('/panorama/task/' + taskId, function (res) {
            if (res.success) {
                updateTaskInfo(res.data);
                PanoramaUI.enableUploadButtons();
                
                // 加载相关数据
                if (typeof PanoramaDevice !== 'undefined' && PanoramaDevice.loadDeviceList) {
                    PanoramaDevice.loadDeviceList();
                }
                if (typeof PanoramaHotspot !== 'undefined' && PanoramaHotspot.loadHotspotData) {
                    PanoramaHotspot.loadHotspotData();
                }
                if (typeof PanoramaPreview !== 'undefined' && PanoramaPreview.loadPreview) {
                    PanoramaPreview.loadPreview();
                }
                
                // 导出按钮状态会在updateTaskInfo中通过updateExportButtonState更新
            } else {
                if (typeof layui !== 'undefined' && layui.layer) {
                    layui.layer.msg('加载任务详情失败: ' + res.msg, { icon: 2 });
                }
            }
        });
    }
    
    /**
     * 更新任务信息显示
     * @param {Object} taskData 任务数据
     */
    function updateTaskInfo(taskData) {
        var taskName = document.getElementById('taskName');
        var modelInfo = document.getElementById('modelInfo');
        var taskDescription = document.getElementById('taskDescription');
        var createTime = document.getElementById('createTime');
        var taskStatus = document.getElementById('taskStatus');
        var zipStatus = document.getElementById('zipStatus');
        var excelStatus = document.getElementById('excelStatus');
        
        if (taskName) {
            taskName.textContent = taskData.TASK_NAME || '-';
        }

        // 合并型号信息显示
        if (modelInfo) {
            var modelInfoText = taskData.MODEL_NAME || '-';
            modelInfo.textContent = modelInfoText;
        }

        if (taskDescription) {
            taskDescription.textContent = taskData.DESCRIPTION || '-';
        }
        
        if (createTime) {
            createTime.textContent = PanoramaCore.formatTimestamp(taskData.CREATE_TIME);
        }
        
        if (taskStatus) {
            taskStatus.textContent = PanoramaCore.getStatusText(taskData.STATUS);
        }

        // 更新上传状态
        if (zipStatus) {
            zipStatus.textContent = taskData.ZIP_FILE_PATH ? '已上传' : '未上传';
        }
        
        if (excelStatus) {
            excelStatus.textContent = '未知'; // 需要额外查询
        }

        // 使用统一的导出按钮管理函数
        PanoramaUI.updateExportButtonState();
    }
    
    /**
     * 清空任务信息
     */
    function clearTaskInfo() {
        PanoramaCore.setCurrentTaskId(null);
        PanoramaCore.setCurrentNodeId(null);
        
        var taskName = document.getElementById('taskName');
        var modelInfo = document.getElementById('modelInfo');
        var taskDescription = document.getElementById('taskDescription');
        var createTime = document.getElementById('createTime');
        var taskStatus = document.getElementById('taskStatus');
        var zipStatus = document.getElementById('zipStatus');
        var excelStatus = document.getElementById('excelStatus');
        var currentNodeInfo = document.getElementById('currentNodeInfo');
        var currentNodeId = document.getElementById('currentNodeId');
        
        if (taskName) taskName.textContent = '未选择任务';
        if (modelInfo) modelInfo.textContent = '-';
        if (taskDescription) taskDescription.textContent = '-';
        if (createTime) createTime.textContent = '-';
        if (taskStatus) taskStatus.textContent = '-';
        if (zipStatus) zipStatus.textContent = '未上传';
        if (excelStatus) excelStatus.textContent = '未上传';

        // 隐藏节点信息
        if (currentNodeInfo) currentNodeInfo.style.display = 'none';
        if (currentNodeId) currentNodeId.textContent = '-';

        PanoramaUI.disableUploadButtons();
        PanoramaUI.updateExportButtonState();

        // 清空表格
        var hotspotTable = PanoramaCore.getHotspotTable();
        if (hotspotTable) {
            hotspotTable.reload({
                data: []
            });
        }

        // 清空预览
        if (typeof PanoramaPreview !== 'undefined' && PanoramaPreview.hidePreview) {
            PanoramaPreview.hidePreview();
        }

        // 显示任务选择蒙版
        PanoramaUI.showTaskSelectionMask();
    }
    
    // ==================== 任务创建 ====================
    
    /**
     * 显示创建任务对话框
     */
    function showCreateTaskDialog() {
        // 检查layer是否可用
        if (typeof layui === 'undefined' || !layui.layer) {
            alert('Layer组件未加载，请检查Layui是否正确引入');
            return;
        }

        // 尝试直接使用HTML内容而不是jQuery对象
        var createTaskDialog = document.getElementById('createTaskDialog');
        if (!createTaskDialog) {
            layui.layer.msg('创建任务对话框模板未找到', { icon: 2 });
            return;
        }
        
        var dialogHtml = createTaskDialog.innerHTML;

        layui.layer.open({
            type: 1,
            title: '创建新任务',
            content: dialogHtml,
            area: ['500px', '400px'],
            btn: ['创建', '取消'],
            btnAlign: 'c',
            yes: function (index, layero) {
                // 防止重复点击
                var createBtn = layero.find('.layui-layer-btn0');
                if (createBtn.hasClass('layui-btn-disabled')) {
                    return false;
                }

                // 手动获取表单数据并提交
                var selectedModelName = layero.find('select[name="modelName"]').val();
                var selectedModelId = layero.find('input[name="modelId"]').val();

                var formData = {
                    taskName: layero.find('input[name="taskName"]').val().trim(),
                    modelId: selectedModelId,
                    modelName: selectedModelName,
                    description: layero.find('textarea[name="description"]').val()
                };

                // 验证必填字段
                if (!formData.taskName) {
                    layui.layer.msg('请输入任务名称', { icon: 2 });
                    return false;
                }

                if (!selectedModelName) {
                    layui.layer.msg('请选择型号', { icon: 2 });
                    return false;
                }

                // 禁用创建按钮，防止重复点击
                createBtn.addClass('layui-btn-disabled').text('创建中...');

                // 创建任务，传递按钮引用用于恢复状态
                createTaskWithButton(formData, createBtn);
                return false; // 阻止默认关闭
            },
            success: function (layero) {
                // 加载型号列表
                loadModelList(function (modelList) {
                    var modelSelect = layero.find('select[name="modelName"]');
                    var options = '<option value="">请选择型号</option>';

                    if (modelList && modelList.length > 0) {
                        for (var i = 0; i < modelList.length; i++) {
                            var model = modelList[i];
                            options += '<option value="' + model.MODEL_NAME + '" data-model-id="' + model.MODEL_ID + '">' + model.MODEL_NAME + '</option>';
                        }
                    }

                    modelSelect.html(options);

                    // 监听型号选择变化，自动设置型号ID
                    layui.form.on('select(modelName)', function (data) {
                        var selectedOption = modelSelect.find('option:selected');
                        var modelId = selectedOption.attr('data-model-id');
                        layero.find('input[name="modelId"]').val(modelId || '');
                    });

                    // 重新渲染表单
                    layui.form.render();
                });
            }
        });
    }
    
    /**
     * 加载型号列表
     * @param {Function} callback 回调函数
     */
    function loadModelList(callback) {
        var $ = layui.$;
        $.get('/panorama/model/list', function (res) {
            if (res.success && res.data) {
                callback(res.data);
            } else {
                layui.layer.msg('加载型号列表失败: ' + (res.msg || '未知错误'), { icon: 2 });
                callback([]);
            }
        }).fail(function () {
            layui.layer.msg('网络请求失败', { icon: 2 });
            callback([]);
        });
    }
    
    /**
     * 创建任务（带按钮状态管理）
     * @param {Object} formData 表单数据
     * @param {Object} createBtn 创建按钮jQuery对象
     */
    function createTaskWithButton(formData, createBtn) {
        // 获取当前用户名并添加到表单数据中，开发阶段默认为adm
        var username = PanoramaCore.getCurrentUsername();
        if (!username) {
            layui.layer.msg('用户未登录，请重新登录', { icon: 2 });
            if (createBtn) {
                createBtn.removeClass('layui-btn-disabled').text('创建');
            }
            return;
        }

        // 添加用户名到表单数据
        formData.username = username;

        // 显示loading效果
        var loadingIndex = layui.layer.load(1, {
            shade: [0.3, '#000']
        });

        var $ = layui.$;
        $.post('/panorama/task/create', formData, function (res) {
            // 关闭loading
            layui.layer.close(loadingIndex);

            if (res.success) {
                layui.layer.closeAll();
                layui.layer.msg('任务创建成功', { icon: 1 });

                // 先加载任务列表，然后自动选择新创建的任务
                loadTaskList(function () {
                    // 任务列表加载完成后，自动选择新创建的任务
                    setTimeout(function () {
                        var taskSelect = document.getElementById('taskSelect');
                        if (taskSelect) {
                            taskSelect.value = res.data.taskId;
                            layui.form.render('select');
                            selectTask(res.data.taskId);
                        }
                    }, 200);
                });
            } else {
                // 恢复按钮状态
                if (createBtn) {
                    createBtn.removeClass('layui-btn-disabled').text('创建');
                }
                layui.layer.msg('创建失败: ' + res.msg, { icon: 2 });
            }
        }).fail(function () {
            // 关闭loading并恢复按钮状态
            layui.layer.close(loadingIndex);
            if (createBtn) {
                createBtn.removeClass('layui-btn-disabled').text('创建');
            }
            layui.layer.msg('网络请求失败', { icon: 2 });
        });
    }
    
    /**
     * 创建任务（兼容旧版本调用）
     * @param {Object} formData 表单数据
     */
    function createTask(formData) {
        createTaskWithButton(formData, null);
    }
    
    // ==================== 任务管理对话框 ====================

    /**
     * 显示任务管理对话框
     */
    function showTaskManagementDialog() {
        var dialogHtml =
            '<div id="taskManagementContainer" style="padding: 0; width: 100%;">' +
                '<div style="padding: 15px; border-bottom: 1px solid #e6e8eb; background: #fafbfc;">' +
                    '<div style="display: flex; justify-content: space-between; align-items: center;">' +
                        '<h3 style="margin: 0; font-size: 16px; color: #333;">' +
                            '<i class="layui-icon layui-icon-table" style="color: #667eea;"></i> 任务管理' +
                        '</h3>' +
                    '</div>' +
                '</div>' +
                '<div style="height: 539px; overflow: hidden; width: 100%;">' +
                    '<table style="width: 100%;" class="layui-hide" id="taskManagementTable" lay-filter="taskManagementTable"></table>' +
                '</div>' +
            '</div>';

        layui.layer.open({
            type: 1,
            title: false,
            content: dialogHtml,
            area: ['1020px', '600px'],
            maxmin: false,
            success: function() {
                // 初始化任务管理表格
                initTaskManagementTable();
            }
        });
    }

    /**
     * 初始化任务管理表格
     */
    function initTaskManagementTable() {
        // 获取当前用户名，开发阶段默认为adm
        var username = PanoramaCore.getCurrentUsername();
        if (!username) {
            layui.layer.msg('用户未登录，请重新登录', { icon: 2 });
            return;
        }

        layui.table.render({
            elem: '#taskManagementTable',
            url: '/panorama/task/management/list?username=' + encodeURIComponent(username),
            page: true,
            limit: 10,
            limits: [10, 20, 50],
            height: 547,
            cols: [[
                { type: 'numbers', title: '序号', width: 40 },
                { field: 'TASK_NAME', title: '任务名称', width: 170 },
                { field: 'MODEL_NAME', title: '所属型号', width: 80 },
                { field: 'STATUS_TEXT', title: '任务状态', width: 80, align: 'center', templet: '#taskStatusTpl' },
                { field: 'DESCRIPTION', title: '任务描述', width: 190, templet: '#taskDescriptionTpl' },
                { field: 'CREATE_TIME', title: '创建时间', width: 150 },
                { title: '操作', width: 210, toolbar: '#taskManagementTableBar', fixed: 'right', align: 'center' }
            ]],
            done: function() {
                // 表格渲染完成回调
            }
        });

        // 监听表格工具条
        layui.table.on('tool(taskManagementTable)', function(obj) {
            var data = obj.data;
            if (obj.event === 'export') {
                exportTaskFromManagement(data.TASK_ID);
            } else if (obj.event === 'edit') {
                editTaskInManagement(data);
            } else if (obj.event === 'delete') {
                deleteTaskWithWarning(data);
            }
        });
    }

    /**
     * 从任务管理中导出任务
     * @param {string} taskId 任务ID
     */
    function exportTaskFromManagement(taskId) {
        // 检查任务状态
        var $ = layui.$;
        $.get('/panorama/task/' + taskId, function(res) {
            if (!res.success) {
                layui.layer.msg('获取任务信息失败，请重试', { icon: 2 });
                return;
            }

            var taskData = res.data;
            var hasZipFile = taskData.ZIP_FILE_PATH && taskData.ZIP_FILE_PATH.trim() !== '';
            var hasExtractPath = taskData.EXTRACT_PATH && taskData.EXTRACT_PATH.trim() !== '';

            if (!hasZipFile) {
                layui.layer.msg('该任务尚未上传全景图ZIP文件，无法导出', { icon: 2 });
                return;
            }

            if (!hasExtractPath) {
                layui.layer.msg('全景图文件正在处理中，请稍后再试', { icon: 2 });
                return;
            }

            // 执行导出
            var loadingIndex = layer.msg('正在导出，请稍候...', { icon: 16, shade: 0.01 });

            if (typeof $.fileDownload === 'function') {
                $.fileDownload('/panorama/export', {
                    httpMethod: 'POST',
                    data: { taskId: taskId },
                    successCallback: function() {
                        layui.layer.close(loadingIndex);
                        layui.layer.msg('导出成功', { icon: 1 });
                    },
                    failCallback: function() {
                        layui.layer.close(loadingIndex);
                        layui.layer.msg('导出失败，请重试', { icon: 2 });
                    }
                });
            } else {
                // 备用方案
                layui.layer.close(loadingIndex);
                layui.layer.msg('导出功能不可用', { icon: 2 });
            }
        }).fail(function() {
            layui.layer.msg('网络请求失败，请检查网络连接', { icon: 2 });
        });
    }

    /**
     * 删除任务（带强烈风险提示）
     */
    function deleteTaskWithWarning(taskData) {
        var taskId = taskData.TASK_ID;
        var taskName = taskData.TASK_NAME;

        // 第一层：获取删除统计信息并显示警告对话框
        var $ = layui.$;
        $.get('/panorama/task/' + taskId + '/delete-stats', function(res) {
            if (!res.success) {
                layui.layer.msg('获取任务信息失败，请重试', { icon: 2 });
                return;
            }

            var stats = res.data;
            var warningContent =
                '<div style="padding: 20px; text-align: center;">' +
                    '<div style="color: #ff4757; font-size: 48px; margin-bottom: 15px;">' +
                        '<i class="layui-icon layui-icon-close-fill"></i>' +
                    '</div>' +
                    '<div style="color: #ff4757; font-size: 18px; font-weight: bold; margin-bottom: 15px;">⚠️ 危险操作警告</div>' +
                    '<div style="color: #333; font-size: 14px; line-height: 1.6; margin-bottom: 20px;">' +
                        '删除任务 <strong style="color: #ff4757;">"' + taskName + '"</strong> 将永久删除以下数据，且<strong style="color: #ff4757;">无法恢复</strong>：' +
                    '</div>' +
                    '<div style="text-align: left; background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">' +
                        '<div style="margin-bottom: 8px;">• 任务基本信息</div>' +
                        '<div style="margin-bottom: 8px;">• 所有热点数据（<strong>' + stats.hotspotCount + '</strong> 个）</div>' +
                        '<div style="margin-bottom: 8px;">• 所有设备数据（<strong>' + stats.deviceCount + '</strong> 个）</div>' +
                        '<div>• 全景图文件和解压目录' + (stats.hasFiles ? '（<strong style="color: #ff4757;">存在文件</strong>）' : '') + '</div>' +
                    '</div>' +
                    '<div style="color: #666; font-size: 12px;">此操作不可撤销，请谨慎操作！</div>' +
                '</div>';

            layui.layer.confirm(warningContent, {
                icon: 0,
                title: '删除确认 - 第1步',
                area: ['500px', 'auto'],
                btn: ['我了解风险，继续删除', '取消操作'],
                btn1: function(index) {
                    layui.layer.close(index);
                    // 第二层：要求输入任务名称确认
                    showTaskNameConfirmation(taskId, taskName, stats);
                }
            });
        }).fail(function() {
            layui.layer.msg('网络请求失败，请重试', { icon: 2 });
        });
    }

    /**
     * 显示任务名称确认对话框（第二层确认）
     */
    function showTaskNameConfirmation(taskId, taskName, stats) {
        var confirmContent =
            '<div style="padding: 20px;">' +
                '<div style="color: #ff4757; font-size: 16px; font-weight: bold; margin-bottom: 15px; text-align: center;">' +
                    '二次确认' +
                '</div>' +
                '<div style="color: #333; margin-bottom: 15px; line-height: 1.6;">' +
                    '为了防止误操作，请在下方输入框中输入任务名称：' +
                '</div>' +
                '<div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px; text-align: center; font-weight: bold; color: #ff4757;">' +
                    taskName +
                '</div>' +
                '<input type="text" id="confirmTaskName" placeholder="请输入任务名称" class="layui-input" style="margin-bottom: 10px;">' +
                '<div style="color: #999; font-size: 12px;">' +
                    '输入完全匹配的任务名称后，删除按钮才会启用' +
                '</div>' +
            '</div>';

        var confirmIndex = layui.layer.open({
            type: 1,
            title: '删除确认 - 第2步',
            area: ['450px', 'auto'],
            content: confirmContent,
            btn: ['删除任务', '取消'],
            btn1: function(index, layero) {
                var $ = layui.$;
                var inputName = $('#confirmTaskName').val().trim();
                if (inputName !== taskName) {
                    layui.layer.msg('任务名称输入不正确，请重新输入', { icon: 2 });
                    return false;
                }

                layui.layer.close(index);
                // 第三层：最终确认
                showFinalConfirmation(taskId, taskName, stats);
                return false;
            },
            success: function(layero, index) {
                var $ = layui.$;
                // 监听输入框变化，控制按钮状态
                $('#confirmTaskName').on('input', function() {
                    var inputName = $(this).val().trim();
                    var deleteBtn = layero.find('.layui-layer-btn0');

                    if (inputName === taskName) {
                        deleteBtn.removeClass('layui-btn-disabled').css({
                            'background-color': '#ff4757',
                            'border-color': '#ff4757'
                        });
                    } else {
                        deleteBtn.addClass('layui-btn-disabled').css({
                            'background-color': '#ccc',
                            'border-color': '#ccc'
                        });
                    }
                });

                // 初始状态禁用删除按钮
                layero.find('.layui-layer-btn0').addClass('layui-btn-disabled').css({
                    'background-color': '#ccc',
                    'border-color': '#ccc'
                });
            }
        });
    }

    /**
     * 显示最终确认对话框（第三层确认）
     */
    function showFinalConfirmation(taskId, taskName, stats) {
        var finalContent =
            '<div style="padding: 20px; text-align: center;">' +
                '<div style="color: #ff4757; font-size: 24px; margin-bottom: 15px;">' +
                    '<i class="layui-icon layui-icon-face-cry"></i>' +
                '</div>' +
                '<div style="color: #ff4757; font-size: 16px; font-weight: bold; margin-bottom: 15px;">' +
                    '最后确认' +
                '</div>' +
                '<div style="color: #333; margin-bottom: 20px; line-height: 1.6;">' +
                    '您即将删除任务 <strong style="color: #ff4757;">"' + taskName + '"</strong><br>' +
                    '包括 <strong>' + stats.hotspotCount + '</strong> 个热点和 <strong>' + stats.deviceCount + '</strong> 个设备数据<br>' +
                    '<strong style="color: #ff4757;">此操作无法撤销！</strong>' +
                '</div>' +
                '<div style="color: #666; font-size: 12px;">' +
                    '点击"确认删除"将立即执行删除操作' +
                '</div>' +
            '</div>';

        layui.layer.confirm(finalContent, {
            icon: 0,
            title: '删除确认 - 最后一步',
            area: ['400px', 'auto'],
            btn: ['确认删除', '我再想想'],
            btn1: function(index) {
                layui.layer.close(index);
                // 执行删除操作
                executeTaskDeletion(taskId, taskName);
            }
        });
    }

    /**
     * 执行任务删除操作
     */
    function executeTaskDeletion(taskId, taskName) {
        var loadingIndex = layer.msg('正在删除任务，请稍候...', { icon: 16, shade: 0.01 });

        var $ = layui.$;
        $.ajax({
            url: '/panorama/task/' + taskId,
            type: 'DELETE',
            success: function(res) {
                layui.layer.close(loadingIndex);

                if (res.success) {
                    layui.layer.msg('任务删除成功', { icon: 1 });

                    // 刷新任务管理表格
                    layui.table.reload('taskManagementTable');

                    // 如果删除的是当前选中的任务，清空当前任务状态
                    var currentTaskId = PanoramaCore.getCurrentTaskId();
                    if (currentTaskId == taskId) {
                        clearTaskInfo();
                        loadTaskList(); // 重新加载任务列表
                    } else {
                        // 只更新任务选择下拉框
                        loadTaskList();
                    }
                } else {
                    layui.layer.msg('删除失败: ' + res.msg, { icon: 2 });
                }
            },
            error: function() {
                layui.layer.close(loadingIndex);
                layui.layer.msg('网络请求失败，请重试', { icon: 2 });
            }
        });
    }

    /**
     * 在任务管理中编辑任务
     * @param {Object} taskData 任务数据
     */
    function editTaskInManagement(taskData) {
        // 参考创建任务的写法，使用.html()获取内容
        var editTaskDialog = document.getElementById('editTaskDialog');
        if (!editTaskDialog) {
            layui.layer.msg('编辑任务对话框模板未找到', { icon: 2 });
            return;
        }

        var dialogHtml = editTaskDialog.innerHTML;

        var editIndex = layui.layer.open({
            type: 1,
            title: '编辑任务',
            content: dialogHtml,
            area: ['500px', '350px'],
            btn: ['保存', '取消'],
            btnAlign: 'c',
            success: function(layero, index) {
                // 在对话框成功打开后填充数据
                layero.find('input[name="taskId"]').val(taskData.TASK_ID);
                layero.find('input[name="taskName"]').val(taskData.TASK_NAME || '');
                layero.find('textarea[name="description"]').val(taskData.DESCRIPTION || '');
            },
            yes: function(index, layero) {
                // 防止重复点击
                var saveBtn = layero.find('.layui-layer-btn0');
                if (saveBtn.hasClass('layui-btn-disabled')) {
                    return false;
                }

                // 获取表单数据
                var formData = {
                    taskId: layero.find('input[name="taskId"]').val(),
                    taskName: layero.find('input[name="taskName"]').val().trim(),
                    description: layero.find('textarea[name="description"]').val().trim()
                };

                // 验证任务名称
                if (!formData.taskName) {
                    layui.layer.msg('请输入任务名称', { icon: 2 });
                    return false;
                }

                // 禁用按钮防止重复提交
                saveBtn.addClass('layui-btn-disabled').text('保存中...');

                // 提交更新
                var $ = layui.$;
                $.ajax({
                    url: '/panorama/task/update',
                    type: 'PUT',
                    data: formData,
                    success: function(res) {
                        if (res.success) {
                            layui.layer.close(editIndex);
                            layui.layer.msg('任务信息更新成功', { icon: 1 });

                            // 刷新任务管理表格
                            layui.table.reload('taskManagementTable');

                            // 如果当前选中的是被编辑的任务，更新任务选择下拉框
                            var currentTaskId = PanoramaCore.getCurrentTaskId();
                            if (currentTaskId == formData.taskId) {
                                loadTaskList();
                            }
                        } else {
                            // 恢复按钮状态
                            saveBtn.removeClass('layui-btn-disabled').text('保存');
                            layui.layer.msg('更新失败: ' + res.msg, { icon: 2 });
                        }
                    },
                    error: function() {
                        // 恢复按钮状态
                        saveBtn.removeClass('layui-btn-disabled').text('保存');
                        layui.layer.msg('网络请求失败，请重试', { icon: 2 });
                    }
                });

                return false; // 阻止默认的关闭行为
            }
        });
    }

    // ==================== 公共API ====================
    return {
        // 任务列表管理
        loadTaskList: loadTaskList,
        selectTask: selectTask,
        updateTaskInfo: updateTaskInfo,
        clearTaskInfo: clearTaskInfo,

        // 任务创建
        showCreateTaskDialog: showCreateTaskDialog,
        createTask: createTask,
        createTaskWithButton: createTaskWithButton,

        // 任务管理
        showTaskManagementDialog: showTaskManagementDialog,
        initTaskManagementTable: initTaskManagementTable,
        exportTaskFromManagement: exportTaskFromManagement,
        editTaskInManagement: editTaskInManagement,
        deleteTaskWithWarning: deleteTaskWithWarning,
        showTaskNameConfirmation: showTaskNameConfirmation,
        showFinalConfirmation: showFinalConfirmation,
        executeTaskDeletion: executeTaskDeletion,

        // 内部函数
        loadModelList: loadModelList
    };
})();

// ==================== 向后兼容性支持 ====================
// 保持原有函数的全局访问方式

window.loadTaskList = PanoramaTask.loadTaskList;
window.selectTask = PanoramaTask.selectTask;
window.updateTaskInfo = PanoramaTask.updateTaskInfo;
window.clearTaskInfo = PanoramaTask.clearTaskInfo;
window.showCreateTaskDialog = PanoramaTask.showCreateTaskDialog;
window.createTask = PanoramaTask.createTask;
window.createTaskWithButton = PanoramaTask.createTaskWithButton;
window.loadModelList = PanoramaTask.loadModelList;
window.showTaskManagementDialog = PanoramaTask.showTaskManagementDialog;
window.deleteTaskWithWarning = PanoramaTask.deleteTaskWithWarning;
window.showTaskNameConfirmation = PanoramaTask.showTaskNameConfirmation;
window.showFinalConfirmation = PanoramaTask.showFinalConfirmation;
window.executeTaskDeletion = PanoramaTask.executeTaskDeletion;
