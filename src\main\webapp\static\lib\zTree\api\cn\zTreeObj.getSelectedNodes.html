<div class="apiDetail">
<div>
	<h2><span>Function()</span><span class="path">zTreeObj.</span>getSelectedNodes</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.core</span> 核心 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>获取 zTree 当前被选中的节点数据集合</p>
			<p class="highlight_red">请通过 zTree 对象执行此方法。</p>
		</div>
	</div>
	<h3>Function 参数说明</h3>
	<div class="desc">
	<h4><b>返回值</b><span>Array(JSON)</span></h4>
	<p>当前被选中的节点数据集合</p>
	</div>
	<h3>function 举例</h3>
	<h4>1. 获取当前被选中的节点数据集合</h4>
	<pre xmlns=""><code>var treeObj = $.fn.zTree.getZTreeObj("tree");
var nodes = treeObj.getSelectedNodes();
</code></pre>
</div>
</div>