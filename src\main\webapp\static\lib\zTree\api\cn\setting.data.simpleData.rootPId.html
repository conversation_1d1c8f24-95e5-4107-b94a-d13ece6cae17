<div class="apiDetail">
<div>
	<h2><span>String / Number</span><span class="path">setting.data.simpleData.</span>rootPId</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.core</span> 核心 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>用于修正根节点父节点数据，即 pIdKey 指定的属性值。<span class="highlight_red">[setting.data.simpleData.enable = true 时生效]</span></p>
			<p>默认值：null</p>
		</div>
	</div>
	<h3>setting 举例</h3>
	<h4>1. 使用简单 Array 格式的数据</h4>
	<pre xmlns=""><code>var setting = {
	data: {
		simpleData: {
			enable: true,
			idKey: "id",
			pIdKey: "pId",
			rootPId: 0
		}
	}
};
var treeNodes = [
    {"id":1, "pId":0, "name":"test1"},
    {"id":11, "pId":1, "name":"test11"},
    {"id":12, "pId":1, "name":"test12"},
    {"id":111, "pId":11, "name":"test111"}
];
......</code></pre>
</div>
</div>