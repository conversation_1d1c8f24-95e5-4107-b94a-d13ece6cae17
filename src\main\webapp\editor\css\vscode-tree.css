/* 重置样式，确保图标正确显示 */
.ztree li span.button {
    background-image: none !important;
}

/* 确保图标不被其他样式覆盖 */
.ztree li span.button.switch {
    background-image: url('vscode-icons/icons/chevron-right.svg') !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: 16px 16px !important;
}

/* 文件夹展开/折叠图标 */
.ztree li span.button.noline_open {
    background-image: url('vscode-icons/icons/chevron-down.svg') !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: 16px 16px !important;
}

.ztree li span.button.noline_close {
    background-image: url('vscode-icons/icons/chevron-right.svg') !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: 16px 16px !important;
}

/* 展开状态 */
.ztree li span.button.root_open,
.ztree li span.button.roots_open,
.ztree li span.button.center_open,
.ztree li span.button.bottom_open {
    background-image: url('vscode-icons/icons/chevron-down.svg') !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: 16px 16px !important;
}

/* 折叠状态 */
.ztree li span.button.root_close,
.ztree li span.button.roots_close,
.ztree li span.button.center_close,
.ztree li span.button.bottom_close {
    background-image: url('vscode-icons/icons/chevron-right.svg') !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: 16px 16px !important;
}

/* 隐藏文件节点前的展开图标 */
.ztree li span.button.switch.noline_docu,
.ztree li span.button.switch.roots_docu,
.ztree li span.button.switch.center_docu,
.ztree li span.button.switch.bottom_docu,
.ztree li span.button.switch.root_docu {
    background-image: none !important;
    width: 8px !important;
    visibility: hidden;
}

/* 确保所有按钮都有正确的尺寸 */
.ztree li span.button {
    width: 16px !important;
    height: 16px !important;
    margin-right: 5px;
    display: inline-block !important;
    vertical-align: middle;
    border: 0 !important;
    cursor: pointer;
    background-color: transparent !important;
    padding: 0 !important;
    line-height: normal !important;
}

/* VSCode风格的文件树样式 */

/* 基本样式 */
.ztree * {
    font-size: 13px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.ztree {
    padding: 5px;
    background-color: #252526;
    color: #cccccc;
}

/* 文件树容器 */
.file-explorer-content {
    background-color: #252526;
}

/* 移除连线 */
.ztree li ul.line {
    background: none;
}

/* 列表项样式 */
.ztree li {
    padding: 0;
    margin: 0;
    list-style: none;
    line-height: 24px;
    text-align: left;
    white-space: nowrap;
    outline: 0;
}

/* 链接样式 */
.ztree li a {
    padding: 1px 3px 0 0;
    margin: 0;
    cursor: pointer;
    height: 24px;
    color: #cccccc;
    background-color: transparent;
    text-decoration: none;
    vertical-align: top;
    display: inline-block;
}

/* 悬停效果 */
.ztree li a:hover {
    background-color: #2a2d2e;
    color: #ffffff;
}

/* 选中节点样式 */
.ztree li a.curSelectedNode {
    padding-top: 0px;
    background-color: #37373d;
    color: #ffffff;
    height: 24px;
    opacity: 1;
}

/* 文件夹图标 - 关闭状态 */
.ztree li span.button.ico_close {
    background-image: url('vscode-icons/icons/default_folder.svg') !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: 16px 16px !important;
    display: inline-block !important;
    vertical-align: middle;
    border: 0 !important;
    cursor: pointer;
    background-color: transparent !important;
    padding: 0 !important;
}

/* 文件夹图标 - 打开状态 */
.ztree li span.button.ico_open {
    background-image: url('vscode-icons/icons/default_folder_opened.svg') !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: 16px 16px !important;
    display: inline-block !important;
    vertical-align: middle;
    border: 0 !important;
    cursor: pointer;
    background-color: transparent !important;
    padding: 0 !important;
}

/* 通用文件图标 */
.ztree li span.button.ico_docu {
    background-image: url('vscode-icons/icons/default_file.svg') !important;
    width: 16px !important;
    height: 16px !important;
    margin-right: 5px;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-size: 16px 16px !important;
    display: inline-block !important;
    vertical-align: middle;
    border: 0 !important;
    cursor: pointer;
    background-color: transparent !important;
    padding: 0 !important;
    line-height: normal !important;
}

/* HTML文件 */
.ztree li a[title$=".html"] span.button.ico_docu {
    background-image: url('vscode-icons/icons/file_type_html.svg') !important;
}

/* CSS文件 */
.ztree li a[title$=".css"] span.button.ico_docu {
    background-image: url('vscode-icons/icons/file_type_css.svg') !important;
}

/* JavaScript文件 */
.ztree li a[title$=".js"] span.button.ico_docu {
    background-image: url('vscode-icons/icons/file_type_js.svg') !important;
}

/* Java文件 */
.ztree li a[title$=".java"] span.button.ico_docu {
    background-image: url('vscode-icons/icons/file_type_java.svg') !important;
}

/* XML文件 */
.ztree li a[title$=".xml"] span.button.ico_docu {
    background-image: url('vscode-icons/icons/file_type_xml.svg') !important;
}

/* JSON文件 */
.ztree li a[title$=".json"] span.button.ico_docu {
    background-image: url('vscode-icons/icons/file_type_json.svg') !important;
}

/* Python文件 */
.ztree li a[title$=".py"] span.button.ico_docu {
    background-image: url('vscode-icons/icons/file_type_python.svg') !important;
}

/* C/C++文件 */
.ztree li a[title$=".c"] span.button.ico_docu,
.ztree li a[title$=".cpp"] span.button.ico_docu,
.ztree li a[title$=".h"] span.button.ico_docu {
    background-image: url('vscode-icons/icons/file_type_c.svg') !important;
}

/* PHP文件 */
.ztree li a[title$=".php"] span.button.ico_docu {
    background-image: url('vscode-icons/icons/file_type_php.svg') !important;
}

/* SQL文件 */
.ztree li a[title$=".sql"] span.button.ico_docu {
    background-image: url('vscode-icons/icons/file_type_sql.svg') !important;
}

/* Markdown文件 */
.ztree li a[title$=".md"] span.button.ico_docu {
    background-image: url('vscode-icons/icons/file_type_markdown.svg') !important;
}

/* 图片文件 */
.ztree li a[title$=".jpg"] span.button.ico_docu,
.ztree li a[title$=".jpeg"] span.button.ico_docu,
.ztree li a[title$=".png"] span.button.ico_docu,
.ztree li a[title$=".gif"] span.button.ico_docu,
.ztree li a[title$=".svg"] span.button.ico_docu {
    background-image: url('vscode-icons/icons/file_type_image.svg') !important;
}

/* PDF文件 */
.ztree li a[title$=".pdf"] span.button.ico_docu {
    background-image: url('vscode-icons/icons/file_type_pdf.svg') !important;
}

/* 子节点列表样式 */
.ztree li ul {
    margin: 0;
    padding: 0 0 0 18px;
}

/* 根节点图标 */
.ztree li span.button.roots_docu,
.ztree li span.button.center_docu,
.ztree li span.button.bottom_docu {
    background: none;
} 