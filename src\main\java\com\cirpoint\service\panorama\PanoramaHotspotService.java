package com.cirpoint.service.panorama;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 全景图热点管理服务
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@Slf4j
@Service
public class PanoramaHotspotService {

    @Autowired
    private PanoramaXmlService panoramaXmlService;

    /**
     * 检查热点标题是否重复
     */
    public JSONObject checkHotspotTitle(Long taskId, String title, Long hotspotId) {
        try {
            // 构建SQL查询，检查同一任务下是否有相同的标题
            String checkSql = "SELECT COUNT(*) AS cnt, " +
                    "LISTAGG(HOTSPOT_ID, ',') WITHIN GROUP (ORDER BY HOTSPOT_ID) AS hotspot_ids " +
                    "FROM PANORAMA_HOTSPOT " +
                    "WHERE TASK_ID = " + taskId + " " +
                    "AND TITLE = '" + title.replace("'", "''") + "' ";

            // 如果是编辑现有热点，排除自己
            if (hotspotId != null) {
                checkSql += "AND HOTSPOT_ID != " + hotspotId;
            }

            JSONArray checkResult = Util.postQuerySql(checkSql);

            if (checkResult != null && checkResult.size() > 0) {
                Object firstRowObj = checkResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    JSONObject firstRow = (JSONObject) firstRowObj;
                    int count = firstRow.getInt("CNT");
                    String hotspotIds = firstRow.getStr("HOTSPOT_IDS");

                    return JSONUtil.createObj()
                            .set("success", true)
                            .set("isDuplicate", count > 0)
                            .set("count", count)
                            .set("conflictHotspotIds", hotspotIds);
                }
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("isDuplicate", false)
                    .set("count", 0);
        } catch (Exception e) {
            log.error("检查热点标题失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "检查热点标题失败: " + e.getMessage());
        }
    }

    /**
     * 解析XML并保存热点信息
     */
    public void parseAndSaveHotspots(Long taskId, List<Element> panoramas) throws Exception {
        for (Element panorama : panoramas) {
            // 获取panorama节点的id属性，用于多节点支持
            String panoramaId = panorama.attributeValue("id");
            if (panoramaId == null || panoramaId.trim().isEmpty()) {
                // 如果没有id属性，使用默认值
                panoramaId = "node1";
                log.warn("Panorama节点没有id属性，使用默认值: {}", panoramaId);
            }

            Element hotspotsElement = panorama.element("hotspots");
            if (hotspotsElement != null) {
                List<Element> hotspots = hotspotsElement.elements("hotspot");

                for (Element hotspot : hotspots) {
                    String skinid = hotspot.attributeValue("skinid");
                    // 只处理stand-alone类型的热点
                    if ("stand-alone".equals(skinid)) {
                        // 传递panoramaId参数到保存方法
                        saveHotspotToDatabase(taskId, panoramaId, hotspot);
                    }
                }
            }
        }
    }

    /**
     * 保存热点信息到数据库
     * @param taskId 任务ID
     * @param panoramaId 所属全景节点ID
     * @param hotspot 热点XML元素
     */
    public void saveHotspotToDatabase(Long taskId, String panoramaId, Element hotspot) {
        try {
            String hotspotXmlId = hotspot.attributeValue("id");
            String originalTitle = hotspot.attributeValue("title");
            String originalDescription = hotspot.attributeValue("description");
            String pan = hotspot.attributeValue("pan");
            String tilt = hotspot.attributeValue("tilt");
            String skinid = hotspot.attributeValue("skinid");
            String url = hotspot.attributeValue("url");
            String target = hotspot.attributeValue("target");

            // 构建包含PANORAMA_ID字段的INSERT语句
            String insertSql = "INSERT INTO PANORAMA_HOTSPOT " +
                    "(HOTSPOT_ID, TASK_ID, PANORAMA_ID, HOTSPOT_XML_ID, TITLE, DESCRIPTION, PAN, TILT, SKINID, URL, TARGET, IS_EDITED, CREATE_TIME, UPDATE_TIME) " +
                    "VALUES (SEQ_PANORAMA_HOTSPOT.NEXTVAL, " + taskId + ", " +
                    (panoramaId != null ? "'" + panoramaId + "'" : "NULL") + ", " +
                    (hotspotXmlId != null ? "'" + hotspotXmlId + "'" : "NULL") + ", " +
                    (originalTitle != null ? "'" + originalTitle.replace("'", "''") + "'" : "NULL") + ", " +
                    (originalDescription != null ? "'" + originalDescription.replace("'", "''") + "'" : "NULL") + ", " +
                    (pan != null ? "'" + pan + "'" : "NULL") + ", " +
                    (tilt != null ? "'" + tilt + "'" : "NULL") + ", " +
                    (skinid != null ? "'" + skinid + "'" : "NULL") + ", " +
                    (url != null ? "'" + url + "'" : "NULL") + ", " +
                    (target != null ? "'" + target + "'" : "NULL") + ", " +
                    "0, SYSDATE, SYSDATE)";

            Util.postCommandSql(insertSql);
        } catch (Exception e) {
            log.error("保存热点信息失败", e);
        }
    }

    /**
     * 获取热点列表
     * @param taskId 任务ID
     * @param panoramaId 全景节点ID（可选，用于多节点支持）
     * @param page 页码
     * @param limit 每页数量
     * @param titleKeyword 标题关键词（可选，用于筛选）
     * @param descriptionKeyword 描述关键词（可选，用于筛选）
     * @return 热点列表
     */
    public JSONObject getHotspotList(Long taskId, String panoramaId, Integer page, Integer limit,
                                   String titleKeyword, String descriptionKeyword) {
        try {
            int offset = (page - 1) * limit;

            // 构建WHERE条件，支持panoramaId和关键词过滤
            StringBuilder whereCondition = new StringBuilder("WHERE h.TASK_ID = " + taskId);
            if (panoramaId != null && !panoramaId.trim().isEmpty()) {
                whereCondition.append(" AND h.PANORAMA_ID = '").append(panoramaId.replace("'", "''")).append("'");
            }

            // 添加标题关键词筛选
            if (titleKeyword != null && !titleKeyword.trim().isEmpty()) {
                whereCondition.append(" AND h.TITLE LIKE '%").append(titleKeyword.replace("'", "''")).append("%'");
            }

            // 添加描述关键词筛选
            if (descriptionKeyword != null && !descriptionKeyword.trim().isEmpty()) {
                whereCondition.append(" AND h.DESCRIPTION LIKE '%").append(descriptionKeyword.replace("'", "''")).append("%'");
            }

            // 查询总数
            String countSql = "SELECT COUNT(*) AS TOTAL FROM PANORAMA_HOTSPOT h " + whereCondition.toString();
            JSONArray countResult = Util.postQuerySql(countSql);
            int total = 0;
            if (countResult != null && countResult.size() > 0) {
                Object firstRowObj = countResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    total = ((JSONObject) firstRowObj).getInt("TOTAL");
                }
            }

            // 查询分页数据，包含PANORAMA_ID字段
            String dataSql = "SELECT * FROM (" +
                    "SELECT h.HOTSPOT_ID, h.TASK_ID, h.PANORAMA_ID, h.HOTSPOT_XML_ID, " +
                    "h.TITLE, h.DESCRIPTION, " +
                    "h.DEVICE_ID, h.PAN, h.TILT, h.SKINID, h.URL, h.TARGET, h.IS_EDITED, " +
                    "h.CREATE_TIME, h.UPDATE_TIME, d.DEVICE_NAME, d.MODEL_NAME, " +
                    "ROW_NUMBER() OVER (ORDER BY h.CREATE_TIME) AS RN " +
                    "FROM PANORAMA_HOTSPOT h " +
                    "LEFT JOIN PANORAMA_DEVICE d ON h.DEVICE_ID = d.DEVICE_ID " +
                    whereCondition.toString() + ") " +
                    "WHERE RN > " + offset + " AND RN <= " + (offset + limit);

            JSONArray dataResult = Util.postQuerySql(dataSql);

            return JSONUtil.createObj()
                    .set("code", 0)
                    .set("msg", "")
                    .set("data", dataResult)
                    .set("count", total);
        } catch (Exception e) {
            log.error("获取热点列表失败: taskId={}, panoramaId={}", taskId, panoramaId, e);
            return JSONUtil.createObj()
                    .set("code", 1)
                    .set("msg", "获取热点列表失败: " + e.getMessage())
                    .set("data", new JSONArray())
                    .set("count", 0);
        }
    }

    /**
     * 获取热点列表（向后兼容性重载方法）
     * @param taskId 任务ID
     * @param panoramaId 全景节点ID（可选，用于多节点支持）
     * @param page 页码
     * @param limit 每页数量
     * @return 热点列表
     */
    public JSONObject getHotspotList(Long taskId, String panoramaId, Integer page, Integer limit) {
        return getHotspotList(taskId, panoramaId, page, limit, null, null);
    }

    /**
     * 添加新热点
     */
    public JSONObject addHotspot(Long taskId, String nodeId, String pan, String tilt,
                                String title, String description, Long deviceId) {
        try {
            // 生成唯一的热点XML ID
            String hotspotXmlId = generateHotspotXmlId();

            // 插入热点到数据库
            String insertSql = "INSERT INTO PANORAMA_HOTSPOT " +
                    "(HOTSPOT_ID, TASK_ID, PANORAMA_ID, HOTSPOT_XML_ID, TITLE, DESCRIPTION, PAN, TILT, SKINID, URL, TARGET, IS_EDITED, CREATE_TIME, UPDATE_TIME) " +
                    "VALUES (SEQ_PANORAMA_HOTSPOT.NEXTVAL, " + taskId + ", " +
                    "'" + nodeId.replace("'", "''") + "', " +
                    "'" + hotspotXmlId + "', " +
                    "'" + title.replace("'", "''") + "', " +
                    (description != null ? "'" + description.replace("'", "''") + "'" : "NULL") + ", " +
                    "'" + pan + "', " +
                    "'" + tilt + "', " +
                    "'stand-alone', " +
                    "'', " +
                    "'', " +
                    "1, SYSDATE, SYSDATE)";

            int result = Util.postCommandSql(insertSql);
            if (result <= 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "数据库插入失败");
            }

            // 获取任务的解压路径
            String querySql = "SELECT EXTRACT_PATH FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray queryResult = Util.postQuerySql(querySql);

            if (queryResult == null || queryResult.size() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到任务信息");
            }

            JSONObject taskInfo = (JSONObject) queryResult.get(0);
            String extractPath = taskInfo.getStr("EXTRACT_PATH");

            // 更新XML文件
            panoramaXmlService.addHotspotToXml(extractPath, nodeId, hotspotXmlId, pan, tilt, title, description);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "热点添加成功")
                    .set("data", JSONUtil.createObj().set("hotspotXmlId", hotspotXmlId));

        } catch (Exception e) {
            log.error("添加热点失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "添加热点失败: " + e.getMessage());
        }
    }

    /**
     * 更新热点信息
     */
    public JSONObject updateHotspot(Long hotspotId, String editedTitle, String editedDescription, Long deviceId) {
        try {
            // 首先获取热点信息，包括任务ID、定位信息（pan、tilt）和标题
            String querySql = "SELECT h.TASK_ID, h.HOTSPOT_XML_ID, h.TITLE, h.DESCRIPTION, " +
                    "h.PAN, h.TILT, t.EXTRACT_PATH FROM PANORAMA_HOTSPOT h " +
                    "JOIN PANORAMA_TASK t ON h.TASK_ID = t.TASK_ID " +
                    "WHERE h.HOTSPOT_ID = " + hotspotId;
            JSONArray queryResult = Util.postQuerySql(querySql);

            if (queryResult == null || queryResult.size() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到热点信息");
            }

            JSONObject hotspotInfo = (JSONObject) queryResult.get(0);
            Long taskId = hotspotInfo.getLong("TASK_ID");
            String hotspotXmlId = hotspotInfo.getStr("HOTSPOT_XML_ID");
            String currentTitle = hotspotInfo.getStr("TITLE");
            String pan = hotspotInfo.getStr("PAN");
            String tilt = hotspotInfo.getStr("TILT");
            String extractPath = hotspotInfo.getStr("EXTRACT_PATH");

            // 更新数据库
            StringBuilder updateSql = new StringBuilder("UPDATE PANORAMA_HOTSPOT SET ");
            boolean hasUpdate = false;

            if (editedTitle != null) {
                updateSql.append("TITLE = '").append(editedTitle.replace("'", "''")).append("'");
                hasUpdate = true;
            }

            if (editedDescription != null) {
                if (hasUpdate) updateSql.append(", ");
                updateSql.append("DESCRIPTION = '").append(editedDescription.replace("'", "''")).append("'");
                hasUpdate = true;
            }

            if (deviceId != null) {
                if (hasUpdate) updateSql.append(", ");
                updateSql.append("DEVICE_ID = ").append(deviceId);
                hasUpdate = true;
            }

            if (hasUpdate) {
                updateSql.append(", IS_EDITED = 1, UPDATE_TIME = SYSDATE");
                updateSql.append(" WHERE HOTSPOT_ID = ").append(hotspotId);

                Util.postCommandSql(updateSql.toString());

                // 更新XML文件中的热点信息，使用定位信息（pan、tilt）进行精确匹配
                if (extractPath != null && pan != null && tilt != null) {
                    panoramaXmlService.updateHotspotInXml(extractPath, pan, tilt, editedTitle, editedDescription);
                }
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "热点信息更新成功");
        } catch (Exception e) {
            log.error("更新热点信息失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "更新热点信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除热点
     */
    public JSONObject deleteHotspot(Long hotspotId) {
        try {
            // 首先获取热点信息，包括任务ID、节点ID、XML ID和坐标信息
            String querySql = "SELECT h.TASK_ID, h.PANORAMA_ID, h.HOTSPOT_XML_ID, h.TITLE, h.DESCRIPTION, " +
                    "h.PAN, h.TILT, t.EXTRACT_PATH FROM PANORAMA_HOTSPOT h " +
                    "JOIN PANORAMA_TASK t ON h.TASK_ID = t.TASK_ID " +
                    "WHERE h.HOTSPOT_ID = " + hotspotId;
            JSONArray queryResult = Util.postQuerySql(querySql);

            if (queryResult == null || queryResult.size() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到热点信息");
            }

            JSONObject hotspotInfo = (JSONObject) queryResult.get(0);
            Long taskId = hotspotInfo.getLong("TASK_ID");
            String nodeId = hotspotInfo.getStr("PANORAMA_ID");
            String hotspotXmlId = hotspotInfo.getStr("HOTSPOT_XML_ID");
            String title = hotspotInfo.getStr("TITLE");
            String pan = hotspotInfo.getStr("PAN");
            String tilt = hotspotInfo.getStr("TILT");
            String extractPath = hotspotInfo.getStr("EXTRACT_PATH");

            // 删除数据库记录
            String deleteSql = "DELETE FROM PANORAMA_HOTSPOT WHERE HOTSPOT_ID = " + hotspotId;
            int result = Util.postCommandSql(deleteSql);
            if (result <= 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "数据库删除失败");
            }

            // 从XML文件中删除热点
            if (extractPath != null && pan != null && tilt != null) {
                panoramaXmlService.removeHotspotFromXml(extractPath, nodeId, pan, tilt, hotspotXmlId);
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "热点删除成功")
                    .set("data", JSONUtil.createObj()
                            .set("hotspotId", hotspotId)
                            .set("hotspotXmlId", hotspotXmlId)
                            .set("nodeId", nodeId)
                            .set("pan", pan)
                            .set("tilt", tilt));

        } catch (Exception e) {
            log.error("删除热点失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "删除热点失败: " + e.getMessage());
        }
    }

    /**
     * 热点定位
     */
    public JSONObject locateHotspot(Long hotspotId) {
        try {
            String sql = "SELECT PAN, TILT FROM PANORAMA_HOTSPOT WHERE HOTSPOT_ID = " + hotspotId;
            JSONArray result = Util.postQuerySql(sql);

            if (result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "热点不存在");
            }

            // 安全获取热点数据
            Object firstRowObj = result.get(0);
            JSONObject hotspot = null;
            if (firstRowObj instanceof JSONObject) {
                hotspot = (JSONObject) firstRowObj;
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", hotspot);
        } catch (Exception e) {
            log.error("热点定位失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "热点定位失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除热点
     */
    public JSONObject batchDeleteHotspots(List<Long> hotspotIds) {
        try {
            List<JSONObject> successList = new ArrayList<>();
            List<JSONObject> failureList = new ArrayList<>();

            for (Long hotspotId : hotspotIds) {
                try {
                    // 复用单个删除的逻辑
                    JSONObject result = deleteHotspot(hotspotId);
                    if (result.getBool("success", false)) {
                        successList.add(JSONUtil.createObj()
                                .set("hotspotId", hotspotId)
                                .set("data", result.getJSONObject("data")));
                    } else {
                        failureList.add(JSONUtil.createObj()
                                .set("hotspotId", hotspotId)
                                .set("error", result.getStr("msg")));
                    }
                } catch (Exception e) {
                    log.error("删除热点失败: hotspotId={}", hotspotId, e);
                    failureList.add(JSONUtil.createObj()
                            .set("hotspotId", hotspotId)
                            .set("error", "删除失败: " + e.getMessage()));
                }
            }

            int totalCount = hotspotIds.size();
            int successCount = successList.size();
            int failureCount = failureList.size();

            JSONObject result = JSONUtil.createObj()
                    .set("success", failureCount == 0) // 全部成功才算成功
                    .set("totalCount", totalCount)
                    .set("successCount", successCount)
                    .set("failureCount", failureCount)
                    .set("successList", successList)
                    .set("failureList", failureList);

            if (failureCount == 0) {
                result.set("msg", "批量删除成功，共删除 " + successCount + " 个热点");
            } else if (successCount == 0) {
                result.set("msg", "批量删除失败，" + failureCount + " 个热点删除失败");
            } else {
                result.set("msg", "批量删除部分成功，成功 " + successCount + " 个，失败 " + failureCount + " 个");
            }

            return result;

        } catch (Exception e) {
            log.error("批量删除热点失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "批量删除热点失败: " + e.getMessage());
        }
    }

    /**
     * 根据热点信息查找热点记录
     * 优先使用坐标匹配，确保准确性
     */
    public JSONObject findHotspotByInfo(Long taskId, String nodeId, String hotspotId,
                                       String title, String description, String skinid) {
        try {

            // 构建基础WHERE条件
            StringBuilder whereCondition = new StringBuilder("WHERE h.TASK_ID = " + taskId);

            // 添加节点ID过滤（如果提供）
            if (nodeId != null && !nodeId.trim().isEmpty()) {
                whereCondition.append(" AND h.PANORAMA_ID = '").append(nodeId.replace("'", "''")).append("'");
            }

            // 构建多重匹配条件（按优先级排序）
            StringBuilder matchConditions = new StringBuilder();

            // 优先级1: 使用标题匹配（最可靠，因为标题在节点内通常是唯一的）
            if (title != null && !title.trim().isEmpty()) {
                matchConditions.append("h.TITLE = '").append(title.replace("'", "''")).append("'");
            }

            // 优先级2: 使用XML ID匹配（结合节点ID）
            if (hotspotId != null && !hotspotId.trim().isEmpty()) {
                if (matchConditions.length() > 0) {
                    matchConditions.append(" OR ");
                }
                matchConditions.append("h.HOTSPOT_XML_ID = '").append(hotspotId.replace("'", "''")).append("'");
            }

            // 优先级3: 使用皮肤ID匹配（结合其他条件）
            if (skinid != null && !skinid.trim().isEmpty()) {
                if (matchConditions.length() > 0) {
                    matchConditions.append(" OR ");
                }
                matchConditions.append("h.SKINID = '").append(skinid.replace("'", "''")).append("'");
            }

            // 如果没有任何匹配条件，返回错误
            if (matchConditions.length() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "缺少有效的热点标识信息");
            }

            // 构建完整的查询SQL
            String sql = "SELECT h.HOTSPOT_ID, h.TASK_ID, h.PANORAMA_ID, h.HOTSPOT_XML_ID, " +
                        "h.TITLE, h.DESCRIPTION, " +
                        "h.DEVICE_ID, h.PAN, h.TILT, h.SKINID, h.URL, h.TARGET, h.IS_EDITED, " +
                        "h.CREATE_TIME, h.UPDATE_TIME, d.DEVICE_NAME, d.MODEL_NAME " +
                        "FROM PANORAMA_HOTSPOT h " +
                        "LEFT JOIN PANORAMA_DEVICE d ON h.DEVICE_ID = d.DEVICE_ID " +
                        whereCondition.toString() + " AND (" + matchConditions.toString() + ") " +
                        "ORDER BY " +
                        "CASE " +
                        "  WHEN h.TITLE = '" + (title != null ? title.replace("'", "''") : "") + "' THEN 1 " +
                        "  WHEN h.HOTSPOT_XML_ID = '" + (hotspotId != null ? hotspotId.replace("'", "''") : "") + "' THEN 2 " +
                        "  ELSE 3 " +
                        "END";

            JSONArray result = Util.postQuerySql(sql);

            if (result == null || result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到匹配的热点记录");
            }

            // 返回第一个匹配的记录（优先级最高的）
            Object firstRowObj = result.get(0);
            JSONObject hotspotData = null;
            if (firstRowObj instanceof JSONObject) {
                hotspotData = (JSONObject) firstRowObj;
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", hotspotData)
                    .set("msg", "查找热点成功");

        } catch (Exception e) {
            log.error("查找热点记录失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "查找热点记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据坐标查找热点记录（最可靠的匹配方式）
     */
    public JSONObject findHotspotByCoordinates(Long taskId, String nodeId, String pan, String tilt) {
        try {

            // 构建基础WHERE条件
            StringBuilder whereCondition = new StringBuilder("WHERE h.TASK_ID = " + taskId);

            // 添加节点ID过滤（如果提供）
            if (nodeId != null && !nodeId.trim().isEmpty()) {
                whereCondition.append(" AND h.PANORAMA_ID = '").append(nodeId.replace("'", "''")).append("'");
            }

            // 添加坐标匹配条件（精确匹配）
            if (pan != null && tilt != null) {
                whereCondition.append(" AND h.PAN = '").append(pan.replace("'", "''")).append("'");
                whereCondition.append(" AND h.TILT = '").append(tilt.replace("'", "''")).append("'");
            } else {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "缺少坐标信息");
            }

            // 构建完整的查询SQL
            String sql = "SELECT h.HOTSPOT_ID, h.TASK_ID, h.PANORAMA_ID, h.HOTSPOT_XML_ID, " +
                        "h.TITLE, h.DESCRIPTION, " +
                        "h.DEVICE_ID, h.PAN, h.TILT, h.SKINID, h.URL, h.TARGET, h.IS_EDITED, " +
                        "h.CREATE_TIME, h.UPDATE_TIME, d.DEVICE_NAME, d.MODEL_NAME " +
                        "FROM PANORAMA_HOTSPOT h " +
                        "LEFT JOIN PANORAMA_DEVICE d ON h.DEVICE_ID = d.DEVICE_ID " +
                        whereCondition.toString();

            JSONArray result = Util.postQuerySql(sql);

            if (result == null || result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到匹配的热点记录");
            }

            // 返回匹配的记录
            Object firstRowObj = result.get(0);
            JSONObject hotspotData = null;
            if (firstRowObj instanceof JSONObject) {
                hotspotData = (JSONObject) firstRowObj;
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", hotspotData)
                    .set("msg", "查找热点成功");

        } catch (Exception e) {
            log.error("根据坐标查找热点记录失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "根据坐标查找热点记录失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一的热点XML ID
     */
    public String generateHotspotXmlId() {
        long timestamp = System.currentTimeMillis();
        String randomStr = Long.toString(Math.abs(new Random().nextLong()), 36).substring(0, 3);
        return "hs_" + timestamp + "_" + randomStr;
    }
}
