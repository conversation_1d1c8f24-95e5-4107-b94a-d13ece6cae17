package com.cirpoint.model.worktime;

import lombok.Data;
import java.time.LocalDate;

/**
 * 工时统计结果数据模型
 * 用于表示计算后的员工工时统计结果
 */
@Data
public class WorkTimeResult {
    
    /**
     * 序号（从1开始）
     */
    private Integer sequence;
    
    /**
     * 员工姓名
     */
    private String employeeName;
    
    /**
     * 工作日期
     */
    private LocalDate workDate;
    
    /**
     * 当日净工时（小时，保留2位小数）
     */
    private Double dailyHours;
    
    /**
     * 员工平均工时（小时，保留2位小数）
     */
    private Double averageHours;
    
    /**
     * 构造函数
     */
    public WorkTimeResult() {
    }
    
    /**
     * 构造函数
     * @param employeeName 员工姓名
     * @param workDate 工作日期
     * @param dailyHours 当日净工时
     * @param averageHours 平均工时
     */
    public WorkTimeResult(String employeeName, LocalDate workDate, Double dailyHours, Double averageHours) {
        this.employeeName = employeeName;
        this.workDate = workDate;
        this.dailyHours = dailyHours;
        this.averageHours = averageHours;
    }
    
    /**
     * 获取工作日期字符串（YYYY-MM-DD格式）
     * @return 日期字符串
     */
    public String getWorkDateString() {
        return workDate != null ? workDate.toString() : "";
    }
    
    /**
     * 获取格式化的当日净工时（保留2位小数）
     * @return 格式化的工时字符串
     */
    public String getFormattedDailyHours() {
        return dailyHours != null ? String.format("%.2f", dailyHours) : "0.00";
    }
    
    /**
     * 获取格式化的平均工时（保留2位小数）
     * @return 格式化的平均工时字符串
     */
    public String getFormattedAverageHours() {
        return averageHours != null ? String.format("%.2f", averageHours) : "0.00";
    }
    
    /**
     * 验证结果数据的有效性
     * @return 验证结果
     */
    public boolean isValid() {
        return employeeName != null && !employeeName.trim().isEmpty()
                && workDate != null
                && dailyHours != null && dailyHours >= 0
                && averageHours != null && averageHours >= 0;
    }
    
    /**
     * 获取用于拼音排序的键值
     * 格式：员工姓名 + 日期
     * @return 排序键
     */
    public String getSortKey() {
        return employeeName + "|" + getWorkDateString();
    }
    
    @Override
    public String toString() {
        return String.format("WorkTimeResult{序号=%d, 姓名='%s', 日期=%s, 净时长=%.2f, 平均时长=%.2f}", 
                sequence, employeeName, workDate, dailyHours, averageHours);
    }
}
