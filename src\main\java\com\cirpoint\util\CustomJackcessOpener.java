package com.cirpoint.util;

import com.healthmarketscience.jackcess.Database;
import com.healthmarketscience.jackcess.DatabaseBuilder;

import java.io.File;
import java.io.IOException;

/**
 * 自定义Access数据库打开器
 * 用于处理Access数据库文件的访问
 */
public class CustomJackcessOpener {
    
    public Database open(File file, String pwd) throws IOException {
        DatabaseBuilder dbd = new DatabaseBuilder(file);
        // 设置读写模式
        dbd.setReadOnly(false);
        // 打开数据库
        return dbd.open();
    }
}
