package com.cirpoint.service.panorama;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.service.ApplicationConfig;
import com.cirpoint.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * 全景图预览服务
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@Slf4j
@Service
public class PanoramaPreviewService extends ApplicationConfig {

    /**
     * 获取全景图预览路径
     */
    public JSONObject getPreviewPath(Long taskId) {
        try {
            String sql = "SELECT EXTRACT_PATH FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray result = Util.postQuerySql(sql);

            if (result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            // 安全获取提取路径
            String extractPath = null;
            if (result.size() > 0) {
                Object firstRowObj = result.get(0);
                if (firstRowObj instanceof JSONObject) {
                    extractPath = ((JSONObject) firstRowObj).getStr("EXTRACT_PATH");
                }
            }
            if (extractPath == null) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "尚未上传全景图文件");
            }

            // 查找index.html文件
            File indexFile = new File(extractPath + File.separator + "index.html");
            if (!indexFile.exists()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到index.html文件");
            }

            // 返回相对于webapps的路径
            String relativePath = extractPath.replace(fileUploadPath, "");
            String previewUrl = "/file/preview" + relativePath + "/index.html";

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", JSONUtil.createObj().set("previewUrl", previewUrl));
        } catch (Exception e) {
            log.error("获取预览路径失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取预览路径失败: " + e.getMessage());
        }
    }
}
