package com.cirpoint.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;

/**
 * 文件下载工具类
 */
@Slf4j
public class FileDownloadUtil {

	public static ResponseEntity<?> fileResponse(File file, String fileName) {
		return handleFileDownload(file, fileName, null);
	}


	public static ResponseEntity<?> fileResponse(File file) {
		return fileResponse(file, file.getName());
	}

	public static ResponseEntity<?> fileResponse(String filePath, String fileName) {
		return fileResponse(new File(filePath), fileName);
	}

	public static ResponseEntity<?> fileResponse(String filePath) {
		return fileResponse(new File(filePath));
	}

	public static ResponseEntity<?> fileResponseAndDelete(File file, String fileName) {
		return handleFileDownload(file, fileName, () -> FileUtil.del(file));
	}


	public static ResponseEntity<?> fileResponseAndDelete(File file) {
		return fileResponseAndDelete(file, file.getName());
	}

	public static ResponseEntity<?> fileResponseAndDelete(String filePath, String fileName) {
		return fileResponseAndDelete(new File(filePath), fileName);
	}

	public static ResponseEntity<?> fileResponseAndDelete(String filePath) {
		return fileResponseAndDelete(new File(filePath));
	}

	/**
	 * 处理文件名中的密级
	 *
	 * @param fileName 原文件名
	 * @return 处理后的文件名
	 */
	private static String processSecurityLevel(String fileName) {
		if (StrUtil.isEmpty(fileName)) {
			return "（内部）";
		}

		// 定义密级列表，按照优先级排序（从低到高）
		String[] securityLevels = {"公开", "内部", "秘密", "机密"};

		// 先清理文件名中多余的括号
		String cleanFileName = fileName.replaceAll("[)）]+$", ""); // 移除末尾多余的括号

		// 移除文件名中所有的密级标记
		String nameWithoutSecurity = cleanFileName;
		String foundLevel = null;

		// 匹配所有可能的密级标记（支持中英文括号）
		Pattern pattern = Pattern.compile("[(（](" + String.join("|", securityLevels) + ")[)）]");
		Matcher matcher = pattern.matcher(cleanFileName);

		while (matcher.find()) {
			// 记录找到的最后一个密级
			foundLevel = matcher.group(1);
			// 移除所有密级标记
			nameWithoutSecurity = nameWithoutSecurity.replace(matcher.group(), "");
		}

		// 清理文件名中可能残留的括号
		nameWithoutSecurity = nameWithoutSecurity
				.replaceAll("[(（]+", "")  // 移除所有左括号
				.replaceAll("[)）]+", "")  // 移除所有右括号
				.trim();

		// 如果没有找到密级，使用默认密级（内部）
		if (foundLevel == null) {
			foundLevel = "内部";
		}

		// 获取文件扩展名（如果有）
		int dotIndex = nameWithoutSecurity.lastIndexOf(".");
		if (dotIndex > -1) {
			String name = nameWithoutSecurity.substring(0, dotIndex).trim();
			String extension = nameWithoutSecurity.substring(dotIndex);
			return name + "（" + foundLevel + "）" + extension;
		} else {
			return nameWithoutSecurity.trim() + "（" + foundLevel + "）";
		}
	}

	/**
	 * 处理文件下载并清理临时文件
	 *
	 * @param file            要下载的文件
	 * @param fileName        下载时显示的文件名（为空则使用原文件名）
	 * @param cleanupFunction 清理函数，用于清理临时文件（可选）
	 * @return 包含文件内容的响应实体
	 * @throws IllegalArgumentException 当文件不存在或读取失败时抛出
	 */
	public static ResponseEntity<?> handleFileDownload(File file, String fileName, Runnable cleanupFunction) {
		try {
			// 参数校验
			if (file == null || !file.exists()) {
				throw new IllegalArgumentException("文件不存在：" +
						(file == null ? "null" : file.getPath()));
			}

			// 处理文件名
			String downloadFileName = fileName;
			if (StrUtil.isEmpty(downloadFileName)) {
				downloadFileName = file.getName();
			}
			downloadFileName = processSecurityLevel(downloadFileName);
			
			// 处理媒体类型
			String contentType = Files.probeContentType(file.toPath());
			MediaType finalMediaType = contentType != null ?
					MediaType.parseMediaType(contentType) :
					MediaType.APPLICATION_OCTET_STREAM;

			// 设置响应头
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(finalMediaType);

			// 处理文件名编码
			try {
				String encodedFileName = URLEncoder.encode(downloadFileName, "UTF-8")
						.replaceAll("\\+", "%20");
				headers.set(HttpHeaders.CONTENT_DISPOSITION,
						"attachment; filename*=UTF-8''" + encodedFileName);
			} catch (UnsupportedEncodingException e) {
				// 降级使用 ISO-8859-1 编码
				headers.setContentDispositionFormData("attachment",
						new String(downloadFileName.getBytes(StandardCharsets.UTF_8),
								StandardCharsets.ISO_8859_1));
			}

			// 设置其他响应头
			headers.add("Set-Cookie", "fileDownload=true; path=/");
			headers.setCacheControl(CacheControl.noStore().mustRevalidate());
			headers.setPragma("no-cache");
			headers.setExpires(0L);

			// 使用流式处理替代一次性加载整个文件
			Resource fileResource = new FileSystemResource(file);
			
			// 检查文件大小
			long fileSize = file.length();
			if (fileSize > Integer.MAX_VALUE) {
				log.warn("文件过大（{}字节），使用流式下载: {}", fileSize, file.getPath());
			}
			
			// 添加文件大小信息到响应头
			headers.setContentLength(fileSize);
			
			// 注册清理函数，在响应完成后执行
			if (cleanupFunction != null) {
				// 使用异步方式清理，避免阻塞响应
				new Thread(() -> {
					try {
						// 等待一段时间确保响应已完成
						Thread.sleep(5000);
						cleanupFunction.run();
					} catch (Exception e) {
						log.error("清理临时文件失败", e);
					}
				}).start();
			}
			
			// 返回文件资源而不是字节数组
			return new ResponseEntity<>(fileResource, headers, HttpStatus.OK);

		} catch (IOException e) {
			throw new IllegalArgumentException("读取文件失败：" + e.getMessage(), e);
		}
	}
}
