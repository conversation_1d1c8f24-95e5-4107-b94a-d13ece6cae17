package com.cirpoint.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.io.file.FileReader;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.lang.generator.UUIDGenerator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.cell.CellUtil;
import com.cirpoint.util.CommonUtil;
import com.cirpoint.util.Util;
import com.cirpoint.util.ws.client.UploadFileAxisClientEx;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;
import org.springframework.stereotype.Service;

/**
 * 清单文件服务类
 */
@Slf4j
@Service
public class ListService extends ApplicationConfig {

	/**
	 * 导出数据包
	 *
	 * @param dataPkgIds 数据包ID列表
	 * @param secLevel   密级
	 * @param queryUser  查询用户
	 * @return 导出的文件
	 */
	public File exportDataPackage(String dataPkgIds, String secLevel, String queryUser) {
		String time = System.currentTimeMillis() + "";
		String zipTempFilePath = tempPath + time;
		FileUtil.mkdir(zipTempFilePath);

		// 调用Thing.Fn.ListData服务查询数据
		JSONObject res = Util.postTwxForObject("Thing.Fn.ListData", "QueryDataPkgAndList",
				JSONUtil.createObj()
						.set("dataPkgIds", dataPkgIds)
						.set("secLevel", secLevel)
						.set("queryUser", queryUser));

		if (!res.getBool("success")) {
			throw new RuntimeException("获取数据包信息失败");
		}

		File resultFile;
		try {
			JSONArray dataPkgs = res.getJSONArray("data");
			String highestSecLevelStr = "内部";
			String zipFileName = "";
			String zipFolder = "";

			for (int x = 0; x < dataPkgs.size(); x++) {
				JSONObject dataPkgObj = dataPkgs.getJSONObject(x);
				JSONArray lists = dataPkgObj.getJSONArray("listData");
				JSONObject dataPkg = dataPkgObj.getJSONObject("dataPkg");

				String dataPkgName = dataPkg.getStr("NAME");
				String dataPkgCode = dataPkg.getStr("CODE");
				String dataPkgFolderName = dataPkgCode.substring(0, dataPkgCode.lastIndexOf("-") + 1) + dataPkgName;
				zipFileName = dataPkgFolderName;
				String dataPkgFolder = zipTempFilePath + File.separator + dataPkgFolderName;
				zipFolder = dataPkgFolder;
				FileUtil.mkdir(dataPkgFolder);

				// 处理Excel数据
				processExcelData(lists, dataPkgFolder, dataPkgFolderName);
			}

			// 生成最终的zip文件
			String dataStr = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
			if (dataPkgs.size() != 1) {
				zipFolder = zipTempFilePath;
				zipFileName = "批量下载数据包";
			}
			zipFileName = zipFileName + "_" + dataStr + "（" + highestSecLevelStr + "）" + ".zip";
			String resultFilePath = tempPath + File.separator + zipFileName;
			ZipUtil.zip(zipFolder, resultFilePath, Charset.forName("GBK"), false);
			resultFile = new File(resultFilePath);
		} finally {
			// 清理临时文件
			FileUtil.del(zipTempFilePath);
		}

		return resultFile;
	}

	/**
	 * 处理Excel数据
	 */
	private void processExcelData(JSONArray lists, String dataPkgFolder, String dataPkgFolderName) {
		JSONArray designData = new JSONArray(), craftData = new JSONArray(),
				processData = new JSONArray(), qualityData = new JSONArray();
		int designIndex = 0, craftIndex = 0, processIndex = 0, qualityIndex = 0;
		String excelName = dataPkgFolderName + "清单文件(内部).xlsx";

		for (int i = 0; i < lists.size(); i++) {
			JSONObject list = lists.getJSONObject(i);
			processListItem(list, dataPkgFolder, designData, craftData,
					processData, qualityData, designIndex, craftIndex, processIndex, qualityIndex);
		}

		// 写入Excel文件
		ExcelWriter writer = ExcelUtil.getWriter(dataPkgFolder + File.separator + excelName);
		writeExcelSheet("设计类", designData, writer, 0);
		writeExcelSheet("工艺类", craftData, writer, 1);
		writeExcelSheet("过程控制", processData, writer, 2);
		writeExcelSheet("质量综合", qualityData, writer, 3);
		writer.close();
	}

	/**
	 * 处理单个文件项
	 */
	private void processListItem(JSONObject list, String dataPkgFolder,
								 JSONArray designData, JSONArray craftData, JSONArray processData, JSONArray qualityData,
								 int designIndex, int craftIndex, int processIndex, int qualityIndex) {
		String type = list.getStr("TYPE");
		String filePath = list.getStr("FILEPATH");
		String securityLevelStr = list.getStr("SECURITY_LEVEL");

		// 处理Excel数据
		JSONArray listExcelData = convertListToExcelData(list, getNextIndex(type, designIndex, craftIndex, processIndex, qualityIndex));
		addToTypeData(type, listExcelData, designData, craftData, processData, qualityData);

		// 复制文件
		if (StrUtil.isNotBlank(filePath)) {
			copyFileToDestination(list, dataPkgFolder);
		}

		// 处理特殊类型文件
		processSpecialFiles(list, dataPkgFolder, type, securityLevelStr);
	}

	private int getNextIndex(String type, int designIndex, int craftIndex, int processIndex, int qualityIndex) {
		switch (type) {
			case "设计类":
				return ++designIndex;
			case "工艺类":
				return ++craftIndex;
			case "过程控制":
				return ++processIndex;
			case "质量综合":
				return ++qualityIndex;
			default:
				return 0;
		}
	}

	private void addToTypeData(String type, JSONArray data, JSONArray designData, JSONArray craftData,
							   JSONArray processData, JSONArray qualityData) {
		switch (type) {
			case "设计类":
				designData.add(data);
				break;
			case "工艺类":
				craftData.add(data);
				break;
			case "过程控制":
				processData.add(data);
				break;
			case "质量综合":
				qualityData.add(data);
				break;
		}
	}

	private JSONArray convertListToExcelData(JSONObject list, int index) {
		String fileType = list.getStr("FILE_TYPE");
		String deliveryState = list.getStr("DELIVERY_STATE", "");
		String fileFormat = list.getStr("FILE_FORMAT");
		String fileName = list.getStr("FILE_NAME").replaceAll("/", "，");

		JSONArray excelData = new JSONArray();
		excelData.add(0, index);
		excelData.add(1, fileType);
		excelData.add(2, "");
		excelData.add(3, "");
		excelData.add(4, "");
		excelData.add(5, deliveryState.equals("备查") ? "√" : "");
		excelData.add(6, deliveryState.equals("提交") ? "√" : "");
		excelData.add(7, fileName);
		excelData.add(8, StrUtil.isEmpty(fileFormat) ? "无实体文件！" : fileFormat);
		return excelData;
	}

	private void writeExcelSheet(String type, JSONArray listData, ExcelWriter writer, int sheetIndex) {
		Object[][] sheetHeaderArray = {
				{"序号", "项目", "文档提供责任单位\n或部门", "存放方式", "存放方式", "是否所产品流转", "是否所产品流转", "文档名称", "文档形式"},
				{"", "", "", "是否存档", "存放单位或部门", "备查", "提交", "", ""}
		};

		// 设置表头和数据
		configureExcelWriter(writer, type, sheetIndex, sheetHeaderArray, listData);

		// 设置样式
		applyExcelStyles(writer);
	}

	private void configureExcelWriter(ExcelWriter writer, String type, int sheetIndex,
									  Object[][] headerArray, JSONArray listData) {
		writer.setSheet(sheetIndex);
		writer.renameSheet(type);

		JSONArray data = JSONUtil.parseArray(headerArray);
		data.add(new JSONArray().add(type));
		data.addAll(listData);

		writer.write(data, false);

		// 合并单元格
		mergeCells(writer, type);

		// 设置列宽
		setColumnWidths(writer);
	}

	private void applyExcelStyles(ExcelWriter writer) {
		// 设置行高
		for (int i = 0; i < writer.getRowCount(); i++) {
			writer.setRowHeight(i, 20);
		}

		// 设置标题样式
		CellStyle headerStyle = createHeaderStyle(writer);
		writer.setRowStyleIfHasData(0, headerStyle);
		writer.setRowStyleIfHasData(1, headerStyle);

		// 设置特殊单元格样式
		setSpecialCellStyles(writer);
	}

	private CellStyle createHeaderStyle(ExcelWriter writer) {
		Font font = writer.createFont();
		font.setBold(true);

		CellStyle style = writer.createCellStyle();
		style.setFont(font);
		style.setVerticalAlignment(VerticalAlignment.CENTER);
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setBorderBottom(BorderStyle.THIN);
		style.setBorderLeft(BorderStyle.THIN);
		style.setBorderTop(BorderStyle.THIN);
		style.setBorderRight(BorderStyle.THIN);
		style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

		return style;
	}

	private void setSpecialCellStyles(ExcelWriter writer) {
		for (int i = 3; i < writer.getRowCount(); i++) {
			Cell cell = writer.getCell(8, i);
			String fileFormat = Convert.toStr(CellUtil.getCellValue(cell));
			if ("无实体文件！".equals(fileFormat)) {
				CellStyle redStyle = createRedTextStyle(writer);
				cell.setCellStyle(redStyle);
			}
		}
	}

	private CellStyle createRedTextStyle(ExcelWriter writer) {
		CellStyle style = writer.createCellStyle();
		style.setBorderBottom(BorderStyle.THIN);
		style.setBorderLeft(BorderStyle.THIN);
		style.setBorderTop(BorderStyle.THIN);
		style.setBorderRight(BorderStyle.THIN);
		style.setVerticalAlignment(VerticalAlignment.CENTER);
		style.setAlignment(HorizontalAlignment.CENTER);

		Font redFont = writer.createFont();
		redFont.setColor(Font.COLOR_RED);
		style.setFont(redFont);

		return style;
	}

	private void mergeCells(ExcelWriter writer, String type) {
		writer.merge(0, 1, 0, 0, "序号", false);
		writer.merge(0, 1, 1, 1, "项目", false);
		writer.merge(0, 1, 2, 2, "文档提供责任单位\n或部门", false);
		writer.merge(0, 0, 3, 4, "存放方式", false);
		writer.merge(0, 0, 5, 6, "是否所产品流转", false);
		writer.merge(0, 1, 7, 7, "文档名称", false);
		writer.merge(0, 1, 8, 8, "文档形式", false);
		writer.merge(2, 2, 0, 8, type, false);
	}

	private void setColumnWidths(ExcelWriter writer) {
		writer.setColumnWidth(0, 5);  // 序号
		writer.setColumnWidth(1, 30); // 项目
		writer.setColumnWidth(2, 25); // 文档提供责任单位或部门
		writer.setColumnWidth(3, 15); // 是否存档
		writer.setColumnWidth(4, 15); // 存放单位或部门
		writer.setColumnWidth(5, 10); // 备查
		writer.setColumnWidth(6, 10); // 提交
		writer.setColumnWidth(7, 40); // 文档名称
		writer.setColumnWidth(8, 20); // 文档形式
	}

	/**
	 * 复制文件到目标目录
	 */
	private void copyFileToDestination(JSONObject list, String dataPkgFolder) {
		String filePath = list.getStr("FILEPATH");
		String fileName = list.getStr("FILE_NAME").replaceAll("/", "，");
		String fileFormat = list.getStr("FILE_FORMAT");
		String securityLevelStr = list.getStr("SECURITY_LEVEL");
		String type = list.getStr("TYPE");

		// 类型文件夹
		String typeFolder = dataPkgFolder + File.separator + type;
		FileUtil.mkdir(typeFolder);

		// 源文件
		File sourceFile = new File(fileUploadPath + filePath);
		if (sourceFile.exists()) {
			// 带密级的文件名称
			String secFileName = fileName + "(" + securityLevelStr + ")";
			// 目标文件
			File destFile = new File(typeFolder + File.separator + secFileName + "." + fileFormat);
			FileUtil.copy(sourceFile, destFile, true);
		}
	}

	/**
	 * 处理特殊类型文件
	 */
	private void processSpecialFiles(JSONObject list, String dataPkgFolder,
									 String type, String securityLevelStr) {
		String fileType = list.getStr("FILE_TYPE");
		String gatheringMethod = list.getStr("GATHERING_METHOD");
		String fileName = list.getStr("FILE_NAME").replaceAll("/", "，");

		// 处理跟踪卡的记录表
		if ("跟踪卡".equals(fileType) && "自动采集".equals(gatheringMethod)) {
			processTrackingCard(list, dataPkgFolder, type);
		}

		// 处理通用跟踪卡
		if ("通用跟踪卡".equals(fileType) && "自动采集".equals(gatheringMethod)) {
			processGeneralTrackingCard(list, dataPkgFolder, type, securityLevelStr, fileName);
		}
	}

	/**
	 * 处理跟踪卡
	 */
	private void processTrackingCard(JSONObject list, String dataPkgFolder,
									 String type) {
		int techcardId = list.getInt("ID");
		String tableName = list.getStr("TABLE_NAME");
		String fileName = list.getStr("FILE_NAME").replaceAll("/", "，");

		JSONObject checkCardRes = Util.postTwxForObject("Thing.Fn.ListData", "QueryCheckCard",
				JSONUtil.createObj()
						.set("techcardId", techcardId)
						.set("tableName", tableName));

		if (checkCardRes.getBool("success")) {
			JSONArray checkCards = checkCardRes.getJSONArray("data");
			if (!checkCards.isEmpty()) {
				String techcardFolder = dataPkgFolder + File.separator + type + File.separator + fileName;
				FileUtil.mkdir(techcardFolder);

				for (int j = 0; j < checkCards.size(); j++) {
					JSONObject checkCard = checkCards.getJSONObject(j);
					String checkCardFileName = checkCard.getStr("FILE_NAME").replaceAll("/", "，");
					String checkCardFilePath = checkCard.getStr("FILEPATH");
					String checkCardFormat = checkCard.getStr("FILE_FORMAT");
					String checkCardSecurityLevelStr = checkCard.getStr("SECURITY_LEVEL");

					if (StrUtil.isNotBlank(checkCardFilePath)) {
						File checkCardSourceFile = new File(fileUploadPath + checkCardFilePath);
						if (checkCardSourceFile.exists()) {
							String checkCardSecFileName = checkCardFileName + "(" + checkCardSecurityLevelStr + ")";
							File checkCardDestFile = new File(techcardFolder + File.separator
									+ checkCardSecFileName + "." + checkCardFormat);
							FileUtil.copy(checkCardSourceFile, checkCardDestFile, true);
						}
					}
				}
			}
		}
	}

	/**
	 * 处理通用跟踪卡
	 */
	private void processGeneralTrackingCard(JSONObject list, String dataPkgFolder,
											String type, String securityLevelStr, String fileName) {
		int listId = list.getInt("ID");
		String tableName = list.getStr("TABLE_NAME");
		String typeFolder = dataPkgFolder + File.separator + type;

		JSONArray addendum = Util.postTwx("Thing.Fn.DataSearch", "QueryAddendumNoPage",
				JSONUtil.createObj()
						.set("addendumTableType", tableName)
						.set("addendumResultId", listId));

		for (int j = 0; j < addendum.size(); j++) {
			JSONObject addendumObj = addendum.getJSONObject(j);
			String excelFilePath = addendumObj.getStr("EXCELFILEPATH");
			String excelFileFormat = addendumObj.getStr("EXCELFILEFORMAT");

			File excelSourceFile = new File(fileUploadPath + excelFilePath);
			if (excelSourceFile.exists()) {
				String formName = addendumObj.getStr("FORMNAME").replaceAll("/", "，")
						+ "(" + securityLevelStr + ")";
				File destFile = new File(typeFolder + File.separator + fileName + "-" + formName
						+ "." + excelFileFormat);
				FileUtil.copy(excelSourceFile, destFile, true);
			}
		}
	}

	/**
	 * 导出清单列表Excel
	 *
	 * @param query 查询参数
	 * @return 导出的Excel文件
	 */
	public File exportListExcel(String query) {
		// 获取数据
		JSONObject res = Util.postTwxForObject("Thing.Fn.ListData", "QueryListData",
				JSONUtil.createObj().set("query", query));

		JSONArray data = res.getJSONArray("data");
		JSONArray sheetData = new JSONArray();

		for (int i = 0; i < data.size(); i++) {
			JSONObject obj = data.getJSONObject(i);
			JSONArray row = new JSONArray();
			row.add(i + 1);
			row.add(obj.getStr("TYPE", ""));
			row.add(obj.getStr("FILE_TYPE", ""));
			row.add(obj.getStr("FILE_NAME", ""));
			row.add(obj.getStr("GATHERING_METHOD", ""));
			row.add(obj.getStr("SOURCE_SYSTEM", ""));
			row.add(obj.getStr("SECURITY_LEVEL", ""));
			row.add(obj.getStr("STATE_CHECK", ""));
			row.add(obj.getStr("DELIVERY_STATE", ""));
			row.add(obj.getStr("USER_FULLNAME", ""));
			row.add(obj.getStr("CREATE_TIMESTAMP", ""));
			row.add(obj.getStr("FILE_FORMAT", ""));
			sheetData.add(row);
		}

		// 设置表头和列宽
		JSONArray headers = JSONUtil.parseArray(Arrays.asList("序号", "类别", "文件类别", "文件名称", "采集方式", "来源系统",
				"密级", "状态", "交付状态", "创建人", "创建日期", "文件形式"));
		JSONArray columnWidths = JSONUtil.parseArray(Arrays.asList(10, 15, 20, 70, 10, 10, 10, 10, 10, 10, 25, 10));
		return CommonUtil.createExcelFile("清单列表", headers, sheetData, columnWidths, 18);
	}

	/**
	 * 获取360全景图片查看路径
	 *
	 * @param path 文件路径
	 * @return JSONObject 包含处理结果
	 */
	public JSONObject get360ImageViewUrl(String path) {
		JSONObject res = new JSONObject();
		try {
			String srcPath = fileUploadPath + path;
			String indexFilePath = srcPath + "_f//index.html";
			String resPath = path + "_f//index.html";

			// 检查index.html是否存在
			if (FileUtil.exist(indexFilePath)) {
				return res.set("success", true).set("path", resPath);
			}

			// 如果不存在，解压并处理文件
			String distPath = srcPath + "_f";
			if (!unzipAndProcessFile(srcPath, distPath, indexFilePath)) {
				return res.set("success", false).set("msg", "处理文件失败");
			}

			return res.set("success", true).set("path", resPath);
		} catch (Exception e) {
			log.error("处理360全景图片失败", e);
			return res.set("success", false).set("msg", e.toString());
		}
	}

	/**
	 * 解压并处理文件
	 *
	 * @param srcPath       源文件路径
	 * @param distPath      目标路径
	 * @param indexFilePath index文件路径
	 * @return 处理结果
	 */
	private boolean unzipAndProcessFile(String srcPath, String distPath, String indexFilePath) {
		// 解压文件
		ZipUtil.unzip(srcPath, distPath);

		// 检查index.html是否存在
		if (!FileUtil.exist(indexFilePath)) {
			throw new IllegalArgumentException("上传的压缩包中没有index.html");
		}

		// 要插入的脚本内容
		String scriptTag = "<link href=\"/DataPackageManagement/plugins/layui-2.8.x/css/layui.css\" rel=\"stylesheet\" />" +
				"<script src=\"/DataPackageManagement/plugins/common/jquery-3.7.0.min.js\"></script>" +
				"<script src=\"/DataPackageManagement/plugins/layui-2.8.x/layui.js\"></script>" +
				"<script src=\"/DataPackageManagement/components/js/config/twxconfig.js\"></script>" +
				"<script src=\"/DataPackageManagement/components/js/util.js\"></script>" +
				"<script src=\"/DataPackageManagement/components/js/handle360.js\"></script>";

		// 读取文件内容
		FileReader fileReader = new FileReader(indexFilePath);
		List<String> lines = fileReader.readLines();

		// 查找插入位置
		int loadIndex = findLoadIndex(lines);
		if (loadIndex >= 0) {
			// 插入新内容
			lines.add(loadIndex, "renderStandAlone();");
			lines.add(lines.size() - 2, scriptTag);

			// 写入文件
			FileWriter writer = new FileWriter(indexFilePath);
			writer.writeLines(lines);
			return true;
		}

		return false;
	}

	/**
	 * 查找加载位置
	 *
	 * @param lines 文件内容行
	 * @return 插入位置
	 */
	private int findLoadIndex(List<String> lines) {
		for (int i = 0; i < lines.size(); i++) {
			if (lines.get(i).trim().equals("pano.readConfigUrlAsync(\"pano.xml\");")) {
				return i;
			}
		}
		return -1;
	}

	/**
	 * 获取PDM文件路径
	 *
	 * @param url zip文件URL
	 * @return JSONObject 包含处理结果
	 */
	public JSONObject getPDMFilePath(String url) {
		JSONObject res = new JSONObject();
		try {
			// 创建临时文件
			String tempFileName = System.currentTimeMillis() + ".zip";
			File tempFile = FileUtil.file(tempPath + File.separator + tempFileName);

			// 下载文件
			HttpUtil.downloadFile(url, tempFile);

			// 解压文件
			File unzipDir = ZipUtil.unzip(tempFile, Charset.forName("GBK"));

			// 处理解压后的文件
			File resFile = processUnzippedFile(unzipDir, tempFile);

			// 复制到上传目录
			String month = new SimpleDateFormat("yyyy-MM").format(new Date());
			String uploadDir = fileUploadPath + File.separator + month;
			FileUtil.mkdir(uploadDir);

			String uuid = UUID.randomUUID().toString();
			String fileFormat = FileNameUtil.extName(resFile);
			String fileName = FileNameUtil.getName(resFile);
			String filePath = uploadDir + File.separator + uuid;

			// 复制文件
			File destFile = FileUtil.file(filePath);
			FileUtil.copy(resFile, destFile, true);

			// 设置返回结果
			return res.set("success", true)
					.set("msg", "下载成功")
					.set("fileFormat", fileFormat)
					.set("fileName", fileName)
					.set("filePath", "//" + month + "//" + uuid);

		} catch (Exception e) {
			log.error("处理PDM文件失败", e);
			return res.set("success", false)
					.set("msg", "下载失败,原因：" + e.getLocalizedMessage());
		}
	}

	/**
	 * 处理解压后的文件
	 *
	 * @param unzipDir 解压目录
	 * @param tempFile 临时zip文件
	 * @return 处理后的文件
	 */
	private File processUnzippedFile(File unzipDir, File tempFile) {
		if (unzipDir.isDirectory()) {
			File[] files = unzipDir.listFiles();
			if (files != null && files.length == 1) {
				return files[0];
			}
			return tempFile;
		}
		return tempFile;
	}


	public JSONObject pushFilesToSystem(String treeId, String username, String pushKey) {
		String thisTempPath = tempPath + File.separator + "push" + File.separator;
		//返回信息
		JSONObject res = new JSONObject();
		res.set("success", true);
		String msg = "推送成功";
		int isSuccess = 1;
		boolean isPushing = false;
		String pushId = "0";
		try {
			//查询配置的package的xml信息
			JSONObject packageXmlJson = Util.postTwxForObject("Thing.Fn.ListData", "GetStringPropertyValue",
					JSONUtil.createObj().set("propertyName", "packageXml")).getJSONArray("rows").getJSONObject(0).getJSONObject("result");

			//查询配置的axis请求的参数信息
			JSONObject axisJson = Util.postTwxForObject("Thing.Fn.ListData", "GetStringPropertyValue",
					JSONUtil.createObj().set("propertyName", "axisParam")).getJSONArray("rows").getJSONObject(0).getJSONObject("result");

			//查询型号下的跟踪卡和照片信息
			JSONObject obj = Util.postTwxForObject("Thing.Fn.ListData", "QueryGZKAndPhoto",
					JSONUtil.createObj().set("id", treeId));

			//存放影像记录的
			JSONObject photoObjs = new JSONObject();

			//存放推送的文件以及文件信息
			List<Map<String, Object>> fileList = new ArrayList<>();

			if (obj.getBool("success")) {
				JSONArray data = obj.getJSONArray("data");
				if (!data.isEmpty()) {
					//增加一条推送信息 并且校验当前是否正在推送
					JSONObject checkRs = Util.postTwxForObject("Thing.Fn.PushFiles", "AddPushInfo",
							JSONUtil.createObj().set("values", JSONUtil.createObj().set("treeId", treeId).set("pushKey", pushKey).set("username", username).toString()));
					if (checkRs.getBool("success")) {
						String rootPath = Util.getFileUploadPath();
						isPushing = true;
						pushId = checkRs.getStr("data");
						logRecord(username, "推送档案", "推送过程结构树" + treeId + "节点下的跟踪卡和照片", 1);
						for (int i = 0; i < data.size(); i++) {
							JSONObject d = data.getJSONObject(i);
							String fileName = d.getStr("FN");
							String createTime = d.getStr("CT");
							String fileFormat = d.getStr("FF");
							String filePath = d.getStr("FP");
							String fileType = d.getStr("FT");
							String fileNumber = d.getStr("FNU", "");
							String modelName = d.getStr("MN", "");
							String phaseName = d.getStr("PN", "");
							String dirName = d.getStr("DN", "");
							String leafName = d.getStr("LN", "");
							int id = d.getInt("ID");
							String parentName = d.getStr("PT", "");

							if ("跟踪卡".equals(fileType)) {
								//跟踪卡 一个文件放到一个压缩包中
								String fullPath = rootPath + filePath;
								//确保文件是存在的
								if (FileUtil.exist(fullPath)) {
									//压缩包的文件路径
									String zipPath = thisTempPath + File.separator + "card-" + id + "-" + System.currentTimeMillis() + "(内部)" + File.separator;
									FileUtil.mkdir(zipPath);
									//生成package.xml
									createPkgXml(packageXmlJson, zipPath);
									//完整的文件名
									String fullName = fileName + "(内部)." + fileFormat;
									//跟踪卡的相对路径
									String relativePath = fileName + File.separator + fullName;
									//将跟踪卡的文件复制到压缩包路径中
									FileUtil.copy(fullPath, zipPath + File.separator + relativePath, true);

									JSONObject archfileXmlJson = new JSONObject();
									JSONArray files = new JSONArray();
									files.add(relativePath);
									archfileXmlJson.set("files", files);
									archfileXmlJson.set("innerId", splitInnerId("p-c-" + id));
									archfileXmlJson.set("classId", "pkg-card");
									JSONObject attrs = new JSONObject();
									attrs.set("createTime", createTime);//编制日期
									attrs.set("security", "内部");//密级
									attrs.set("title", fileName);//题名
									attrs.set("fileNumber", fileNumber);
									attrs.set("number", "");//档号
									attrs.set("modelName", modelName);//型号名称
									attrs.set("phaseName", phaseName);//阶段名称
									attrs.set("dirName", dirName);//专业名称
									attrs.set("leafName", leafName);//过程名称

									archfileXmlJson.set("attrs", attrs);

									createArchFileXml(archfileXmlJson, zipPath, fileName);

									File zipFile = ZipUtil.zip(zipPath);
									FileUtil.del(zipPath);
									Map<String, Object> map = new HashMap<>();
									map.put("modelName", modelName);
									map.put("phaseName", phaseName);
									map.put("dirName", dirName);
									map.put("leafName", leafName);
									map.put("type", "跟踪卡");
									map.put("fileName", zipFile.getName());
									map.put("file", zipFile);
									map.put("title", fileName);
									map.put("createTime", cn.hutool.core.date.DateUtil.now());
									fileList.add(map);
								}

							} else if ("影像记录".equals(fileType)) {
								//影像记录 按照parentName分类 放到文件夹中并且压缩
								JSONArray photos = photoObjs.getJSONArray(parentName);
								if (ObjectUtil.isNull(photos)) {
									photos = new JSONArray();
									photos.add(d);
									photoObjs.set(parentName, photos);
								} else {
									photos.add(d);
								}
							}
						}

						//处理影像记录的打包
						for (String key : photoObjs.keySet()) {
							if (StrUtil.isNotBlank(key)) {
								//压缩包的文件路径
								String zipPath = thisTempPath + File.separator + "photo-" + treeId + "-" + key + "-" + System.currentTimeMillis() + "(内部)" + File.separator;
								FileUtil.mkdir(zipPath);
								//生成package.xml
								createPkgXml(packageXmlJson, zipPath);
								//存放照片的路径
								String photoPath = zipPath + key;
								FileUtil.mkdir(photoPath);

								JSONArray photos = photoObjs.getJSONArray(key);

								JSONObject archfileXmlJson = new JSONObject();
								JSONArray files = new JSONArray();

								archfileXmlJson.set("files", files);
								archfileXmlJson.set("innerId", splitInnerId("p-p-" + treeId + "-" + key));
								archfileXmlJson.set("classId", "pkg-photos");

								String modelName = photos.getJSONObject(0).getStr("MN", "");
								String phaseName = photos.getJSONObject(0).getStr("PN", "");
								String dirName = photos.getJSONObject(0).getStr("DN", "");
								String leafName = photos.getJSONObject(0).getStr("LN", "");

								String title = key + "照片";
								JSONObject attrs = new JSONObject();
								attrs.set("createTime", photos.getJSONObject(0).getStr("CT"));
								attrs.set("security", "内部");
								attrs.set("title", title);//题名
								attrs.set("fileNumber", photos.getJSONObject(0).getStr("FNU", ""));
								attrs.set("number", "");//档号
								attrs.set("modelName", modelName);//型号名称
								attrs.set("phaseName", phaseName);//阶段名称
								attrs.set("dirName", dirName);//专业名称
								attrs.set("leafName", leafName);//过程名称

								for (int i = 0; i < photos.size(); i++) {
									JSONObject d = photos.getJSONObject(i);
									String fileName = d.getStr("FN");
									String fileFormat = d.getStr("FF");
									String filePath = d.getStr("FP");

									String fullName = fileName + "(内部)." + fileFormat;
									//将照片下载到存放照片的路径中
									try {
										File file = HttpUtil.downloadFileFromUrl(filePath, photoPath);
										FileUtil.rename(file, fullName, false, true);
										files.add(key + File.separator + fullName);
									} catch (Exception e) {
										System.err.println("路径：'" + filePath + "'的文件下载失败，原因：" + e.getLocalizedMessage());
									}
								}
								archfileXmlJson.set("attrs", attrs);

								createArchFileXml(archfileXmlJson, zipPath, key);
								File zipFile = ZipUtil.zip(zipPath);
								FileUtil.del(zipPath);
								Map<String, Object> map = new HashMap<>();
								map.put("modelName", modelName);
								map.put("phaseName", phaseName);
								map.put("dirName", dirName);
								map.put("leafName", leafName);
								map.put("type", "照片包");
								map.put("fileName", zipFile.getName());
								map.put("createTime", DateUtil.now());
								map.put("file", zipFile);
								map.put("title", title);
								fileList.add(map);
							}
						}

						int photoNums = 0;
						int cardNums = 0;
						JSONArray postArr = new JSONArray();
						for (Map<String, Object> map : fileList) {
							JSONObject postObj = new JSONObject();
							postObj.set("modelName", map.get("modelName"));
							postObj.set("phaseName", map.get("phaseName"));
							postObj.set("dirName", map.get("dirName"));
							postObj.set("leafName", map.get("leafName"));
							postObj.set("type", map.get("type"));
							postObj.set("title", map.get("title"));
							postObj.set("fileName", map.get("fileName"));
							postObj.set("createTime", map.get("createTime"));
							postObj.set("id", new UUIDGenerator().next());
							postArr.add(postObj);
							if (map.get("type").equals("跟踪卡")) {
								cardNums++;
							} else {
								photoNums++;
							}
						}
						String pushInfo = "本次共推送跟踪卡" + cardNums + "个，照片包" + photoNums + "个";
						Util.postTwxForObject("Thing.Fn.PushFiles", "AddPushLists", JSONUtil.createObj().set("pushId", pushId).set("values", postArr.toString()));
						Util.postTwxForObject("Thing.Fn.PushFiles", "UpdatePushInfo", JSONUtil.createObj().set("id", pushId).set("pushInfo", pushInfo));

						//延迟2秒开始推送 以便于前端能查询到pushInfo
						Thread.sleep(2000);

						for (Map<String, Object> map : fileList) {
							File file = (File) map.get("file");
							uploadAxis(file, axisJson, treeId, username);
						}


					} else {
						res = checkRs;
					}
				} else {
					res.set("success", false);
					res.set("msg", "未查询到跟踪卡和照片！");
				}
			} else {
				res = obj;
			}
		} catch (Exception e) {
			res.set("success", false);
			msg = "推送失败，原因：" + e.getLocalizedMessage();
			isSuccess = 2;
			res.set("msg", msg);
		} finally {
			if (isPushing) {
				Util.postTwxForObject("Thing.Fn.PushFiles", "ClosePush", JSONUtil.createObj().set("id", pushId).set("msg", msg).set("isSuccess", isSuccess));
				if (isSuccess == 1) {
					logRecord(username, "推送结果", "推送过程结构树" + treeId + "节点下的跟踪卡和照片", 1);
				} else {
					logRecord(username, "推送结果", "推送过程结构树" + treeId + "节点下的跟踪卡和照片" + msg, 0);
				}

			}
		}
		return res;
	}

	/**
	 * 记录日志信息
	 */
	public static void logRecord(String username, String opt, String log, int result) {
		JSONObject params = new JSONObject();
		params.set("userName", username);
		params.set("op", opt);
		params.set("content", log);
		params.set("result", result);
		Util.postTwxForString("Thing.UserLogUtil", "logRecord", params);
	}


	public static void uploadAxis(File zipFile, JSONObject j, String treeId, String username) throws Exception {
		UploadFileAxisClientEx client = new UploadFileAxisClientEx();
		client.setFileSize(j.getInt("fileSize"));
		client.setWsUrl(j.getStr("wsUrl"));
		client.setMethod(j.getStr("method"));
		client.setAuth(j.getStr("auth"));
		client.setDataPackage(j.getStr("dataPackage"));
		client.setNamespaceURL(j.getStr("namespaceURL"));
		logRecord(username, "推送文件", "开始推送结构树" + treeId + "下的文件:" + zipFile.getAbsolutePath(), 1);
		client.upload(zipFile);
	}

	/**
	 * 创建文档的描述xml
	 */
	private static void createArchFileXml(JSONObject j, String path, String xmlName) {
		Document doc = DocumentHelper.createDocument();
		Element objEl = doc.addElement("obj");
		objEl.addElement("innerId").addText(j.getStr("innerId"));
		objEl.addElement("classId").addText(j.getStr("classId"));
		Element attrsEl = objEl.addElement("attrs");
		JSONObject attrs = j.getJSONObject("attrs");
		for (String key : attrs.keySet()) {
			attrsEl.addElement(key).addText(attrs.getStr(key));
		}
		Element filesEl = objEl.addElement("files");
		JSONArray files = j.getJSONArray("files");
		for (int i = 0; i < files.size(); i++) {
			filesEl.addElement("file").addText(files.getStr(i));
		}
		writeXml(doc, path, xmlName);
	}

	/**
	 * 将document写入到xml文件中
	 */
	private static void writeXml(Document doc, String path, String xmlName) {
		OutputFormat format = OutputFormat.createPrettyPrint();
		XMLWriter writer = null;
		try {
			writer = new XMLWriter(Files.newOutputStream(Paths.get(path + File.separator + xmlName + ".xml")), format);
			writer.write(doc);
		} catch (Exception e) {
			log.error(e.getMessage());
		} finally {
			try {
				if (writer != null) {
					writer.close();
				}
			} catch (IOException e) {
				log.error(e.getMessage());
			}
		}
	}

	/**
	 * 创建数据包的包描述xml
	 */
	private static void createPkgXml(JSONObject j, String path) {
		Document doc = DocumentHelper.createDocument();
		Element packageEl = doc.addElement("package");
		packageEl.addElement("app_name").addText(j.getStr("app_name"));
		packageEl.addElement("file_type").addText(j.getStr("file_type"));
		packageEl.addElement("org_name").addText(j.getStr("org_name"));
		packageEl.addElement("app_id").addText(j.getStr("app_id"));
		packageEl.addElement("source_id").addText(j.getStr("source_id"));
		packageEl.addElement("fail_url").addText(j.getStr("fail_url"));
		packageEl.addElement("success_url").addText(j.getStr("success_url"));
		writeXml(doc, path, "package");
	}

	private static String splitInnerId(String innerId) {
		if (innerId.length() > 36) {
			return innerId.substring(0, 36);
		} else {
			return innerId;
		}
	}

}