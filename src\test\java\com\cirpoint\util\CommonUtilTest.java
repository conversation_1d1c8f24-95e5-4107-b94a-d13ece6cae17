package com.cirpoint.util;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class CommonUtilTest {

    private static final String TEST_TEMP_DIR = "target/test-temp";
    private List<File> filesToDelete;

    @BeforeEach
    public void setup() {
        // 设置Thingworx配置
        CommonUtil.setThingworxDomain("http://twx:8011");
        CommonUtil.setThingworxKey("8ea8ace9-1167-4417-939f-a7ddc58d9429");
        
        // 设置临时目录
        CommonUtil.setTempPath(TEST_TEMP_DIR);
        
        // 创建测试临时目录
        File tempDir = new File(TEST_TEMP_DIR);
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }
        
        // 初始化待删除文件列表
        filesToDelete = new ArrayList<>();
    }

    @Test
    public void testMyComparison() {
        assertFalse(CommonUtil.compareValues("13", "±0.3", "Φ"));
    }

    @Test
    public void testBasicNumericComparison() {
        // 基本数值比对测试
        assertTrue(CommonUtil.compareValues("62.5", "±0.3", "62.6"));
        assertTrue(CommonUtil.compareValues("62.5", "±0.3", "62.3"));
        assertFalse(CommonUtil.compareValues("62.5", "±0.3", "62.9"));
        
        // 带单位的测试
        assertTrue(CommonUtil.compareValues("62.5", "±0.3", "62.6mm"));
        assertTrue(CommonUtil.compareValues("62.5", "±0.3", "62.3cm"));
    }

    @Test
    public void testToleranceFormats() {
        // ±格式
        assertTrue(CommonUtil.compareValues("80", "±0.3", "80.2"));
        assertFalse(CommonUtil.compareValues("80", "±0.3", "80.4"));

        // 0.000/-0.2格式
        assertTrue(CommonUtil.compareValues("5", "0.000/-0.2", "5.0"));
        assertTrue(CommonUtil.compareValues("5", "0.000/-0.2", "4.8"));
        assertFalse(CommonUtil.compareValues("5", "0.000/-0.2", "5.1"));
        assertFalse(CommonUtil.compareValues("5", "0.000/-0.2", "4.7"));

        // -0.1/-0.3格式
        assertTrue(CommonUtil.compareValues("5", "-0.1/-0.3", "4.8"));
        assertFalse(CommonUtil.compareValues("5", "-0.1/-0.3", "4.6"));

        // 0.1~0.3格式
        assertTrue(CommonUtil.compareValues("5", "0.1~0.3", "5.2"));
        assertFalse(CommonUtil.compareValues("5", "0.1~0.3", "5.4"));
    }

    @Test
    public void testSpecialDesignValues() {
        // ST和M的情况
        assertTrue(CommonUtil.compareValues("4-ST5", "", "任意值"));
        assertTrue(CommonUtil.compareValues("M8", "±0.3", "任意值"));
        assertTrue(CommonUtil.compareValues("4-ST3×0.5×10", "", "任意值"));

        // 典型值测试
        assertTrue(CommonUtil.compareValues("2 典型", "", "1.9;2.1"));
        assertTrue(CommonUtil.compareValues("2 典型", "", "1.8~2.2"));
        assertTrue(CommonUtil.compareValues("2 典型", "", "1.7;2.3"));

        // 特殊符号测试
        assertTrue(CommonUtil.compareValues("Φ7", "±0.3", "7.2"));
        assertTrue(CommonUtil.compareValues("R5", "±0.3", "5.2"));
        assertTrue(CommonUtil.compareValues("C5", "±0.3", "5.2"));
    }

    @Test
    public void testMultipleValues() {
        // 4-Φ5.5格式
        assertTrue(CommonUtil.compareValues("4-Φ5.5", "", "5.4;5.5;5.5;5.6"));
        assertTrue(CommonUtil.compareValues("4-Φ5.5", "", "5.3~5.7"));
        assertTrue(CommonUtil.compareValues("4-Φ5.5", "", "5.4;5.5;5.6")); // 数量不对

        // 2-Φ5销孔配作Φ2格式
        assertTrue(CommonUtil.compareValues("2-Φ5销孔配作Φ2", "", "5.0;5.0"));
        assertTrue(CommonUtil.compareValues("2-Φ5销孔配作Φ2", "", "5.0"));
    }

    @Test
    public void testDescriptiveValues() {
        // 描述性要求
        assertTrue(CommonUtil.compareValues("产品外形特征符合模型要求", "", "任意值"));
        assertTrue(CommonUtil.compareValues("表面均应清除毛刺，无多余物、霉点、油污等杂质", "", "任意值"));
        assertTrue(CommonUtil.compareValues("（侧边加强筋刻刻生产年份、批次号及产品标识，5号字体，字深0.5mm）", "", "任意值"));
    }

    @Test
    public void testSpecialTolerances() {
        // 特殊公差值
        assertTrue(CommonUtil.compareValues("80", "--", "任意值"));
        assertTrue(CommonUtil.compareValues("80", "/", "任意值"));
        assertTrue(CommonUtil.compareValues("80", "", "任意值"));
    }

    @Test
    public void testMeasuredValueFormats() {
        // 单个值测试
        assertTrue(CommonUtil.compareValues("80", "±0.3", "80.2"));
        assertTrue(CommonUtil.compareValues("80", "±0.3", "80.2mm"));

        // 多个值测试（分号分隔）
        assertTrue(CommonUtil.compareValues("80", "±0.3", "79.8;80.0;80.2"));
        assertFalse(CommonUtil.compareValues("80", "±0.3", "79.8;80.0;80.4"));

        // 范围值测试（波浪线分隔）
        assertTrue(CommonUtil.compareValues("80", "±0.3", "79.8~80.2"));
        assertFalse(CommonUtil.compareValues("80", "±0.3", "79.8~80.4"));
    }

    @Test
    public void testErrorHandling() {
        // 格式错误处理
        assertTrue(CommonUtil.compareValues("invalid", "±0.3", "80.2"));
        assertTrue(CommonUtil.compareValues("80", "invalid", "80.2"));
        assertTrue(CommonUtil.compareValues("80", "±0.3", "invalid"));

        // 空值处理
        assertTrue(CommonUtil.compareValues(null, "±0.3", "80.2"));
        assertTrue(CommonUtil.compareValues("80", null, "80.2"));
        assertFalse(CommonUtil.compareValues("80", "±0.3", null));
    }

    @Test
    public void testComplexScenarios() {
        // 组合场景测试
        assertTrue(CommonUtil.compareValues("4-Φ5.5", "±0.2", "5.4;5.5;5.5;5.6"));
        assertTrue(CommonUtil.compareValues("2 典型", "±0.3", "1.8~2.3"));
        assertTrue(CommonUtil.compareValues("R5", "0.1~0.3", "5.2"));
        
        // 边界值测试
        assertTrue(CommonUtil.compareValues("80", "±0.3", "79.7"));
        assertFalse(CommonUtil.compareValues("80", "±0.3", "79.6"));
        assertTrue(CommonUtil.compareValues("80", "±0.3", "80.3"));
        assertFalse(CommonUtil.compareValues("80", "±0.3", "80.4"));
    }

    @Test
    public void testRealBusinessData() {
        // 基本数值测试 ±公差
        assertTrue(CommonUtil.compareValues("62.5", "±0.3", "62.53"));
        assertTrue(CommonUtil.compareValues("80", "±0.3", "80.14"));
        assertFalse(CommonUtil.compareValues("52", "±0.3", "49.94")); // 超出公差范围
        assertFalse(CommonUtil.compareValues("13", "±0.2", "13.28"));
        assertTrue(CommonUtil.compareValues("13", "±0.2", "12.82"));
        assertTrue(CommonUtil.compareValues("13", "±0.2", "13.17"));
        assertFalse(CommonUtil.compareValues("13", "±0.2", "12.70"));
        assertTrue(CommonUtil.compareValues("45.5", "±0.3", "45.54"));
        assertFalse(CommonUtil.compareValues("66", "±0.3", "65.32"));
        assertTrue(CommonUtil.compareValues("16", "±0.2", "16.18"));
        assertFalse(CommonUtil.compareValues("41", "±0.1", "41.60")); // 超出公差范围
        assertFalse(CommonUtil.compareValues("26", "±0.2", "26.34"));
        assertTrue(CommonUtil.compareValues("16", "±0.2", "16.10"));
        assertFalse(CommonUtil.compareValues("16", "±0.2", "16.34"));
        assertTrue(CommonUtil.compareValues("23", "±0.2", "23.00"));
        assertTrue(CommonUtil.compareValues("2", "±0.1", "2.10"));
        assertTrue(CommonUtil.compareValues("28.25", "±0.2", "28.10"));
        assertTrue(CommonUtil.compareValues("28.25", "±0.2", "28.17"));
        assertTrue(CommonUtil.compareValues("57.78", "±0.3", "57.9"));
        assertTrue(CommonUtil.compareValues("4", "±0.1", "4.1"));
        assertTrue(CommonUtil.compareValues("14", "±0.2", "14.2"));
        assertTrue(CommonUtil.compareValues("3", "±0.1", "3.10"));
        assertTrue(CommonUtil.compareValues("Φ54", "±0.1", "∅54.08"));

        // 特殊格式公差测试
        assertTrue(CommonUtil.compareValues("4-Φ5", "0.000/-0.2", "∅5.00；∅4.96；∅4.90；∅4.96"));
        assertTrue(CommonUtil.compareValues("42", "0.28/0.1", "42.14"));
        assertFalse(CommonUtil.compareValues("23", "-0.1/-0.3", "22.94"));
        assertTrue(CommonUtil.compareValues("6", "0.000/-0.1", "5.94"));
        assertTrue(CommonUtil.compareValues("4", "0.05/0.000", "4.04"));
        assertTrue(CommonUtil.compareValues("103", "0.5/-0.5", "103.20"));

        // 典型值测试
        assertTrue(CommonUtil.compareValues("2 典型", "±0.1", "1.98～2.06"));

        // 多数量值测试
        assertTrue(CommonUtil.compareValues("4-Φ5.5", "±0.1", "∅5.40～∅5.56"));
        assertTrue(CommonUtil.compareValues("4-ST5", "±0.1", "4-ST5"));
        assertFalse(CommonUtil.compareValues("2-Φ5销孔配作Φ2", "/", ""));

        // 特殊符号测试
        assertTrue(CommonUtil.compareValues("8 销孔", "±0.2", "8.12"));
        assertTrue(CommonUtil.compareValues("8.5", "±0.2", "∅8.57"));
        assertTrue(CommonUtil.compareValues("Φ7", "±0.2", "∅7.04"));

        // 无公差值测试
        assertTrue(CommonUtil.compareValues("2", "", "2.1"));
        assertTrue(CommonUtil.compareValues("18", "", "18.2"));
        assertTrue(CommonUtil.compareValues("45", "--", "45"));
        assertFalse(CommonUtil.compareValues("0.05", "/", ""));

        // 特殊单位测试
        assertFalse(CommonUtil.compareValues("45", "±1′", "45.1"));

        assertTrue(CommonUtil.compareValues("45", "±1′", "45°1′"));


        // ST类型测试
        assertTrue(CommonUtil.compareValues("4-ST3×0.5×10", "/", "4-ST3×0.5×10"));

        // 描述性要求测试
        assertFalse(CommonUtil.compareValues("侧边加强筋刻刻生产年份、批次号及产品标识，5号字体，字深0.5mm", "/", ""));
        assertFalse(CommonUtil.compareValues("产品外形特征符合模型要求；表面均应清除毛刺，无多余物、霉点、油污等杂质", "/", ""));
    }

    @Test
    public void testImportXmlToThingworx() {
        // 测试文件不存在的情况
        assertFalse(CommonUtil.importXmlToThingworx("nonexistent.xml"));

        // 测试文件存在的情况
        String xmlPath = "C:\\TestOut\\Things_Thing.Fn.TestEvaluation.xml";
        assertTrue(CommonUtil.importXmlToThingworx(xmlPath));

        // 测试无效的服务器地址
        assertFalse(CommonUtil.importXmlToThingworx(xmlPath));
    }

    @Test
    public void testExcel(){
        String filePath = "C:\\TestOut\\test.xlsx";
        CommonUtil.processMesExcel(filePath, 2);
    }

    /**
     * 测试处理MES三级表Excel的方法
     */
    @Test
    public void testProcessMesExcel() throws IOException {
        // 创建测试用的Excel文件
        String testFilePath = createTestExcelWithMergedCells();
        
        try {
            // 执行方法
            String resultFilePath = CommonUtil.processMesExcel(testFilePath, 2);
            
            // 验证结果
            assertNotNull(resultFilePath, "处理后的文件路径不应为空");
            assertTrue(new File(resultFilePath).exists(), "处理后的文件应该存在");
            assertTrue(resultFilePath.contains("_deal"), "处理后的文件名应包含_deal");
            
            // 验证文件内容 - 检查合并单元格已被拆分
            try (Workbook workbook = WorkbookFactory.create(new File(resultFilePath))) {
                Sheet sheet = workbook.getSheetAt(0);
                
                // 验证所有合并单元格已被拆分（从数据行开始）
                int mergedRegionsAfterRow1 = countMergedRegionsAfterRow(sheet, 1);
                assertEquals(0, mergedRegionsAfterRow1, "数据行的合并单元格应被拆分");
                
                // 验证数据一致性 - 检查拆分后的单元格值
                Row row1 = sheet.getRow(1);
                Row row2 = sheet.getRow(2);
                
                String b2Value = getCellValue(row1.getCell(1));
                String c2Value = getCellValue(row1.getCell(2));
                String b3Value = getCellValue(row2.getCell(1));
                String c3Value = getCellValue(row2.getCell(2));
                
                assertEquals("合并值", b2Value, "B2单元格值应保持不变");
                assertEquals("合并值", c2Value, "C2单元格值应与合并区域第一个单元格相同");
                assertEquals("合并值", b3Value, "B3单元格值应与合并区域第一个单元格相同");
                assertEquals("合并值", c3Value, "C3单元格值应与合并区域第一个单元格相同");
            }
            
            // 清理测试文件
//            new File(testFilePath).delete();
//            new File(resultFilePath).delete();
            
        } catch (Exception e) {
            // 如果测试失败，确保清理测试文件
            new File(testFilePath).delete();
            fail("处理MES三级表Excel时发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 创建包含合并单元格的测试Excel文件
     */
    private String createTestExcelWithMergedCells() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("测试Sheet");
        
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("列1");
        headerRow.createCell(1).setCellValue("列2");
        headerRow.createCell(2).setCellValue("列3");
        
        // 创建数据行
        Row dataRow1 = sheet.createRow(1);
        dataRow1.createCell(0).setCellValue("A2");
        dataRow1.createCell(1).setCellValue("合并值");
        dataRow1.createCell(2).setCellValue(""); // 这个会被合并
        
        Row dataRow2 = sheet.createRow(2);
        dataRow2.createCell(0).setCellValue("A3");
        dataRow2.createCell(1).setCellValue(""); // 这个会被合并
        dataRow2.createCell(2).setCellValue(""); // 这个会被合并
        
        // 添加合并区域 - 合并B2:C3 (1,1)到(2,2)
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 1, 2));
        
        // 创建文件
        String filePath = TEST_TEMP_DIR + "/test_merged_cells.xlsx";
        try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
            workbook.write(outputStream);
        } finally {
            workbook.close();
        }
        
        return filePath;
    }
    
    /**
     * 获取单元格值的字符串表示
     */
    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        CellType cellType = cell.getCellType();
        if (cellType == CellType.FORMULA) {
            cellType = cell.getCachedFormulaResultType();
        }
        
        switch (cellType) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                }
                double numValue = cell.getNumericCellValue();
                if (numValue == (long) numValue) {
                    return String.valueOf((long) numValue);
                }
                return String.valueOf(numValue);
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case BLANK:
                return "";
            default:
                return "";
        }
    }
    
    /**
     * 计算指定行之后的合并单元格数量
     */
    private int countMergedRegionsAfterRow(Sheet sheet, int startRow) {
        int count = 0;
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress region = sheet.getMergedRegion(i);
            if (region.getFirstRow() >= startRow) {
                count++;
            }
        }
        return count;
    }
}
