/**
 * 全景图热点编辑系统 - 导出功能模块
 * 
 * <AUTHOR>
 * @date 2025-06-10
 * @description 处理全景图包导出功能
 * @requires panorama-core.js
 */

/**
 * 全景图编辑器导出功能模块
 */
var PanoramaExport = (function () {
    'use strict';

    // ==================== 导出条件检查 ====================

    /**
     * 检查导出条件并给出明确提示
     */
    function checkExportConditions() {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) {
            layui.layer.msg('请先选择一个任务', { icon: 2 });
            return;
        }

        // 显示检查状态
        var checkingIndex = layer.msg('正在检查导出条件...', { icon: 16, shade: 0.01 });

        var $ = layui.$;
        $.get('/panorama/task/' + currentTaskId, function (res) {
            layui.layer.close(checkingIndex);

            if (!res.success) {
                layui.layer.msg('获取任务信息失败，请重试', { icon: 2 });
                return;
            }

            var taskData = res.data;
            var hasZipFile = taskData.ZIP_FILE_PATH && taskData.ZIP_FILE_PATH.trim() !== '';
            var hasExtractPath = taskData.EXTRACT_PATH && taskData.EXTRACT_PATH.trim() !== '';

            if (!hasZipFile) {
                layui.layer.alert('导出失败：该任务尚未上传全景图ZIP文件。<br><br>请先上传全景图文件后再进行导出。', {
                    icon: 2,
                    title: '导出条件不满足'
                });
                return;
            }

            if (!hasExtractPath) {
                layui.layer.alert('导出失败：全景图文件正在处理中。<br><br>请稍等片刻，待文件处理完成后再进行导出。', {
                    icon: 2,
                    title: '导出条件不满足'
                });
                return;
            }

            // 条件满足，执行导出
            executeExport(currentTaskId);

        }).fail(function () {
            layui.layer.close(checkingIndex);
            layui.layer.msg('网络请求失败，请检查网络连接', { icon: 2 });
        });
    }

    /**
     * 执行导出操作
     * @param {string} taskId 任务ID
     */
    function executeExport(taskId) {
        // 显示导出确认对话框
        layui.layer.confirm('确定要导出当前任务的全景图包吗？', {
            icon: 3,
            title: '确认导出',
            btn: ['确认导出', '取消']
        }, function (index) {
            layui.layer.close(index);
            performExport(taskId);
        });
    }

    /**
     * 执行实际的导出操作
     * @param {string} taskId 任务ID
     */
    function performExport(taskId) {
        // 显示导出进度
        var exportingIndex = layer.msg('正在导出，请稍候...', { icon: 16, shade: 0.01 });
        // 检查是否支持jQuery.fileDownload
        var $ = layui.$;
        if (typeof $.fileDownload === 'function') {
            // 使用jQuery.fileDownload进行文件下载
            $.fileDownload('/panorama/export', {
                httpMethod: 'POST',
                data: { taskId: taskId },
                successCallback: function () {
                    layui.layer.close(exportingIndex);
                    handleExportSuccess(taskId);
                },
                failCallback: function (responseHtml, url, error) {
                    layui.layer.close(exportingIndex);
                    handleExportError(responseHtml, error);
                }
            });
        } else {
            // 备用方案：使用传统的form提交方式
            exportWithFormSubmit(taskId, exportingIndex);
        }
    }

    /**
     * 使用表单提交方式导出（备用方案）
     * @param {string} taskId 任务ID
     * @param {number} loadingIndex 加载层索引
     */
    function exportWithFormSubmit(taskId, loadingIndex) {
        try {
            // 创建隐藏的表单
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = '/panorama/export';
            form.style.display = 'none';

            // 添加任务ID参数
            var taskIdInput = document.createElement('input');
            taskIdInput.type = 'hidden';
            taskIdInput.name = 'taskId';
            taskIdInput.value = taskId;
            form.appendChild(taskIdInput);

            // 提交表单
            document.body.appendChild(form);
            form.submit();

            // 清理表单
            setTimeout(function () {
                document.body.removeChild(form);
                layui.layer.close(loadingIndex);

                // 由于无法检测下载是否成功，显示通用提示
                layui.layer.msg('导出请求已发送，请检查浏览器下载', { icon: 1 });
            }, 2000);

        } catch (error) {
            layui.layer.close(loadingIndex);
            layui.layer.msg('导出失败，请重试', { icon: 2 });
        }
    }

    // ==================== 导出结果处理 ====================

    /**
     * 处理导出成功
     * @param {string} taskId 任务ID
     */
    function handleExportSuccess(taskId) {
        layui.layer.msg('导出成功', { icon: 1 });

        // 可选：更新任务状态为已导出
        updateTaskStatusToExported(taskId);
    }

    /**
     * 处理导出失败
     * @param {string} responseHtml 响应HTML
     * @param {Object} error 错误对象
     */
    function handleExportError(responseHtml, error) {
        var errorMessage = '导出失败，请重试';

        // 尝试从响应中提取错误信息
        if (responseHtml && typeof responseHtml === 'string') {
            try {
                var errorData = JSON.parse(responseHtml);
                if (errorData.msg) {
                    errorMessage = '导出失败: ' + errorData.msg;
                }
            } catch (e) {
                // 解析失败，使用默认错误信息
            }
        }

        layui.layer.msg(errorMessage, { icon: 2 });
    }

    /**
     * 更新任务状态为已导出
     * @param {string} taskId 任务ID
     */
    function updateTaskStatusToExported(taskId) {
        var $ = layui.$;
        $.ajax({
            url: '/panorama/task/updateStatus',
            type: 'POST',
            data: {
                taskId: taskId,
                status: PanoramaCore.CONFIG.TASK_STATUS.EXPORTED
            },
            success: function (res) {
                if (res.success) {
                    // 状态更新成功，刷新任务信息
                    if (typeof PanoramaTask !== 'undefined' && PanoramaTask.loadTaskList) {
                        PanoramaTask.loadTaskList();
                    }
                }
            },
            error: function () {
                // 状态更新失败，静默处理
                console.warn('更新任务状态失败');
            }
        });
    }

    // ==================== 导出状态管理 ====================

    /**
     * 检查任务是否可以导出
     * @param {string} taskId 任务ID
     * @param {Function} callback 回调函数
     */
    function checkExportability(taskId, callback) {
        if (!taskId) {
            callback(false, '任务ID无效');
            return;
        }

        var $ = layui.$;
        $.get('/panorama/task/' + taskId, function (res) {
            if (res.success) {
                var taskData = res.data;
                var hasZipFile = taskData.ZIP_FILE_PATH && taskData.ZIP_FILE_PATH.trim() !== '';
                var hasExtractPath = taskData.EXTRACT_PATH && taskData.EXTRACT_PATH.trim() !== '';

                if (!hasZipFile) {
                    callback(false, '尚未上传全景图ZIP文件');
                } else if (!hasExtractPath) {
                    callback(false, '全景图文件正在处理中');
                } else {
                    callback(true, '可以导出');
                }
            } else {
                callback(false, '获取任务信息失败');
            }
        }).fail(function () {
            callback(false, '网络请求失败');
        });
    }

    /**
     * 获取导出状态文本
     * @param {string} taskId 任务ID
     * @param {Function} callback 回调函数
     */
    function getExportStatusText(taskId, callback) {
        checkExportability(taskId, function (canExport, message) {
            if (canExport) {
                callback('可导出');
            } else {
                callback('不可导出: ' + message);
            }
        });
    }

    // ==================== 批量导出支持 ====================

    /**
     * 批量检查多个任务的导出状态
     * @param {Array} taskIds 任务ID数组
     * @param {Function} callback 回调函数
     */
    function batchCheckExportability(taskIds, callback) {
        if (!taskIds || taskIds.length === 0) {
            callback([]);
            return;
        }

        var results = [];
        var completed = 0;

        taskIds.forEach(function (taskId, index) {
            checkExportability(taskId, function (canExport, message) {
                results[index] = {
                    taskId: taskId,
                    canExport: canExport,
                    message: message
                };

                completed++;
                if (completed === taskIds.length) {
                    callback(results);
                }
            });
        });
    }

    /**
     * 批量导出多个任务
     * @param {Array} taskIds 任务ID数组
     */
    function batchExport(taskIds) {
        if (!taskIds || taskIds.length === 0) {
            layui.layer.msg('请选择要导出的任务', { icon: 2 });
            return;
        }

        // 首先检查所有任务的导出条件
        batchCheckExportability(taskIds, function (results) {
            var exportableTaskIds = results.filter(function (result) {
                return result.canExport;
            }).map(function (result) {
                return result.taskId;
            });

            var nonExportableCount = taskIds.length - exportableTaskIds.length;

            if (exportableTaskIds.length === 0) {
                layui.layer.msg('所选任务均不满足导出条件', { icon: 2 });
                return;
            }

            var confirmMessage = '将导出 ' + exportableTaskIds.length + ' 个任务';
            if (nonExportableCount > 0) {
                confirmMessage += '（' + nonExportableCount + ' 个任务不满足导出条件将被跳过）';
            }
            confirmMessage += '，是否继续？';

            layui.layer.confirm(confirmMessage, {
                icon: 3,
                title: '批量导出确认'
            }, function (index) {
                layui.layer.close(index);

                // 逐个导出任务
                exportTasksSequentially(exportableTaskIds);
            });
        });
    }

    /**
     * 顺序导出多个任务
     * @param {Array} taskIds 任务ID数组
     */
    function exportTasksSequentially(taskIds) {
        if (taskIds.length === 0) {
            layui.layer.msg('批量导出完成', { icon: 1 });
            return;
        }

        var currentTaskId = taskIds.shift();
        var remainingCount = taskIds.length;

        layui.layer.msg('正在导出任务 ' + currentTaskId + '（剩余 ' + remainingCount + ' 个）', {
            icon: 16,
            time: 2000
        });

        performExport(currentTaskId);

        // 延迟处理下一个任务，避免服务器压力过大
        setTimeout(function () {
            exportTasksSequentially(taskIds);
        }, 3000);
    }

    // ==================== 公共API ====================
    return {
        // 导出条件检查
        checkExportConditions: checkExportConditions,
        executeExport: executeExport,
        performExport: performExport,

        // 导出结果处理
        handleExportSuccess: handleExportSuccess,
        handleExportError: handleExportError,
        updateTaskStatusToExported: updateTaskStatusToExported,

        // 导出状态管理
        checkExportability: checkExportability,
        getExportStatusText: getExportStatusText,

        // 批量导出
        batchCheckExportability: batchCheckExportability,
        batchExport: batchExport,
        exportTasksSequentially: exportTasksSequentially,

        // 内部函数
        exportWithFormSubmit: exportWithFormSubmit
    };
})();

// ==================== 向后兼容性支持 ====================
// 保持原有函数的全局访问方式

window.checkExportConditions = PanoramaExport.checkExportConditions;
window.executeExport = PanoramaExport.executeExport;
window.performExport = PanoramaExport.performExport;
