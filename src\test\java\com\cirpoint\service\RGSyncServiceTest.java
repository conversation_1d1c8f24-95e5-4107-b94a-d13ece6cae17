package com.cirpoint.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 热管同步服务测试类
 */
@Slf4j
@SpringBootTest
public class RGSyncServiceTest {

    @Autowired
    private RGSyncService rgSyncService;

    /**
     * 测试生成模拟文件
     */
    @Test
    public void testGenerateMockFiles() {
        int count = rgSyncService.generateMockFiles();
        log.info("成功生成{}个模拟文件", count);
    }

    @Test
    public void testSyncRgPhoto() {
        JSONArray objects = rgSyncService.collectRgPhoto("-1");
        log.info(objects.toString());
    }

    /**
     * 测试从文件名中提取型号和编号
     */
    @SuppressWarnings("unchecked")
    @Test
    public void testExtractModelAndCodes() {
        // 测试正常情况
        String fileName1 = "20230101-000001-[RG_01](01,02,03)-测试.BMP";
        JSONObject result1 = rgSyncService.extractModelAndCodes(fileName1);
        
        assertNotNull(result1, "提取结果不应为空");
        assertEquals("RG_01", result1.getStr("model"), "提取的型号应为RG_01");
        
        List<String> codes1 = result1.get("codes", List.class);
        assertNotNull(codes1, "提取的编号列表不应为空");
        assertEquals(3, codes1.size(), "应提取到3个编号");
        assertEquals("01", codes1.get(0), "第一个编号应为01");
        assertEquals("02", codes1.get(1), "第二个编号应为02");
        assertEquals("03", codes1.get(2), "第三个编号应为03");
        
        // 测试只有一个编号的情况
        String fileName2 = "20230202-000002-[RG_02](05)-样品.BMP";
        JSONObject result2 = rgSyncService.extractModelAndCodes(fileName2);
        
        assertEquals("RG_02", result2.getStr("model"), "提取的型号应为RG_02");
        
        List<String> codes2 = result2.get("codes", List.class);
        assertEquals(1, codes2.size(), "应提取到1个编号");
        assertEquals("05", codes2.get(0), "编号应为05");
        
        // 测试异常情况 - 缺少型号
        String fileName3 = "20230303-000003-(07,08)-测试.BMP";
        JSONObject result3 = rgSyncService.extractModelAndCodes(fileName3);
        
        assertEquals("", result3.getStr("model"), "缺少型号时应返回空字符串");
        
        List<String> codes3 = result3.get("codes", List.class);
        assertEquals(2, codes3.size(), "应提取到2个编号");
        
        // 测试异常情况 - 缺少编号
        String fileName4 = "20230404-000004-[RG_04]-测试.BMP";
        JSONObject result4 = rgSyncService.extractModelAndCodes(fileName4);
        
        assertEquals("RG_04", result4.getStr("model"), "提取的型号应为RG_04");
        
        List<String> codes4 = result4.get("codes", List.class);
        assertTrue(codes4.isEmpty(), "缺少编号时应返回空列表");
        
        log.info("文件名提取测试通过");
    }
}
