package com.cirpoint.service.tools;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工时测试数据生成器测试类
 * 验证重复记录生成功能
 */
public class WorkTimeTestDataGeneratorTest {

    private WorkTimeTestDataGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new WorkTimeTestDataGenerator();
        // 设置临时目录
        ReflectionTestUtils.setField(generator, "tempDir", System.getProperty("java.io.tmpdir"));
    }

    @Test
    @DisplayName("测试基本测试数据生成")
    public void testBasicTestDataGeneration() {
        byte[] excelData = generator.generateTestExcel(3, 2);
        
        assertNotNull(excelData, "生成的Excel数据不应为空");
        assertTrue(excelData.length > 0, "生成的Excel数据应有内容");
    }

    @Test
    @DisplayName("测试参数边界值")
    public void testParameterBoundaries() {
        // 测试最小值
        byte[] excelData1 = generator.generateTestExcel(1, 1);
        assertNotNull(excelData1, "最小参数应能正常生成数据");
        
        // 测试超出范围的值会被调整
        byte[] excelData2 = generator.generateTestExcel(100, 50); // 超出员工数量和天数限制
        assertNotNull(excelData2, "超出范围的参数应被调整后正常生成数据");
    }

    @Test
    @DisplayName("测试异常参数处理")
    public void testInvalidParameters() {
        // 测试无效参数
        assertThrows(IllegalArgumentException.class, () -> {
            generator.generateTestExcel(0, 5);
        }, "员工数量为0应抛出异常");
        
        assertThrows(IllegalArgumentException.class, () -> {
            generator.generateTestExcel(5, 0);
        }, "天数为0应抛出异常");
        
        assertThrows(IllegalArgumentException.class, () -> {
            generator.generateTestExcel(-1, 5);
        }, "负数员工数量应抛出异常");
    }

    @Test
    @DisplayName("测试生成的数据包含重复记录场景")
    public void testDuplicateRecordScenarios() {
        // 生成包含重复记录的测试数据
        byte[] excelData = generator.generateTestExcel(6, 3); // 至少6个员工以覆盖所有重复记录场景
        
        assertNotNull(excelData, "包含重复记录的测试数据应能正常生成");
        assertTrue(excelData.length > 1000, "包含重复记录的数据应该比较大");
        
        // 注意：这里我们只能测试数据生成不出错，具体的重复记录验证需要在实际使用中测试
        System.out.println("生成的测试数据大小: " + excelData.length + " 字节");
    }

    @Test
    @DisplayName("测试多次生成数据的一致性")
    public void testConsistentGeneration() {
        byte[] data1 = generator.generateTestExcel(2, 1);
        byte[] data2 = generator.generateTestExcel(2, 1);
        
        assertNotNull(data1);
        assertNotNull(data2);
        
        // 由于包含随机元素和时间戳，两次生成的数据不会完全相同
        // 但都应该是有效的Excel数据
        assertTrue(data1.length > 0);
        assertTrue(data2.length > 0);
    }
}
