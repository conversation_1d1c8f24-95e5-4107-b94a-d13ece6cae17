package com.cirpoint.service.tools;

import cn.hutool.core.collection.CollUtil;
import com.cirpoint.constant.WorkTimeConstants;
import com.cirpoint.model.worktime.AttendanceRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 门禁数据去重处理器
 * 用于处理员工在短时间内多次刷卡产生的重复记录
 * 去重规则：
 * - 同一员工在同一场所、同一状态（进入/离开）下
 * - 在2分钟（120秒）时间窗口内存在多条刷卡记录
 * - 仅保留时间最新的一条记录，删除其余重复记录
 */
@Slf4j
@Component
public class DuplicateRecordProcessor {

    /**
     * 时间窗口阈值（秒）- 2分钟内的记录视为重复
     */
    private static final long TIME_WINDOW_SECONDS = WorkTimeConstants.DUPLICATE_TIME_WINDOW_SECONDS;

    /**
     * 移除重复的门禁记录
     * @param records 原始门禁记录列表
     * @return 去重后的门禁记录列表
     * @throws IllegalArgumentException 当输入参数为null时
     */
    public List<AttendanceRecord> removeDuplicateRecords(List<AttendanceRecord> records) {
        if (records == null) {
            throw new IllegalArgumentException("门禁记录列表不能为null");
        }
        if (CollUtil.isEmpty(records)) {
            return new ArrayList<>();
        }

        log.info("开始门禁数据去重处理，原始记录数: {}", records.size());

        // 1. 按分组键对记录进行分组
        Map<String, List<AttendanceRecord>> groupedRecords = groupRecordsByKey(records);

        // 2. 对每个分组进行去重处理
        List<AttendanceRecord> deduplicatedRecords = new ArrayList<>();
        int totalRemovedCount = 0;

        for (Map.Entry<String, List<AttendanceRecord>> entry : groupedRecords.entrySet()) {
			List<AttendanceRecord> groupRecords = entry.getValue();
            
            List<AttendanceRecord> deduplicatedGroup = deduplicateGroup(groupRecords);
            deduplicatedRecords.addAll(deduplicatedGroup);
            
            int removedCount = groupRecords.size() - deduplicatedGroup.size();
            totalRemovedCount += removedCount;
        }

        // 3. 按原始时间顺序重新排序
        deduplicatedRecords.sort(Comparator.comparing(AttendanceRecord::getEventTime));

        log.info("门禁数据去重处理完成，原始记录数: {}, 去重后记录数: {}, 移除重复记录数: {}", 
                records.size(), deduplicatedRecords.size(), totalRemovedCount);

        return deduplicatedRecords;
    }

    /**
     * 按分组键对记录进行分组
     * 分组键格式：员工姓名+工号|场所|方向
     * @param records 门禁记录列表
     * @return 分组后的记录Map
     */
    private Map<String, List<AttendanceRecord>> groupRecordsByKey(List<AttendanceRecord> records) {
        return records.stream()
                .filter(Objects::nonNull)
                .filter(AttendanceRecord::isValid)
                .collect(Collectors.groupingBy(
                    this::generateGroupKey,
                    LinkedHashMap::new,
                    Collectors.toList()
                ));
    }

    /**
     * 生成分组键
     * @param record 门禁记录
     * @return 分组键字符串
     */
    private String generateGroupKey(AttendanceRecord record) {
        // 生成员工复合标识符：姓名+工号（如果工号存在）
        String employeeCompositeKey = getEmployeeCompositeKey(record);

        return String.format("%s%s%s%s%s",
                employeeCompositeKey,
                WorkTimeConstants.DUPLICATE_GROUP_KEY_SEPARATOR,
                record.getLocation(),
                WorkTimeConstants.DUPLICATE_GROUP_KEY_SEPARATOR,
                record.getDirection());
    }

    /**
     * 获取员工复合标识符
     * @param record 门禁记录
     * @return 员工复合标识符（姓名+工号）
     */
    private String getEmployeeCompositeKey(AttendanceRecord record) {
        String employeeName = record.getEmployeeName();
        String employeeId = record.getEmployeeId();

        if (employeeName == null) {
            return "";
        }
        if (employeeId != null && !employeeId.trim().isEmpty()) {
            return employeeName + "+" + employeeId;
        } else {
            return employeeName;
        }
    }

    /**
     * 对单个分组进行去重处理
     * @param groupRecords 同一分组的记录列表
     * @return 去重后的记录列表
     */
    private List<AttendanceRecord> deduplicateGroup(List<AttendanceRecord> groupRecords) {
        if (CollUtil.isEmpty(groupRecords)) {
            return new ArrayList<>();
        }

        if (groupRecords.size() == 1) {
            return new ArrayList<>(groupRecords);
        }

        // 按时间升序排序
        groupRecords.sort(Comparator.comparing(AttendanceRecord::getEventTime));

        List<AttendanceRecord> result = new ArrayList<>(groupRecords.size());
        AttendanceRecord lastKeptRecord = null;

        for (AttendanceRecord currentRecord : groupRecords) {
            if (lastKeptRecord == null) {
                // 第一条记录直接保留
                result.add(currentRecord);
                lastKeptRecord = currentRecord;
            } else {
                // 计算与上一条保留记录的时间间隔
                Duration timeDiff = Duration.between(
                    lastKeptRecord.getEventTime(),
                    currentRecord.getEventTime()
                );

                if (timeDiff.getSeconds() > TIME_WINDOW_SECONDS) {
                    // 时间间隔超过阈值，保留当前记录
                    result.add(currentRecord);
                    lastKeptRecord = currentRecord;
                }
                // 时间间隔在阈值内的记录被忽略（保留较早 的记录）
            }
        }

        return result;
    }

    /**
     * 获取去重统计信息
     * @param originalRecords 原始记录
     * @param deduplicatedRecords 去重后记录
     * @return 统计信息字符串
     */
    public String getDeduplicationStats(List<AttendanceRecord> originalRecords, 
                                       List<AttendanceRecord> deduplicatedRecords) {
        int originalCount = originalRecords != null ? originalRecords.size() : 0;
        int deduplicatedCount = deduplicatedRecords != null ? deduplicatedRecords.size() : 0;
        int removedCount = originalCount - deduplicatedCount;
        
        return String.format("去重统计 - 原始记录: %d, 去重后记录: %d, 移除重复记录: %d", 
                           originalCount, deduplicatedCount, removedCount);
    }
}
