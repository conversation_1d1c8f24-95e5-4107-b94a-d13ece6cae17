# 服务器端口
server.port=7081

# 默认使用开发环境
spring.profiles.active=dev

# 公共配置
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# 应用名称
spring.application.name=file-handle

#Thingworx key配置
thingworx.key=8ea8ace9-1167-4417-939f-a7ddc58d9429

# 文件上传配置
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB

# 增加Tomcat请求头大小限制
server.max-http-header-size=100MB

# 表格列配置
table.column.design-value=2
table.column.tolerance=4
table.column.measured-value=7
table.column.result=9
table.row.data-start=5

# 监控配置基础设置
management.metrics.tags.application=FileHandle

# 日志配置
logging.level.root=INFO
logging.level.com.cirpoint=DEBUG

# 日志查看器配置
log.viewer.enabled=true
log.viewer.log-path=logs
log.viewer.current-log-file=application.log
log.viewer.archived-path=logs/archived
log.viewer.max-sse-clients=50
log.viewer.push-interval-ms=2000
log.viewer.default-page-size=100
log.viewer.max-page-size=10000
log.viewer.read-buffer-size=8192
log.viewer.sse-timeout-ms=1800000
log.viewer.max-log-line-cache=100
log.viewer.max-search-results=1000

#Thingworx 配置
thingworx.domain=http://twx:8011

# PDF字体路径
app.pdf-font-path=C:/Program Files/Apache Software Foundation/Tomcat 8.5/webapps/FileHandle/pdfFont
excel.tpl.path=C:/Program Files/Apache Software Foundation/Tomcat 8.5/webapps/FileHandle/excelTpl

file.upload.path=D:/DataPkgFile/
file.temp.path=E:/DpkgTemp/
push.temp.path=E:/DpkgPushTemp/

# 电缆同步文件路径（使用正斜杠，确保UTF-8编码） F:/ThingworxDataCollect/07-质量技术处/070103-热敏电阻/
dl.sync.path=F:/ThingworxDataCollect/07-\u8D28\u91CF\u6280\u672F\u5904/070103-\u70ED\u654F\u7535\u963B/

# 热管照片同步文件路径（使用正斜杠，确保UTF-8编码） F:/ThingworxDataCollect/04-热控事业部/040401-热管探伤设备/
rg.sync.path=F:/ThingworxDataCollect/04-\u70ed\u63a7\u4e8b\u4e1a\u90e8/040401-\u70ed\u7ba1\u63a2\u4f24\u8bbe\u5907/

# 在线编辑器配置
# 注意：editor.base.path 已弃用，请使用多路径管理功能

# 终端相关配置
# 是否启用终端功能
terminal.enabled=true
# 禁止执行的命令列表，多个命令用逗号分隔
terminal.blocked.commands=rm -rf,mkfs,dd,:(){ :|:& };:,> /dev/sda,chmod -R 777 /,format,del /f /s /q,deltree,fdisk,shutdown,reboot,halt,init 0,init 6

# 监控配置
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always

# 启用定时任务
spring.task.scheduling.pool.size=5

# SpringDoc OpenAPI配置
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.tags-sorter=alpha
springdoc.swagger-ui.operations-sorter=method

# API文档配置
springdoc.api-docs.path=/v3/api-docs
springdoc.packages-to-scan=com.cirpoint.controller
springdoc.paths-to-match=/**

# 电缆同步定时任务配置
dl.sync.cron=0 30 1 * * ?
# 电缆同步日志路径
dl.sync.log.path=dl-report-sync

# 热管照片同步定时任务配置
rg.sync.cron=0 50 1 * * ?
# 热管照片同步日志路径
rg.sync.log.path=rg-photo-sync

# 档案推送日志目录配置
archive.log.dir=logs/archive-push

# 质测数据库文件存放位置
quality.test.db.path=C:/TestOut/test.mdb

#质测数据同步定时任务配置
quality.test.sync.cron=0 0 1 * * ?

#质测数据同步日志路径
quality.test.sync.log.path=logs/quality-test-sync
