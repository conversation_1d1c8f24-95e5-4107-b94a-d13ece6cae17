/* DOCX批量转PDF工具样式 */

.docx2pdf-container {
    padding: 20px;
}

.tool-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e6e6e6;
}

/* 上传区域样式 */
.upload-area {
    text-align: center;
}

.upload-drag-area {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    min-height: 200px;
    border: 2px dashed #e2e2e2;
    border-radius: 4px;
    cursor: pointer;
    transition: border-color 0.3s, background-color 0.3s;
}

.upload-drag-area:hover {
    border-color: #1E9FFF;
    background-color: #f9f9f9;
}

.upload-drag-area.drag-over {
    border-color: #1E9FFF;
    background-color: #f0f8ff;
}

.upload-drag-area i {
    font-size: 48px;
    color: #999;
    margin-bottom: 15px;
}

.upload-drag-area p {
    margin: 5px 0;
    color: #333;
}

.upload-tip {
    font-size: 12px;
    color: #999;
}

.upload-input {
    display: none;
}

.upload-actions {
    margin-top: 15px;
}

/* 状态区域样式 */
.uploaded-file-info {
    margin-bottom: 20px;
}

.file-status {
    padding: 2px 8px;
    border-radius: 2px;
    font-size: 12px;
    color: #fff;
}

.file-status.waiting {
    background-color: #999;
}

.file-status.processing {
    background-color: #1E9FFF;
}

.file-status.success {
    background-color: #5FB878;
}

.file-status.error {
    background-color: #FF5722;
}

.progress-info {
    margin: 20px 0;
}

.progress-text {
    margin-top: 5px;
    text-align: center;
    color: #666;
}

.operation-buttons {
    text-align: center;
    margin-top: 20px;
}

/* 结果区域样式 */
.result-summary {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.result-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.result-label {
    font-weight: bold;
    color: #333;
}

.result-value {
    font-size: 16px;
    color: #666;
}

.result-value.success {
    color: #5FB878;
}

.result-value.error {
    color: #FF5722;
}

.download-area {
    text-align: center;
    margin: 20px 0;
    padding: 20px;
    border-top: 1px solid #f0f0f0;
}

.error-info {
    margin-top: 20px;
}

.error-list {
    max-height: 200px;
    overflow-y: auto;
}

.error-item {
    margin-bottom: 10px;
    padding: 10px;
    background-color: #fff5f5;
    border-left: 2px solid #FF5722;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .upload-drag-area {
        min-height: 150px;
    }
    
    .result-summary {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
} 