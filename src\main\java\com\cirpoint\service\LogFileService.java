package com.cirpoint.service;

import com.cirpoint.config.LogViewerConfig;
import com.cirpoint.model.LogEntry;
import com.cirpoint.model.LogFile;
import com.cirpoint.model.PageResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 日志文件管理服务
 * 负责日志文件的发现、读取、搜索和下载功能
 */
@Service
public class LogFileService {
    
    private static final Logger logger = LoggerFactory.getLogger(LogFileService.class);
    
    @Autowired
    private LogViewerConfig config;
    
    /**
     * 获取所有日志文件信息
     */
    public List<LogFile> getAllLogFiles() {
        List<LogFile> logFiles = new ArrayList<>();
        
        try {
            // 1. 添加当前日志文件
            addCurrentLogFile(logFiles);
            
            // 2. 发现归档日志文件
            addArchivedLogFiles(logFiles);
            
            // 3. 按最后修改时间倒序排序
            logFiles.sort((f1, f2) -> f1.compareByLastModified(f2));
            
            logger.debug("Found {} log files", logFiles.size());
            
        } catch (Exception e) {
            logger.error("Error discovering log files", e);
        }
        
        return logFiles;
    }
    
    /**
     * 添加当前日志文件
     */
    private void addCurrentLogFile(List<LogFile> logFiles) {
        File currentLog = new File(config.getCurrentLogFilePath());
        if (currentLog.exists() && currentLog.isFile()) {
            LogFile logFile = createLogFile(currentLog, LogFile.LogFileType.CURRENT);
            logFiles.add(logFile);
            logger.debug("Added current log file: {}", currentLog.getName());
        } else {
            logger.warn("Current log file not found: {}", config.getCurrentLogFilePath());
        }
    }
    
    /**
     * 添加归档日志文件
     */
    private void addArchivedLogFiles(List<LogFile> logFiles) {
        File archivedDir = new File(config.getArchivedLogPath());
        if (!archivedDir.exists() || !archivedDir.isDirectory()) {
            logger.debug("Archived log directory not found: {}", config.getArchivedLogPath());
            return;
        }
        
        File[] archivedFiles = archivedDir.listFiles((dir, name) -> 
            name.startsWith("application.") && name.endsWith(".log"));
        
        if (archivedFiles != null) {
            for (File file : archivedFiles) {
                LogFile logFile = createLogFile(file, LogFile.LogFileType.ARCHIVED);
                logFiles.add(logFile);
            }
            logger.debug("Added {} archived log files", archivedFiles.length);
        }
    }
    
    /**
     * 创建LogFile对象
     */
    private LogFile createLogFile(File file, LogFile.LogFileType type) {
        LogFile logFile = new LogFile();
        logFile.setFileName(file.getName());
        logFile.setFileSize(file.length());
        logFile.setLastModified(new Date(file.lastModified()));
        logFile.setArchived(type == LogFile.LogFileType.ARCHIVED);
        logFile.setFilePath(file.getAbsolutePath());
        logFile.setType(type);
        logFile.setDisplayName(logFile.generateDisplayName());
        return logFile;
    }
    
    /**
     * 分页读取指定日志文件
     */
    public PageResult<LogEntry> readLogFileWithPagination(String fileName, int page, int size) {
        // 参数验证
        if (!isValidLogFile(fileName)) {
            logger.warn("Invalid log file requested: {}", fileName);
            return PageResult.empty(page, size);
        }
        
        File logFile = getLogFile(fileName);
        if (!logFile.exists()) {
            logger.warn("Log file not found: {}", fileName);
            return PageResult.empty(page, size);
        }
        
        // 安全的分页大小
        int safeSize = config.getSafePageSize(size);
        
        try {
            // 读取所有日志行
            List<LogEntry> allLogs = readAllLogsFromFile(logFile);
            
            // 分页处理
            return paginateResults(allLogs, page, safeSize);
            
        } catch (Exception e) {
            logger.error("Error reading log file: " + fileName, e);
            return PageResult.empty(page, safeSize);
        }
    }
    
    /**
     * 搜索日志内容
     */
    public PageResult<LogEntry> searchInLogFile(String fileName, String keyword, 
                                               String level, int page, int size) {
        // 参数验证
        if (!isValidLogFile(fileName)) {
            logger.warn("Invalid log file for search: {}", fileName);
            return PageResult.empty(page, size);
        }
        
        File logFile = getLogFile(fileName);
        if (!logFile.exists()) {
            logger.warn("Log file not found for search: {}", fileName);
            return PageResult.empty(page, size);
        }
        
        // 安全的分页大小
        int safeSize = config.getSafePageSize(size);
        
        try {
            List<LogEntry> matchedLogs = searchLogsInFile(logFile, keyword, level);
            
            // 限制搜索结果数量
            if (matchedLogs.size() > config.getMaxSearchResults()) {
                logger.warn("Search results truncated from {} to {}", 
                    matchedLogs.size(), config.getMaxSearchResults());
                matchedLogs = matchedLogs.subList(0, config.getMaxSearchResults());
            }
            
            return paginateResults(matchedLogs, page, safeSize);
            
        } catch (Exception e) {
            logger.error("Error searching in log file: " + fileName, e);
            return PageResult.empty(page, safeSize);
        }
    }
    
    /**
     * 在文件中搜索日志
     */
    private List<LogEntry> searchLogsInFile(File logFile, String keyword, String level) 
            throws IOException {
        List<LogEntry> matchedLogs = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(logFile), StandardCharsets.UTF_8))) {
            
            String line;
            while ((line = reader.readLine()) != null) {
                LogEntry entry = new LogEntry(line);
                
                // 级别过滤
                if (StringUtils.hasText(level) && !level.equalsIgnoreCase(entry.getLevel())) {
                    continue;
                }
                
                // 关键词搜索（不区分大小写）
                if (StringUtils.hasText(keyword)) {
                    String searchText = entry.getRawLine().toLowerCase();
                    String searchKeyword = keyword.toLowerCase();
                    if (!searchText.contains(searchKeyword)) {
                        continue;
                    }
                }
                
                matchedLogs.add(entry);
            }
        }
        
        return matchedLogs;
    }
    
    /**
     * 读取文件中的所有日志
     */
    private List<LogEntry> readAllLogsFromFile(File logFile) throws IOException {
        List<LogEntry> logs = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(logFile), StandardCharsets.UTF_8))) {
            
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    LogEntry entry = new LogEntry(line);
                    logs.add(entry);
                }
            }
        }
        
        return logs;
    }
    
    /**
     * 分页处理结果
     */
    private PageResult<LogEntry> paginateResults(List<LogEntry> allResults, int page, int size) {
        long total = allResults.size();
        int start = page * size;
        
        if (start >= total) {
            return PageResult.empty(page, size);
        }
        
        int end = Math.min(start + size, (int) total);
        List<LogEntry> pageContent = allResults.subList(start, end);
        
        return new PageResult<>(pageContent, page, size, total);
    }
    
    /**
     * 获取日志文件的Resource对象（用于下载）
     */
    public Resource getLogFileResource(String fileName) {
        if (!isValidLogFile(fileName)) {
            logger.warn("Invalid log file for download: {}", fileName);
            return null;
        }
        
        File logFile = getLogFile(fileName);
        if (!logFile.exists()) {
            logger.warn("Log file not found for download: {}", fileName);
            return null;
        }
        
        return new FileSystemResource(logFile);
    }
    
    /**
     * 验证文件路径安全性
     */
    private boolean isValidLogFile(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return false;
        }
        
        // 防止路径遍历攻击
        if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
            logger.warn("Potential path traversal attack detected: {}", fileName);
            return false;
        }
        
        // 检查文件扩展名
        if (!fileName.endsWith(".log")) {
            logger.warn("Invalid file extension: {}", fileName);
            return false;
        }
        
        // 检查文件名格式
        if (!fileName.startsWith("application.")) {
            logger.warn("Invalid log file name format: {}", fileName);
            return false;
        }
        
        return true;
    }
    
    /**
     * 根据文件名获取File对象
     */
    private File getLogFile(String fileName) {
        // 当前日志文件
        if (config.getCurrentLogFile().equals(fileName)) {
            return new File(config.getCurrentLogFilePath());
        }
        
        // 归档日志文件
        return new File(config.getArchivedLogPath(), fileName);
    }
    
    /**
     * 获取日志文件统计信息
     */
    public Map<String, Object> getLogFileStats(String fileName) {
        Map<String, Object> stats = new HashMap<>();
        
        if (!isValidLogFile(fileName)) {
            return stats;
        }
        
        File logFile = getLogFile(fileName);
        if (!logFile.exists()) {
            return stats;
        }
        
        try {
            long lineCount = 0;
            Map<String, Long> levelCounts = new HashMap<>();
            
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(new FileInputStream(logFile), StandardCharsets.UTF_8))) {
                
                String line;
                while ((line = reader.readLine()) != null) {
                    if (!line.trim().isEmpty()) {
                        lineCount++;
                        LogEntry entry = new LogEntry(line);
                        String level = entry.getLevel();
                        levelCounts.put(level, levelCounts.getOrDefault(level, 0L) + 1);
                    }
                }
            }
            
            stats.put("totalLines", lineCount);
            stats.put("levelCounts", levelCounts);
            stats.put("fileSize", logFile.length());
            stats.put("lastModified", new Date(logFile.lastModified()));
            
        } catch (Exception e) {
            logger.error("Error getting stats for log file: " + fileName, e);
        }
        
        return stats;
    }
    
    /**
     * 获取日志级别列表
     */
    public List<String> getAvailableLogLevels() {
        return Arrays.asList("DEBUG", "INFO", "WARN", "ERROR", "TRACE");
    }
    
    /**
     * 检查文件是否可访问
     */
    public boolean isFileAccessible(String fileName) {
        if (!isValidLogFile(fileName)) {
            return false;
        }
        
        File logFile = getLogFile(fileName);
        return logFile.exists() && logFile.canRead();
    }
} 