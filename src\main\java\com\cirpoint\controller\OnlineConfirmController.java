package com.cirpoint.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.model.PdfOptions;
import com.cirpoint.service.OnlineConfirmService;
import com.cirpoint.util.FileDownloadUtil;
import com.cirpoint.util.FileUploadUtil;
import java.io.File;
import java.io.IOException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/online")
public class OnlineConfirmController {

	private final OnlineConfirmService onlineConfirmService;

	@Autowired
	public OnlineConfirmController(OnlineConfirmService onlineConfirmService) {
		this.onlineConfirmService = onlineConfirmService;
	}

	/**
	 * 导入Excel文件
	 *
	 * @param file     Excel文件
	 * @param id       节点ID
	 * @param thing    事物名称
	 * @param saveUser 保存用户
	 * @return 导入结果
	 */
	@PostMapping("/import/excel")
	public ResponseEntity<?> importExcel(@RequestParam("uploadFile") MultipartFile file,
										 @RequestParam("id") String id,
										 @RequestParam("thing") String thing,
										 @RequestParam("saveUser") String saveUser) {
		return ResponseEntity.ok(onlineConfirmService.importExcel(file, id, thing, saveUser));
	}

	/**
	 * 导入PDF文件
	 *
	 * @param file  PDF文件
	 * @param id    节点ID
	 * @param thing 事物名称
	 * @return 导入结果
	 */
	@PostMapping("/import/pdf")
	public ResponseEntity<?> importPdf(@RequestParam("uploadFile") MultipartFile file,
									   @RequestParam("id") String id,
									   @RequestParam("thing") String thing) {
		return ResponseEntity.ok(onlineConfirmService.importPdf(file, id, thing));
	}

	/**
	 * 导入表格数据
	 *
	 * @param file      Excel文件
	 * @param pid       父级ID
	 * @param treeId    树ID
	 * @param tableName 表格名称
	 * @param tableNum  表格编号
	 * @param type      类型
	 * @param level     级别
	 * @param thing     事物名称
	 * @param saveUser  保存用户
	 * @return 导入结果
	 */
	@PostMapping("/import/table")
	public ResponseEntity<?> importTable(@RequestParam("uploadFile") MultipartFile file,
										 @RequestParam("pid") String pid,
										 @RequestParam("treeId") String treeId,
										 @RequestParam("tableName") String tableName,
										 @RequestParam("tableNum") String tableNum,
										 @RequestParam("type") String type,
										 @RequestParam("level") String level,
										 @RequestParam("thing") String thing,
										 @RequestParam(value = "security", defaultValue = "1") String security,
										 @RequestParam("saveUser") String saveUser) {
		JSONObject result = onlineConfirmService.importTable(file, pid, treeId, tableName,
				tableNum, type, level, thing, security, saveUser);
		return ResponseEntity.ok(result);
	}

	/**
	 * 批量导入表格数据
	 *
	 * @param file      上传的Excel文件
	 * @param extraData 额外的JSON数据
	 * @return 导入结果
	 */
	@PostMapping("/batch/import/table")
	public ResponseEntity<JSONObject> batchImportTable(@RequestPart("file") MultipartFile file,
													   @RequestPart("extraData") String extraData) throws Exception {
		return ResponseEntity.ok(onlineConfirmService.batchImportTable(file, extraData));
	}

	/**
	 * 导入大型ZIP文件
	 *
	 * @param file      上传的文件
	 * @param chunk     当前分片序号
	 * @param chunks    总分片数
	 * @param name      文件名
	 * @param reqIdent  请求标识
	 * @param extraData 额外数据
	 * @return 导入结果
	 */
	@SuppressWarnings("MismatchedQueryAndUpdateOfCollection")
	@PostMapping("/import/big/zip")
	public ResponseEntity<?> importBigZip(
			@RequestParam(value = "file") MultipartFile file,
			@RequestParam(value = "chunk", required = false) Integer chunk,
			@RequestParam(value = "chunks", required = false) Integer chunks,
			@RequestParam("name") String name,
			@RequestParam("reqIdent") String reqIdent,
			@RequestParam("extraData") String extraData) {

		// 处理文件上传
		JSONObject uploadResult = FileUploadUtil.handleFileUpload(file, chunk, chunks, name, reqIdent, extraData);

		// 如果上传完成且成功，则处理导入
		if (uploadResult.getBool("success") && uploadResult.getBool("isAll", false)) {
			JSONObject extraDataObj = new JSONObject(extraData);
			JSONObject result = onlineConfirmService.importBigZip(
					uploadResult.getStr("file"),
					extraDataObj.getStr("thing"),
					extraDataObj.getStr("tableId"),
					extraDataObj.getStr("tablePId"),
					extraDataObj.getStr("srcType")
			);
			return ResponseEntity.ok(result);
		}

		return ResponseEntity.ok(uploadResult);
	}

	/**
	 * 导出在线确认数据到Excel
	 *
	 * @return Excel文件
	 */
	@PostMapping("/export/excel")
	public ResponseEntity<?> exportExcel(@RequestParam("id") String id,
										 @RequestParam("thing") String thing) {
		return FileDownloadUtil.fileResponseAndDelete(onlineConfirmService.exportToExcel(id, thing));
	}

	/**
	 * 导出PDF文件
	 *
	 * @param id      节点ID
	 * @param thing   事物名称
	 * @param options PDF导出选项对象
	 * @return PDF文件
	 */
	@PostMapping("/export/pdf")
	public ResponseEntity<?> exportPdf(@RequestParam("id") String id,
									   @RequestParam("thing") String thing,
									   @ModelAttribute PdfOptions options) {
		// 如果options为null，创建默认选项对象
		if (options == null) {
			options = new PdfOptions();
		}

		// 调用服务导出PDF
		JSONObject result = onlineConfirmService.exportToPdf(id, thing, options);
		return FileDownloadUtil.fileResponseAndDelete(result.getStr("data"));
	}

	/**
	 * 从PDF中提取页码信息
	 *
	 * @param pdfPath PDF文件路径
	 * @param bTables B表信息的JSON字符串
	 * @return 页码信息
	 */
	@PostMapping("/extract/pdf/pages")
	public ResponseEntity<JSONObject> extractPdfPages(@RequestParam("pdfPath") String pdfPath,
													  @RequestParam("bTables") String bTables) {
		JSONObject result = onlineConfirmService.extractPdfPageNumbers(pdfPath, bTables);
		return ResponseEntity.ok(result);
	}

	/**
	 * 导出多个PDF
	 *
	 * @param id      节点ID
	 * @param pid     父节点ID
	 * @param thing   Thing名称
	 * @param options PDF导出选项对象
	 * @return PDF文件下载响应
	 */
	@PostMapping("/export/more/pdf")
	public ResponseEntity<?> exportMore(@RequestParam("id") String id,
										@RequestParam("pid") String pid,
										@RequestParam("thing") String thing,
										@ModelAttribute PdfOptions options) {
		// 如果options为null，创建默认选项对象
		if (options == null) {
			options = new PdfOptions();
		}

		String pdfPath = onlineConfirmService.exportMore(id, pid, thing, options);
		return FileDownloadUtil.fileResponseAndDelete(pdfPath);
	}

	/**
	 * 导出图片压缩包
	 *
	 * @param id    节点ID
	 * @param thing 事物名称
	 * @return ZIP文件
	 */
	@PostMapping("/export/img")
	public ResponseEntity<?> exportImg(@RequestParam("id") String id,
									   @RequestParam("thing") String thing) {
		return FileDownloadUtil.fileResponseAndDelete(onlineConfirmService.exportToImgZip(id, thing));
	}

	/**
	 * 导出型号的压缩包
	 *
	 * @param id    节点ID
	 * @param thing 事物名称
	 * @return 压缩文件
	 */
	@PostMapping("/export/zip")
	public ResponseEntity<?> exportZip(@RequestParam("id") String id,
									   @RequestParam("thing") String thing) {
		JSONObject result = onlineConfirmService.exportZip(id, thing);
		return FileDownloadUtil.fileResponseAndDelete(result.getStr("data"));
	}


	/**
	 * 生成压缩包
	 *
	 * @param downloadId 下载ID
	 * @param id         节点ID
	 * @param thing      事物名称
	 * @param creator    创建者
	 * @param module     模块
	 * @return 生成结果
	 */
	@PostMapping("/generate/zip")
	public ResponseEntity<?> generateZip(@RequestParam("downloadId") String downloadId,
										 @RequestParam("id") String id,
										 @RequestParam("thing") String thing,
										 @RequestParam("creator") String creator,
										 @RequestParam("module") String module) {

		onlineConfirmService.generateZip(downloadId, id, thing, creator, module);
		return ResponseEntity.ok("压缩包生成成功");
	}

	/**
	 * 生成多个PDF文件
	 *
	 * @param id         节点ID
	 * @param pid        父节点ID
	 * @param thing      事物名称
	 * @param creator    创建者
	 * @param module     模块名称
	 * @param downloadId 下载ID
	 * @param options    PDF导出选项对象
	 * @return 生成结果
	 */
	@PostMapping("/generate/more/pdf")
	public ResponseEntity<?> generateMorePdf(@RequestParam("id") String id,
											 @RequestParam("pid") String pid,
											 @RequestParam("thing") String thing,
											 @RequestParam("creator") String creator,
											 @RequestParam("module") String module,
											 @RequestParam("downloadId") String downloadId,
											 @ModelAttribute PdfOptions options) {
		// 如果options为null，创建默认选项对象
		if (options == null) {
			options = new PdfOptions();
		}

		JSONObject result = onlineConfirmService.generateMorePdf(id, pid, thing, creator, module, downloadId, options);
		return ResponseEntity.ok(result);
	}

	/**
	 * 导出所选节点以及所有子节点的pdf，将其打包成zip文件下载
	 *
	 * @param id      节点ID
	 * @param thing   事物名称
	 * @param name    文件名
	 * @param options PDF导出选项对象
	 * @return ResponseEntity包含下载文件
	 */
	@PostMapping("/export/pdf/zip")
	public ResponseEntity<?> exportPdfZip(
			@RequestParam("id") String id,
			@RequestParam("thing") String thing,
			@RequestParam("name") String name,
			@ModelAttribute PdfOptions options) {
		// 如果options为null，创建默认选项对象
		if (options == null) {
			options = new PdfOptions();
		}

		JSONObject result = onlineConfirmService.exportPdfZip(id, thing, options);
		return FileDownloadUtil.fileResponseAndDelete(result.getStr("data"), name + ".zip");
	}

	/**
	 * 导出所选节点以及所有子节点的Excel，将其打包成zip文件下载
	 *
	 * @param id    节点ID
	 * @param pid   父节点ID
	 * @param thing 事物名称
	 * @return 包含Excel压缩包的响应实体
	 * @throws IOException 如果文件处理出错
	 */
	@PostMapping("/export/more/excel")
	public ResponseEntity<?> exportExcelZip(
			@RequestParam("id") String id,
			@RequestParam("pid") String pid,
			@RequestParam("thing") String thing) throws IOException {

		File file = onlineConfirmService.exportExcelZip(id, pid, thing);
		return FileDownloadUtil.fileResponseAndDelete(file);
	}

	/**
	 * 导出工时统计
	 *
	 * @param id    节点ID
	 * @param name  文件名
	 * @param thing 事物名称
	 * @return 文件下载响应
	 */
	@PostMapping("/export/work/hours")
	public ResponseEntity<?> exportWorkHours(
			@RequestParam String id,
			@RequestParam String name,
			@RequestParam String thing) {
		JSONObject result = onlineConfirmService.exportWorkHours(id, name, thing);
		if (result.getBool("success")) {
			String filePath = result.getStr("data");
			return FileDownloadUtil.fileResponseAndDelete(filePath, name + ".xlsx");
		} else {
			return ResponseEntity.ok(result.toString());
		}
	}

	/**
	 * 处理Base64签名文件
	 *
	 * @param thing Base64编码的内容
	 * @param id    节点ID
	 * @return 处理结果
	 */
	@PostMapping("/deal/base64")
	public ResponseEntity<?> dealBase64(@RequestParam("thing") String thing,
										@RequestParam("id") String id) {
		JSONObject result = onlineConfirmService.dealBase64(thing, id);
		return ResponseEntity.ok(result);
	}

	/**
	 * 处理签名HTML并转换为元数据
	 *
	 * @param thing HTML内容
	 * @param id    节点ID
	 * @return 元数据数组
	 */
	@PostMapping("/deal/sign/html")
	public ResponseEntity<?> dealSignHtml(@RequestParam("thing") String thing,
										  @RequestParam("id") String id) {
		JSONArray result = onlineConfirmService.dealSignHtml(thing, id);
		return ResponseEntity.ok(result);
	}

	/**
	 * 处理Base64文件上传
	 *
	 * @param src Base64编码的字符串
	 * @return ResponseEntity
	 */
	@PostMapping("/base64/file")
	public ResponseEntity<JSONObject> handleBase64File(@RequestParam String src) {
		// 处理Base64字符串
		src = src.replaceAll(" ", "+");

		// 转换为图片文件
		String filePath = onlineConfirmService.convertBase64ToImage(src);

		// 返回结果
		return ResponseEntity.ok(JSONUtil.createObj().set("filePath", filePath));
	}

	/**
	 * 导出多个PDF并返回文件路径（专为Thingworx服务调用设计）
	 *
	 * @param id      节点ID
	 * @param pid     父节点ID
	 * @param thing   Thing名称
	 * @param options PDF导出选项对象
	 * @return 包含PDF文件路径的JSON对象
	 */
	@PostMapping("/export/more/pdf/path")
	public ResponseEntity<JSONObject> exportMorePdfPath(@RequestParam("id") String id,
														@RequestParam("pid") String pid,
														@RequestParam("thing") String thing,
														@ModelAttribute PdfOptions options) {
		// 如果options为null，创建默认选项对象
		if (options == null) {
			options = new PdfOptions();
		}

		// 导出PDF并获取文件路径
		String pdfPath = onlineConfirmService.exportMore(id, pid, thing, options);

		// 构建返回结果
		JSONObject result = new JSONObject();
		result.set("success", true);
		result.set("data", pdfPath);
		result.set("msg", "PDF导出成功");

		return ResponseEntity.ok(result);
	}

	/**
	 * 导出确认表列表
	 *
	 * @return Excel文件下载响应
	 */
	@PostMapping("/export/confirm/table/list")
	public ResponseEntity<?> exportConfirmTableList(
			@RequestParam("nodeName") String nodeName,
			@RequestParam("nodeId") String nodeId,
			@RequestParam("thing") String thing) {
		return FileDownloadUtil.fileResponseAndDelete(onlineConfirmService.exportConfirmTableList(nodeName, nodeId, thing));
	}
}
