/**
 * Pano2VR热点定位与节点切换监听脚本
 *
 * 此脚本用于：
 * 1. 接收来自父页面的定位指令并调用pano2vr的JavaScript API实现热点定位功能
 * 2. 监听全景图节点切换事件，支持多节点全景图功能
 *
 * <AUTHOR>
 * @date 2025-06-06
 * @version 2.0 - 增加多节点支持
 */

(function () {
    'use strict';

    // 全局变量
    var HOTSPOT_SKIN_ID = 'stand-alone'; // 热点皮肤ID常量
    var currentNodeId = null; // 当前节点ID
    var isNodeSwitchListenerInitialized = false; // 节点切换监听器是否已初始化
    var isHighlightActive = false; // 热点高亮状态
    var highlightTimer = null; // 高亮自动停止定时器
    var highlightStylesInjected = false; // 高亮样式是否已注入

    // 等待pano2vr加载完成
    var waitForPano = function (callback) {
        var checkInterval = setInterval(function () {
            // 检查pano对象是否存在且已初始化
            if (typeof pano !== 'undefined' && pano && typeof pano.moveTo === 'function') {
                clearInterval(checkInterval);
                callback();
            }
        }, 100); // 每100ms检查一次

        // 10秒超时
        setTimeout(function () {
            clearInterval(checkInterval);
        }, 10000);
    };

    // 获取当前节点ID
    var getCurrentNodeId = function () {
        try {
            // 检查pano对象是否存在且包含getCurrentNode方法
            return pano.getCurrentNode();
        } catch (error) {
            // 静默处理错误，避免控制台报错
        }
        // 默认返回node1
        return 'node1';
    };

    // 初始化节点切换监听器
    var initNodeSwitchListener = function () {
        if (isNodeSwitchListenerInitialized) {
            return; // 避免重复初始化
        }

        try {
            // 获取初始节点ID
            currentNodeId = getCurrentNodeId();

            // 向父页面发送初始节点信息
            notifyParentNodeSwitch(currentNodeId, 'initial');

            // 优先使用事件监听方式
            pano.on('changenode', function (event) {
                handleNodeSwitch(getCurrentNodeId());
            });
            isNodeSwitchListenerInitialized = true;

        } catch (error) {
            // 初始化节点切换监听器失败
        }
    };

    // 初始化热点点击监听器
    var initHotspotClickListener = function () {
        try {
            // 监听Pano2VR的节点切换事件，在每次节点切换时重新绑定热点点击事件
            if (typeof pano !== 'undefined' && pano && typeof pano.addListener === 'function') {
                pano.addListener('changenode', function (args) {
                    // 延迟一下确保热点已经加载完成
                    setTimeout(function () {
                        bindHotspotClickEvents();
                    }, 500);
                });
            }

            // 初始绑定热点点击事件
            setTimeout(function () {
                bindHotspotClickEvents();
            }, 1000);

        } catch (error) {
            // 初始化热点点击监听器失败
        }
    };

    // 绑定热点点击事件
    var bindHotspotClickEvents = function () {
        try {
            // 获取当前页面的所有热点
            if (typeof pano !== 'undefined' && pano) {
                // 尝试不同的热点访问方式
                var hotspots = null;
                if (pano.L && Array.isArray(pano.L)) {
                    hotspots = pano.L;
                }

                if (hotspots && Array.isArray(hotspots)) {
                    for (var i = 0; i < hotspots.length; i++) {
                        var hotspot = hotspots[i];

                        // 获取热点的DOM元素
                        if (hotspot.j.__div && hotspot.skinid === HOTSPOT_SKIN_ID) {
                            var divElement = hotspot.j.__div;

                            // 移除之前的点击事件监听器（避免重复绑定）
                            divElement.removeEventListener('click', hotspotClickHandler);

                            // 将热点信息存储到DOM元素的data属性中
                            divElement.hotspotData = {
                                id: hotspot.id || '',
                                title: hotspot.title || '',
                                description: hotspot.description || '',
                                skinid: hotspot.skinid || '',
                                url: hotspot.url || '',
                                target: hotspot.target || '',
                                pan: hotspot.pan || '',
                                tilt: hotspot.tilt || ''
                            };

                            // 绑定点击事件
                            divElement.addEventListener('click', hotspotClickHandler);

                            // 绑定右键菜单事件
                            divElement.addEventListener('contextmenu', hotspotRightClickHandler);
                        }
                    }
                }
            }
        } catch (error) {
            // 绑定热点点击事件失败
        }
    };

    // 热点点击事件处理器
    var hotspotClickHandler = function (event) {
        try {
            // 阻止事件冒泡，避免触发其他点击事件
            event.stopPropagation();

            // 如果当前正在高亮状态，停止高亮
            if (isHighlightActive) {
                stopHighlightAllHotspots();
            }

            // 获取热点数据
            var hotspotData = this.hotspotData;
            if (!hotspotData) {
                return;
            }

            // 向父页面发送热点点击消息
            var message = {
                type: 'hotspotClick',
                hotspot: hotspotData,
                nodeId: currentNodeId,
                timestamp: new Date().getTime()
            };

            window.parent.postMessage(message, '*');

        } catch (error) {
            // 热点点击处理失败
        }
    };

    // 热点右键菜单处理器
    var hotspotRightClickHandler = function (event) {
        try {
            // 阻止默认右键菜单
            event.preventDefault();
            event.stopPropagation();

            // 获取热点数据
            var hotspotData = this.hotspotData;
            if (!hotspotData) {
                return;
            }

            // 创建右键菜单
            showHotspotContextMenu(event, hotspotData);

        } catch (error) {
            // 右键菜单处理失败
        }
    };

    // 显示热点右键菜单
    var showHotspotContextMenu = function (event, hotspotData) {
        try {
            // 移除已存在的菜单
            var existingMenu = document.getElementById('hotspotContextMenu');
            if (existingMenu) {
                existingMenu.parentNode.removeChild(existingMenu);
            }

            // 创建菜单容器
            var menu = document.createElement('div');
            menu.id = 'hotspotContextMenu';
            menu.style.cssText =
                'position: fixed; ' +
                'background: white; ' +
                'border: 1px solid #ccc; ' +
                'border-radius: 4px; ' +
                'box-shadow: 0 2px 8px rgba(0,0,0,0.15); ' +
                'z-index: 10000; ' +
                'min-width: 120px; ' +
                'font-family: Arial, sans-serif; ' +
                'font-size: 14px;';

            // 创建删除菜单项
            var deleteItem = document.createElement('div');
            deleteItem.style.cssText =
                'padding: 8px 16px; ' +
                'cursor: pointer; ' +
                'color: #ff4757; ' +
                'border-bottom: 1px solid #eee;';
            deleteItem.textContent = '删除热点';
            deleteItem.onmouseover = function() { this.style.backgroundColor = '#f5f5f5'; };
            deleteItem.onmouseout = function() { this.style.backgroundColor = 'white'; };
            deleteItem.onclick = function() {
                // 隐藏菜单
                hideHotspotContextMenu();

                // 向父页面发送删除热点请求
                var message = {
                    type: 'hotspotRightClickDelete',
                    hotspot: hotspotData,
                    nodeId: currentNodeId,
                    timestamp: new Date().getTime()
                };
                window.parent.postMessage(message, '*');
            };

            // 创建取消菜单项
            var cancelItem = document.createElement('div');
            cancelItem.style.cssText =
                'padding: 8px 16px; ' +
                'cursor: pointer; ' +
                'color: #666;';
            cancelItem.textContent = '取消';
            cancelItem.onmouseover = function() { this.style.backgroundColor = '#f5f5f5'; };
            cancelItem.onmouseout = function() { this.style.backgroundColor = 'white'; };
            cancelItem.onclick = function() {
                hideHotspotContextMenu();
            };

            // 组装菜单
            menu.appendChild(deleteItem);
            menu.appendChild(cancelItem);

            // 设置菜单位置
            var x = event.clientX;
            var y = event.clientY;

            // 确保菜单不会超出视窗
            var menuWidth = 120;
            var menuHeight = 80;
            if (x + menuWidth > window.innerWidth) {
                x = window.innerWidth - menuWidth - 10;
            }
            if (y + menuHeight > window.innerHeight) {
                y = window.innerHeight - menuHeight - 10;
            }

            menu.style.left = x + 'px';
            menu.style.top = y + 'px';

            // 添加到页面
            document.body.appendChild(menu);

            // 点击其他地方隐藏菜单
            setTimeout(function() {
                document.addEventListener('click', hideHotspotContextMenu);
            }, 100);

        } catch (error) {
            // 显示右键菜单失败
        }
    };

    // 隐藏热点右键菜单
    var hideHotspotContextMenu = function () {
        try {
            var menu = document.getElementById('hotspotContextMenu');
            if (menu) {
                menu.parentNode.removeChild(menu);
            }
            document.removeEventListener('click', hideHotspotContextMenu);
        } catch (error) {
            // 隐藏菜单失败
        }
    };

    // ==================== 热点高亮功能 ====================

    /**
     * 注入高亮样式
     */
    var injectHighlightStyles = function () {
        if (highlightStylesInjected) {
            return;
        }

        try {
            var style = document.createElement('style');
            style.id = 'hotspotHighlightStyles';
            style.type = 'text/css';

            var css =
                '@keyframes hotspotBlink {' +
                '  0% { ' +
                '    transform: scale(1); ' +
                '    opacity: 1; ' +
                '    box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7); ' +
                '  }' +
                '  50% { ' +
                '    transform: scale(1.2); ' +
                '    opacity: 0.8; ' +
                '    box-shadow: 0 0 0 10px rgba(255, 0, 0, 0.3); ' +
                '  }' +
                '  100% { ' +
                '    transform: scale(1); ' +
                '    opacity: 1; ' +
                '    box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7); ' +
                '  }' +
                '}' +
                '.hotspot-highlight {' +
                '  animation: hotspotBlink 1s infinite !important;' +
                '  border: 3px solid #ff0000 !important;' +
                '  border-radius: 50% !important;' +
                '  background-color: rgba(255, 0, 0, 0.2) !important;' +
                '  z-index: 9999 !important;' +
                '}' +
                '.hotspot-highlight .ggskin_svg {' +
                '  filter: drop-shadow(0 0 8px #ff0000) !important;' +
                '}' +
                '.hotspot-highlight .ggskin_text {' +
                '  color: #ffffff !important;' +
                '  text-shadow: 0 0 4px #ff0000 !important;' +
                '  font-weight: bold !important;' +
                '}';

            if (style.styleSheet) {
                // IE8及以下版本
                style.styleSheet.cssText = css;
            } else {
                style.appendChild(document.createTextNode(css));
            }

            document.head.appendChild(style);
            highlightStylesInjected = true;

        } catch (error) {
            // 注入样式失败，静默处理
        }
    };

    /**
     * 高亮特定热点
     * @param {number} targetPan 目标热点的pan坐标
     * @param {number} targetTilt 目标热点的tilt坐标
     */
    var highlightSpecificHotspot = function (targetPan, targetTilt) {
        try {
            // 先停止所有高亮
            stopHighlightAllHotspots();

            // 注入高亮样式
            injectHighlightStyles();

            // 获取所有热点
            if (typeof pano !== 'undefined' && pano && pano.L && Array.isArray(pano.L)) {
                var hotspots = pano.L;
                var found = false;
                var targetPanNum = parseFloat(targetPan);
                var targetTiltNum = parseFloat(targetTilt);
                var tolerance = 0.1; // 容差0.1度
                var closestHotspot = null;
                var closestDistance = Infinity;

                for (var i = 0; i < hotspots.length; i++) {
                    var hotspot = hotspots[i];

                    // 检查热点基本属性（包容新热点可能没有skinid或skinid为空的情况）
                    var isValidHotspot = hotspot.j && hotspot.j.__div &&
                        (hotspot.skinid === HOTSPOT_SKIN_ID ||
                         hotspot.skinid === '' ||
                         hotspot.skinid === undefined);

                    if (isValidHotspot) {
                        // 尝试多种方式获取坐标
                        var hotspotPan = null;
                        var hotspotTilt = null;

                        // 方式1：直接从hotspot对象获取
                        if (hotspot.pan !== undefined && hotspot.tilt !== undefined) {
                            hotspotPan = parseFloat(hotspot.pan);
                            hotspotTilt = parseFloat(hotspot.tilt);
                        }

                        // 方式2：从hotspotData获取（新热点可能存储在这里）
                        if ((isNaN(hotspotPan) || isNaN(hotspotTilt)) &&
                            hotspot.j.__div.hotspotData) {
                            hotspotPan = parseFloat(hotspot.j.__div.hotspotData.pan);
                            hotspotTilt = parseFloat(hotspot.j.__div.hotspotData.tilt);
                        }

                        // 检查坐标是否有效
                        if (!isNaN(hotspotPan) && !isNaN(hotspotTilt)) {
                            var panDiff = Math.abs(hotspotPan - targetPanNum);
                            var tiltDiff = Math.abs(hotspotTilt - targetTiltNum);
                            var distance = Math.sqrt(panDiff * panDiff + tiltDiff * tiltDiff);

                            // 精确匹配
                            if (panDiff < tolerance && tiltDiff < tolerance) {
                                var divElement = hotspot.j.__div;

                                // 添加高亮类名
                                if (divElement.className.indexOf('hotspot-highlight') === -1) {
                                    divElement.className += ' hotspot-highlight';
                                    found = true;
                                }
                                break; // 找到目标热点后退出循环
                            }

                            // 记录最接近的热点（备用方案）
                            if (distance < closestDistance) {
                                closestDistance = distance;
                                closestHotspot = hotspot;
                            }
                        }
                    }
                }

                // 如果精确匹配失败，尝试高亮最接近的热点（距离在合理范围内）
                if (!found && closestHotspot && closestDistance < 1.0) {
                    var divElement = closestHotspot.j.__div;
                    if (divElement && divElement.className.indexOf('hotspot-highlight') === -1) {
                        divElement.className += ' hotspot-highlight';
                        found = true;
                    }
                }

                if (found) {
                    // 设置高亮状态
                    isHighlightActive = true;

                    // 设置5秒后自动停止高亮（单个热点高亮时间较短）
                    highlightTimer = setTimeout(function () {
                        stopHighlightAllHotspots();
                    }, 5000);

                    // 向父页面发送高亮开始消息
                    window.parent.postMessage({
                        type: 'hotspotHighlightStarted',
                        highlightedCount: 1,
                        targetPan: targetPan,
                        targetTilt: targetTilt,
                        nodeId: currentNodeId,
                        timestamp: new Date().getTime()
                    }, '*');
                }
            }
        } catch (error) {
            // 高亮失败，静默处理
        }
    };

    /**
     * 停止热点高亮
     */
    var stopHighlightAllHotspots = function () {
        try {
            if (!isHighlightActive) {
                return;
            }

            // 清除定时器
            if (highlightTimer) {
                clearTimeout(highlightTimer);
                highlightTimer = null;
            }

            // 获取所有热点并移除高亮类名
            if (typeof pano !== 'undefined' && pano && pano.L && Array.isArray(pano.L)) {
                var hotspots = pano.L;
                var stoppedCount = 0;

                for (var i = 0; i < hotspots.length; i++) {
                    var hotspot = hotspots[i];

                    if (hotspot.j && hotspot.j.__div) {
                        var divElement = hotspot.j.__div;

                        // 移除高亮类名
                        divElement.className = divElement.className.replace(/\s*hotspot-highlight/g, '');
                        stoppedCount++;
                    }
                }

                // 向父页面发送高亮停止消息
                window.parent.postMessage({
                    type: 'hotspotHighlightStopped',
                    stoppedCount: stoppedCount,
                    nodeId: currentNodeId,
                    timestamp: new Date().getTime()
                }, '*');
            }

            // 重置高亮状态
            isHighlightActive = false;

        } catch (error) {
            // 停止高亮失败，静默处理
        }
    };

    // 处理节点切换
    var handleNodeSwitch = function (newNodeId) {
        if (newNodeId && newNodeId !== currentNodeId) {
            // 节点切换时停止当前高亮
            if (isHighlightActive) {
                stopHighlightAllHotspots();
            }

            currentNodeId = newNodeId;
            notifyParentNodeSwitch(newNodeId, 'switch');
        }
    };

    // 向父页面发送节点切换消息
    var notifyParentNodeSwitch = function (nodeId, type) {
        try {
            var message = {
                type: 'nodeSwitch',
                nodeId: nodeId,
                switchType: type, // 'initial' 或 'switch'
                timestamp: new Date().getTime()
            };

            window.parent.postMessage(message, '*');
        } catch (error) {
            // 发送节点切换消息失败
        }
    };

    // 初始化消息监听器
    var initMessageListener = function () {
        window.addEventListener('message', function (event) {
            // 安全检查：验证消息来源（可根据需要调整）
            // if (event.origin !== window.location.origin) return;

            var data = event.data;
            if (!data || typeof data !== 'object') return;

            // 处理热点定位消息
            if (data.type === 'locateHotspot') {
                handleHotspotLocation(data);
            }
            // 处理热点更新消息
            else if (data.type === 'updateHotspot') {
                handleHotspotUpdate(data);
            }
            // 处理添加热点到视图消息
            else if (data.type === 'addHotspotToView') {
                handleAddHotspotToView(data);
            }
            // 处理从视图删除热点消息
            else if (data.type === 'removeHotspotFromView') {
                handleRemoveHotspotFromView(data);
            }
            // 处理停止高亮消息
            else if (data.type === 'stopHighlightAllHotspots') {
                stopHighlightAllHotspots();
            }
        }, false);
    };

    // 处理热点定位
    var handleHotspotLocation = function (data) {
        try {
            var pan = parseFloat(data.pan);
            var tilt = parseFloat(data.tilt);
            var speed = parseFloat(data.speed) || 10.0; // 默认快速
            var highlightTarget = data.highlightTarget || false; // 是否高亮目标热点

            // 验证数据
            if (isNaN(pan) || isNaN(tilt)) {
                return;
            }

            // 如果需要高亮目标热点，立即开始高亮（与视图移动同时进行）
            if (highlightTarget) {
                highlightSpecificHotspot(pan, tilt);
            }

            // 获取当前FOV，保持不变
            var currentFov = pano.getFov();

            // 使用moveTo方法实现平滑过渡
            // moveTo(pan, tilt, fov, speed, roll, projection)
            pano.moveTo(pan, tilt, currentFov, speed);

            // 向父页面发送定位完成消息
            setTimeout(function () {
                try {
                    window.parent.postMessage({
                        type: 'hotspotLocationComplete',
                        pan: pan,
                        tilt: tilt,
                        success: true,
                        highlighted: highlightTarget
                    }, '*');
                } catch (e) {
                    // 静默处理错误
                }
            }, (1000 / speed) + 100); // 根据速度计算等待时间

        } catch (error) {
            // 向父页面发送错误消息
            try {
                window.parent.postMessage({
                    type: 'hotspotLocationComplete',
                    success: false,
                    error: error.message
                }, '*');
            } catch (e) {
                // 静默处理错误
            }
        }
    };

    // 处理热点更新
    var handleHotspotUpdate = function (data) {
        try {
            if (!data || !data.hotspot) {
                return;
            }

            var updateInfo = data.hotspot;
            var pan = updateInfo.pan;
            var tilt = updateInfo.tilt;
            var newTitle = updateInfo.title;
            var newDescription = updateInfo.description;

            // 获取当前页面的所有热点
            if (typeof pano !== 'undefined' && pano && pano.L) {
                var hotspots = pano.L;
                var updated = false;

                for (var i = 0; i < hotspots.length; i++) {
                    var hotspot = hotspots[i];

                    // 通过坐标匹配热点（最可靠的方式）
                    if (hotspot.pan && hotspot.tilt &&
                        Math.abs(parseFloat(hotspot.pan) - parseFloat(pan)) < 0.01 &&
                        Math.abs(parseFloat(hotspot.tilt) - parseFloat(tilt)) < 0.01) {

                        // 更新热点属性
                        if (newTitle !== undefined && newTitle !== null) {
                            hotspot.title = newTitle;
                        }
                        if (newDescription !== undefined && newDescription !== null) {
                            hotspot.description = newDescription;
                        }

                        // 更新DOM元素中的显示文本
                        if (hotspot.j.__div) {
                            var divElement = hotspot.j.__div;

                            // 更新存储的数据
                            if (divElement.hotspotData) {
                                if (newTitle !== undefined && newTitle !== null) {
                                    divElement.hotspotData.title = newTitle;
                                }
                                if (newDescription !== undefined && newDescription !== null) {
                                    divElement.hotspotData.description = newDescription;
                                }
                            }

                            // 更新DOM中的文本显示并重新居中
                            var textElement = divElement.querySelector('.ggskin.ggskin_text > div');
                            if (textElement && newTitle !== undefined && newTitle !== null) {
                                textElement.textContent = newTitle;

                                // 重新计算文本居中位置
                                adjustHotspotTextPosition(divElement, textElement);
                            }
                        }

                        updated = true;
                        break; // 找到匹配的热点后退出循环
                    }
                }

                // 向父页面发送更新完成消息
                var message = {
                    type: 'hotspotUpdateComplete',
                    success: updated,
                    pan: pan,
                    tilt: tilt,
                    timestamp: new Date().getTime()
                };

                window.parent.postMessage(message, '*');

            }
        } catch (error) {
            // 向父页面发送错误消息
            try {
                window.parent.postMessage({
                    type: 'hotspotUpdateComplete',
                    success: false,
                    error: error.message
                }, '*');
            } catch (e) {
                // 静默处理错误
            }
        }
    };

    // 处理添加热点到视图
    var handleAddHotspotToView = function (data) {
        try {
            if (!data || !data.hotspot) {
                return;
            }

            var hotspotInfo = data.hotspot;
            var hotspotId = hotspotInfo.id;
            var pan = parseFloat(hotspotInfo.pan);
            var tilt = parseFloat(hotspotInfo.tilt);
            var title = hotspotInfo.title || '';
            var description = hotspotInfo.description || '';

            // 验证数据
            if (!hotspotId || isNaN(pan) || isNaN(tilt)) {
                return;
            }

            // 创建热点DOM结构（直接包含点击事件）
            var hotspotContainer = createHotspotDOM(hotspotId, title, description, pan, tilt);

            // 使用pano2vr的addHotspot方法添加热点
            if (typeof pano !== 'undefined' && pano && typeof pano.addHotspot === 'function') {
                pano.addHotspot(hotspotId, pan, tilt, hotspotContainer);

                // 手动设置热点对象的属性，确保高亮功能正常工作
                setTimeout(function() {
                    if (pano.L && Array.isArray(pano.L)) {
                        for (var i = 0; i < pano.L.length; i++) {
                            var hotspot = pano.L[i];
                            if (hotspot.id === hotspotId) {
                                // 确保新热点有正确的属性
                                hotspot.skinid = HOTSPOT_SKIN_ID;
                                hotspot.pan = pan.toString();
                                hotspot.tilt = tilt.toString();
                                hotspot.title = title;
                                hotspot.description = description;
                                break;
                            }
                        }
                    }
                }, 100);

                // 向父页面发送添加完成消息
                window.parent.postMessage({
                    type: 'addHotspotComplete',
                    success: true,
                    hotspotId: hotspotId,
                    pan: pan,
                    tilt: tilt
                }, '*');
            }

        } catch (error) {
            // 向父页面发送错误消息
            try {
                window.parent.postMessage({
                    type: 'addHotspotComplete',
                    success: false,
                    error: error.message
                }, '*');
            } catch (e) {
                // 静默处理错误
            }
        }
    };

    // 处理从视图删除热点
    var handleRemoveHotspotFromView = function (data) {
        try {
            if (!data) {
                return;
            }

            var hotspotId = data.hotspotId;
            var pan = data.pan;
            var tilt = data.tilt;
            var removed = false;

            // 获取当前页面的所有热点
            if (typeof pano !== 'undefined' && pano && pano.L) {
                var hotspots = pano.L;

                // 使用倒序遍历，避免删除元素时索引问题
                for (var i = hotspots.length - 1; i >= 0; i--) {
                    var hotspot = hotspots[i];

                    // 多重匹配条件确保准确删除
                    var matchById = hotspotId && hotspot.id === hotspotId;
                    var matchByCoordinates = pan && tilt && hotspot.pan && hotspot.tilt &&
                        Math.abs(parseFloat(hotspot.pan) - parseFloat(pan)) < 0.01 &&
                        Math.abs(parseFloat(hotspot.tilt) - parseFloat(tilt)) < 0.01 &&
                        hotspot.skinid === HOTSPOT_SKIN_ID;

                    if (matchById || matchByCoordinates) {
                        // 使用pano2vr的removeHotspot方法删除热点
                        if (typeof pano.removeHotspot === 'function') {
                            pano.removeHotspot(hotspot.id);
                            removed = true;
                        } else {
                            // 如果没有removeHotspot方法，手动从数组中移除
                            hotspots.splice(i, 1);

                            // 如果有DOM元素，也要移除
                            if (hotspot.j && hotspot.j.__div && hotspot.j.__div.parentNode) {
                                hotspot.j.__div.parentNode.removeChild(hotspot.j.__div);
                            }
                            removed = true;
                        }

                        // 如果是通过坐标匹配的，理论上应该是唯一的，但为了安全继续检查
                        if (matchByCoordinates) {
                            break;
                        }
                    }
                }
            }

            // 向父页面发送删除完成消息
            window.parent.postMessage({
                type: 'removeHotspotComplete',
                success: removed,
                hotspotId: hotspotId,
                pan: pan,
                tilt: tilt
            }, '*');

        } catch (error) {
            // 向父页面发送错误消息
            try {
                window.parent.postMessage({
                    type: 'removeHotspotComplete',
                    success: false,
                    error: error.message
                }, '*');
            } catch (e) {
                // 静默处理错误
            }
        }
    };

    // 创建热点DOM结构
    var createHotspotDOM = function (hotspotId, title, description, pan, tilt) {
        // 创建主容器
        var hotspotContainer = document.createElement('div');
        hotspotContainer.className = 'ggskin ggskin_hotspot ' + HOTSPOT_SKIN_ID;
        hotspotContainer.style.cssText = 'height: 0px; position: absolute; visibility: visible; width: 0px; pointer-events: auto; cursor: pointer; transform-origin: 50% 50%; transition: none;';

        // 直接为热点容器添加热点数据和点击事件
        hotspotContainer.hotspotData = {
            id: hotspotId,
            title: title,
            description: description,
            skinid: HOTSPOT_SKIN_ID,
            url: '',
            target: '',
            pan: pan.toString(),
            tilt: tilt.toString()
        };

        // 直接添加点击事件
        hotspotContainer.addEventListener('click', hotspotClickHandler);

        // 添加右键菜单事件
        hotspotContainer.addEventListener('contextmenu', hotspotRightClickHandler);

        // 创建SVG图标容器
        var svgContainer = document.createElement('div');
        svgContainer.className = 'ggskin ggskin_svg';
        svgContainer.style.cssText = 'height: 32px; left: -16px; position: absolute; top: -16px; visibility: inherit; width: 32px; pointer-events: auto; transform-origin: 50% 50%; transition: none;';

        // 创建SVG图标
        var svgIcon = document.createElement('img');
        svgIcon.className = 'ggskin ggskin_svg';
        svgIcon.src = '/panorama/hostpot.svg';
        svgIcon.style.cssText = 'position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; -webkit-user-drag: none; pointer-events: none; visibility: inherit;';

        svgContainer.appendChild(svgIcon);

        // 创建文本容器
        var textContainer = document.createElement('div');
        textContainer.className = 'ggskin ggskin_text';
        textContainer.style.cssText = 'height: auto; position: absolute; top: 22px; left: 0; visibility: inherit; pointer-events: auto; text-shadow: rgb(0, 0, 0) 1px 1px 4px; transform-origin: 50% 0; transition: none; transform: translateX(-50%); white-space: nowrap;';

        // 创建文本内容
        var textContent = document.createElement('div');
        textContent.style.cssText = 'position: relative; box-sizing: border-box; cursor: default; width: auto; height: auto; border: 1px solid rgb(0, 0, 0); color: rgb(255, 255, 255); text-align: center; white-space: nowrap; padding: 2px 5px; overflow: hidden;';
        textContent.textContent = title;

        // 组装DOM结构
        textContainer.appendChild(textContent);

        // 组装DOM结构
        hotspotContainer.appendChild(svgContainer);
        hotspotContainer.appendChild(textContainer);

        return hotspotContainer;
    };

    // 调整热点文本位置，确保居中显示
    var adjustHotspotTextPosition = function (hotspotDiv, textElement) {
        // 由于新的CSS transform属性可以自动处理居中，
        // 此函数在大多数情况下不再需要复杂的JS计算。
        // 保留函数结构以兼容现有调用，但内容可以简化或清空。
    };

    // 初始化双击添加热点监听器
    var initDoubleClickListener = function () {
        try {
            // 监听Pano2VR的双击事件
            if (typeof pano !== 'undefined' && pano ) {
                pano.on('playerdblclick', function (args) {
                    // 获取双击位置的坐标
                    var pan = args.pan;
                    var tilt = args.tilt;

                    // 向父页面发送添加热点消息
                    var message = {
                        type: 'addHotspot',
                        pan: pan,
                        tilt: tilt,
                        nodeId: currentNodeId,
                        timestamp: new Date().getTime()
                    };

                    window.parent.postMessage(message, '*');
                });
            }
        } catch (error) {
            // 初始化双击监听器失败
        }
    };

    // 防止重复初始化的标志
    var isInitialized = false;

    // 初始化所有功能
    var initAllFeatures = function () {
        if (isInitialized) {
            return;
        }

        initMessageListener();
        initNodeSwitchListener();
        initHotspotClickListener();
        initDoubleClickListener();

        isInitialized = true;
    };

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function () {
            waitForPano(initAllFeatures);
        });
    } else {
        waitForPano(initAllFeatures);
    }

    // 为了兼容性，也在window.onload时尝试初始化
    var originalOnload = window.onload;
    window.onload = function () {
        if (originalOnload) originalOnload();
        waitForPano(initAllFeatures);
    };

})();
