package com.cirpoint.controller;

import com.cirpoint.model.PathEntry;
import com.cirpoint.service.MultiPathService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多路径管理控制器
 */
@RestController
@RequestMapping("/multiPath")
public class MultiPathController {

    @Autowired
    private MultiPathService multiPathService;

    /**
     * 获取所有路径
     */
    @GetMapping("/list")
    public ResponseEntity<List<PathEntry>> getAllPaths() {
        List<PathEntry> paths = multiPathService.getAllPaths();
        return ResponseEntity.ok(paths);
    }

    /**
     * 获取所有已启用的路径
     */
    @GetMapping("/enabled")
    public ResponseEntity<List<PathEntry>> getEnabledPaths() {
        List<PathEntry> paths = multiPathService.getEnabledPaths();
        return ResponseEntity.ok(paths);
    }

    /**
     * 添加新路径
     */
    @PostMapping("/add")
    public ResponseEntity<Map<String, Object>> addPath(@RequestParam String path) {
        Map<String, Object> response = new HashMap<>();
        boolean success = multiPathService.addPath(path);

        response.put("success", success);
        if (!success) {
            response.put("message", "路径无效或已存在");
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 删除路径
     */
    @PostMapping("/remove")
    public ResponseEntity<Map<String, Object>> removePath(@RequestParam String id) {
        Map<String, Object> response = new HashMap<>();
        boolean success = multiPathService.removePath(id);

        response.put("success", success);
        if (!success) {
            response.put("message", "删除路径失败");
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 启用路径
     */
    @PostMapping("/enable")
    public ResponseEntity<Map<String, Object>> enablePath(@RequestParam String id) {
        Map<String, Object> response = new HashMap<>();
        boolean success = multiPathService.enablePath(id);

        response.put("success", success);
        if (!success) {
            response.put("message", "启用路径失败");
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 禁用路径
     */
    @PostMapping("/disable")
    public ResponseEntity<Map<String, Object>> disablePath(@RequestParam String id) {
        Map<String, Object> response = new HashMap<>();
        boolean success = multiPathService.disablePath(id);

        response.put("success", success);
        if (!success) {
            response.put("message", "禁用路径失败");
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 获取单个路径信息
     */
    @GetMapping("/path/{id}")
    public ResponseEntity<PathEntry> getPath(@PathVariable String id) {
        PathEntry path = multiPathService.getPath(id);
        if (path != null) {
            return ResponseEntity.ok(path);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
}
