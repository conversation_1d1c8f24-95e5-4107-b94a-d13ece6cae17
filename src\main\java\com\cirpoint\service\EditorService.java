package com.cirpoint.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileReader;
import cn.hutool.core.io.file.FileWriter;
import com.cirpoint.model.FileNode;
import com.cirpoint.model.FileOperationRequest;
import com.cirpoint.model.FileRenameRequest;
import com.cirpoint.model.FilePropertiesResponse;
import com.cirpoint.model.PathEntry;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.SimpleDateFormat;
import java.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;

/**
 * 文件操作服务类
 */
@Service
public class EditorService {

	@Autowired
	private MultiPathService multiPathService;



	@PostConstruct
	public void init() {
		// 初始化方法，可以在这里添加其他初始化逻辑
	}


	/**
	 * 获取文件树结构
	 *
	 * @param path 当前目录路径，为空时表示根目录，否则为绝对路径
	 * @return 返回当前目录下的文件和文件夹列表
	 */
	public List<FileNode> getFileTree(String path) {
		List<FileNode> nodes = new ArrayList<>();

		// 如果路径为空，返回所有启用的路径作为根节点
		if (!StringUtils.hasText(path)) {
			List<PathEntry> enabledPaths = multiPathService.getEnabledPaths();

			// 如果没有启用的路径，返回空列表
			if (enabledPaths.isEmpty()) {
				return nodes;
			}

			// 为每个启用的路径创建一个根节点
			for (PathEntry pathEntry : enabledPaths) {
				FileNode rootNode = createRootNode(pathEntry.getPath());
				nodes.add(rootNode);
			}

			return nodes;
		}

		// 处理绝对路径的情况
		File currentDir = new File(path);

		// 如果目录不存在或不是目录，直接返回空列表
		if (!currentDir.exists() || !currentDir.isDirectory()) {
			return nodes;
		}

		// 生成父节点ID
		String parentId = UUID.nameUUIDFromBytes(path.getBytes()).toString();

		// 获取文件和目录列表
		File[] files = currentDir.listFiles();
		if (files != null) {
			for (File file : files) {
				// 生成文件节点
				FileNode node = createFileNode(file, parentId, path);
				nodes.add(node);
			}
		}

		return nodes;
	}

	/**
	 * 统一文件路径分隔符
	 *
	 * @param path 需要统一格式的路径
	 * @return 统一使用/作为分隔符的路径
	 */
	private String normalizePathSeparator(String path) {
		if (path == null || path.isEmpty()) {
			return path;
		}
		// 将所有路径分隔符(包括/和\)统一替换为/
		return path.replaceAll("[/\\\\]+", "/");
	}


	/**
	 * 创建根节点
	 *
	 * @param absolutePath 绝对路径
	 * @return 根节点
	 */
	private FileNode createRootNode(String absolutePath) {
		FileNode rootNode = new FileNode();

		// 设置根节点基本属性
		rootNode.setId(UUID.nameUUIDFromBytes(absolutePath.getBytes()).toString());
		rootNode.setPId("-1");
		rootNode.setParent(true);

		// 设置根节点名称
		File baseFile = new File(absolutePath);
		rootNode.setName(baseFile.getName());

		// 设置路径
		rootNode.setPath(normalizePathSeparator(absolutePath));
		// 设置绝对路径
		rootNode.setAbsolutePath(normalizePathSeparator(absolutePath));

		return rootNode;
	}

	/**
	 * 创建文件节点
	 *
	 * @param file       文件对象
	 * @param parentId   父节点ID
	 * @param parentPath 父节点路径（绝对路径）
	 * @return 文件节点
	 */
	private FileNode createFileNode(File file, String parentId, String parentPath) {
		FileNode node = new FileNode();

		// 获取绝对路径
		String absolutePath = FileUtil.getAbsolutePath(file);

		// 设置节点属性
		node.setId(UUID.nameUUIDFromBytes(absolutePath.getBytes()).toString());
		node.setPId(parentId);
		node.setName(file.getName());
		node.setPath(normalizePathSeparator(absolutePath)); // 使用绝对路径
		node.setParent(file.isDirectory());
		node.setAbsolutePath(normalizePathSeparator(absolutePath));

		return node;
	}


	/**
	 * 判断文件是否为图片
	 */
	public boolean isImageFile(String fileName) {
		fileName = fileName.toLowerCase();
		return fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") ||
				fileName.endsWith(".png") || fileName.endsWith(".gif") ||
				fileName.endsWith(".bmp") || fileName.endsWith(".webp") ||
				fileName.endsWith(".svg") || fileName.endsWith(".ico");
	}

	/**
	 * 获取文件内容
	 *
	 * @param path 文件绝对路径
	 * @return 文件内容
	 */
	public String getFileContent(String path) {
		File file = new File(path);
		String fileName = file.getName().toLowerCase();

		// 判断是否为图片文件
		if (isImageFile(fileName)) {
			// 对于图片文件，返回特殊标记，前端会处理为图片预览
			return "IMAGE_FILE";
		}

		// 定义文本文件的扩展名列表
		boolean isTextFile = fileName.endsWith(".txt") || fileName.endsWith(".java") ||
				fileName.endsWith(".js") || fileName.endsWith(".css") ||
				fileName.endsWith(".html") || fileName.endsWith(".xml") ||
				fileName.endsWith(".json") || fileName.endsWith(".properties") ||
				fileName.endsWith(".yml") || fileName.endsWith(".yaml") ||
				fileName.endsWith(".md") || fileName.endsWith(".sql") ||
				fileName.endsWith(".jsp") || fileName.endsWith(".conf") ||
				fileName.endsWith(".config") || fileName.endsWith(".ini") ||
				fileName.endsWith(".log") || fileName.endsWith(".sh") ||
				fileName.endsWith(".bat") || fileName.endsWith(".cmd");

		if (!isTextFile) {
			throw new IllegalArgumentException("不支持打开此类型的文件，请下载后查看");
		}

		return new FileReader(file).readString();
	}

	/**
	 * 保存文件内容
	 *
	 * @param path    文件绝对路径
	 * @param content 文件内容
	 */
	public void saveFileContent(String path, String content) {
		File file = new File(path);
		FileWriter writer = new FileWriter(file);
		writer.write(content);
	}

	/**
	 * 创建文件或文件夹
	 *
	 * @param request 文件操作请求
	 * @return 新创建的文件或文件夹的UUID
	 * @throws IllegalArgumentException 如果存在同名文件或文件夹
	 */
	public String createFileOrFolder(FileOperationRequest request) {
		// 父目录的绝对路径
		String parentPath = request.getPath();
		File file = new File(parentPath, request.getName());

		// 检查同名文件或文件夹是否已存在
		if (file.exists()) {
			throw new IllegalArgumentException("已存在同名" + ("folder".equals(request.getType()) ? "文件夹" : "文件") + "：" + request.getName());
		}

		if ("folder".equals(request.getType())) {
			FileUtil.mkdir(file);
		} else {
			FileUtil.touch(file);
		}

		// 获取绝对路径并生成ID
		String absolutePath = FileUtil.getAbsolutePath(file);
		return UUID.nameUUIDFromBytes(absolutePath.getBytes()).toString();
	}

	/**
	 * 删除文件或文件夹
	 *
	 * @param path 文件或文件夹的绝对路径
	 */
	public void delete(String path) {
		File file = new File(path);
		FileUtil.del(file);
	}

	/**
	 * 重命名文件或文件夹
	 *
	 * @param request 重命名请求
	 * @return 重命名后文件或文件夹的UUID
	 * @throws IllegalArgumentException 如果目标文件名已存在
	 */
	public String rename(FileRenameRequest request) {
		// 旧文件的绝对路径
		String oldPath = request.getOldPath();
		File oldFile = new File(oldPath);

		// 获取父目录路径
		String parentPath = oldFile.getParent();

		// 构建新文件的绝对路径
		File newFile = new File(parentPath, request.getNewName());

		// 检查目标文件是否已存在
		if (newFile.exists()) {
			throw new IllegalArgumentException("已存在同名" + (oldFile.isDirectory() ? "文件夹" : "文件") + "：" + request.getNewName());
		}

		// 执行重命名操作
		FileUtil.rename(oldFile, request.getNewName(), true);

		// 获取新文件的绝对路径并生成ID
		String absolutePath = FileUtil.getAbsolutePath(newFile);
		return UUID.nameUUIDFromBytes(absolutePath.getBytes()).toString();
	}

	/**
	 * 获取所有启用的路径
	 *
	 * @return 启用的路径列表
	 */
	public List<PathEntry> getEnabledPaths() {
		return multiPathService.getEnabledPaths();
	}



	/**
	 * 获取文件作为Resource
	 *
	 * @param path 文件绝对路径
	 * @return 文件资源
	 * @throws IllegalArgumentException 如果文件不存在或不是文件
	 */
	public Resource getFileAsResource(String path) {
		File file = new File(path);
		if (!file.exists() || !file.isFile()) {
			throw new IllegalArgumentException("文件不存在：" + path);
		}
		return new FileSystemResource(file);
	}

	/**
	 * 搜索文件
	 *
	 * @param keyword       搜索关键词
	 * @param searchContent 是否搜索文件内容
	 * @param folderPath    搜索的文件夹绝对路径，为空则搜索所有启用的路径
	 * @param fileTypes     文件类型过滤，格式如：.java,.js,.html
	 * @return 搜索结果列表
	 */
	public List<Map<String, Object>> searchFiles(String keyword, boolean searchContent, String folderPath, String fileTypes) {
		List<Map<String, Object>> results = new ArrayList<>();

		// 获取搜索目录
		if (StringUtils.hasText(folderPath)) {
			// 在指定文件夹中搜索
			File baseDir = new File(folderPath);
			if (baseDir.exists() && baseDir.isDirectory()) {
				// 解析文件类型过滤器
				Set<String> fileTypeSet = parseFileTypes(fileTypes);
				// 执行搜索
				searchFilesRecursively(baseDir, keyword, searchContent, results, "", fileTypeSet);
			}
		} else {
			// 在所有启用的路径中搜索
			List<PathEntry> enabledPaths = multiPathService.getEnabledPaths();
			for (PathEntry pathEntry : enabledPaths) {
				File baseDir = new File(pathEntry.getPath());
				if (baseDir.exists() && baseDir.isDirectory()) {
					// 解析文件类型过滤器
					Set<String> fileTypeSet = parseFileTypes(fileTypes);
					// 执行搜索
					searchFilesRecursively(baseDir, keyword, searchContent, results, "", fileTypeSet);
				}
			}
		}

		return results;
	}

	/**
	 * 解析文件类型过滤器
	 *
	 * @param fileTypes 文件类型字符串，格式如：.java,.js,.html
	 * @return 文件类型集合
	 */
	private Set<String> parseFileTypes(String fileTypes) {
		Set<String> fileTypeSet = new HashSet<>();
		if (StringUtils.hasText(fileTypes)) {
			String[] types = fileTypes.split(",");
			for (String type : types) {
				String trimmedType = type.trim();
				if (!trimmedType.isEmpty()) {
					// 确保文件类型以.开头
					if (!trimmedType.startsWith(".")) {
						trimmedType = "." + trimmedType;
					}
					fileTypeSet.add(trimmedType.toLowerCase());
				}
			}
		}
		return fileTypeSet;
	}

	/**
	 * 递归搜索文件
	 *
	 * @param dir           当前目录
	 * @param keyword       关键词
	 * @param searchContent 是否搜索文件内容
	 * @param results       结果列表
	 * @param relativePath  相对路径（用于显示）
	 * @param fileTypes     文件类型过滤集合
	 */
	private void searchFilesRecursively(File dir, String keyword, boolean searchContent,
										List<Map<String, Object>> results, String relativePath, Set<String> fileTypes) {
		File[] files = dir.listFiles();
		if (files == null) {
			return;
		}

		for (File file : files) {
			// 获取绝对路径
			String absolutePath = FileUtil.getAbsolutePath(file);

			// 构建用于显示的路径
			String displayPath = relativePath.isEmpty() ?
					file.getName() : relativePath + "/" + file.getName();

			if (file.isDirectory()) {
				// 递归搜索子目录
				searchFilesRecursively(file, keyword, searchContent, results, displayPath, fileTypes);
			} else {
				// 检查文件类型是否匹配
				if (!fileTypes.isEmpty()) {
					String fileName = file.getName().toLowerCase();
					boolean matchesFileType = false;

					for (String fileType : fileTypes) {
						if (fileName.endsWith(fileType)) {
							matchesFileType = true;
							break;
						}
					}

					// 如果不匹配指定的文件类型，则跳过
					if (!matchesFileType) {
						continue;
					}
				}

				// 检查文件名是否匹配
				boolean nameMatches = file.getName().toLowerCase().contains(keyword.toLowerCase());

				// 如果需要搜索内容且是文本文件，则检查文件内容
				boolean contentMatches = false;
				List<String> matchedLines = new ArrayList<>();

				if (searchContent && isTextFile(file.getName())) {
					try {
						String content = new FileReader(file).readString();
						String[] lines = content.split("\\r?\\n");

						for (int i = 0; i < lines.length; i++) {
							if (lines[i].toLowerCase().contains(keyword.toLowerCase())) {
								contentMatches = true;
								// 添加匹配行及其行号
								matchedLines.add("第 " + (i + 1) + " 行: " + lines[i].trim());

								// 限制匹配行数，避免结果过大
								if (matchedLines.size() >= 5) {
									matchedLines.add("...");
									break;
								}
							}
						}
					} catch (Exception e) {
						// 忽略读取错误
					}
				}

				// 如果文件名或内容匹配，添加到结果
				if (nameMatches || contentMatches) {
					Map<String, Object> result = new HashMap<>();
					result.put("path", normalizePathSeparator(absolutePath)); // 使用绝对路径
					result.put("displayPath", displayPath); // 添加用于显示的路径
					result.put("name", file.getName());
					result.put("isDirectory", false);
					result.put("nameMatches", nameMatches);
					result.put("contentMatches", contentMatches);
					result.put("matchedLines", matchedLines);
					results.add(result);
				}
			}
		}
	}

	/**
	 * 判断是否为文本文件
	 */
	private boolean isTextFile(String fileName) {
		fileName = fileName.toLowerCase();
		return fileName.endsWith(".txt") || fileName.endsWith(".java") ||
				fileName.endsWith(".js") || fileName.endsWith(".css") ||
				fileName.endsWith(".html") || fileName.endsWith(".xml") ||
				fileName.endsWith(".json") || fileName.endsWith(".properties") ||
				fileName.endsWith(".yml") || fileName.endsWith(".yaml") ||
				fileName.endsWith(".md") || fileName.endsWith(".sql") ||
				fileName.endsWith(".jsp") || fileName.endsWith(".conf") ||
				fileName.endsWith(".config") || fileName.endsWith(".ini") ||
				fileName.endsWith(".log") || fileName.endsWith(".sh") ||
				fileName.endsWith(".bat") || fileName.endsWith(".cmd");
	}

	/**
	 * 导出文件或文件夹为ZIP
	 * 支持单个文件、单个文件夹或多个文件的导出
	 *
	 * @param paths 要导出的文件或文件夹绝对路径数组
	 * @return 包含ZIP文件的资源对象
	 * @throws IOException 如果导出过程中发生IO错误
	 */
	public Resource exportFiles(String[] paths) throws IOException {
		// 创建临时目录用于存放ZIP文件
		File tempDir = Files.createTempDirectory("export_temp").toFile();
		File zipFile = new File(tempDir, "export.zip");

		try {
			// 创建ZIP输出流
			java.util.zip.ZipOutputStream zipOut = new java.util.zip.ZipOutputStream(
					new java.io.FileOutputStream(zipFile));

			// 处理每个路径
			for (String path : paths) {
				if (StringUtils.hasText(path)) {
					File sourceFile = new File(path);

					if (sourceFile.exists()) {
						// 添加文件或目录到ZIP
						// 使用文件名或目录名作为ZIP中的路径
						String entryPath = sourceFile.getName();
						addToZip(sourceFile, entryPath, zipOut);
					}
				}
			}

			zipOut.close();

			// 返回ZIP文件资源
			return new FileSystemResource(zipFile);
		} catch (IOException e) {
			// 清理临时文件
			deleteDirectory(tempDir);
			throw e;
		}
	}

	/**
	 * 递归添加文件或目录到ZIP
	 *
	 * @param file      要添加的文件或目录
	 * @param entryPath ZIP中的路径
	 * @param zipOut    ZIP输出流
	 * @throws IOException 如果添加过程中发生IO错误
	 */
	private void addToZip(File file, String entryPath, java.util.zip.ZipOutputStream zipOut) throws IOException {
		if (file.isDirectory()) {
			// 如果是目录，递归添加其内容
			File[] children = file.listFiles();
			if (children != null) {
				for (File childFile : children) {
					String childPath = entryPath + "/" + childFile.getName();
					addToZip(childFile, childPath, zipOut);
				}
			}
		} else {
			// 如果是文件，直接添加到ZIP
			java.util.zip.ZipEntry zipEntry = new java.util.zip.ZipEntry(entryPath);
			zipOut.putNextEntry(zipEntry);

			java.io.FileInputStream fis = new java.io.FileInputStream(file);
			byte[] buffer = new byte[1024];
			int length;
			while ((length = fis.read(buffer)) > 0) {
				zipOut.write(buffer, 0, length);
			}

			fis.close();
			zipOut.closeEntry();
		}
	}

	/**
	 * 递归删除目录及其内容
	 *
	 * @param directory 要删除的目录
	 */
	private void deleteDirectory(File directory) {
		if (directory.exists()) {
			File[] files = directory.listFiles();
			if (files != null) {
				for (File file : files) {
					if (file.isDirectory()) {
						deleteDirectory(file);
					} else {
						file.delete();
					}
				}
			}
			directory.delete();
		}
	}

	/**
	 * 导入ZIP文件到指定目录
	 *
	 * @param file       上传的ZIP文件
	 * @param targetPath 目标目录绝对路径
	 * @return 是否导入成功
	 * @throws IOException 如果导入过程中发生IO错误
	 */
	public boolean importFiles(MultipartFile file, String targetPath) throws IOException {
		if (file == null || file.isEmpty()) {
			return false;
		}

		System.out.println("导入ZIP文件到目录: " + targetPath);

		// 确保目标目录存在
		File targetDir = new File(targetPath);
		if (!targetDir.exists()) {
			targetDir.mkdirs();
		}

		// 创建临时文件用于存储上传的ZIP
		File tempFile = File.createTempFile("import_", ".zip");
		file.transferTo(tempFile);

		try {
			// 使用 CommonUtil.customUnzip 方法解压 ZIP 文件
			System.out.println("开始解压ZIP文件: " + tempFile.getAbsolutePath() + " 到目录: " + targetDir.getAbsolutePath());

			try {
				// 首先尝试使用 UTF-8 编码解压
				System.out.println("尝试使用 UTF-8 编码解压");
				com.cirpoint.util.CommonUtil.customUnzip(tempFile.getAbsolutePath(), targetDir.getAbsolutePath());
				System.out.println("使用 UTF-8 编码解压成功");
			} catch (Exception e) {
				// 如果 UTF-8 解压失败，尝试使用 GBK 编码
				System.out.println("UTF-8 编码解压失败，尝试使用 GBK 编码: " + e.getMessage());
				try {
					// 使用 ZipFile 和 GBK 编码解压
					java.util.zip.ZipFile zipFile = new java.util.zip.ZipFile(tempFile, java.nio.charset.Charset.forName("GBK"));
					java.util.Enumeration<? extends java.util.zip.ZipEntry> entries = zipFile.entries();

					while (entries.hasMoreElements()) {
						java.util.zip.ZipEntry entry = entries.nextElement();
						File entryFile = new File(targetDir, entry.getName());

						// 创建目标文件的父目录
						if (entry.isDirectory()) {
							entryFile.mkdirs();
							continue;
						}

						// 确保目标文件的父目录存在
						File parent = entryFile.getParentFile();
						if (parent != null && !parent.exists()) {
							parent.mkdirs();
						}

						// 复制文件内容
						try (java.io.InputStream in = zipFile.getInputStream(entry);
							 java.io.OutputStream out = java.nio.file.Files.newOutputStream(entryFile.toPath())) {
							byte[] buffer = new byte[8192];
							int len;
							while ((len = in.read(buffer)) != -1) {
								out.write(buffer, 0, len);
							}
						}
					}

					zipFile.close();
					System.out.println("使用 GBK 编码解压成功");
				} catch (Exception e2) {
					// 如果 GBK 也失败，记录错误并返回失败
					System.err.println("GBK 编码解压也失败: " + e2.getMessage());
					e2.printStackTrace();
					return false;
				}
			}

			return true;
		} catch (Exception e) {
			System.err.println("解压ZIP文件失败: " + e.getMessage());
			e.printStackTrace();
			return false;
		} finally {
			// 清理临时文件
			if (tempFile.exists()) {
				tempFile.delete();
			}
		}
	}

	/**
	 * 导入单个文件到指定目录
	 *
	 * @param file       上传的文件
	 * @param targetPath 目标目录绝对路径
	 * @return 是否导入成功
	 * @throws IOException 如果导入过程中发生IO错误
	 */
	public boolean importSingleFile(MultipartFile file, String targetPath) throws IOException {
		if (file.isEmpty()) {
			return false;
		}

		// 获取文件名
		String fileName = file.getOriginalFilename();
		if (fileName == null) {
			return false;
		}

		System.out.println("导入单文件到目录: " + targetPath);

		// 确保目标路径是目录
		File targetDir = new File(targetPath);
		if (!targetDir.exists()) {
			targetDir.mkdirs();
		} else if (!targetDir.isDirectory()) {
			return false;
		}

		// 创建目标文件
		File targetFile = new File(targetDir, fileName);

		// 保存文件
		try (FileOutputStream fos = new FileOutputStream(targetFile)) {
			fos.write(file.getBytes());
		}

		System.out.println("导入单个文件: " + fileName + " 到目录: " + targetPath);
		return true;
	}

	/**
	 * 获取文件属性
	 *
	 * @param path 文件绝对路径
	 * @return 文件属性信息
	 */
	public FilePropertiesResponse getFileProperties(String path) {
		File file = new File(path);
		if (!file.exists()) {
			throw new IllegalArgumentException("文件不存在：" + path);
		}

		FilePropertiesResponse properties = new FilePropertiesResponse();
		properties.setName(file.getName());
		properties.setPath(path);
		properties.setDirectory(file.isDirectory());

		// 设置文件大小（对于目录，计算总大小）
		long size = file.isDirectory() ? FileUtil.size(file) : file.length();
		properties.setSize(size);
		properties.setReadableSize(FileUtil.readableFileSize(size));

		try {
			// 获取基本文件属性（包括创建时间和修改时间）
			BasicFileAttributes attrs = Files.readAttributes(file.toPath(), BasicFileAttributes.class);
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

			// 创建时间
			long creationTime = attrs.creationTime().toMillis();
			properties.setCreationTime(creationTime);
			properties.setCreationTimeStr(sdf.format(new Date(creationTime)));

			// 最后修改时间
			long lastModifiedTime = attrs.lastModifiedTime().toMillis();
			properties.setLastModifiedTime(lastModifiedTime);
			properties.setLastModifiedTimeStr(sdf.format(new Date(lastModifiedTime)));

			// 如果是目录，计算子项数量
			if (file.isDirectory()) {
				File[] children = file.listFiles();
				properties.setChildCount(children != null ? children.length : 0);
			}
		} catch (IOException e) {
			// 如果无法获取属性，使用文件的最后修改时间
			long lastModified = file.lastModified();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			properties.setLastModifiedTime(lastModified);
			properties.setLastModifiedTimeStr(sdf.format(new Date(lastModified)));
		}

		return properties;
	}
}
