/* 员工工时统计工具样式 */

.worktime-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.tool-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e6e6e6;
}

/* 功能说明样式 */
.feature-list {
    margin: 0;
    padding-left: 20px;
}

.feature-list li {
    margin-bottom: 8px;
    line-height: 1.6;
}

/* 上传区域样式 */
.upload-area {
    position: relative;
}

.upload-drag-area {
    border: 2px dashed #d2d6de;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    background-color: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-drag-area:hover {
    border-color: #1e9fff;
    background-color: #f0f9ff;
}

.upload-drag-area.dragover {
    border-color: #1e9fff;
    background-color: #e6f7ff;
    transform: scale(1.02);
}

.upload-drag-area i {
    font-size: 48px;
    color: #c0c4cc;
    margin-bottom: 15px;
    display: block;
}

.upload-drag-area p {
    margin: 5px 0;
    color: #606266;
}

.upload-tip {
    font-size: 12px;
    color: #909399;
}

.upload-input {
    display: none;
}

.upload-actions {
    margin-top: 20px;
    text-align: center;
}

.upload-actions .layui-btn {
    margin: 0 10px;
}

/* 文件信息样式 */
.file-info {
    margin-top: 20px;
}

.file-info p {
    margin: 5px 0;
}

/* 进度条样式 */
.progress-area {
    margin-top: 20px;
}

.progress-text {
    text-align: center;
    margin-top: 10px;
    color: #606266;
    font-size: 14px;
}

/* 工具操作区域 */
.tool-actions {
    margin-bottom: 20px;
}

.tool-actions .layui-btn {
    margin-bottom: 10px;
}

/* 快速统计样式 */
.quick-stats h4 {
    margin: 0 0 10px 0;
    color: #67c23a;
}

.quick-stats p {
    margin: 5px 0;
    font-size: 14px;
}

/* 结果区域样式 */
.result-summary {
    margin-bottom: 20px;
}

.download-area {
    text-align: center;
    padding: 20px;
}

.download-area .layui-btn {
    padding: 12px 30px;
    font-size: 16px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.download-area .layui-btn .layui-icon {
    font-size: 18px;
    line-height: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .worktime-container {
        padding: 10px;
    }
    
    .tool-header {
        padding: 15px;
    }
    
    .tool-header h2 {
        font-size: 24px;
    }
    
    .upload-drag-area {
        padding: 30px 15px;
    }
    
    .upload-drag-area i {
        font-size: 36px;
    }
    
    .upload-actions .layui-btn {
        margin: 5px;
        width: 100%;
    }
    
    .tool-actions .layui-btn {
        width: 100%;
    }
}

/* 动画效果 */
.layui-card {
    transition: all 0.3s ease;
}

.layui-card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 按钮状态样式 */
.layui-btn[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 加载状态样式 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 1000;
}

/* 成功状态样式 */
.success-highlight {
    border-color: #67c23a !important;
    background-color: #f0f9ff !important;
}

/* 错误状态样式 */
.error-highlight {
    border-color: #f56c6c !important;
    background-color: #fef0f0 !important;
}

/* 自定义滚动条 */
.worktime-container::-webkit-scrollbar {
    width: 6px;
}

.worktime-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.worktime-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.worktime-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
