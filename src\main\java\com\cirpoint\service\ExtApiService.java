package com.cirpoint.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.util.Util;
import java.io.File;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-03-11 10:30
 * @description 外单位509的API服务
 **/

@Slf4j
@Service
public class ExtApiService extends ApplicationConfig {

	//查询实际汇总表并转换为table格式
	public JSONObject getActualTable(String treeId, String tableConfigId) {
		JSONObject param = new JSONObject();
		param.set("processTreeId", treeId);
		param.set("table_config_id", tableConfigId);
		JSONObject query = new JSONObject();
		query.set("queryUser", "all");
		param.set("query", query);
		JSONArray data = Util.postTwxForArray("Thing.Fn.SecondTable", "QueryTableData", param);

		JSONObject headerRes = Util.postTwxForObject("Thing.Fn.SecondTable", "GetSecondTableHeader", new JSONObject().set("id", tableConfigId));
		if (headerRes.getBool("success")) {
			JSONArray headers = headerRes.getJSONArray("result");
			// 转换为最终数据格式
			return convertToFinalDataFormat(headers, data);
		}

		return new JSONObject();
	}

	private JSONObject getSummaryTable(String treeId, String tableConfigId) {
		JSONObject param = new JSONObject();
		param.set("tree_id", treeId);
		param.set("table_config_id", tableConfigId);
		JSONArray data = Util.postTwxForObject("Thing.Fn.SecondTable", "QueryQualitySummary", param).getJSONArray("data");

		JSONObject headerRes = Util.postTwxForObject("Thing.Fn.SecondTable", "GetSecondTableHeader", new JSONObject().set("id", tableConfigId));
		if (headerRes.getBool("success")) {
			JSONArray headers = headerRes.getJSONArray("result");
			// 转换为最终数据格式
			return convertToFinalDataFormat(headers, data);
		}
		return new JSONObject();
	}


	private JSONObject convertToFinalDataFormat(JSONArray headers, JSONArray data) {
		JSONObject finalData = new JSONObject();
		JSONArray tableData = new JSONArray();
		JSONArray meta = new JSONArray();
		JSONArray merged = new JSONArray();
		int header = headers.size();

		// 计算每个单元格的占用情况，记录每个位置是否被上方单元格占用
		boolean[][] cellOccupied = new boolean[header][100]; // 假设最大列数不超过100

		// 存储field和cellColumnIndex的映射关系
		JSONObject fieldColumnMap = new JSONObject();
		// 记录序号列的位置
		int serialNumberColumnIndex = -1;

		// 首先遍历所有行，计算单元格占用情况，同时记录field和列索引的映射
		for (int i = 0; i < header; i++) {
			JSONArray cols = headers.getJSONArray(i);
			for (int j = 0; j < cols.size(); j++) {
				JSONObject col = cols.getJSONObject(j);
				int colspan = col.getInt("colspan", 1);
				int rowspan = col.getInt("rowspan", 1);
				int cellColumnIndex = col.getInt("cellColumnIndex", 0);
				String field = col.getStr("field");
				String title = col.getStr("title");

				// 记录序号列的位置
				if ("序号".equals(title)) {
					serialNumberColumnIndex = cellColumnIndex;
				}

				// 记录field和列索引的映射
				if (field != null && field.startsWith("V") && !field.contains("_")) {
					fieldColumnMap.set(field, cellColumnIndex);
				}

				// 标记被该单元格占用的所有位置
				if (rowspan > 1) {
					for (int r = 1; r < rowspan && (i + r) < header; r++) {
						for (int c = 0; c < colspan; c++) {
							cellOccupied[i + r][cellColumnIndex + c] = true;
						}
					}
				}
			}
		}

		// 生成表头数据和元数据
		for (int i = 0; i < header; i++) {
			JSONArray cols = headers.getJSONArray(i);
			JSONArray row = new JSONArray();

			// 当前行的实际列索引
			int actualColIndex = 0;

			for (int j = 0; j < cols.size(); j++) {
				JSONObject col = cols.getJSONObject(j);
				int colspan = col.getInt("colspan", 1);
				int rowspan = col.getInt("rowspan", 1);
				String title = col.getStr("title");
				int cellColumnIndex = col.getInt("cellColumnIndex", 0);

				// 处理被上方单元格占用的空位
				while (actualColIndex < cellColumnIndex) {
					if (cellOccupied[i][actualColIndex]) {
						row.add("");
					}
					actualColIndex++;
				}

				// 添加表格数据
				row.add(title);
				actualColIndex++;

				// 如果有合并列，添加空字符串占位
				if (colspan > 1) {
					for (int c = 1; c < colspan; c++) {
						row.add("");
						actualColIndex++;
					}
				}

				// 处理合并单元格信息
				if (colspan > 1 || rowspan > 1) {
					JSONObject mergedCell = new JSONObject();
					mergedCell.set("row", i);
					mergedCell.set("col", cellColumnIndex);
					mergedCell.set("rowspan", rowspan);
					mergedCell.set("colspan", colspan);
					mergedCell.set("removed", false);
					merged.add(mergedCell);
				}

				// 为每个单元格创建元数据
				JSONObject metaItem = new JSONObject();
				metaItem.set("row", i);
				metaItem.set("col", cellColumnIndex);
				metaItem.set("eles", new JSONArray());
				meta.add(metaItem);

				// 如果有合并列，为每个合并的单元格也创建元数据
				if (colspan > 1) {
					for (int c = 1; c < colspan; c++) {
						JSONObject colMetaItem = new JSONObject();
						colMetaItem.set("row", i);
						colMetaItem.set("col", cellColumnIndex + c);
						colMetaItem.set("eles", new JSONArray());
						meta.add(colMetaItem);
					}
				}
			}

			// 处理当前行末尾被上方单元格占用的空位
			if (i > 0) {
				int totalCols = tableData.getJSONArray(0).size();
				while (actualColIndex < totalCols) {
					if (cellOccupied[i][actualColIndex]) {
						row.add("");
					}
					actualColIndex++;
				}
			}

			tableData.add(row);
		}

		// 处理数据行
		if (data != null && !data.isEmpty()) {
			int totalCols = tableData.getJSONArray(0).size();

			for (int i = 0; i < data.size(); i++) {
				JSONObject rowData = data.getJSONObject(i);
				JSONArray row = new JSONArray();

				// 初始化所有列为空字符串
				for (int j = 0; j < totalCols; j++) {
					row.add("");
				}

				// 填充数据
				for (String key : rowData.keySet()) {
					if (key.startsWith("V") && fieldColumnMap.containsKey(key)) {
						int colIndex = fieldColumnMap.getInt(key);
						String value = rowData.getStr(key, "");

						// 如果是序号列，确保显示为整数
						if (colIndex == serialNumberColumnIndex && value != null && !value.isEmpty()) {
							try {
								double numValue = Double.parseDouble(value);
								value = String.valueOf((int) numValue);
							} catch (NumberFormatException e) {
								// 如果转换失败，保持原值
							}
						}

						row.set(colIndex, value);
					}
				}

				tableData.add(row);

				// 为数据行的每个单元格创建元数据
				for (int j = 0; j < totalCols; j++) {
					JSONObject metaItem = new JSONObject();
					metaItem.set("row", header + i);
					metaItem.set("col", j);
					metaItem.set("eles", new JSONArray());
					meta.add(metaItem);
				}
			}
		}

		finalData.set("tableData", tableData);
		finalData.set("meta", meta);
		finalData.set("merged", merged);
		finalData.set("header", header);

		return finalData;
	}

	private JSONObject getConfirmTable(String treeId, String tableConfigId) {
		JSONObject param = new JSONObject();
		param.set("treeId", treeId);
		param.set("tableConfigId", tableConfigId);
		JSONArray data = Util.postTwxForObject("Thing.Fn.SecondTable", "QueryProcessQualityData", param).getJSONArray("data");

		// 创建最终返回的数据结构
		JSONObject finalData = new JSONObject();
		JSONArray tableData = new JSONArray();
		JSONArray meta = new JSONArray();
		JSONArray merged = new JSONArray();

		// 如果没有数据，返回空对象
		if (data == null || data.isEmpty()) {
			return finalData;
		}

		// 遍历所有表格数据
		int rowIndex = 0;
		for (int i = 0; i < data.size(); i++) {
			JSONObject tableInfo = data.getJSONObject(i);

			// 获取表格名称和列信息
			String tableName = tableInfo.getStr("name");
			JSONArray cols = tableInfo.getJSONArray("cols");
			JSONArray datas = tableInfo.getJSONArray("datas");

			// 添加表格标题行
			JSONArray titleRow = new JSONArray();
			for (int j = 0; j < cols.size(); j++) {
				if (j == 0) {
					titleRow.add(tableName);
				} else {
					titleRow.add("");
				}
			}
			tableData.add(titleRow);

			// 为标题行添加元数据
			for (int j = 0; j < cols.size(); j++) {
				JSONObject metaItem = new JSONObject();
				metaItem.set("row", rowIndex);
				metaItem.set("col", j);

				// 为第一列添加样式（向左对齐和加粗）
				if (j == 0) {
					metaItem.set("visualRow", rowIndex);
					metaItem.set("visualCol", j);
					metaItem.set("prop", 0);
					metaItem.set("spanned", true);
					metaItem.set("rowspan", 1);
					metaItem.set("colspan", cols.size());
					metaItem.set("className", "htLeft c-bold font-size-22");
				}

				metaItem.set("eles", new JSONArray());
				meta.add(metaItem);
			}

			// 添加合并单元格信息（标题行第一列合并到最后一列）
			JSONObject mergedCell = new JSONObject();
			mergedCell.set("row", rowIndex);
			mergedCell.set("col", 0);
			mergedCell.set("rowspan", 1);
			mergedCell.set("colspan", cols.size());
			mergedCell.set("removed", false);
			merged.add(mergedCell);

			rowIndex++;

			// 添加列标题行
			JSONArray headerRow = new JSONArray();
			for (int j = 0; j < cols.size(); j++) {
				headerRow.add(cols.getStr(j));
			}
			tableData.add(headerRow);

			// 为列标题行添加元数据（加粗）
			for (int j = 0; j < cols.size(); j++) {
				JSONObject metaItem = new JSONObject();
				metaItem.set("row", rowIndex);
				metaItem.set("col", j);
				metaItem.set("visualRow", rowIndex);
				metaItem.set("visualCol", j);
				metaItem.set("className", "c-bold htCenter");
				metaItem.set("eles", new JSONArray());
				meta.add(metaItem);
			}

			rowIndex++;

			// 添加数据行
			for (int j = 0; j < datas.size(); j++) {
				JSONArray rowData = datas.getJSONArray(j);
				JSONArray dataRow = new JSONArray();

				for (int k = 0; k < rowData.size(); k++) {
					Object cellValue = rowData.get(k);
					// 处理数字类型，确保显示为整数（如果是整数）
					if (cellValue instanceof Number) {
						double numValue = ((Number) cellValue).doubleValue();
						if (numValue == Math.floor(numValue)) {
							dataRow.add(String.valueOf((int) numValue));
						} else {
							dataRow.add(String.valueOf(cellValue));
						}
					} else {
						dataRow.add(String.valueOf(cellValue));
					}
				}

				tableData.add(dataRow);

				// 为数据行添加元数据
				for (int k = 0; k < rowData.size(); k++) {
					JSONObject metaItem = new JSONObject();
					metaItem.set("row", rowIndex);
					metaItem.set("col", k);
					metaItem.set("eles", new JSONArray());
					meta.add(metaItem);
				}

				rowIndex++;
			}
		}

		// 设置最终数据
		finalData.set("tableData", tableData);
		finalData.set("meta", meta);
		finalData.set("merged", merged);
		finalData.set("header", 0); // 不需要设置header值，因为我们使用合并单元格来处理标题

		return finalData;
	}

	private JSONObject getPhotoTable(String treeId, String tableConfigId) {
		JSONObject param = new JSONObject();
		param.set("treeId", treeId);
		param.set("tableConfigId", tableConfigId);
		JSONArray data = Util.postTwxForObject("Thing.Fn.SecondTable", "QueryProcessQualityPhotoData", param).getJSONArray("data");

		// 创建最终返回的数据结构
		JSONObject finalData = new JSONObject();
		JSONArray tableData = new JSONArray();
		JSONArray meta = new JSONArray();
		JSONArray merged = new JSONArray();

		// 如果没有数据，返回空对象
		if (data == null || data.isEmpty()) {
			return finalData;
		}

		// 遍历所有表格数据
		int rowIndex = 0;
		for (int i = 0; i < data.size(); i++) {
			JSONObject tableInfo = data.getJSONObject(i);

			// 获取表格名称和列信息
			String tableName = tableInfo.getStr("name");
			JSONArray cols = tableInfo.getJSONArray("cols");
			JSONArray datas = tableInfo.getJSONArray("datas");

			// 添加列标题行（设置在最上方）
			JSONArray headerRow = new JSONArray();
			for (int j = 0; j < cols.size(); j++) {
				headerRow.add(cols.getStr(j));
			}
			tableData.add(headerRow);

			// 为列标题行添加元数据（加粗）
			for (int j = 0; j < cols.size(); j++) {
				JSONObject metaItem = new JSONObject();
				metaItem.set("row", rowIndex);
				metaItem.set("col", j);
				metaItem.set("visualRow", rowIndex);
				metaItem.set("visualCol", j);
				metaItem.set("className", "c-bold htCenter");
				metaItem.set("eles", new JSONArray());
				meta.add(metaItem);
			}

			rowIndex++;

			// 添加表格标题行
			JSONArray titleRow = new JSONArray();
			for (int j = 0; j < cols.size(); j++) {
				if (j == 0) {
					titleRow.add(tableName);
				} else {
					titleRow.add("");
				}
			}
			tableData.add(titleRow);

			// 为标题行添加元数据
			for (int j = 0; j < cols.size(); j++) {
				JSONObject metaItem = new JSONObject();
				metaItem.set("row", rowIndex);
				metaItem.set("col", j);

				// 为第一列添加样式（向左对齐和加粗）
				if (j == 0) {
					metaItem.set("visualRow", rowIndex);
					metaItem.set("visualCol", j);
					metaItem.set("prop", 0);
					metaItem.set("spanned", true);
					metaItem.set("rowspan", 1);
					metaItem.set("colspan", cols.size());
					metaItem.set("className", "htLeft c-bold font-size-22");
				}

				metaItem.set("eles", new JSONArray());
				meta.add(metaItem);
			}

			// 添加合并单元格信息（标题行第一列合并到最后一列）
			JSONObject mergedCell = new JSONObject();
			mergedCell.set("row", rowIndex);
			mergedCell.set("col", 0);
			mergedCell.set("rowspan", 1);
			mergedCell.set("colspan", cols.size());
			mergedCell.set("removed", false);
			merged.add(mergedCell);

			rowIndex++;

			// 添加数据行
			for (int j = 0; j < datas.size(); j++) {
				JSONArray rowData = datas.getJSONArray(j);
				JSONArray dataRow = new JSONArray();

				for (int k = 0; k < rowData.size(); k++) {
					Object cellValue = rowData.get(k);

					// 处理照片列（通常是第6列，索引为5）
					if (k == 5 && cellValue instanceof String && ((String) cellValue).startsWith("[")) {
						try {
							// 解析照片数据
							JSONArray photos = new JSONArray(cellValue);
							// 照片单元格不显示文字
							dataRow.add("");

							// 创建照片元数据
							JSONObject photoMetaItem = new JSONObject();
							photoMetaItem.set("row", rowIndex);
							photoMetaItem.set("col", k);
							JSONArray photoEles = new JSONArray();

							// 处理每张照片
							for (int p = 0; p < photos.size(); p++) {
								JSONObject photo = photos.getJSONObject(p);
								String photoPath = photo.getStr("PHOTO_PATH");
								String photoNumber = photo.getStr("PHOTO_NUMBER");
								String photoFormat = photo.getStr("PHOTO_FORMAT");

								// 创建照片元素
								JSONObject photoEle = new JSONObject();
								photoEle.set("id", "photo-" + System.currentTimeMillis() + "-" + p);
								photoEle.set("type", "photo");
								photoEle.set("src", "/File/" + photoPath);
								photoEle.set("date", cn.hutool.core.date.DateUtil.today());
								photoEle.set("photoPath", photoPath);
								photoEle.set("photoName", photoNumber);
								photoEle.set("photoShowNum", photoNumber); // 使用原照片对象的PHOTO_NUMBER
								photoEle.set("photoFormat", photoFormat);
								photoEle.set("class", "sign-img photo");

								photoEles.add(photoEle);
							}

							photoMetaItem.set("eles", photoEles);
							meta.add(photoMetaItem);
						} catch (Exception e) {
							// 如果解析失败，直接显示原始值
							dataRow.add(String.valueOf(cellValue));
						}
					} else if (cellValue instanceof Number) {
						// 处理数字类型，确保显示为整数（如果是整数）
						double numValue = ((Number) cellValue).doubleValue();
						if (numValue == Math.floor(numValue)) {
							dataRow.add(String.valueOf((int) numValue));
						} else {
							dataRow.add(String.valueOf(cellValue));
						}
					} else {
						dataRow.add(String.valueOf(cellValue));
					}
				}

				tableData.add(dataRow);

				// 为数据行添加元数据（除了照片列，因为已经在上面处理过了）
				for (int k = 0; k < rowData.size(); k++) {
					// 跳过照片列，因为已经在上面处理过了
					if (k == 5 && rowData.get(k) instanceof String && ((String) rowData.get(k)).startsWith("[")) {
						continue;
					}

					JSONObject metaItem = new JSONObject();
					metaItem.set("row", rowIndex);
					metaItem.set("col", k);
					metaItem.set("eles", new JSONArray());
					meta.add(metaItem);
				}

				rowIndex++;
			}
		}

		// 设置最终数据
		finalData.set("tableData", tableData);
		finalData.set("meta", meta);
		finalData.set("merged", merged);
		finalData.set("header", 1); // 设置header为0，表示第一行是表头

		return finalData;
	}

	private JSONObject getTableDataFromDb(String tableId) {
		String sql = "select * from QUALITY_REPORT where id=" + tableId;
		JSONArray objects = Util.postQuerySql(sql);
		if (objects.isEmpty()) {
			throw new RuntimeException("数据库中不存在该表格数据");
		}

		JSONObject tableObj = objects.getJSONObject(0);
		// 数据源 auto 自动填充
		String dataSource = tableObj.getStr("DATA_SOURCE");
		if (dataSource.equals("auto")) {
			String treeId = tableObj.getStr("TREE_ID");
			String reportType = tableObj.getStr("REPORT_TYPE");
			String tableConfigId = tableObj.getStr("DATA_TYPE");
			if (StrUtil.isNotEmpty(reportType) && StrUtil.isNotEmpty(treeId) && StrUtil.isNotEmpty(tableConfigId)) {
				JSONObject tableData = null;
				switch (reportType) {
					case "confirm":
						tableData = getConfirmTable(treeId, tableConfigId);
						break;
					case "photo":
						tableData = getPhotoTable(treeId, tableConfigId);
						break;
					case "summary":
						tableData = getSummaryTable(treeId, tableConfigId);
						break;
					case "actual":
						tableData = getActualTable(treeId, tableConfigId);
						break;
				}

				if (tableData != null && !tableData.isEmpty()) {
					tableObj.set("SAVE_DATA", tableData);

					//将表格数据转换为html
					String html = Util.postTwx("Thing.Util.HandsonTable", "TableData2Html", new JSONObject().set("str", tableData.toString())).getJSONObject(0).getStr("result");
					tableObj.set("HTML_DATA", html);
				}
			}
		}

		JSONObject tableData = tableObj.getJSONObject("SAVE_DATA");
		if (tableData == null) {
			throw new RuntimeException("表格数据为空");
		}
		String tableHeader = tableObj.getStr("TABLE_HEADER", "0");
		if (tableData.getInt("header", 0) == 0) {
			int headerNum;
			if (tableHeader.contains("-")) {
				headerNum = Integer.parseInt(tableHeader.split("-")[1]);
			} else {
				headerNum = Integer.parseInt(tableHeader);
			}
			tableData.set("header", headerNum);
			tableObj.set("SAVE_DATA", tableData);
			tableObj.set("TABLE_HEADER", headerNum);
		}

		return tableObj;
	}

	public String getTableData(String tableId) {
		JSONObject tableObj = getTableDataFromDb(tableId);
		return tableObj.getJSONObject("SAVE_DATA").toString();
	}

	public String getTableFile(String tableId) {
		JSONObject tableObj = getTableDataFromDb(tableId);
		JSONObject tableData = tableObj.getJSONObject("SAVE_DATA");

		// 临时目录
		String tempDir = tempPath + File.separator + System.currentTimeMillis();
		FileUtil.mkdir(tempDir);

		String dataJsonFilePath = tempDir + File.separator + "data.json";
		FileUtil.writeUtf8String(tableData.toString(), dataJsonFilePath);

		// 检查表格中是否存在图片文件
		JSONArray imageFiles = getImageFiles(tableData);
		if (!imageFiles.isEmpty()) {
			// 创建图片文件夹
			String imageDir = tempDir + File.separator + "photo";
			FileUtil.mkdir(imageDir);
			for (int i = 0; i < imageFiles.size(); i++) {
				String srcPath = fileUploadPath + imageFiles.getStr(i);
				String destPath = imageDir + File.separator + imageFiles.getStr(i);
				if (FileUtil.exist(srcPath)) {
					FileUtil.copy(srcPath, destPath, true);
				}
			}
		}
		// 导出确认表的pdf到临时目录
		Util.tableData2Pdf(tableObj, tempDir, pdfFontPath);

		// 压缩目录 - 将zip文件放在临时目录的父级目录中
		String timestamp = String.valueOf(System.currentTimeMillis());
		timestamp = timestamp + "_" + UUID.randomUUID().toString().replace("-", "");
		String zipFilePath = tempPath + File.separator + timestamp + ".zip";
		ZipUtil.zip(tempDir, zipFilePath);

		// 将文件移动到文件仓库中
		String relativePath = "//ExtTable//" + timestamp + ".zip";
		FileUtil.move(new File(zipFilePath), new File(fileUploadPath + File.separator + relativePath), true);

		// 清理临时目录
		FileUtil.del(tempDir);

		return relativePath;
	}

	/**
	 * 获取表格中的图片文件
	 *
	 * @param tableData 表格数据
	 * @return 图片文件列表
	 */
	public JSONArray getImageFiles(JSONObject tableData) {
		JSONArray imageFiles = new JSONArray();
		JSONArray metaArray = tableData.getJSONArray("meta");
		for (int i = 0; i < metaArray.size(); i++) {
			JSONObject meta = metaArray.getJSONObject(i);
			JSONArray eles = meta.getJSONArray("eles");
			if (eles != null) {
				for (int j = 0; j < eles.size(); j++) {
					JSONObject ele = eles.getJSONObject(j);
					String eleType = ele.getStr("type");
					String photoPath = "";
					if (eleType.equals("photo")) {
						photoPath = ele.getStr("photoPath");
					} else if (eleType.equals("sign")) {
						photoPath = ele.getStr("src");
					}
					if (photoPath != null && !photoPath.isEmpty()) {
						imageFiles.add(photoPath);
					}
				}
			}
		}
		return imageFiles;
	}
}
