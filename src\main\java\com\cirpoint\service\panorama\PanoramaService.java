package com.cirpoint.service.panorama;

import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 全景图热点编辑服务 - 门面服务
 * 统一入口，协调各个子服务
 *
 * <AUTHOR>
 * @date 2025-06-20 (重构)
 */
@Slf4j
@Service
public class PanoramaService {

    @Autowired
    private PanoramaModelService panoramaModelService;

    @Autowired
    private PanoramaTaskService panoramaTaskService;

    @Autowired
    private PanoramaDeviceService panoramaDeviceService;

    @Autowired
    private PanoramaHotspotService panoramaHotspotService;

    @Autowired
    private PanoramaFileService panoramaFileService;

    @Autowired
    private PanoramaXmlService panoramaXmlService;

    @Autowired
    private PanoramaExportService panoramaExportService;

    @Autowired
    private PanoramaPreviewService panoramaPreviewService;

    // ==================== 型号管理相关方法 ====================

    /**
     * 获取型号列表
     */
    public JSONObject getModelList() {
        return panoramaModelService.getModelList();
    }

    // ==================== 任务管理相关方法 ====================

    /**
     * 创建新任务
     */
    public JSONObject createTask(String taskName, String modelId, String modelName, String description, String username) {
        return panoramaTaskService.createTask(taskName, modelId, modelName, description, username);
    }

    /**
     * 获取任务列表（根据用户权限过滤）
     */
    public JSONObject getTaskList(String username) {
        return panoramaTaskService.getTaskList(username);
    }

    /**
     * 获取任务详情
     */
    public JSONObject getTaskDetail(Long taskId) {
        return panoramaTaskService.getTaskDetail(taskId);
    }

    /**
     * 获取任务管理列表（分页，根据用户权限过滤）
     */
    public JSONObject getTaskManagementList(Integer page, Integer limit, String username) {
        return panoramaTaskService.getTaskManagementList(page, limit, username);
    }

    /**
     * 更新任务信息（仅任务名称和描述）
     */
    public JSONObject updateTask(Long taskId, String taskName, String description) {
        return panoramaTaskService.updateTask(taskId, taskName, description);
    }

    /**
     * 获取任务删除前的统计信息
     */
    public JSONObject getTaskDeleteStats(Long taskId) {
        return panoramaTaskService.getTaskDeleteStats(taskId);
    }

    /**
     * 删除任务（级联删除相关数据）
     */
    public JSONObject deleteTask(Long taskId) {
        return panoramaTaskService.deleteTask(taskId);
    }

    /**
     * 检查任务是否已存在热点数据
     */
    public JSONObject checkExistingHotspots(Long taskId) {
        return panoramaTaskService.checkExistingHotspots(taskId);
    }

    /**
     * 清理任务的热点数据和文件信息
     */
    public JSONObject clearTaskData(Long taskId) {
        return panoramaTaskService.clearTaskData(taskId);
    }

    // ==================== 设备管理相关方法 ====================

    /**
     * 上传单机信息Excel文件
     */
    public JSONObject uploadDeviceExcel(Long taskId, MultipartFile file) throws IOException {
        return panoramaDeviceService.uploadDeviceExcel(taskId, file);
    }

    /**
     * 获取任务下的设备列表（分页）
     */
    public JSONObject getDeviceList(Long taskId, int page, int limit) {
        return panoramaDeviceService.getDeviceList(taskId, page, limit);
    }

    /**
     * 导出设备数据为Excel文件
     */
    public File exportDeviceExcel(Long taskId) {
        return panoramaDeviceService.exportDeviceExcel(taskId);
    }

    /**
     * 重新导入指定任务的型号单机数据
     */
    public JSONObject refreshDeviceData(Long taskId) {
        return panoramaDeviceService.refreshDeviceData(taskId);
    }

    // ==================== 热点管理相关方法 ====================

    /**
     * 检查热点标题是否重复
     */
    public JSONObject checkHotspotTitle(Long taskId, String title, Long hotspotId) {
        return panoramaHotspotService.checkHotspotTitle(taskId, title, hotspotId);
    }

    /**
     * 获取热点列表
     */
    public JSONObject getHotspotList(Long taskId, String panoramaId, Integer page, Integer limit,
                                   String titleKeyword, String descriptionKeyword) {
        return panoramaHotspotService.getHotspotList(taskId, panoramaId, page, limit, titleKeyword, descriptionKeyword);
    }

    /**
     * 获取热点列表（向后兼容性重载方法）
     */
    public JSONObject getHotspotList(Long taskId, String panoramaId, Integer page, Integer limit) {
        return panoramaHotspotService.getHotspotList(taskId, panoramaId, page, limit);
    }

    /**
     * 添加新热点
     */
    public JSONObject addHotspot(Long taskId, String nodeId, String pan, String tilt,
                                String title, String description, Long deviceId) {
        return panoramaHotspotService.addHotspot(taskId, nodeId, pan, tilt, title, description, deviceId);
    }

    /**
     * 更新热点信息
     */
    public JSONObject updateHotspot(Long hotspotId, String editedTitle, String editedDescription, Long deviceId) {
        return panoramaHotspotService.updateHotspot(hotspotId, editedTitle, editedDescription, deviceId);
    }

    /**
     * 删除热点
     */
    public JSONObject deleteHotspot(Long hotspotId) {
        return panoramaHotspotService.deleteHotspot(hotspotId);
    }

    /**
     * 热点定位
     */
    public JSONObject locateHotspot(Long hotspotId) {
        return panoramaHotspotService.locateHotspot(hotspotId);
    }

    /**
     * 批量删除热点
     */
    public JSONObject batchDeleteHotspots(List<Long> hotspotIds) {
        return panoramaHotspotService.batchDeleteHotspots(hotspotIds);
    }

    /**
     * 根据热点信息查找热点记录
     */
    public JSONObject findHotspotByInfo(Long taskId, String nodeId, String hotspotId,
                                       String title, String description, String skinid) {
        return panoramaHotspotService.findHotspotByInfo(taskId, nodeId, hotspotId, title, description, skinid);
    }

    /**
     * 根据坐标查找热点记录（最可靠的匹配方式）
     */
    public JSONObject findHotspotByCoordinates(Long taskId, String nodeId, String pan, String tilt) {
        return panoramaHotspotService.findHotspotByCoordinates(taskId, nodeId, pan, tilt);
    }

    // ==================== 文件处理相关方法 ====================

    /**
     * 上传全景图ZIP包
     */
    public JSONObject uploadPanoramaZip(Long taskId, MultipartFile file) throws IOException {
        return panoramaFileService.uploadPanoramaZip(taskId, file);
    }

    // ==================== XML处理相关方法 ====================

    /**
     * 获取当前全景节点ID
     */
    public JSONObject getCurrentNode(Long taskId) {
        return panoramaXmlService.getCurrentNode(taskId);
    }

    // ==================== 导出功能相关方法 ====================

    /**
     * 导出修改后的全景图包
     */
    public String exportPanorama(Long taskId) {
        return panoramaExportService.exportPanorama(taskId);
    }

    // ==================== 预览功能相关方法 ====================

    /**
     * 获取全景图预览路径
     */
    public JSONObject getPreviewPath(Long taskId) {
        return panoramaPreviewService.getPreviewPath(taskId);
    }

    // ==================== 工具方法 ====================

    /**
     * 生成唯一的热点XML ID
     */
    public String generateHotspotXmlId() {
        return panoramaHotspotService.generateHotspotXmlId();
    }
}