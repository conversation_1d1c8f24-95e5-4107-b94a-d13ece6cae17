/**
 * Word文档(DOC/DOCX)批量转PDF工具前端逻辑
 */
layui.use(['jquery', 'layer', 'element'], function() {
    const $ = layui.jquery;
    const layer = layui.layer;
    const element = layui.element;
    
    // 页面元素
    const $uploadArea = $('#uploadArea');
    const $uploadDragArea = $('#uploadDragArea');
    const $fileInput = $('#fileInput');
    const $selectFileBtn = $('#selectFileBtn');
    const $statusCard = $('#statusCard');
    const $startConvertBtn = $('#startConvertBtn');
    const $progressInfo = $('#progressInfo');
    const $resultCard = $('#resultCard');
    const $downloadBtn = $('#downloadBtn');
    const $fileListBody = $('#fileListBody');
    const $errorInfo = $('#errorInfo');
    const $errorList = $('#errorList');
    const $downloadArea = $('#downloadArea');
    
    // 数据存储
    let uploadedFile = null;
    let wordFiles = [];
    let convertedFiles = {
        total: 0,
        success: 0,
        failed: 0,
        errors: []
    };
    let isConverting = false;
    let conversionStartTime = 0;
    let conversionCompleted = false; // 标记转换是否已完成
    
    // 文件大小格式化
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return (bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i];
    }
    
    // 初始化拖放上传
    function initDragDrop() {
        $uploadDragArea.on('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('drag-over');
        });
        
        $uploadDragArea.on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('drag-over');
        });
        
        $uploadDragArea.on('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('drag-over');
            
            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        });
        
        $uploadDragArea.on('click', function() {
            $fileInput.click();
        });
        
        $selectFileBtn.on('click', function() {
            $fileInput.click();
        });
        
        $fileInput.on('change', function(e) {
            const files = e.target.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        });
    }
    
    // 处理文件上传
    function handleFileUpload(file) {
        // 验证文件类型
        if (file.type !== 'application/zip' && !file.name.endsWith('.zip')) {
            layer.msg('请上传ZIP格式的文件', {icon: 2});
            return;
        }
        
        // 验证文件大小
        if (file.size > 500 * 1024 * 1024) { // 500MB
            layer.msg('文件大小不能超过500MB', {icon: 2});
            return;
        }
        
        // 清空之前的文件列表和状态
        uploadedFile = null;
        wordFiles = [];
        $fileListBody.empty();
        $statusCard.hide();
        $resultCard.hide();
        $errorInfo.hide();
        $progressInfo.hide();
        isConverting = false;
        conversionCompleted = false; // 重置转换完成状态
        
        // 启用开始转换按钮
        $startConvertBtn.prop('disabled', false).removeClass('layui-btn-disabled');
        
        // 显示上传中提示
        const loadingIndex = layer.load(1, {
            shade: [0.1, '#fff']
        });
        
        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);
        
        // 上传文件
        $.ajax({
            url: '/file/docx2pdf/uploadZip',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(res) {
                layer.close(loadingIndex);
                
                if (res.code === 0) {
                    uploadedFile = {
                        name: file.name,
                        size: file.size,
                        path: res.data.taskId
                    };
                    
                    wordFiles = res.data.docxFiles || [];
                    
                    // 显示文件列表
                    showFileList();
                } else {
                    layer.msg(res.msg || '上传失败', {icon: 2});
                }
            },
            error: function() {
                layer.close(loadingIndex);
                layer.msg('上传失败，请检查网络连接', {icon: 2});
            }
        });
    }
    
    // 显示文件列表
    function showFileList() {
        if (!uploadedFile || wordFiles.length === 0) {
            layer.msg('未发现ZIP包中的DOC或DOCX文件', {icon: 0});
            return;
        }
        
        $statusCard.show();
        $fileListBody.empty();
        
        // 添加ZIP文件信息
        $fileListBody.append(`
            <tr>
                <td colspan="5" class="zip-info">
                    <strong>ZIP文件：</strong>${uploadedFile.name} (${formatFileSize(uploadedFile.size)})
                </td>
            </tr>
        `);
        
        // 添加Word文档文件列表
        wordFiles.forEach((file, index) => {
            $fileListBody.append(`
                <tr data-index="${index}">
                    <td>${index + 1}</td>
                    <td>${file.name}</td>
                    <td>${formatFileSize(file.size)}</td>
                    <td>
                        <span class="file-status waiting">等待转换</span>
                    </td>
                </tr>
            `);
        });
        
        // 更新计数
        $('#totalCount').text(wordFiles.length);
        
    }
    
    // 开始转换
    function startConversion() {
        if (isConverting) return;
        if (conversionCompleted) return; // 如果已经转换完成，不允许再次转换
        if (!uploadedFile || wordFiles.length === 0) {
            layer.msg('请先上传ZIP文件', {icon: 0});
            return;
        }
        
        isConverting = true;
        conversionStartTime = Date.now();
        convertedFiles = {
            total: wordFiles.length,
            success: 0,
            failed: 0,
            errors: []
        };
        
        // 更新UI状态
        $startConvertBtn.prop('disabled', true).addClass('layui-btn-disabled');
        $progressInfo.show();
        $resultCard.hide();
        
        // 重置进度条
        element.progress('conversionProgress', '0%');
        $('#processedCount').text('0');
        
        // 更新文件状态
        $fileListBody.find('.file-status').removeClass('waiting success error').addClass('processing').text('转换中...');
        
        // 发送转换请求
        $.ajax({
            url: '/file/docx2pdf/convert',
            type: 'POST',
            data: {
                zipPath: uploadedFile.path
            },
            dataType: 'json',
            success: function(res) {
                if (res.code === 0) {
                    // 开始实时获取转换进度
                    startProgressPolling(uploadedFile.path);
                } else {
                    handleConversionError(res.msg || '转换失败');
                }
            },
            error: function() {
                handleConversionError('转换失败，请检查网络连接');
            }
        });
    }
    
    // 开始轮询获取转换进度
    function startProgressPolling(taskId) {
        const totalFiles = wordFiles.length;
        let lastProcessed = 0;
        
        const progressTimer = setInterval(function() {
            // 轮询获取转换状态
            $.ajax({
                url: '/file/docx2pdf/status',
                type: 'GET',
                data: { taskId: taskId },
                dataType: 'json',
                success: function(res) {
                    if (res.code === 0) {
                        const data = res.data;
                        const processed = data.processed;
                        const percent = data.progress;
                        
                        // 更新进度条
                        element.progress('conversionProgress', percent + '%');
                        $('#processedCount').text(processed);
                        
                        // 当进度为100%且状态为pending时，提示用户正在打包处理
                        if (percent === 100 && data.status === "pending") {
                            layer.msg('文档转换完成，正在进行打包处理...', {icon: 16, time: 0});
                        }
                        
                        // 更新文件状态（只更新新处理的文件）
                        for (let i = lastProcessed; i < processed; i++) {
                            if (i < wordFiles.length) {
                                const $statusCell = $fileListBody.find(`tr[data-index=${i}] .file-status`);
                                
                                // 检查该文件是否在错误列表中
                                const fileName = wordFiles[i].name;
                                const hasError = data.errors && data.errors.some(error => error.file === fileName);
                                
                                if (hasError) {
                                    $statusCell.removeClass('processing').addClass('error').text('转换失败');
                                } else {
                                    $statusCell.removeClass('processing').addClass('success').text('转换成功');
                                }
                            }
                        }
                        
                        lastProcessed = processed;
                        
                        // 转换完成
                        if (data.status === "completed") {
                            clearInterval(progressTimer);
                            // 关闭可能存在的"正在打包"提示
                            layer.closeAll('dialog');
                            handleConversionComplete(res);
                        }
                    } else {
                        clearInterval(progressTimer);
                        handleConversionError(res.msg || '获取转换进度失败');
                    }
                },
                error: function() {
                    // 请求失败时不要立即停止轮询，允许一定的网络波动
                    console.error('获取转换进度请求失败');
                }
            });
        }, 1000); // 每秒轮询一次
    }
    
    // 处理转换完成
    function handleConversionComplete(res) {
        isConverting = false;
        
        if (res.code === 0) {
            // 标记转换已完成，保持按钮禁用状态
            conversionCompleted = true;
            $startConvertBtn.prop('disabled', true).addClass('layui-btn-disabled');
            
            const data = res.data;
            const totalTime = Math.round((Date.now() - conversionStartTime) / 1000);
            
            // 更新结果数据
            $('#totalFiles').text(data.total);
            $('#successFiles').text(data.success);
            $('#failedFiles').text(data.failed);
            $('#totalTime').text(totalTime + '秒');
            
            // 不再直接显示结果卡片
            // $resultCard.show();
            
            // 显示下载按钮（如果有成功转换的文件）
            if (data.success > 0) {
                // 设置下载链接
                console.log('设置下载链接, 文件名:', data.pdfZipPath);
                
                // 构建完整的下载URL，并存储到按钮的data属性中
                const downloadUrl = '/file/docx2pdf/download?token=' + encodeURIComponent(data.pdfZipPath);
                console.log('完整下载链接:', downloadUrl);
                
                // 存储下载链接到按钮的自定义数据属性
                $downloadBtn.data('download-url', downloadUrl);
                
                // 显示下载区域
                $downloadArea.show();
            } else {
                $downloadArea.hide();
            }
            
            // 显示错误信息（如果有）
            if (data.failed > 0 && data.errors && data.errors.length > 0) {
                $errorInfo.show();
                $errorList.empty();
                
                data.errors.forEach(error => {
                    $errorList.append(`
                        <div class="error-item">
                            <div><strong>文件：</strong>${error.file}</div>
                            <div><strong>错误：</strong>${error.message}</div>
                        </div>
                    `);
                });
            } else {
                $errorInfo.hide();
            }
            
            element.init('collapse'); // 重新初始化折叠面板
            
            // 使用layer弹窗显示结果卡片
            layer.open({
                type: 1,
                title: '转换结果',
                content: $resultCard,
                area: ['550px', 'auto'],
                shadeClose: true,
                maxmin: true,
                success: function(layero, index) {
                    // 确保弹窗中的组件正常初始化
                    element.init();
                }
            });
        } else {
            handleConversionError(res.msg || '转换失败');
        }
    }
    
    // 处理转换错误
    function handleConversionError(message) {
        isConverting = false;
        conversionCompleted = false; // 转换失败时重置状态
        
        // 恢复按钮状态
        $startConvertBtn.prop('disabled', false).removeClass('layui-btn-disabled');
        
        // 显示错误提示
        layer.msg(message, {icon: 2});
        
        // 更新文件状态
        $fileListBody.find('.file-status.processing').removeClass('processing').addClass('error').text('转换失败');
    }
    
    // 清空文件列表
    function clearFileList() {
        if (isConverting) {
            layer.msg('转换进行中，无法清空列表', {icon: 0});
            return;
        }
        
        layer.confirm('确定要清空文件列表吗？', {icon: 3, title: '提示'}, function(index) {
            uploadedFile = null;
            wordFiles = [];
            
            $fileListBody.empty();
            $statusCard.hide();
            $resultCard.hide();
            $fileInput.val('');
            
            layer.close(index);
        });
    }
    
    // 绑定事件
    function bindEvents() {
        initDragDrop();
        
        $startConvertBtn.on('click', startConversion);
        
        // 使用表单提交方式处理下载
        $downloadBtn.on('click', function(e) {
            e.preventDefault(); // 阻止默认行为
            
            const url = $(this).data('download-url');
            console.log('下载按钮被点击，使用链接:', url);
            
            if (url) {
                // 直接使用window.open打开下载链接
                window.open(url, '_blank');
                
                layer.msg('开始下载PDF文件...', {icon: 1});
            } else {
                console.error('下载链接无效');
                layer.msg('下载链接无效，请重新转换', {icon: 2});
            }
        });
    }
    
    // 初始化
    function init() {
        bindEvents();
        element.init();
    }
    
    // 执行初始化
    init();
}); 