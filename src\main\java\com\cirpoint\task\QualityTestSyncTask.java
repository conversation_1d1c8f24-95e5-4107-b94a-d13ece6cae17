package com.cirpoint.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.cirpoint.util.AccessToOracleImporter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 质测数据同步定时任务
 */
@Slf4j
@Component
public class QualityTestSyncTask {

	@Value("${quality.test.sync.log.path}")
	private String logPath;

	@Value("${quality.test.db.path}")
	private String dbPath;

	@Autowired
	private AccessToOracleImporter accessToOracleImporter;

	// 日志文件写入器
	private FileWriter logFileWriter;
	
	// 日志列表，用于收集执行日志
	private List<String> logMessages;

	/**
	 * 质测数据同步任务（定时执行）
	 * 从Access数据库导入数据到Oracle
	 */
	@Scheduled(cron = "${quality.test.sync.cron}")
	public void syncQualityTestData() {
		syncQualityTestData(false);
	}
	
	/**
	 * 质测数据同步任务（可手动或定时执行）
	 * 从Access数据库导入数据到Oracle
	 * 
	 * @param collectLogs 是否收集日志
	 * @return 如果collectLogs为true，返回收集到的日志；否则返回null
	 */
	public List<String> syncQualityTestData(boolean collectLogs) {
		log.info("开始执行质测数据同步定时任务");
		
		// 初始化日志列表
		if (collectLogs) {
			logMessages = new ArrayList<>();
			logMessages.add("开始执行质测数据同步定时任务");
		}

		try {
			// 获取应用程序所在目录（jar包所在目录）
			String appPath = getAppPath();
			// 构建完整的日志路径
			String fullLogPath = appPath + File.separator + logPath;
			log.info("日志路径: {}", fullLogPath);
			if (collectLogs) {
				logMessages.add("日志路径: " + fullLogPath);
			}

			// 清理30天前的日志文件
			cleanLogFiles(fullLogPath);

			// 创建日志目录
			FileUtil.mkdir(fullLogPath);

			// 生成日志文件名（使用时间戳）
			String logFileName = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss").format(new Date()) + ".log";
			File logFile = new File(fullLogPath + File.separator + logFileName);
			if (collectLogs) {
				logMessages.add("日志文件: " + logFile.getAbsolutePath());
			}

			try {
				// 实例化日志文件写入器
				logFileWriter = new FileWriter(logFile);

				// 写入任务开始时间
				writeLog("=== 质测数据同步任务开始 ===", collectLogs);
				writeLog("执行时间：" + DateUtil.now(), collectLogs);
				writeLog("Access数据库路径：" + dbPath, collectLogs);
				writeLog("", collectLogs);

				try {
					// 检查数据库文件是否存在
					if (!FileUtil.exist(dbPath)) {
						String errorMsg = "Access数据库文件不存在: " + dbPath;
						writeLog("同步失败：", collectLogs);
						writeLog("错误信息：" + errorMsg, collectLogs);
						log.error(errorMsg);
						return collectLogs ? logMessages : null;
					}

					// 配置映射JSON文件路径
					String resourcePath = "config/table_mapping.json";
					writeLog("映射配置文件路径：" + resourcePath + " (classpath资源)", collectLogs);
					writeLog("", collectLogs);

					// 设置日志记录器并执行数据导入
					writeLog("开始导入数据...", collectLogs);
					// 将当前日志写入器传递给导入器
					accessToOracleImporter.setLogWriter(logFileWriter);
					// 设置日志收集标志
					accessToOracleImporter.setCollectLogs(collectLogs);
					if (collectLogs) {
						accessToOracleImporter.setLogList(logMessages);
					}
					
					// 执行导入
					accessToOracleImporter.importData(dbPath, resourcePath);

					writeLog("数据导入成功", collectLogs);
					log.info("质测数据同步定时任务执行成功");
				} catch (Exception e) {
					// 写入错误信息
					writeLog("同步失败：", collectLogs);
					writeLog("错误信息：" + e.getMessage(), collectLogs);

					// 记录完整的堆栈跟踪
					StringWriter sw = new StringWriter();
					e.printStackTrace(new PrintWriter(sw));
					writeLog("详细错误：\n" + sw.toString(), collectLogs);

					log.error("质测数据同步定时任务执行失败", e);
				}

				// 写入任务结束时间
				writeLog("", collectLogs);
				writeLog("=== 质测数据同步任务结束 ===", collectLogs);
				writeLog("结束时间：" + DateUtil.now(), collectLogs);

			} catch (IOException e) {
				log.error("写入质测数据同步日志失败", e);
				if (collectLogs) {
					logMessages.add("写入质测数据同步日志失败: " + e.getMessage());
				}
			} finally {
				// 确保日志文件写入器关闭
				if (logFileWriter != null) {
					try {
						logFileWriter.close();
					} catch (IOException e) {
						log.error("关闭日志文件写入器失败", e);
						if (collectLogs) {
							logMessages.add("关闭日志文件写入器失败: " + e.getMessage());
						}
					}
				}
			}
		} catch (Exception e) {
			log.error("获取项目路径失败", e);
			if (collectLogs) {
				logMessages.add("获取项目路径失败: " + e.getMessage());
			}
		}
		
		return collectLogs ? logMessages : null;
	}

	/**
	 * 写入日志到文件
	 *
	 * @param message 日志消息
	 * @param collectLogs 是否收集日志
	 */
	private void writeLog(String message, boolean collectLogs) {
		try {
			if (logFileWriter != null) {
				logFileWriter.write(message + "\n");
				logFileWriter.flush();
			}
			if (collectLogs && logMessages != null) {
				logMessages.add(message);
			}
		} catch (IOException e) {
			log.error("写入日志失败: {}", message, e);
		}
	}

	/**
	 * 清理指定天数之前的日志文件
	 *
	 * @param logDirPath 日志目录路径
	 */
	private void cleanLogFiles(String logDirPath) {
		try {
			log.info("开始清理30天前的日志文件");
			Path logDir = Paths.get(logDirPath);

			// 如果日志目录不存在，直接返回
			if (!Files.exists(logDir)) {
				return;
			}

			// 获取30天前的时间
			LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
			Instant cutoffTime = thirtyDaysAgo.atZone(ZoneId.systemDefault()).toInstant();

			try (Stream<Path> pathStream = Files.list(logDir)) {
				pathStream.filter(path -> {
					try {
						BasicFileAttributes attrs = Files.readAttributes(path, BasicFileAttributes.class);
						return attrs.creationTime().toInstant().isBefore(cutoffTime) &&
								path.toString().toLowerCase().endsWith(".log");
					} catch (IOException e) {
						return false;
					}
				}).forEach(path -> {
					try {
						log.info("删除过期日志文件: {}", path.getFileName());
						Files.delete(path);
					} catch (IOException e) {
						log.error("删除日志文件失败: {}", path.getFileName(), e);
					}
				});
			}
		} catch (IOException e) {
			log.error("清理日志文件时发生错误", e);
		}
	}

	/**
	 * 获取应用程序所在目录路径
	 *
	 * @return 应用程序所在目录的绝对路径
	 */
	private String getAppPath() {
		try {
			// 获取当前类所在的jar包路径
			String path = QualityTestSyncTask.class.getProtectionDomain().getCodeSource().getLocation().toURI().getPath();
			File jarFile = new File(path);

			// 如果是jar包，返回jar包所在目录
			if (jarFile.isFile()) {
				return jarFile.getParentFile().getAbsolutePath();
			} else {
				// 如果是开发环境，返回classes目录的上两级目录
				return jarFile.getAbsolutePath();
			}
		} catch (URISyntaxException e) {
			log.error("获取应用程序路径失败", e);
			// 返回当前工作目录
			return System.getProperty("user.dir");
		}
	}
} 