package com.cirpoint.controller.tools;

import cn.hutool.json.JSONObject;
import com.cirpoint.service.FileService;
import com.cirpoint.service.tools.DocxToPdfService;
import com.cirpoint.util.FileDownloadUtil;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Objects;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * DOCX转PDF工具控制器
 */
@RestController
@RequestMapping("/file/docx2pdf")
public class DocxToPdfController {

    private final FileService fileService;
    private final DocxToPdfService docxToPdfService;

    @Value("${file.temp.path}")
    private String tempDir;

    private static final Logger logger = LoggerFactory.getLogger(DocxToPdfController.class);

    @Autowired
    public DocxToPdfController(FileService fileService, DocxToPdfService docxToPdfService) {
        this.fileService = fileService;
        this.docxToPdfService = docxToPdfService;
    }

    /**
     * 上传ZIP文件（用于DOCX批量转PDF）
     * 直接上传到临时目录并处理，不保存到文件仓库
     *
     * @param file 上传的ZIP文件
     * @return 上传结果
     */
    @PostMapping("/uploadZip")
    public ResponseEntity<JSONObject> uploadZip(@RequestParam("file") MultipartFile file) throws IOException {
        JSONObject result = new JSONObject();
        
        try {
            // 验证文件类型
            if (!Objects.requireNonNull(file.getOriginalFilename()).toLowerCase().endsWith(".zip")) {
                result.set("code", 1);
                result.set("msg", "请上传ZIP格式的文件");
                return ResponseEntity.ok(result);
            }
            
            // 验证文件大小
            if (file.getSize() > 500 * 1024 * 1024) { // 100MB
                result.set("code", 1);
                result.set("msg", "文件大小不能超过100MB");
                return ResponseEntity.ok(result);
            }
            
            // 创建临时目录
            String sessionId = UUID.randomUUID().toString();
            Path tempDirPath = Paths.get(tempDir);
            if (!Files.exists(tempDirPath)) {
                Files.createDirectories(tempDirPath);
            }
            
            // 保存文件到临时目录
            String fileName = sessionId + "_" + file.getOriginalFilename();
            String filePath = tempDir + File.separator + fileName;
            File targetFile = new File(filePath);
            
            try (FileOutputStream fos = new FileOutputStream(targetFile)) {
                fos.write(file.getBytes());
            }
            
            // 验证ZIP文件完整性
            try {
                // 使用ZipFile验证ZIP文件完整性，如果文件损坏会抛出异常
                java.util.zip.ZipFile zipFile = new java.util.zip.ZipFile(targetFile);
                zipFile.close();
            } catch (java.util.zip.ZipException e) {
                // ZIP文件损坏或格式不正确
                targetFile.delete(); // 删除损坏的文件
                result.set("code", 1);
                result.set("msg", "上传的ZIP文件已损坏或格式不正确");
                return ResponseEntity.ok(result);
            }
            
            // 处理ZIP文件，解析其中的DOCX文件
            try {
                JSONObject processResult = docxToPdfService.processZipFile(filePath);
                return ResponseEntity.ok(processResult);
            } catch (Exception e) {
                // 处理ZIP文件时发生错误
                targetFile.delete(); // 清理临时文件
                result.set("code", 1);
                if (e.getMessage() != null && e.getMessage().contains("MALFORMED")) {
                    result.set("msg", "ZIP文件包含无效的文件名或编码错误，请检查ZIP文件");
                } else {
                    result.set("msg", "处理ZIP文件失败: " + e.getMessage());
                }
                return ResponseEntity.ok(result);
            }
            
        } catch (Exception e) {
            result.set("code", 1);
            result.set("msg", "上传文件处理失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 开始DOCX转PDF转换
     *
     * @param zipPath ZIP文件路径或任务ID
     * @return 转换结果
     */
    @PostMapping("/convert")
    public ResponseEntity<JSONObject> convertDocxToPdf(@RequestParam("zipPath") String zipPath) {
        String taskId = zipPath;
        if (zipPath.contains("/")) {
            // 如果是文件路径，则取文件名部分作为任务ID
            String[] parts = zipPath.split("/");
            String fileName = parts[parts.length - 1];
            if (fileName.contains(".")) {
                taskId = fileName.substring(0, fileName.lastIndexOf("."));

            }
        }
        
        JSONObject result = docxToPdfService.startConversion(taskId);
        return ResponseEntity.ok(result);
    }
    
    /**
     * 获取转换任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    @GetMapping("/status")
    public ResponseEntity<JSONObject> getConversionStatus(@RequestParam("taskId") String taskId) {
        JSONObject result = docxToPdfService.getConversionStatus(taskId);
        return ResponseEntity.ok(result);
    }
    
    /**
     * 下载转换后的PDF文件
     * @param token PDF压缩包路径
     * @param response 响应对象
     * @return 响应对象
     */
    @GetMapping("/download")
    public ResponseEntity<?> downloadPdfZip(@RequestParam("token") String token,
                                    HttpServletResponse response) {
        logger.info("收到下载请求，文件名: {}", token);
        
        try {
            // 参数验证
            if (token == null || token.isEmpty()) {
                return new ResponseEntity<>("无效的下载参数", HttpStatus.BAD_REQUEST);
            }
            
            // 安全检查：防止路径遍历
            if (token.contains("..") || token.startsWith("/") || token.startsWith("\\")) {
                logger.error("检测到可能的路径遍历攻击: {}", token);
                return new ResponseEntity<>("无效的文件路径", HttpStatus.BAD_REQUEST);
            }
            
            // 构建完整文件路径
            String filePath = tempDir + File.separator + token;
            File file = new File(filePath);
            
            // 验证文件是否存在且是有效的ZIP文件
            if (!file.exists() || !file.isFile()) {
                logger.error("文件不存在或不是文件: {}", filePath);
                return new ResponseEntity<>("文件不存在或已过期", HttpStatus.NOT_FOUND);
            }
            
            // 文件类型检查
            if (!token.toLowerCase().endsWith(".zip")) {
                logger.error("请求下载的文件不是ZIP格式: {}", token);
                return new ResponseEntity<>("仅支持下载ZIP格式的文件", HttpStatus.BAD_REQUEST);
            }
            
            // 验证文件大小
            logger.info("文件存在，大小: {}KB", file.length()/1024);
            if (file.length() == 0) {
                logger.error("文件大小为零: {}", filePath);
                return new ResponseEntity<>("文件内容为空", HttpStatus.BAD_REQUEST);
            }
            
            // 使用FileDownloadUtil处理文件下载
            try {
                return FileDownloadUtil.fileResponse(file, "转换结果.zip");
            } catch (Exception e) {
                logger.error("文件下载处理失败", e);
                return new ResponseEntity<>("文件下载处理失败: " + e.getMessage(), 
                        HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            logger.error("文件下载发生错误", e);
            return new ResponseEntity<>("下载失败: " + e.getMessage(), 
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}