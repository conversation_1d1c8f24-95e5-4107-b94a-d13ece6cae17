/**
 * 全景图热点编辑系统 - 热点管理模块
 * 
 * <AUTHOR>
 * @date 2025-06-10
 * @description 处理热点编辑、更新、定位等功能
 * @requires panorama-core.js, panorama-ui.js, panorama-device.js
 */

/**
 * 全景图编辑器热点管理模块
 */
var PanoramaHotspot = (function() {
    'use strict';
    
    // ==================== 热点数据管理 ====================
    
    /**
     * 加载热点数据
     */
    function loadHotspotData() {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) return;

        // 如果有当前节点ID，按节点过滤；否则加载所有热点
        var whereCondition = {
            taskId: currentTaskId
        };

        var currentNodeId = PanoramaCore.getCurrentNodeId();
        if (currentNodeId) {
            whereCondition.panoramaId = currentNodeId;
        }

        var hotspotTable = PanoramaCore.getHotspotTable();
        if (hotspotTable) {
            hotspotTable.reload({
                where: whereCondition
            });
        }
    }
    
    /**
     * 为指定节点刷新热点表格
     * @param {String} nodeId 节点ID
     */
    function refreshHotspotTableForNode(nodeId) {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        var hotspotTable = PanoramaCore.getHotspotTable();
        if (!currentTaskId || !hotspotTable) {
            return;
        }

        try {
            // 显示加载状态
            var loadingIndex = layui.layer.load(2, {
                shade: [0.1, '#fff'],
                content: '正在加载节点热点...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '40px',
                        'width': '200px'
                    });
                }
            });

            // 重新加载表格数据，包含panoramaId过滤参数
            hotspotTable.reload({
                where: {
                    taskId: currentTaskId,
                    panoramaId: nodeId
                },
                page: {
                    curr: 1 // 重置到第一页，因为不同节点的热点数量可能不同
                },
                done: function (res, curr, count) {
                    // 关闭加载状态
                    layui.layer.close(loadingIndex);

                    // 显示加载结果
                    if (res && res.code === 0) {
                        var hotspotCount = res.count || 0;

                        // 如果没有热点，显示提示
                        if (hotspotCount === 0) {
                            layui.layer.msg('当前节点暂无热点数据', {
                                icon: 0,
                                time: 2000
                            });
                        }
                    } else {
                        layui.layer.msg('加载热点数据失败', { icon: 2 });
                    }
                }
            });

        } catch (error) {
            layui.layer.msg('刷新热点表格失败', { icon: 2 });
        }
    }
    
    // ==================== 热点编辑功能 ====================
    
    /**
     * 编辑热点
     * @param {Object} hotspotData 热点数据
     */
    function editHotspot(hotspotData) {
        // 先加载设备列表
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) {
            layui.layer.msg('请先选择任务', { icon: 2 });
            return;
        }

        // 加载设备数据
        var $ = layui.$;
        $.get('/panorama/device/list', { taskId: currentTaskId }, function (res) {
            if (res.code == 0) {
                PanoramaCore.setDeviceList(res.data);
                showHotspotEditTab(hotspotData);
            } else {
                layui.layer.msg('加载设备列表失败', { icon: 2 });
            }
        });
    }

    /**
     * 添加新热点
     * @param {Object} hotspotData 热点数据（包含pan、tilt、nodeId等）
     */
    function addHotspot(hotspotData) {
        // 先加载设备列表
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) {
            layui.layer.msg('请先选择任务', { icon: 2 });
            return;
        }

        // 加载设备数据
        var $ = layui.$;
        $.get('/panorama/device/list', { taskId: currentTaskId }, function (res) {
            if (res.code == 0) {
                PanoramaCore.setDeviceList(res.data);
                // 构造新热点数据，标记为新增模式
                var newHotspotData = {
                    pan: hotspotData.pan,
                    tilt: hotspotData.tilt,
                    nodeId: hotspotData.nodeId,
                    isNew: true,
                    TITLE: '',
                    DESCRIPTION: ''
                };
                showHotspotEditTab(newHotspotData);
            } else {
                layui.layer.msg('加载设备列表失败', { icon: 2 });
            }
        });
    }

    /**
     * 显示热点编辑Tab对话框
     * @param {Object} hotspotData 热点数据
     */
    function showHotspotEditTab(hotspotData) {
        // 构建选择模式的内容
        var selectModeContent = buildSelectModeContent(hotspotData);

        // 构建填写模式的内容
        var inputModeContent = buildInputModeContent(hotspotData);

        // 使用layer.tab创建Tab对话框
        layui.layer.tab({
            area: ['500px', '310px'],
            tab: [{
                title: '📋 选择模式',
                content: selectModeContent
            }, {
                title: '✏️ 填写模式',
                content: inputModeContent
            }],
            success: function (layero, index) {
                // 渲染表单组件
                layui.form.render();

                // 监听选择模式的设备选择变化
                layui.form.on('select(selectModeTitle)', function (data) {
                    var deviceId = data.value;
                    var deviceList = PanoramaCore.getDeviceList();
                    if (deviceId && deviceList) {
                        var selectedDevice = deviceList.find(function (device) {
                            return device.DEVICE_ID == deviceId;
                        });
                        if (selectedDevice) {
                            var description = selectedDevice.DEVICE_NAME + ' ' +
                                (selectedDevice.DEVICE_CODE || '') + ' ' +
                                (selectedDevice.BATCH_NO || '');
                            layero.find('textarea[name="selectModeDescription"]').val(description.trim());
                        }
                    }
                });

                // 绑定选择模式的保存按钮事件
                layero.find('#saveSelectModeBtn').on('click', function () {
                    saveHotspotData(layero, hotspotData, index, 'select');
                });

                // 绑定选择模式的重置按钮事件
                layero.find('#resetSelectModeBtn').on('click', function () {
                    resetSelectModeForm(layero, hotspotData);
                });

                // 绑定填写模式的保存按钮事件
                layero.find('#saveInputModeBtn').on('click', function () {
                    saveHotspotData(layero, hotspotData, index, 'input');
                });

                // 绑定填写模式的重置按钮事件
                layero.find('#resetInputModeBtn').on('click', function () {
                    resetInputModeForm(layero, hotspotData);
                });
            }
        });
    }

    /**
     * 构建选择模式的内容
     * @param {Object} hotspotData 热点数据
     * @returns {string} HTML内容
     */
    function buildSelectModeContent(hotspotData) {
        var deviceList = PanoramaCore.getDeviceList();
        var options = '<option value="">请选择设备名称</option>';
        
        if (deviceList && deviceList.length > 0) {
            for (var i = 0; i < deviceList.length; i++) {
                var device = deviceList[i];
                var selected = '';
                if (hotspotData.DEVICE_ID && hotspotData.DEVICE_ID == device.DEVICE_ID) {
                    selected = 'selected';
                } else if (hotspotData.TITLE && hotspotData.TITLE === device.DEVICE_NAME) {
                    selected = 'selected';
                }
                options += '<option value="' + device.DEVICE_ID + '" ' + selected + '>' + device.DEVICE_NAME + '</option>';
            }
        }

        var currentDescription = (hotspotData.DESCRIPTION || '').replace(/"/g, '&quot;');

        return [
            '<div style="padding: 20px; min-height: 200px;">',
                '<form class="layui-form">',
                    '<div class="layui-form-item">',
                        '<label class="layui-form-label">热点标题</label>',
                        '<div class="layui-input-block">',
                            '<select name="selectModeTitle" lay-filter="selectModeTitle" lay-search>',
                                options,
                            '</select>',
                        '</div>',
                    '</div>',
                    '<div class="layui-form-item layui-form-text">',
                        '<label class="layui-form-label">热点描述</label>',
                        '<div class="layui-input-block">',
                            '<textarea name="selectModeDescription" placeholder="选择设备后自动填充，可手动修改" class="layui-textarea" rows="4">' + currentDescription + '</textarea>',
                        '</div>',
                    '</div>',
                    '<div class="layui-form-item" style="text-align: center; margin-bottom: 0px;">',
                        '<button type="button" class="layui-btn" id="saveSelectModeBtn">保存</button>',
                        '<button type="button" class="layui-btn layui-btn-primary" id="resetSelectModeBtn">重置</button>',
                    '</div>',
                '</form>',
            '</div>'
        ].join('');
    }

    /**
     * 构建填写模式的内容
     * @param {Object} hotspotData 热点数据
     * @returns {string} HTML内容
     */
    function buildInputModeContent(hotspotData) {
        var currentTitle = (hotspotData.TITLE || '').replace(/"/g, '&quot;');
        var currentDescription = (hotspotData.DESCRIPTION || '').replace(/"/g, '&quot;');

        return [
            '<div style="padding: 20px; min-height: 200px;">',
                '<form class="layui-form">',
                    '<div class="layui-form-item">',
                        '<label class="layui-form-label">热点标题</label>',
                        '<div class="layui-input-block">',
                            '<input type="text" name="inputModeTitle" value="' + currentTitle + '" placeholder="请输入自定义热点标题" autocomplete="off" class="layui-input">',
                        '</div>',
                    '</div>',
                    '<div class="layui-form-item layui-form-text">',
                        '<label class="layui-form-label">热点描述</label>',
                        '<div class="layui-input-block">',
                            '<textarea name="inputModeDescription" placeholder="请输入自定义热点描述" class="layui-textarea" rows="4">' + currentDescription + '</textarea>',
                        '</div>',
                    '</div>',
                    '<div class="layui-form-item" style="text-align: center; margin-bottom: 0px;">',
                        '<button type="button" class="layui-btn" id="saveInputModeBtn">保存</button>',
                        '<button type="button" class="layui-btn layui-btn-primary" id="resetInputModeBtn">重置</button>',
                    '</div>',
                '</form>',
            '</div>'
        ].join('');
    }
    
    // ==================== 热点表单处理 ====================

    /**
     * 重置选择模式表单
     * @param {Object} layero 对话框jQuery对象
     * @param {Object} hotspotData 热点数据
     */
    function resetSelectModeForm(layero, hotspotData) {
        // 重置下拉选择框到初始状态
        var select = layero.find('select[name="selectModeTitle"]');
        var deviceList = PanoramaCore.getDeviceList();

        // 根据热点数据设置初始值
        if (hotspotData.DEVICE_ID) {
            select.val(hotspotData.DEVICE_ID);
        } else if (hotspotData.TITLE) {
            // 尝试根据标题匹配设备
            var matchedDevice = deviceList ? deviceList.find(function (device) {
                return device.DEVICE_NAME === hotspotData.TITLE;
            }) : null;
            if (matchedDevice) {
                select.val(matchedDevice.DEVICE_ID);
            } else {
                select.val('');
            }
        } else {
            select.val('');
        }

        // 重置描述框到初始状态
        var initialDescription = hotspotData.DESCRIPTION || '';
        layero.find('textarea[name="selectModeDescription"]').val(initialDescription);

        // 重新渲染表单组件
        layui.form.render('select');

        layui.layer.msg('表单已重置', { icon: 1, time: 1000 });
    }

    /**
     * 重置填写模式表单
     * @param {Object} layero 对话框jQuery对象
     * @param {Object} hotspotData 热点数据
     */
    function resetInputModeForm(layero, hotspotData) {
        // 重置标题输入框到初始状态
        var initialTitle = hotspotData.TITLE || '';
        layero.find('input[name="inputModeTitle"]').val(initialTitle);

        // 重置描述输入框到初始状态
        var initialDescription = hotspotData.DESCRIPTION || '';
        layero.find('textarea[name="inputModeDescription"]').val(initialDescription);

        layui.layer.msg('表单已重置', { icon: 1, time: 1000 });
    }

    /**
     * 保存热点数据
     * @param {Object} layero 对话框jQuery对象
     * @param {Object} hotspotData 热点数据
     * @param {number} index 对话框索引
     * @param {string} mode 模式（'select' 或 'input'）
     */
    function saveHotspotData(layero, hotspotData, index, mode) {
        var editedTitle, editedDescription, matchedDevice = null;
        var deviceList = PanoramaCore.getDeviceList();

        if (mode === 'select') {
            // 选择模式
            var selectedDeviceId = layero.find('select[name="selectModeTitle"]').val();
            editedDescription = layero.find('textarea[name="selectModeDescription"]').val().trim();

            if (!selectedDeviceId) {
                layui.layer.msg('请选择设备名称', { icon: 2 });
                return;
            }

            // 查找选中的设备
            if (deviceList) {
                matchedDevice = deviceList.find(function (device) {
                    return device.DEVICE_ID == selectedDeviceId;
                });
                if (matchedDevice) {
                    editedTitle = matchedDevice.DEVICE_NAME;
                }
            }

        } else {
            // 填写模式
            editedTitle = layero.find('input[name="inputModeTitle"]').val().trim();
            editedDescription = layero.find('textarea[name="inputModeDescription"]').val().trim();

            if (!editedTitle) {
                layui.layer.msg('请输入热点标题', { icon: 2 });
                return;
            }
        }

        // 检查热点标题是否重复（新热点没有HOTSPOT_ID，传入null）
        var hotspotId = hotspotData.isNew ? null : hotspotData.HOTSPOT_ID;
        checkHotspotTitle(editedTitle, hotspotId, function (isDuplicate, count) {
            if (isDuplicate) {
                // 标题重复，显示确认对话框
                layui.layer.confirm('热点标题"' + editedTitle + '"在当前任务中已被使用（共' + count + '个热点），是否继续保存？', {
                    icon: 3,
                    title: '热点标题重复',
                    btn: ['继续保存', '取消']
                }, function (confirmIndex) {
                    // 用户确认继续保存
                    layui.layer.close(confirmIndex);
                    proceedToSaveHotspot(hotspotData, editedTitle, editedDescription, matchedDevice, index);
                }, function (confirmIndex) {
                    // 用户取消保存
                    layui.layer.close(confirmIndex);
                });
            } else {
                // 标题不重复，直接保存
                proceedToSaveHotspot(hotspotData, editedTitle, editedDescription, matchedDevice, index);
            }
        });
    }

    /**
     * 检查热点标题是否重复
     * @param {string} title 标题
     * @param {string} hotspotId 热点ID
     * @param {Function} callback 回调函数
     */
    function checkHotspotTitle(title, hotspotId, callback) {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        var $ = layui.$;

        $.post('/panorama/hotspot/checkTitle', {
            taskId: currentTaskId,
            title: title,
            hotspotId: hotspotId
        }, function (res) {
            if (res.success) {
                callback(res.isDuplicate, res.count);
            } else {
                layui.layer.msg('检查热点标题失败: ' + res.msg, { icon: 2 });
                callback(false, 0);
            }
        }).fail(function () {
            layui.layer.msg('网络请求失败', { icon: 2 });
            callback(false, 0);
        });
    }

    /**
     * 执行热点保存
     * @param {Object} hotspotData 热点数据
     * @param {string} editedTitle 编辑后的标题
     * @param {string} editedDescription 编辑后的描述
     * @param {Object} matchedDevice 匹配的设备
     * @param {number} index 对话框索引
     */
    function proceedToSaveHotspot(hotspotData, editedTitle, editedDescription, matchedDevice, index) {
        if (hotspotData.isNew) {
            // 新增热点
            var formData = {
                taskId: PanoramaCore.getCurrentTaskId(),
                nodeId: hotspotData.nodeId,
                pan: hotspotData.pan,
                tilt: hotspotData.tilt,
                title: editedTitle,
                description: editedDescription,
                deviceId: matchedDevice ? matchedDevice.DEVICE_ID : null
            };
            addNewHotspot(formData);
        } else {
            // 更新现有热点
            var formData = {
                hotspotId: hotspotData.HOTSPOT_ID,
                editedTitle: editedTitle,
                editedDescription: editedDescription,
                deviceId: matchedDevice ? matchedDevice.DEVICE_ID : null
            };
            updateHotspot(formData);
        }
        layui.layer.close(index);
    }

    /**
     * 添加新热点
     * @param {Object} formData 表单数据
     */
    function addNewHotspot(formData) {
        var $ = layui.$;
        $.post('/panorama/hotspot/add', formData, function (res) {
            if (res.success) {
                layui.layer.closeAll();
                layui.layer.msg('热点添加成功', { icon: 1 });

                var hotspotTable = PanoramaCore.getHotspotTable();
                if (hotspotTable) {
                    hotspotTable.reload();
                }

                // 向iframe发送添加热点消息，立即在前端渲染
                sendAddHotspotToIframe(formData, res.data);

            } else {
                layui.layer.msg('添加失败: ' + res.msg, { icon: 2 });
            }
        }).fail(function () {
            layui.layer.msg('网络请求失败', { icon: 2 });
        });
    }

    /**
     * 更新热点信息
     * @param {Object} formData 表单数据
     */
    function updateHotspot(formData) {
        var $ = layui.$;
        $.post('/panorama/hotspot/update', formData, function (res) {
            if (res.success) {
                layui.layer.closeAll();
                layui.layer.msg('热点信息更新成功', { icon: 1 });

                var hotspotTable = PanoramaCore.getHotspotTable();
                if (hotspotTable) {
                    hotspotTable.reload();
                }

                // 即时更新全景图中的热点信息
                sendHotspotUpdateToIframe(formData);

                // 移除updatePreview()调用，避免刷新全景图导致用户忘记编辑的热点位置
            } else {
                layui.layer.msg('更新失败: ' + res.msg, { icon: 2 });
            }
        }).fail(function () {
            layui.layer.msg('网络请求失败', { icon: 2 });
        });
    }

    /**
     * 向iframe发送添加热点消息
     * @param {Object} formData 表单数据
     * @param {Object} responseData 后端返回的热点数据
     */
    function sendAddHotspotToIframe(formData, responseData) {
        var currentNodeId = PanoramaCore.getCurrentNodeId();

        // 构建添加热点消息
        var message = {
            type: 'addHotspotToView',
            hotspot: {
                id: responseData.hotspotXmlId || ('hs_' + Date.now() + '_' + Math.random().toString(36).substr(2, 3)),
                pan: formData.pan,
                tilt: formData.tilt,
                title: formData.title,
                description: formData.description,
                skinid: 'stand-alone',
                url: '',
                target: ''
            },
            nodeId: currentNodeId
        };

        // 发送消息给iframe
        var iframe = document.getElementById('panoramaFrame');
        if (iframe && iframe.contentWindow) {
            try {
                iframe.contentWindow.postMessage(message, '*');
            } catch (e) {
                // 发送失败，静默处理
            }
        }
    }

    /**
     * 向iframe发送热点更新消息
     * @param {Object} formData 表单数据
     */
    function sendHotspotUpdateToIframe(formData) {
        // 首先获取热点的坐标信息
        if (!formData.hotspotId) {
            return;
        }

        var $ = layui.$;
        $.ajax({
            url: '/panorama/hotspot/locate',
            type: 'POST',
            data: {
                hotspotId: formData.hotspotId
            },
            success: function (res) {
                if (res.success && res.data) {
                    var pan = res.data.PAN;
                    var tilt = res.data.TILT;
                    var currentNodeId = PanoramaCore.getCurrentNodeId();

                    // 构建热点更新消息
                    var message = {
                        type: 'updateHotspot',
                        hotspot: {
                            pan: pan,
                            tilt: tilt,
                            title: formData.editedTitle,
                            description: formData.editedDescription
                        },
                        nodeId: currentNodeId
                    };

                    // 发送消息给iframe
                    var iframe = document.getElementById('panoramaFrame');
                    if (iframe && iframe.contentWindow) {
                        try {
                            iframe.contentWindow.postMessage(message, '*');
                        } catch (e) {
                            // 发送失败，静默处理
                        }
                    }
                }
            },
            error: function () {
                // 获取坐标失败，静默处理
            }
        });
    }

    // ==================== 热点定位功能 ====================

    /**
     * 热点定位功能
     * @param {Number} hotspotId 热点ID
     */
    function locateHotspot(hotspotId) {
        if (!hotspotId) {
            layui.layer.msg('热点ID无效', { icon: 2 });
            return;
        }

        // 检查iframe是否已加载
        var iframe = document.getElementById('panoramaFrame');
        if (!iframe || !iframe.src || iframe.style.display === 'none') {
            layui.layer.msg('请先上传全景图文件', { icon: 2 });
            return;
        }

        // 调用后端API获取热点位置信息
        var $ = layui.$;
        $.ajax({
            url: '/panorama/hotspot/locate',
            type: 'POST',
            data: {
                hotspotId: hotspotId
            },
            success: function (res) {
                if (res.success && res.data) {
                    var pan = parseFloat(res.data.PAN);
                    var tilt = parseFloat(res.data.TILT);

                    // 验证数据有效性
                    if (isNaN(pan) || isNaN(tilt)) {
                        layui.layer.msg('热点位置数据无效', { icon: 2 });
                        return;
                    }

                    // 向iframe发送定位消息，包含高亮信息
                    var message = {
                        type: 'locateHotspot',
                        pan: pan,
                        tilt: tilt,
                        speed: 10.0, // 快速过渡效果
                        highlightTarget: true // 标记需要高亮目标热点
                    };

                    try {
                        iframe.contentWindow.postMessage(message, '*');
                    } catch (e) {
                        layui.layer.msg('定位失败，请确保全景图已完全加载', { icon: 2 });
                    }
                } else {
                    layui.layer.msg('获取热点位置失败', { icon: 2 });
                }
            },
            error: function () {
                layui.layer.msg('定位请求失败，请重试', { icon: 2 });
            }
        });
    }

    // ==================== 热点高亮功能 ====================
    // 注意：热点高亮功能已集成到定位功能中，定位热点时会自动高亮目标热点

    // ==================== 热点删除功能 ====================

    /**
     * 删除热点
     * @param {Object} hotspotData 热点数据
     */
    function deleteHotspot(hotspotData) {
        if (!hotspotData || !hotspotData.HOTSPOT_ID) {
            layui.layer.msg('热点数据无效', { icon: 2 });
            return;
        }

        // 显示删除确认对话框
        showDeleteConfirmDialog(hotspotData);
    }

    /**
     * 显示删除确认对话框
     * @param {Object} hotspotData 热点数据
     */
    function showDeleteConfirmDialog(hotspotData) {
        var title = hotspotData.TITLE || '未命名热点';
        var description = hotspotData.DESCRIPTION || '';

        var confirmContent =
            '<div style="padding: 20px; text-align: center;">' +
                '<div style="color: #ff4757; font-size: 20px; margin-bottom: 15px;">' +
                    '<i class="layui-icon layui-icon-close-fill"></i>' +
                '</div>' +
                '<div style="color: #ff4757; font-size: 16px; font-weight: bold; margin-bottom: 15px;">' +
                    '确认删除热点' +
                '</div>' +
                '<div style="color: #333; margin-bottom: 20px; line-height: 1.6;">' +
                    '<div style="margin-bottom: 8px;"><strong>热点标题：</strong>' + title + '</div>' +
                    (description ? '<div style="margin-bottom: 8px;"><strong>热点描述：</strong>' + description + '</div>' : '') +
                    '<div style="color: #ff4757; font-weight: bold; margin-top: 15px;">此操作不可撤销！</div>' +
                '</div>' +
                '<div style="color: #666; font-size: 12px;">' +
                    '删除后将从表格、全景图和XML文件中移除此热点' +
                '</div>' +
            '</div>';

        layui.layer.confirm(confirmContent, {
            icon: 0,
            title: '删除确认',
            area: ['400px', 'auto'],
            btn: ['确认删除', '取消'],
            btn1: function(index) {
                layui.layer.close(index);
                // 执行删除操作
                executeHotspotDeletion(hotspotData);
            }
        });
    }

    /**
     * 执行热点删除操作
     * @param {Object} hotspotData 热点数据
     */
    function executeHotspotDeletion(hotspotData) {
        var loadingIndex = layer.msg('正在删除热点，请稍候...', { icon: 16, shade: 0.01 });

        var $ = layui.$;
        $.ajax({
            url: '/panorama/hotspot/' + hotspotData.HOTSPOT_ID,
            type: 'DELETE',
            success: function(res) {
                layui.layer.close(loadingIndex);

                if (res.success) {
                    layui.layer.msg('热点删除成功', { icon: 1 });

                    // 刷新热点表格
                    var hotspotTable = PanoramaCore.getHotspotTable();
                    if (hotspotTable) {
                        hotspotTable.reload();
                    }

                    // 从全景图中移除热点
                    if (res.data) {
                        sendRemoveHotspotToIframe(res.data);
                    }

                } else {
                    layui.layer.msg('删除失败: ' + res.msg, { icon: 2 });
                }
            },
            error: function() {
                layui.layer.close(loadingIndex);
                layui.layer.msg('网络请求失败，请重试', { icon: 2 });
            }
        });
    }

    /**
     * 向iframe发送删除热点消息
     * @param {Object} hotspotData 热点数据
     */
    function sendRemoveHotspotToIframe(hotspotData) {
        var message = {
            type: 'removeHotspotFromView',
            hotspotId: hotspotData.hotspotXmlId,
            nodeId: hotspotData.nodeId,
            pan: hotspotData.pan,
            tilt: hotspotData.tilt
        };

        // 发送消息给iframe
        var iframe = document.getElementById('panoramaFrame');
        if (iframe && iframe.contentWindow) {
            try {
                iframe.contentWindow.postMessage(message, '*');
            } catch (e) {
                // 发送失败，静默处理
            }
        }
    }

    // ==================== 热点批量删除功能 ====================

    /**
     * 显示批量删除对话框
     */
    function showBatchDeleteDialog() {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) {
            layui.layer.msg('请先选择任务', { icon: 2 });
            return;
        }

        var currentNodeId = PanoramaCore.getCurrentNodeId();
        if (!currentNodeId) {
            layui.layer.msg('请先选择节点', { icon: 2 });
            return;
        }

        // 创建批量删除对话框
        var dialogContent = buildBatchDeleteDialogContent();

        var dialogIndex = layui.layer.open({
            type: 1,
            title: '批量删除热点',
            area: ['800px', '600px'],
            content: dialogContent,
            success: function(layero, index) {
                // 对话框打开成功后初始化
                initBatchDeleteDialog(index);
            }
        });
    }

    /**
     * 构建批量删除对话框内容
     */
    function buildBatchDeleteDialogContent() {
        return '<div id="batchDeleteContainer">' +
                   '<div class="batch-delete-filters">' +
                       '<div class="layui-form layui-form-pane">' +
                           '<div class="layui-form-item">' +
                               '<div class="layui-inline">' +
                                   '<label class="layui-form-label" style="width: 80px;">标题筛选</label>' +
                                   '<div class="layui-input-inline" style="width: 200px;">' +
                                       '<input type="text" id="titleFilter" placeholder="输入标题关键词" class="layui-input">' +
                                   '</div>' +
                               '</div>' +
                               '<div class="layui-inline">' +
                                   '<label class="layui-form-label" style="width: 80px;">描述筛选</label>' +
                                   '<div class="layui-input-inline" style="width: 200px;">' +
                                       '<input type="text" id="descriptionFilter" placeholder="输入描述关键词" class="layui-input">' +
                                   '</div>' +
                               '</div>' +
                           '</div>' +
                       '</div>' +
                   '</div>' +
                   '<div class="batch-delete-table">' +
                       '<table id="batchDeleteTable" lay-filter="batchDeleteTable"></table>' +
                   '</div>' +
                   '<div class="batch-delete-actions">' +
                       '<div>' +
                           '<span id="selectedCount">已选择 0 个热点</span>' +
                       '</div>' +
                       '<button type="button" class="layui-btn layui-btn-danger" id="confirmBatchDelete" disabled>' +
                           '<i class="layui-icon layui-icon-delete"></i> 删除选中热点' +
                       '</button>' +
                       '<button type="button" class="layui-btn layui-btn-primary" id="cancelBatchDelete">' +
                           '取消' +
                       '</button>' +
                   '</div>' +
               '</div>';
    }

    /**
     * 初始化批量删除对话框
     */
    function initBatchDeleteDialog(dialogIndex) {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        var currentNodeId = PanoramaCore.getCurrentNodeId();

        // 初始化表格
        var batchDeleteTable = layui.table.render({
            elem: '#batchDeleteTable',
            url: '/panorama/hotspot/list',
            where: {
                taskId: currentTaskId,
                panoramaId: currentNodeId,
                page: 1,
                limit: 999999 // 设置一个很大的limit值来获取所有数据
            },
            page: false, // 不分页，一次性显示所有
            height: 380, // 自适应高度，减去20px的边距
            cols: [[
                { type: 'checkbox', fixed: 'left' },
                { type: 'numbers', title: '序号', width: 60 },
                {
                    field: 'TITLE',
                    title: '热点标题',
                    width: 200,
                    templet: function(d) {
                        var title = d.TITLE || '-';
                        return '<span title="' + title + '">' + title + '</span>';
                    }
                },
                {
                    field: 'DESCRIPTION',
                    title: '热点描述',
                    width: 390,
                    templet: function(d) {
                        var desc = d.DESCRIPTION || '-';
                        return '<span title="' + desc + '">' + desc + '</span>';
                    }
                }
            ]],
            done: function(res, curr, count) {
                if (count === 0) {
                    layui.layer.msg('当前节点暂无热点数据', { icon: 0 });
                }
            }
        });

        // 绑定筛选事件
        bindBatchDeleteFilters(batchDeleteTable);

        // 绑定表格选择事件
        bindBatchDeleteTableEvents(batchDeleteTable, dialogIndex);

        // 绑定按钮事件
        bindBatchDeleteButtonEvents(dialogIndex);
    }

    /**
     * 绑定批量删除筛选事件
     */
    function bindBatchDeleteFilters(batchDeleteTable) {
        var titleFilter = document.getElementById('titleFilter');
        var descriptionFilter = document.getElementById('descriptionFilter');

        // 防抖函数
        var debounceTimer = null;
        var debounceFilter = function() {
            if (debounceTimer) {
                clearTimeout(debounceTimer);
            }
            debounceTimer = setTimeout(function() {
                var titleKeyword = titleFilter.value.trim();
                var descKeyword = descriptionFilter.value.trim();

                // 重新加载表格数据，带筛选条件
                var currentTaskId = PanoramaCore.getCurrentTaskId();
                var currentNodeId = PanoramaCore.getCurrentNodeId();

                batchDeleteTable.reload({
                    where: {
                        taskId: currentTaskId,
                        panoramaId: currentNodeId,
                        titleKeyword: titleKeyword,
                        descriptionKeyword: descKeyword,
                        page: 1,
                        limit: 999999 // 确保筛选时也获取所有数据
                    }
                });
            }, 300);
        };

        titleFilter.addEventListener('input', debounceFilter);
        descriptionFilter.addEventListener('input', debounceFilter);
    }

    /**
     * 绑定批量删除表格事件
     */
    function bindBatchDeleteTableEvents(batchDeleteTable, dialogIndex) {
        // 监听复选框选择
        layui.table.on('checkbox(batchDeleteTable)', function(obj) {
            updateSelectedCount();
        });
    }

    /**
     * 绑定批量删除按钮事件
     */
    function bindBatchDeleteButtonEvents(dialogIndex) {
        var confirmBtn = document.getElementById('confirmBatchDelete');
        var cancelBtn = document.getElementById('cancelBatchDelete');

        if (confirmBtn) {
            confirmBtn.addEventListener('click', function() {
                executeBatchDelete(dialogIndex);
            });
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', function() {
                layui.layer.close(dialogIndex);
            });
        }
    }

    /**
     * 更新选中计数
     */
    function updateSelectedCount() {
        var checkStatus = layui.table.checkStatus('batchDeleteTable');
        var selectedData = checkStatus.data;
        var count = selectedData.length;

        var selectedCountEl = document.getElementById('selectedCount');
        var confirmBtn = document.getElementById('confirmBatchDelete');

        if (selectedCountEl) {
            selectedCountEl.textContent = '已选择 ' + count + ' 个热点';
        }

        if (confirmBtn) {
            if (count > 0) {
                confirmBtn.disabled = false;
                confirmBtn.className = 'layui-btn layui-btn-danger';
            } else {
                confirmBtn.disabled = true;
                confirmBtn.className = 'layui-btn layui-btn-danger layui-btn-disabled';
            }
        }
    }

    /**
     * 执行批量删除
     */
    function executeBatchDelete(dialogIndex) {
        var checkStatus = layui.table.checkStatus('batchDeleteTable');
        var selectedData = checkStatus.data;

        if (selectedData.length === 0) {
            layui.layer.msg('请选择要删除的热点', { icon: 2 });
            return;
        }

        // 显示确认对话框
        showBatchDeleteConfirmDialog(selectedData, dialogIndex);
    }

    /**
     * 显示批量删除确认对话框
     */
    function showBatchDeleteConfirmDialog(selectedData, parentDialogIndex) {
        var count = selectedData.length;
        var hotspotList = '';

        for (var i = 0; i < Math.min(selectedData.length, 5); i++) {
            var hotspot = selectedData[i];
            hotspotList += '<div style="margin: 5px 0;">• ' + (hotspot.TITLE || '未命名热点') + '</div>';
        }

        if (selectedData.length > 5) {
            hotspotList += '<div style="margin: 5px 0; color: #666;">... 还有 ' + (selectedData.length - 5) + ' 个热点</div>';
        }

        var confirmContent =
            '<div style="padding: 20px; text-align: center;">' +
                '<div style="color: #ff4757; font-size: 20px; margin-bottom: 15px;">' +
                    '<i class="layui-icon layui-icon-close-fill"></i>' +
                '</div>' +
                '<div style="color: #ff4757; font-size: 16px; font-weight: bold; margin-bottom: 15px;">' +
                    '确认批量删除热点' +
                '</div>' +
                '<div style="color: #333; margin-bottom: 20px; line-height: 1.6;">' +
                    '<div style="margin-bottom: 10px;">您即将删除 <strong style="color: #ff4757;">' + count + '</strong> 个热点：</div>' +
                    '<div style="max-height: 150px; overflow-y: auto; text-align: left; background: #f8f8f8; padding: 10px; border-radius: 4px;">' +
                        hotspotList +
                    '</div>' +
                    '<div style="color: #ff4757; font-weight: bold; margin-top: 15px;">此操作不可撤销！</div>' +
                '</div>' +
                '<div style="color: #666; font-size: 12px;">' +
                    '删除后将从表格、全景图和XML文件中移除这些热点' +
                '</div>' +
            '</div>';

        layui.layer.confirm(confirmContent, {
            icon: 0,
            title: '批量删除确认',
            area: ['450px', 'auto'],
            btn: ['确认删除 ' + count + ' 个热点', '取消操作'],
            btn1: function(index) {
                layui.layer.close(index);
                // 执行批量删除操作
                performBatchDelete(selectedData, parentDialogIndex);
            }
        });
    }

    /**
     * 执行批量删除操作
     */
    function performBatchDelete(selectedData, parentDialogIndex) {
        var hotspotIds = [];
        for (var i = 0; i < selectedData.length; i++) {
            hotspotIds.push(selectedData[i].HOTSPOT_ID);
        }

        var loadingIndex = layer.msg('正在删除热点 0/' + hotspotIds.length + '...', { icon: 16, shade: 0.01 });

        var $ = layui.$;
        $.ajax({
            url: '/panorama/hotspot/batch',
            type: 'DELETE',
            contentType: 'application/json',
            data: JSON.stringify(hotspotIds),
            success: function(res) {
                layui.layer.close(loadingIndex);

                if (res.success) {
                    layui.layer.msg('批量删除成功，共删除 ' + res.successCount + ' 个热点', { icon: 1 });

                    // 关闭批量删除对话框
                    layui.layer.close(parentDialogIndex);

                    // 刷新主表格
                    var hotspotTable = PanoramaCore.getHotspotTable();
                    if (hotspotTable) {
                        hotspotTable.reload();
                    }

                    // 批量发送删除消息给iframe
                    for (var i = 0; i < res.successList.length; i++) {
                        var successItem = res.successList[i];
                        if (successItem.data) {
                            sendRemoveHotspotToIframe(successItem.data);
                        }
                    }

                } else {
                    var errorMsg = res.msg || '批量删除失败';
                    if (res.failureCount > 0 && res.successCount > 0) {
                        errorMsg += '，成功删除 ' + res.successCount + ' 个，失败 ' + res.failureCount + ' 个';
                    }
                    layui.layer.msg(errorMsg, { icon: 2 });
                }
            },
            error: function() {
                layui.layer.close(loadingIndex);
                layui.layer.msg('网络请求失败，请重试', { icon: 2 });
            }
        });
    }

    // ==================== 公共API ====================
    return {
        // 热点数据管理
        loadHotspotData: loadHotspotData,
        refreshHotspotTableForNode: refreshHotspotTableForNode,

        // 热点编辑
        editHotspot: editHotspot,
        addHotspot: addHotspot,
        showHotspotEditTab: showHotspotEditTab,

        // 热点表单处理
        resetSelectModeForm: resetSelectModeForm,
        resetInputModeForm: resetInputModeForm,
        saveHotspotData: saveHotspotData,
        checkHotspotTitle: checkHotspotTitle,
        proceedToSaveHotspot: proceedToSaveHotspot,
        addNewHotspot: addNewHotspot,
        updateHotspot: updateHotspot,
        sendAddHotspotToIframe: sendAddHotspotToIframe,
        sendHotspotUpdateToIframe: sendHotspotUpdateToIframe,

        // 热点定位
        locateHotspot: locateHotspot,

        // 热点删除
        deleteHotspot: deleteHotspot,
        sendRemoveHotspotToIframe: sendRemoveHotspotToIframe,

        // 热点批量删除
        showBatchDeleteDialog: showBatchDeleteDialog,

        // 内部函数
        buildSelectModeContent: buildSelectModeContent,
        buildInputModeContent: buildInputModeContent
    };
})();

// ==================== 向后兼容性支持 ====================
// 保持原有函数的全局访问方式

window.loadHotspotData = PanoramaHotspot.loadHotspotData;
window.refreshHotspotTableForNode = PanoramaHotspot.refreshHotspotTableForNode;
window.editHotspot = PanoramaHotspot.editHotspot;
window.addHotspot = PanoramaHotspot.addHotspot;
window.showHotspotEditTab = PanoramaHotspot.showHotspotEditTab;
window.resetSelectModeForm = PanoramaHotspot.resetSelectModeForm;
window.resetInputModeForm = PanoramaHotspot.resetInputModeForm;
window.saveHotspotData = PanoramaHotspot.saveHotspotData;
window.checkHotspotTitle = PanoramaHotspot.checkHotspotTitle;
window.proceedToSaveHotspot = PanoramaHotspot.proceedToSaveHotspot;
window.updateHotspot = PanoramaHotspot.updateHotspot;
window.sendHotspotUpdateToIframe = PanoramaHotspot.sendHotspotUpdateToIframe;
window.locateHotspot = PanoramaHotspot.locateHotspot;
window.deleteHotspot = PanoramaHotspot.deleteHotspot;
window.sendRemoveHotspotToIframe = PanoramaHotspot.sendRemoveHotspotToIframe;
window.showBatchDeleteDialog = PanoramaHotspot.showBatchDeleteDialog;
window.buildSelectModeContent = PanoramaHotspot.buildSelectModeContent;
window.buildInputModeContent = PanoramaHotspot.buildInputModeContent;
