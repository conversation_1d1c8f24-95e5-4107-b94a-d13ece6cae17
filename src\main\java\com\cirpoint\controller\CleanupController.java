package com.cirpoint.controller;

import cn.hutool.json.JSONObject;
import com.cirpoint.service.CleanupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 清理控制器
 * 提供文件和数据库清理的接口
 */
@Slf4j
@RestController
@RequestMapping("/api/cleanup")
@Tag(name = "清理管理", description = "提供文件和数据库清理的接口")
public class CleanupController {

    @Autowired
    private CleanupService cleanupService;

    /**
     * 清理过期数据
     * @param days 清理多少天前的数据（默认7天）
     * @return 清理结果
     */
    @Operation(summary = "清理过期数据", description = "清理指定天数前的文件和数据库记录")
    @PostMapping("/expired")
    public JSONObject cleanupExpiredData(
            @Parameter(description = "清理多少天前的数据", example = "7")
            @RequestParam(defaultValue = "7") int days) {
        log.info("收到清理请求，清理{}天前的数据", days);
        return cleanupService.cleanupExpiredDataWithResult(days);
    }

    /**
     * 测试清理功能
     * @return 清理结果
     */
    @Operation(summary = "测试清理功能", description = "测试清理功能（默认清理7天前的数据）")
    @PostMapping("/test")
    public JSONObject testCleanup() {
        log.info("收到测试清理请求");
        return cleanupService.cleanupExpiredDataWithResult(7);
    }
} 