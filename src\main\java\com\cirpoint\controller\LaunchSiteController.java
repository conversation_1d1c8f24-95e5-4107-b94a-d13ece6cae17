package com.cirpoint.controller;

import com.cirpoint.model.Result;
import com.cirpoint.service.LaunchSiteService;
import com.cirpoint.util.FileDownloadUtil;
import java.io.File;
import java.io.IOException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/launch")
public class LaunchSiteController {

	private final LaunchSiteService launchSiteService;

	@Autowired
	public LaunchSiteController(LaunchSiteService launchSiteService) {
		this.launchSiteService = launchSiteService;
	}

	/**
	 * 导入分级承诺映射模板
	 *
	 * @param file     Excel文件
	 * @param username 导入用户
	 * @return 导入结果
	 */
	@PostMapping("/import/mapping")
	public ResponseEntity<?> importMappingTemplate(@RequestParam("file") MultipartFile file,
												   @RequestParam("username") String username) throws IOException {
		return Result.ok(launchSiteService.importMappingTemplate(file, username));
	}

	/**
	 * 导出分级承诺映射数据
	 *
	 * @return Excel文件
	 */
	@PostMapping("/export/mapping")
	public ResponseEntity<?> exportMappingData() {
		File file = launchSiteService.exportMappingData();
		return FileDownloadUtil.fileResponseAndDelete(file);
	}


	/**
	 * 转换自动表格
	 *
	 * @param reportId 报告ID
	 * @return 表格数据
	 */
	@GetMapping("/convert/auto/table")
	public ResponseEntity<?> convertAutoTable(@RequestParam("reportId") String reportId) {
		String tableData = launchSiteService.convertAutoTable(reportId);
		return Result.ok("成功", tableData);
	}
}
