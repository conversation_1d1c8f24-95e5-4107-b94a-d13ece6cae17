<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">treeNode.</span>isFirstNode</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Judge whether the node is the sibling nodes's first node.</p>
			<p class="highlight_red">If you use the 'exhide' pack, so this attribute will only support the node which be shown. </p>
			<p class="highlight_red">Do not initialize or modify it, it is created by the zTree.</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p> true means: the node is first node.</p>
	<p> false means: the node is not first node.</p>
	<p class="highlight_red">If the node has been hidden, isFirstNode = false</p>
	</div>
	<h3>Examples of treeNode</h3>
	<h4>1. Judge whether the first selected node is the sibling nodes's first node.</h4>
	<pre xmlns=""><code>var treeObj = $.fn.zTree.getZTreeObj("tree");
var sNodes = treeObj.getSelectedNodes();
if (sNodes.length > 0) {
	var isFirstNode = sNodes[0].isFirstNode;
}
</code></pre>
</div>
</div>