package com.cirpoint.controller;

import com.cirpoint.config.EditorSecurityConfig;
import com.cirpoint.model.*;
import com.cirpoint.service.EditorService;
import com.cirpoint.service.MultiPathService;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件操作控制器
 */
@RestController
@RequestMapping("/editor")
@RequiredArgsConstructor
public class EditorController {

    private final EditorService editorService;
    private final MultiPathService multiPathService;
    private final EditorSecurityConfig securityConfig;

    /**
     * 验证编辑器访问密码
     */
    @PostMapping("/verify")
    public ResponseEntity<Map<String, Boolean>> verifyPassword(@RequestBody PasswordVerifyRequest request) {
        boolean verified = true;

        if (securityConfig.isProductionEnvironment()) {
            String encryptedPassword = request.getPassword();
            String configPassword = securityConfig.getEditorPassword();

            // 如果配置文件中没有设置密码，则不允许访问
            if (configPassword == null || configPassword.isEmpty()) {
                verified = false;
            } else {
                // 验证密码是否匹配
                String hashedConfigPassword = DigestUtils.md5DigestAsHex(configPassword.getBytes(StandardCharsets.UTF_8));
                verified = hashedConfigPassword.equals(encryptedPassword);
            }
        }

        Map<String, Boolean> response = new HashMap<>();
        response.put("verified", verified);
        return ResponseEntity.ok(response);
    }

    /**
     * 获取文件树结构
     *
     * @param path 当前目录路径，为空时表示根目录
     * @return 返回当前目录下的文件和文件夹列表
     */
    @RequestMapping(value = "/tree", method = {RequestMethod.GET, RequestMethod.POST})
    public ResponseEntity<List<FileNode>> getFileTree(@RequestParam(required = false) String path,
                                                     @RequestBody(required = false) Map<String, String> requestBody) {
        // 如果path为null，尝试从请求体中获取
        String finalPath = path;
        if (finalPath == null && requestBody != null && requestBody.containsKey("path")) {
            finalPath = requestBody.get("path");
        }
        List<FileNode> nodes = editorService.getFileTree(finalPath);
        // 兼容前端，确保absolutePath字段被序列化
        return ResponseEntity.ok(nodes);
    }

    /**
     * 获取文件内容
     */
    @GetMapping("/content")
    public ResponseEntity<?> getFileContent(@RequestParam String path) {
        try {
            String content = editorService.getFileContent(path);
            // 检查是否为图片文件
            if ("IMAGE_FILE".equals(content)) {
                // 对于图片文件，返回特殊标记和文件路径
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("isImage", true);
                response.put("path", path);
                return ResponseEntity.ok(response);
            } else {
                // 对于文本文件，返回文件内容
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("isImage", false);
                response.put("data", content);
                return ResponseEntity.ok(response);
            }
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("msg", e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 保存文件内容
     */
    @PostMapping("/save")
    public ResponseEntity<String> saveFileContent(@RequestBody FileSaveRequest request) {
        editorService.saveFileContent(request.getPath(), request.getContent());
        return ResponseEntity.ok("success");
    }

    /**
     * 创建文件或文件夹
     * @return 返回新创建文件或文件夹的UUID
     */
    @PostMapping("/create")
    public ResponseEntity<?> createFileOrFolder(
            @RequestParam String path,
            @RequestParam String name,
            @RequestParam String type) {
        try {
            String uuid = editorService.createFileOrFolder(new FileOperationRequest(path, name, type));
            Map<String, String> response = new HashMap<>();
            response.put("uuid", uuid);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("创建" + (type.equals("folder") ? "文件夹" : "文件") + "失败");
        }
    }

    /**
     * 删除文件或文件夹
     */
    @PostMapping("/delete")
    public ResponseEntity<Void> delete(@RequestParam String path) {
        editorService.delete(path);
        return ResponseEntity.ok().build();
    }

    /**
     * 重命名文件或文件夹
     * @return 返回重命名后文件或文件夹的UUID
     */
    @PostMapping("/rename")
    public ResponseEntity<?> rename(
            @RequestParam String oldPath,
            @RequestParam String newName) {
        try {
            String uuid = editorService.rename(new FileRenameRequest(oldPath, newName));
            Map<String, String> response = new HashMap<>();
            response.put("uuid", uuid);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("重命名失败");
        }
    }

    /**
     * 添加并启用路径
     */
    @PostMapping("/addAndEnablePath")
    public ResponseEntity<Map<String, Object>> addAndEnablePath(@RequestParam String path) {
        Map<String, Object> response = new HashMap<>();

        // 先添加路径
        boolean added = multiPathService.addPath(path);
        if (!added) {
            response.put("success", false);
            response.put("message", "路径添加失败，可能是路径无效或已存在");
            return ResponseEntity.ok(response);
        }

        // 获取所有路径
        List<PathEntry> allPaths = multiPathService.getAllPaths();

        // 找到刚添加的路径
        PathEntry newPath = null;
        for (PathEntry entry : allPaths) {
            if (entry.getPath().equals(path)) {
                newPath = entry;
                break;
            }
        }

        // 启用路径
        if (newPath != null) {
            boolean enabled = multiPathService.enablePath(newPath.getId());
            response.put("success", enabled);
            if (!enabled) {
                response.put("message", "路径添加成功但启用失败");
            }
        } else {
            response.put("success", false);
            response.put("message", "路径添加成功但无法找到该路径");
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 获取启用的路径列表
     */
    @GetMapping("/enabledPaths")
    public ResponseEntity<List<PathEntry>> getEnabledPaths() {
        List<PathEntry> enabledPaths = multiPathService.getEnabledPaths();
        return ResponseEntity.ok(enabledPaths);
    }





    /**
     * 下载文件
     */
    @GetMapping("/download")
    public ResponseEntity<Resource> downloadFile(@RequestParam String path) {
        try {
            Resource resource = editorService.getFileAsResource(path);
            String filename = resource.getFilename();

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, "application/octet-stream")
                    .body(resource);
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 获取图片URL
     */
    @GetMapping("/image")
    public ResponseEntity<Resource> getImage(@RequestParam String path) {
        try {
            Resource resource = editorService.getFileAsResource(path);
            String contentType = determineContentType(path);

            return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .body(resource);
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 根据文件扩展名确定内容类型
     */
    private String determineContentType(String path) {
        String extension = path.substring(path.lastIndexOf(".") + 1).toLowerCase();
        switch (extension) {
            case "png":
                return "image/png";
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "svg":
                return "image/svg+xml";
            case "webp":
                return "image/webp";
            case "ico":
                return "image/x-icon";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 搜索文件
     */
    @GetMapping("/search")
    public ResponseEntity<List<Map<String, Object>>> searchFiles(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "false") boolean searchContent,
            @RequestParam(required = false) String folderPath,
            @RequestParam(required = false) String fileTypes) {
        try {
            List<Map<String, Object>> results = editorService.searchFiles(keyword, searchContent, folderPath, fileTypes);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * 导出文件或文件夹
     * 支持单个文件、单个文件夹或多个文件的导出
     */
    @GetMapping("/export")
    public ResponseEntity<Resource> exportFiles(@RequestParam String paths, @RequestParam(required = false) String fileName) {
        try {
            // paths参数是以逗号分隔的多个文件/文件夹路径
            Resource zipResource = editorService.exportFiles(paths.split(","));

            // 文件名处理与过滤
            String safeFileName = (fileName != null && !fileName.trim().isEmpty()) ? sanitizeFileName(fileName) : "export";
            safeFileName += ".zip";

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + safeFileName + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, "application/zip")
                    .body(zipResource);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 过滤文件名非法字符
     */
    private String sanitizeFileName(String fileName) {
        return fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
    }

    /**
     * 导入文件到指定目录
     * 支持ZIP文件和多个单文件的导入
     *
     * @param files 上传的文件列表
     * @param targetPath 目标目录路径
     * @return 导入结果
     */
    @PostMapping("/import")
    public ResponseEntity<Map<String, Object>> importFiles(
            @RequestParam("file") MultipartFile[] files,
            @RequestParam("targetPath") String targetPath) {
        Map<String, Object> response = new HashMap<>();

        try {
            if (files.length == 0) {
                response.put("success", false);
                response.put("message", "没有选择文件");
                return ResponseEntity.ok(response);
            }

            System.out.println("导入文件数量: " + files.length + ", 目标路径: " + targetPath);
            boolean allSuccess = true;

            for (MultipartFile file : files) {
                String fileName = file.getOriginalFilename();
                if (fileName == null) {
                    continue;
                }

                System.out.println("处理文件: " + fileName);
                boolean isZip = fileName.toLowerCase().endsWith(".zip");
                boolean result;

                if (isZip) {
                    // 导入ZIP文件
                    System.out.println("开始导入ZIP文件: " + fileName);
                    result = editorService.importFiles(file, targetPath);
                } else {
                    // 导入单个文件
                    System.out.println("开始导入单个文件: " + fileName);
                    result = editorService.importSingleFile(file, targetPath);
                }

                System.out.println("导入结果: " + (result ? "成功" : "失败"));
                if (!result) {
                    allSuccess = false;
                }
            }

            response.put("success", allSuccess);
            if (!allSuccess) {
                response.put("message", "部分文件导入失败");
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取文件属性
     */
    @GetMapping("/properties")
    public ResponseEntity<FilePropertiesResponse> getFileProperties(@RequestParam String path) {
        try {
            FilePropertiesResponse properties = editorService.getFileProperties(path);
            return ResponseEntity.ok(properties);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(null);
        }
    }

    @Data
    public static class FileSaveRequest {
        private String path;
        private String content;
    }

    @Data
    public static class PasswordVerifyRequest {
        private String password;
    }
}
