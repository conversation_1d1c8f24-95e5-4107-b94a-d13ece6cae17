package com.cirpoint.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.util.CommonUtil;
import com.cirpoint.util.FileDownloadUtil;
import com.cirpoint.util.Util;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Service
public class SystemService extends ApplicationConfig {

	/**
	 * 获取客户端IP地址
	 * 该方法通过检查HTTP请求中的不同头部字段来确定客户端的IP地址
	 * 它优先考虑通过代理传递的客户端IP地址，以适应多种网络环境
	 *
	 * @param request HTTP请求对象，用于获取请求头部信息
	 * @return 客户端的IP地址如果无法确定，则返回服务器看到的请求地址
	 */
	public String getClientIp(HttpServletRequest request) {
		// 尝试从请求头部获取客户端IP地址，该头部通常由反向代理设置
		String ip = request.getHeader("x-forwarded-for");
		if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
			// 多次反向代理后会有多个ip值，第一个ip才是真实ip
			if (ip.contains(",")) {
				ip = ip.split(",")[0];
			}
		}
		// 如果未成功获取IP地址，尝试从其他头部获取
		if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_CLIENT_IP");
		}
		if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_X_FORWARDED_FOR");
		}
		if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("X-Real-IP");
		}
		// 如果所有头部都未提供有效信息，使用请求的远程地址作为最后的手段
		if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		return ip;
	}

	/**
	 * 导入用户数据
	 *
	 * @param file 上传的Excel文件
	 * @return 导入结果
	 */
	public JSONObject importUsers(MultipartFile file) throws IOException {
		// 创建临时文件保存上传的Excel
		String currTime = System.currentTimeMillis() + "";
		String fullPath = tempPath + File.separator + currTime;

		// 确保目录存在
		FileUtil.mkdir(tempPath);
		// 保存文件
		File destFile = new File(fullPath);
		file.transferTo(destFile);

		// 设置Excel表头映射
		Map<String, String> headerAlias = new HashMap<>();
		headerAlias.put("用户名称", "username");
		headerAlias.put("全名", "fullname");
		headerAlias.put("工号", "workno");
		headerAlias.put("密级", "security");
		headerAlias.put("性别", "sex");
		headerAlias.put("IP地址", "ip");
		headerAlias.put("单位", "unit");
		headerAlias.put("部门", "department");
		headerAlias.put("岗位", "job");
		headerAlias.put("所属角色", "rolename");
		headerAlias.put("关联用户名", "code");
		// 读取Excel并处理数据
		JSONArray arr = Util.readExcelToObject(fullPath, headerAlias);
		return Util.postTwxForObject("Thing.Fn.SystemManagement", "BatchImportUser",
				JSONUtil.createObj().set("users", arr));
	}

	/**
	 * 获取文件信息用于打开或下载
	 *
	 * @param fileName 文件名
	 * @param filePath 文件相对路径
	 * @return 文件对象和MIME类型
	 */
	public ResponseEntity<?> getFileForOpen(String fileName, String filePath) {
		// 获取文件上传路径
		String fullPath = fileUploadPath + filePath;
		return FileDownloadUtil.fileResponse(fullPath, fileName);
	}

	/**
	 * 导出日志Excel
	 *
	 * @param content     日志内容
	 * @param stime       开始时间
	 * @param etime       结束时间
	 * @param operation   操作类型
	 * @param result      操作结果
	 * @param username    用户名
	 * @param currentUser 当前用户
	 * @return 生成的Excel文件
	 */
	public File exportLogExcel(String content, String stime, String etime, String operation,
							   String result, String username, String currentUser) {
		// 查询日志数据
		JSONObject params = JSONUtil.createObj()
				.set("content", content)
				.set("stime", stime)
				.set("etime", etime)
				.set("operation", operation)
				.set("result", result)
				.set("username", username)
				.set("currentUser", currentUser);
		JSONArray logList = Util.postTwx("Thing.UserLogUtil", "QueryData", params);

		// 准备数据
		JSONArray data = new JSONArray();
		for (int i = 0; i < logList.size(); i++) {
			JSONObject logObj = logList.getJSONObject(i);
			JSONArray row = new JSONArray();
			row.add(logObj.getInt("ROWNO"));
			row.add(logObj.getStr("USER_FULLNAME", ""));
			row.add(logObj.getStr("USERNAME", ""));
			row.add(logObj.getStr("OPERATION", ""));
			row.add(logObj.getStr("CONTENT", ""));
			row.add(DateUtil.formatDateTime(logObj.getDate("LOGTIME")));
			row.add(logObj.getInt("REQRESULT") == 1 ? "成功" : "失败");
			data.add(row);
		}

		// 设置表头和列宽
		JSONArray headers = JSONUtil.parseArray(Arrays.asList("序号", "姓名", "用户名", "操作", "操作内容", "操作时间", "结果"));
		JSONArray columnWidths = JSONUtil.parseArray(Arrays.asList(8, 8, 8, 18, 80, 21, 8));
		return CommonUtil.createExcelFile("日志", headers, data, columnWidths, 18);
	}


	/**
	 * 导出确认表日志Excel
	 *
	 * @param queryStr 查询参数
	 * @return 生成Excel文件
	 */
	public File exportConfirmLogExcel(String queryStr) {
		JSONObject param = JSONUtil.parseObj(queryStr);
		// 查询日志数据
		JSONArray logList = Util.postTwxForObject("Thing.Util.HandsonTable", "QueryLog", param).getJSONArray("data");

		// 准备数据
		JSONArray data = new JSONArray();
		for (int i = 0; i < logList.size(); i++) {
			JSONObject logObj = logList.getJSONObject(i);
			JSONArray row = new JSONArray();
			row.add(logObj.getInt("ROWNO"));
			row.add(logObj.getStr("USER_FULLNAME", ""));
			row.add(logObj.getStr("USERNAME", ""));
			row.add(logObj.getStr("MODULE_TYPE", ""));
			row.add(logObj.getStr("MODEL_NAME", ""));
			row.add(logObj.getStr("OPERATION", ""));
			row.add(logObj.getStr("CONTENT", ""));
			row.add(DateUtil.formatDateTime(logObj.getDate("LOG_TIME")));
			row.add(logObj.getStr("MY_IP", ""));
			row.add(logObj.getInt("REQ_RESULT") == 1 ? "成功" : "失败");
			data.add(row);
		}

		// 设置表头和列宽
		JSONArray headers = JSONUtil.parseArray(Arrays.asList("序号", "姓名", "用户名", "模块", "型号", "操作", "操作内容", "操作时间", "操作IP", "结果"));
		JSONArray columnWidths = JSONUtil.parseArray(Arrays.asList(8, 13, 8, 13, 10, 18, 80, 21, 20, 8));
		return CommonUtil.createExcelFile("确认表日志", headers, data, columnWidths, 18);
	}


	/**
	 * 导出用户Excel文件
	 *
	 * @return 生成的Excel文件
	 */
	public File exportUserExcel() {
		// 查询用户数据
		JSONObject userRs = Util.postTwxForObject("Thing.Fn.SystemManagement", "QueryAllUser", new JSONObject());
		JSONArray data = new JSONArray();
		if (userRs.getBool("success")) {
			JSONArray users = userRs.getJSONArray("data");
			for (int i = 0; i < users.size(); i++) {
				JSONObject user = users.getJSONObject(i);
				JSONArray row = new JSONArray();
				row.add(user.getStr("USER_NAME", ""));
				row.add(user.getStr("USER_FULLNAME", ""));
				row.add(user.getStr("USER_WORKNO", ""));
				row.add(user.getStr("USER_SECURITYLEVELNAME", ""));
				row.add(user.getStr("USER_SEX", ""));
				row.add(user.getStr("USER_IP", ""));
				row.add(user.getStr("USER_UNIT", ""));
				row.add(user.getStr("USER_DEPARTMENT", ""));
				row.add(user.getStr("USER_JOB", ""));
				row.add(user.getStr("USER_ROLE", ""));
				row.add(user.getStr("USER_CODE", ""));
				data.add(row);
			}
		}


		// 设置表头和列宽
		JSONArray headers = JSONUtil.parseArray(Arrays.asList("用户名称", "全名", "工号", "密级", "性别", "IP地址", "单位", "部门", "岗位", "所属角色", "关联用户名"));
		JSONArray columnWidths = JSONUtil.parseArray(Arrays.asList(30, 30, 30, 13, 13, 30, 20, 20, 20, 30, 30));

		return CommonUtil.createExcelFile("用户", headers, data, columnWidths, 28);
	}

	/**
	 * 导出用户导入模板
	 *
	 * @return 生成的Excel模板文件
	 */
	public File exportUserTemplate() {
		// 准备空数据
		JSONArray data = new JSONArray();
		for (int i = 0; i < 400; i++) {
			JSONArray row = new JSONArray();
			for (int j = 0; j < 10; j++) {
				row.add("");
			}
			data.add(row);
		}


		// 设置表头和列宽
		JSONArray headers = JSONUtil.parseArray(Arrays.asList("用户名称", "全名", "工号", "密级", "性别", "IP地址", "单位", "部门", "岗位", "所属角色", "关联用户名"));
		JSONArray columnWidths = JSONUtil.parseArray(Arrays.asList(30, 30, 13, 13, 30, 20, 20, 20, 30, 30, 30));
		return CommonUtil.createExcelFile("批量导入用户模板", headers, data, columnWidths, 28);
	}
}
