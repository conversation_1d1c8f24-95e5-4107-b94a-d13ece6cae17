<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>员工工时统计工具</title>
    <link rel="shortcut icon" href="images/tool.png" type="image/x-icon" />
    <link rel="stylesheet" href="../static/lib/layui/css/layui.css" />
    <link rel="stylesheet" href="../tools/css/tool.css" />
    <link rel="stylesheet" href="css/worktime-statistics.css" />
</head>
<body>
    <div class="layui-layout layui-layout-admin">
        <!-- 顶部导航 -->
        <div class="layui-header">
            <div class="layui-logo">工具集合</div>
            <ul class="layui-nav layui-layout-left">
                <li class="layui-nav-item"><a href="../tools.html">返回工具列表</a></li>
            </ul>
        </div>
        
        <!-- 主体内容区域 -->
        <div class="layui-body">
            <div class="worktime-container">
                <div class="tool-header">
                    <h2>员工工时统计工具</h2>
                    <p>上传员工门禁打卡XLSX文件，系统将自动计算每位员工的日工作时长</p>
                </div>
                
                <!-- 功能说明区域 -->
                <div class="layui-row layui-col-space20">
                    <div class="layui-col-md12">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <i class="layui-icon layui-icon-tips"></i> 功能说明
                            </div>
                            <div class="layui-card-body">
                                <div class="layui-alert layui-alert-info">
                                    <ul class="feature-list">
                                        <li><strong>支持场景：</strong>正常单次进出、日内多次进出、跨天工作、多地点工作</li>
                                        <li><strong>数据清洗：</strong>自动处理悬挂记录、连续相同方向等异常数据</li>
                                        <li><strong>文件要求：</strong>Excel格式(.xlsx)，大小不超过100MB，必需列：人员姓名、门禁点、出/入、事件时间</li>
                                        <li><strong>输出结果：</strong>按员工姓名拼音排序，包含序号、姓名、日期、净时长、平均时长</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 主要功能区域 -->
                <div class="layui-row layui-col-space20">
                    <!-- 文件上传区域 -->
                    <div class="layui-col-md12" id="uploadSection">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <i class="layui-icon layui-icon-upload"></i> 文件上传
                            </div>
                            <div class="layui-card-body">
                                <div class="upload-area">
                                    <div class="upload-drag-area" id="uploadDragArea">
                                        <i class="layui-icon layui-icon-upload-drag"></i>
                                        <p>点击或拖拽Excel文件到此处上传</p>
                                        <p class="upload-tip">支持.xlsx格式，大小不超过100MB</p>
                                    </div>
                                    <input type="file" id="fileInput" accept=".xlsx" class="upload-input" />
                                    <div class="upload-actions">
                                        <button type="button" id="selectFileBtn" class="layui-btn">
                                            <i class="layui-icon layui-icon-file"></i> 选择文件
                                        </button>
                                        <button type="button" id="processBtn" class="layui-btn layui-btn-normal" disabled>
                                            <i class="layui-icon layui-icon-play"></i> 开始统计
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 文件信息显示 -->
                                <div class="file-info" id="fileInfo" style="display: none;">
                                    <div class="layui-alert layui-alert-normal">
                                        <p><strong>已选择文件：</strong><span id="fileName"></span></p>
                                        <p><strong>文件大小：</strong><span id="fileSize"></span></p>
                                    </div>
                                </div>
                                
                                <!-- 处理进度 -->
                                <div class="progress-area" id="progressArea" style="display: none;">
                                    <div class="layui-progress layui-progress-big" lay-showpercent="true">
                                        <div class="layui-progress-bar layui-bg-blue" lay-percent="0%"></div>
                                    </div>
                                    <p class="progress-text">正在处理文件，请稍候...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 工具操作区域 -->
                    <div class="layui-col-md4" id="toolSection" style="display: none;">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <i class="layui-icon layui-icon-util"></i> 工具操作
                            </div>
                            <div class="layui-card-body">
                                <div class="tool-actions">
                                    <button type="button" id="generateTestBtn" class="layui-btn layui-btn-warm layui-btn-fluid">
                                        <i class="layui-icon layui-icon-template-1"></i> 生成测试数据
                                    </button>
                                </div>

                                <!-- 快速统计 -->
                                <div class="quick-stats" id="quickStats" style="display: none;">
                                    <div class="layui-alert layui-alert-success">
                                        <h4>处理结果</h4>
                                        <p><strong>处理记录数：</strong><span id="recordCount">0</span></p>
                                        <p><strong>员工人数：</strong><span id="employeeCount">0</span></p>
                                        <p><strong>统计天数：</strong><span id="dayCount">0</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 结果区域 -->
                <div class="layui-row layui-col-space20">
                    <div class="layui-col-md12">
                        <div class="layui-card" id="resultCard" style="display: none;">
                            <div class="layui-card-header">
                                <i class="layui-icon layui-icon-ok-circle"></i> 统计结果
                            </div>
                            <div class="layui-card-body">
                                <div class="result-summary">
                                    <div class="layui-alert layui-alert-success">
                                        <p><strong>统计完成！</strong>工时计算已完成，请下载结果文件查看详细数据。</p>
                                    </div>
                                </div>
                                
                                <div class="download-area">
                                    <a href="javascript:void(0);" id="downloadBtn" class="layui-btn layui-btn-normal layui-btn-lg">
                                        <i class="layui-icon layui-icon-download-circle"></i> 下载统计结果
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../static/lib/layui/layui.js"></script>
    <script src="js/worktime-statistics.js"></script>
    <script>
        layui.use(['element'], function(){
            var element = layui.element;
            // 手动初始化导航元素
            element.render('nav');
        });
    </script>
</body>
</html>
