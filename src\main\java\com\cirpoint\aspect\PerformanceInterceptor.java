package com.cirpoint.aspect;

import com.cirpoint.annotation.PerformanceMonitor;
import com.cirpoint.annotation.DisableLogging;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 性能统计拦截器
 * 用于收集和统计方法的执行时间等性能数据
 */
@Aspect
@Component
@Slf4j
public class PerformanceInterceptor implements HandlerInterceptor {

    // 存储方法执行次数
    private final ConcurrentHashMap<String, AtomicLong> methodInvocationCount = new ConcurrentHashMap<>();
    // 存储方法总执行时间
    private final ConcurrentHashMap<String, AtomicLong> methodTotalTime = new ConcurrentHashMap<>();
    // 存储方法最大执行时间
    private final ConcurrentHashMap<String, AtomicLong> methodMaxTime = new ConcurrentHashMap<>();

    /**
     * 监控带有@PerformanceMonitor注解的方法
     */
    @Around("@annotation(com.cirpoint.annotation.PerformanceMonitor)")
    public Object aroundAnnotatedMethod(ProceedingJoinPoint point) throws Throwable {
        return processPerformance(point, true);
    }

    /**
     * 监控web包下所有Controller的方法
     */
    @Around("within(com.cirpoint.controller..*) && " +
           "(@annotation(org.springframework.web.bind.annotation.RequestMapping) || " +
           "@annotation(org.springframework.web.bind.annotation.GetMapping) || " +
           "@annotation(org.springframework.web.bind.annotation.PostMapping) || " +
           "@annotation(org.springframework.web.bind.annotation.PutMapping) || " +
           "@annotation(org.springframework.web.bind.annotation.DeleteMapping) || " +
           "@annotation(org.springframework.web.bind.annotation.PatchMapping))")
    public Object aroundControllerMethod(ProceedingJoinPoint point) throws Throwable {
        return processPerformance(point, false);
    }

    /**
     * 处理性能统计
     */
    private Object processPerformance(ProceedingJoinPoint point, boolean isAnnotated) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        
        // 检查是否禁用日志
        if (method.isAnnotationPresent(DisableLogging.class)) {
            return point.proceed();
        }

        String methodName = method.getDeclaringClass().getSimpleName() + "." + method.getName();
        
        // 获取请求类型
        String requestType = getRequestType(method);
        if (requestType != null) {
            methodName = requestType + " " + methodName;
        }
        
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        
        // 如果是带注解的方法或者debug日志开启，则记录参数
        if (isAnnotated || log.isDebugEnabled()) {
            log.info("方法: {} 开始执行，参数: {}", methodName, Arrays.toString(point.getArgs()));
        }
        
        Object result;
        try {
            // 执行目标方法
            result = point.proceed();
            return result;
        } finally {
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 更新统计数据
            methodInvocationCount.computeIfAbsent(methodName, k -> new AtomicLong()).incrementAndGet();
            methodTotalTime.computeIfAbsent(methodName, k -> new AtomicLong()).addAndGet(executionTime);
            methodMaxTime.computeIfAbsent(methodName, k -> new AtomicLong())
                    .updateAndGet(current -> Math.max(current, executionTime));
            
            // 记录执行信息
            log.info("方法: {} 执行完成，耗时: {}ms", methodName, executionTime);
        }
    }

    /**
     * 获取请求类型
     */
    private String getRequestType(Method method) {
        if (method.isAnnotationPresent(GetMapping.class)) return "GET";
        if (method.isAnnotationPresent(PostMapping.class)) return "POST";
        if (method.isAnnotationPresent(PutMapping.class)) return "PUT";
        if (method.isAnnotationPresent(DeleteMapping.class)) return "DELETE";
        if (method.isAnnotationPresent(PatchMapping.class)) return "PATCH";
        RequestMapping requestMapping = method.getAnnotation(RequestMapping.class);
        if (requestMapping != null && requestMapping.method().length > 0) {
            return requestMapping.method()[0].name();
        }
        return null;
    }

    /**
     * 获取方法的性能统计信息
     */
    public String getMethodStatistics(String methodName) {
        AtomicLong count = methodInvocationCount.get(methodName);
        AtomicLong totalTime = methodTotalTime.get(methodName);
        AtomicLong maxTime = methodMaxTime.get(methodName);
        
        if (count == null) {
            return "<h1 style='color: #ff4444;'>未找到方法 [" + methodName + "] 的性能统计信息</h1>";
        }
        
        long invocations = count.get();
        double averageTime = totalTime.get() / (double) invocations;
        long totalTimeMs = totalTime.get();
        
        // 获取方法上的注解描述
        String description = "";
        try {
            String[] parts = methodName.split("\\.");
            if (parts.length == 2) {
                Class<?> clazz = Class.forName("com.cirpoint.web." + parts[0]);
                for (Method method : clazz.getDeclaredMethods()) {
                    if (method.getName().equals(parts[1])) {
                        PerformanceMonitor monitor = method.getAnnotation(PerformanceMonitor.class);
                        if (monitor != null) {
                            description = monitor.description();
                        }
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取方法描述失败", e);
        }

        String performanceStatus = getPerformanceStatus(averageTime);
        String statusColor = getStatusColor(averageTime);

		return "<!DOCTYPE html>\n" +
				"<html>\n" +
				"<head>\n" +
				"    <meta charset=\"UTF-8\">\n" +
				"    <title>性能监控报告</title>\n" +
				"    <style>\n" +
				"        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }\n" +
				"        .container { max-width: 800px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n" +
				"        h1 { color: #333; text-align: center; margin-bottom: 30px; font-size: 24px; }\n" +
				"        .section { margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; }\n" +
				"        .section-title { color: #2c3e50; font-size: 18px; margin-bottom: 10px; font-weight: bold; }\n" +
				"        .info-item { margin: 10px 0; color: #555; }\n" +
				"        .status { padding: 5px 10px; border-radius: 3px; color: white; display: inline-block; }\n" +
				"        .timestamp { color: #888; text-align: center; font-size: 12px; margin-top: 20px; }\n" +
				"    </style>\n" +
				"</head>\n" +
				"<body>\n" +
				"    <div class=\"container\">\n" +
				"        <h1>🔍 性能监控报告</h1>\n" +
				"        <div class=\"section\">\n" +
				"            <div class=\"section-title\">📌 基本信息</div>\n" +
				"            <div class=\"info-item\">方法名称：" + methodName + "</div>\n" +
				"            <div class=\"info-item\">功能描述：" + (description.isEmpty() ? "未设置描述" : description) + "</div>\n" +
				"        </div>\n" +
				"        <div class=\"section\">\n" +
				"            <div class=\"section-title\">📊 调用统计</div>\n" +
				"            <div class=\"info-item\">总调用次数：" + invocations + " 次</div>\n" +
				"            <div class=\"info-item\">总执行时间：" + String.format("%.2f", totalTimeMs / 1000.0) + " 秒</div>\n" +
				"            <div class=\"info-item\">平均执行时间：" + String.format("%.2f", averageTime) + " 毫秒</div>\n" +
				"            <div class=\"info-item\">最长执行时间：" + maxTime.get() + " 毫秒</div>\n" +
				"            <div class=\"info-item\">最短执行时间：" + totalTimeMs / invocations + " 毫秒</div>\n" +
				"        </div>\n" +
				"        <div class=\"section\">\n" +
				"            <div class=\"section-title\">📈 性能分析</div>\n" +
				"            <div class=\"info-item\">每秒处理能力：" + String.format("%.2f", 1000.0 / averageTime) + " 次</div>\n" +
				"            <div class=\"info-item\">性能状态：<span class=\"status\" style=\"background-color: " +
				statusColor + "\">" + performanceStatus + "</span></div>\n" +
				"        </div>\n" +
				"        <div class=\"timestamp\">\n" +
				"            报告生成时间：" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\n" +
				"        </div>\n" +
				"    </div>\n" +
				"</body>\n" +
				"</html>";
    }

    /**
     * 评估性能状态
     */
    private String getPerformanceStatus(double averageTime) {
        if (averageTime < 100) {
            return "优秀";
        } else if (averageTime < 500) {
            return "良好";
        } else if (averageTime < 1000) {
            return "一般";
        } else {
            return "需要优化";
        }
    }

    /**
     * 获取状态对应的颜色
     */
    private String getStatusColor(double averageTime) {
        if (averageTime < 100) {
            return "#4caf50";  // 绿色
        } else if (averageTime < 500) {
            return "#2196f3";  // 蓝色
        } else if (averageTime < 1000) {
            return "#ff9800";  // 橙色
        } else {
            return "#f44336";  // 红色
        }
    }

    /**
     * 重置某个方法的统计信息
     */
    public boolean resetMethodStatistics(String methodName) {
        boolean exists = methodInvocationCount.containsKey(methodName);
        if (exists) {
            methodInvocationCount.remove(methodName);
            methodTotalTime.remove(methodName);
            methodMaxTime.remove(methodName);
        }
        return exists;
    }

    /**
     * 重置所有统计信息
     */
    public void resetAllStatistics() {
        methodInvocationCount.clear();
        methodTotalTime.clear();
        methodMaxTime.clear();
    }

    /**
     * 获取所有方法的性能统计信息
     * @return HTML格式的统计信息
     */
    public String getAllMethodStatistics() {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>\n")
            .append("<html>\n")
            .append("<head>\n")
            .append("    <meta charset=\"UTF-8\">\n")
            .append("    <title>所有方法性能监控报告</title>\n")
            .append("    <style>\n")
            .append("        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }\n")
            .append("        .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n")
            .append("        h1 { color: #333; text-align: center; margin-bottom: 30px; font-size: 24px; }\n")
            .append("        .method-stats { margin-bottom: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; }\n")
            .append("        .method-name { color: #2c3e50; font-size: 18px; margin-bottom: 10px; font-weight: bold; }\n")
            .append("        .stats-item { margin: 5px 0; color: #555; }\n")
            .append("        .status { padding: 5px 10px; border-radius: 3px; color: white; display: inline-block; }\n")
            .append("        .timestamp { color: #888; text-align: center; font-size: 12px; margin-top: 20px; }\n")
            .append("    </style>\n")
            .append("</head>\n")
            .append("<body>\n")
            .append("    <div class=\"container\">\n")
            .append("        <h1>🔍 所有方法性能监控报告</h1>\n");

        for (String methodName : methodInvocationCount.keySet()) {
            long invocations = methodInvocationCount.get(methodName).get();
            long totalTimeMs = methodTotalTime.get(methodName).get();
            long maxTimeMs = methodMaxTime.get(methodName).get();
            double averageTime = totalTimeMs * 1.0 / invocations;
            
            String performanceStatus = getPerformanceStatus(averageTime);
            String statusColor = getStatusColor(averageTime);

            html.append("        <div class=\"method-stats\">\n")
                .append("            <div class=\"method-name\">").append(methodName).append("</div>\n")
                .append("            <div class=\"stats-item\">总调用次数：").append(invocations).append(" 次</div>\n")
                .append("            <div class=\"stats-item\">总执行时间：").append(String.format("%.2f", totalTimeMs / 1000.0)).append(" 秒</div>\n")
                .append("            <div class=\"stats-item\">平均执行时间：").append(String.format("%.2f", averageTime)).append(" 毫秒</div>\n")
                .append("            <div class=\"stats-item\">最长执行时间：").append(maxTimeMs).append(" 毫秒</div>\n")
                .append("            <div class=\"stats-item\">每秒处理能力：").append(String.format("%.2f", 1000.0 / averageTime)).append(" 次</div>\n")
                .append("            <div class=\"stats-item\">性能状态：<span class=\"status\" style=\"background-color: ")
                .append(statusColor).append("\">").append(performanceStatus).append("</span></div>\n")
                .append("        </div>\n");
        }

        html.append("        <div class=\"timestamp\">\n")
            .append("            报告生成时间：").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("\n")
            .append("        </div>\n")
            .append("    </div>\n")
            .append("</body>\n")
            .append("</html>");

        return html.toString();
    }

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        // 检查是否禁用日志
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            if (method.isAnnotationPresent(DisableLogging.class)) {
                request.setAttribute("DISABLE_LOGGING", true);
                return true;
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, 
                                @NonNull Object handler, @Nullable Exception ex) {
	}
}
