package com.cirpoint.service.tools;

import cn.hutool.core.collection.CollUtil;
import com.cirpoint.constant.WorkTimeConstants;
import com.cirpoint.model.worktime.AttendanceRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 跨日数据验证器
 * 用于验证员工打卡记录中的跨日悬挂记录，提高工时计算的准确性
 */
@Slf4j
@Component
public class CrossDayValidator {

    /**
     * 执行跨日验证，返回需要忽略的异常记录
     * @param allRecords 所有打卡记录
     * @return 需要忽略的异常记录集合
     */
    public Set<AttendanceRecord> validateCrossDayRecords(List<AttendanceRecord> allRecords) {
        Set<AttendanceRecord> invalidRecords = new HashSet<>();
        
        if (CollUtil.isEmpty(allRecords)) {
            return invalidRecords;
        }
        
        log.info("开始执行跨日验证，总记录数: {}", allRecords.size());
        
        // 1. 构建员工+场所+日期的记录索引
        Map<String, Map<LocalDate, List<AttendanceRecord>>> index = buildEmployeeLocationDateIndex(allRecords);
        
        // 2. 对每个员工+场所组合进行跨日验证
        for (Map.Entry<String, Map<LocalDate, List<AttendanceRecord>>> entry : index.entrySet()) {
            String employeeLocation = entry.getKey();
            Map<LocalDate, List<AttendanceRecord>> dateRecords = entry.getValue();
            
            validateEmployeeLocationRecords(employeeLocation, dateRecords, invalidRecords);
        }
        
        log.info("跨日验证完成，发现异常记录数: {}", invalidRecords.size());
        return invalidRecords;
    }
    
    /**
     * 构建员工+场所+日期的记录索引
     * @param allRecords 所有记录
     * @return 索引结构：员工+场所 -> 日期 -> 记录列表
     */
    private Map<String, Map<LocalDate, List<AttendanceRecord>>> buildEmployeeLocationDateIndex(
            List<AttendanceRecord> allRecords) {
        
        Map<String, Map<LocalDate, List<AttendanceRecord>>> index = new HashMap<>();
        
        for (AttendanceRecord record : allRecords) {
            String employeeLocation = record.getEmployeeName() + "|" + record.getLocation();
            LocalDate date = record.getEventTime().toLocalDate();
            
            index.computeIfAbsent(employeeLocation, k -> new HashMap<>())
                 .computeIfAbsent(date, k -> new ArrayList<>())
                 .add(record);
        }
        
        // 对每个日期的记录按时间排序
        for (Map<LocalDate, List<AttendanceRecord>> dateRecords : index.values()) {
            for (List<AttendanceRecord> records : dateRecords.values()) {
                records.sort(Comparator.comparing(AttendanceRecord::getEventTime));
            }
        }
        
        return index;
    }
    
    /**
     * 验证单个员工+场所组合的跨日记录
     * @param employeeLocation 员工+场所标识
     * @param dateRecords 按日期分组的记录
     * @param invalidRecords 异常记录集合（输出参数）
     */
    private void validateEmployeeLocationRecords(String employeeLocation,
                                               Map<LocalDate, List<AttendanceRecord>> dateRecords,
                                               Set<AttendanceRecord> invalidRecords) {
        
        // 按日期排序
        List<LocalDate> sortedDates = dateRecords.keySet().stream()
                .sorted()
                .collect(Collectors.toList());
        
        log.debug("验证员工场所: {}, 涉及日期数: {}", employeeLocation, sortedDates.size());
        
        // 验证每一天的记录
        for (int i = 0; i < sortedDates.size(); i++) {
            LocalDate currentDate = sortedDates.get(i);
            LocalDate previousDate = i > 0 ? sortedDates.get(i - 1) : null;
            LocalDate nextDate = i < sortedDates.size() - 1 ? sortedDates.get(i + 1) : null;
            
            List<AttendanceRecord> currentDayRecords = dateRecords.get(currentDate);
            List<AttendanceRecord> previousDayRecords = previousDate != null ? dateRecords.get(previousDate) : null;
            List<AttendanceRecord> nextDayRecords = nextDate != null ? dateRecords.get(nextDate) : null;
            
            // 规则1：验证前置"出"记录
            validateLeadingOutRecord(currentDayRecords, previousDayRecords, invalidRecords);
            
            // 规则2：验证后置"入"记录
            validateTrailingInRecord(currentDayRecords, nextDayRecords, invalidRecords);
        }
    }
    
    /**
     * 验证前置"出"记录
     * 规则：如果某日第一条记录为"出"，但前一日没有对应的"入"记录，则标记为异常
     * @param currentDayRecords 当日记录
     * @param previousDayRecords 前一日记录
     * @param invalidRecords 异常记录集合（输出参数）
     */
    private void validateLeadingOutRecord(List<AttendanceRecord> currentDayRecords,
                                        List<AttendanceRecord> previousDayRecords,
                                        Set<AttendanceRecord> invalidRecords) {
        if (CollUtil.isEmpty(currentDayRecords)) {
            return;
        }
        
        // 获取当日第一条记录
        AttendanceRecord firstRecord = currentDayRecords.get(0);
        
        // 如果第一条记录是"出"
        if (WorkTimeConstants.DIRECTION_OUT.equals(firstRecord.getDirection())) {
            // 检查前一日是否有对应的"入"记录
            boolean hasPreviousDayInRecord = false;
            
            if (CollUtil.isNotEmpty(previousDayRecords)) {
                // 检查前一日最后一条记录是否为"入"
                AttendanceRecord lastPreviousRecord = previousDayRecords.get(previousDayRecords.size() - 1);
                if (WorkTimeConstants.DIRECTION_IN.equals(lastPreviousRecord.getDirection())) {
                    hasPreviousDayInRecord = true;
                }
            }
            
            if (!hasPreviousDayInRecord) {
                invalidRecords.add(firstRecord);
                log.warn("检测到跨日悬挂出场记录: 员工={}, 场所={}, 时间={}", 
                        firstRecord.getEmployeeName(), 
                        firstRecord.getLocation(), 
                        firstRecord.getEventTime());
            }
        }
    }
    
    /**
     * 验证后置"入"记录
     * 规则：如果某日最后一条记录为"入"，但后一日没有对应的"出"记录，则标记为异常
     * @param currentDayRecords 当日记录
     * @param nextDayRecords 后一日记录
     * @param invalidRecords 异常记录集合（输出参数）
     */
    private void validateTrailingInRecord(List<AttendanceRecord> currentDayRecords,
                                        List<AttendanceRecord> nextDayRecords,
                                        Set<AttendanceRecord> invalidRecords) {
        if (CollUtil.isEmpty(currentDayRecords)) {
            return;
        }
        
        // 获取当日最后一条记录
        AttendanceRecord lastRecord = currentDayRecords.get(currentDayRecords.size() - 1);
        
        // 如果最后一条记录是"入"
        if (WorkTimeConstants.DIRECTION_IN.equals(lastRecord.getDirection())) {
            // 检查后一日是否有对应的"出"记录
            boolean hasNextDayOutRecord = false;
            
            if (CollUtil.isNotEmpty(nextDayRecords)) {
                // 检查后一日第一条记录是否为"出"
                AttendanceRecord firstNextRecord = nextDayRecords.get(0);
                if (WorkTimeConstants.DIRECTION_OUT.equals(firstNextRecord.getDirection())) {
                    hasNextDayOutRecord = true;
                }
            }
            
            if (!hasNextDayOutRecord) {
                invalidRecords.add(lastRecord);
                log.warn("检测到跨日悬挂入场记录: 员工={}, 场所={}, 时间={}", 
                        lastRecord.getEmployeeName(), 
                        lastRecord.getLocation(), 
                        lastRecord.getEventTime());
            }
        }
    }
}
