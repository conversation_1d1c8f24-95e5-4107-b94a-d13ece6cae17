package com.cirpoint.service.panorama;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.service.ApplicationConfig;
import com.cirpoint.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;

/**
 * 全景图导出服务
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@Slf4j
@Service
public class PanoramaExportService extends ApplicationConfig {

    /**
     * 导出修改后的全景图包
     */
    public String exportPanorama(Long taskId) {
        try {
            // 1. 获取任务信息
            String querySql = "SELECT TASK_NAME, EXTRACT_PATH FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray queryResult = Util.postQuerySql(querySql);

            if (queryResult == null || queryResult.size() == 0) {
                throw new RuntimeException("任务不存在");
            }

            JSONObject taskInfo = (JSONObject) queryResult.get(0);
            String taskName = taskInfo.getStr("TASK_NAME");
            String extractPath = taskInfo.getStr("EXTRACT_PATH");

            if (extractPath == null || extractPath.trim().isEmpty()) {
                throw new RuntimeException("任务尚未上传全景图文件");
            }

            File extractDir = new File(extractPath);
            if (!extractDir.exists() || !extractDir.isDirectory()) {
                throw new RuntimeException("全景图文件目录不存在");
            }

            // 2. 创建导出目录和ZIP文件名
            String exportDir = tempPath + File.separator + "panorama_export_" + System.currentTimeMillis();
            FileUtil.mkdir(exportDir);

            String zipFileName = (taskName != null ? taskName : "panorama_task_" + taskId) + "_exported.zip";
            String zipFilePath = exportDir + File.separator + zipFileName;

            // 3. 创建临时导出副本目录
            String tempExportPath = exportDir + File.separator + "temp_export";
            FileUtil.mkdir(tempExportPath);

            // 复制原始文件到临时目录
            FileUtil.copyContent(new File(extractPath), new File(tempExportPath), true);

            // 4. 清理临时目录中HTML文件的热点定位脚本引用
            try {
                cleanHotspotLocatorScriptFromDirectory(tempExportPath);
            } catch (Exception e) {
                log.warn("清理热点定位脚本引用失败: {}", e.getMessage());
            }

            // 5. 将临时目录重新打包成ZIP文件
            ZipUtil.zip(tempExportPath, zipFilePath);

            // 6. 验证ZIP文件是否创建成功
            File zipFile = new File(zipFilePath);
            if (!zipFile.exists() || zipFile.length() == 0) {
                throw new RuntimeException("ZIP文件创建失败");
            }

            // 7. 清理临时导出目录
            try {
                FileUtil.del(tempExportPath);
            } catch (Exception e) {
                log.warn("清理临时导出目录失败: {}, 错误: {}", tempExportPath, e.getMessage());
            }

            // 8. 更新任务状态为已导出
            String updateSql = "UPDATE PANORAMA_TASK SET STATUS = 2, UPDATE_TIME = SYSDATE WHERE TASK_ID = " + taskId;
            Util.postCommandSql(updateSql);

            return zipFilePath;

        } catch (Exception e) {
            log.error("导出全景图包失败", e);
            throw new RuntimeException("导出全景图包失败: " + e.getMessage());
        }
    }

    /**
     * 清理目录下所有HTML文件中的热点定位脚本引用
     * 用于导出时移除编辑相关的脚本引用
     *
     * @param directoryPath 目录路径
     */
    private void cleanHotspotLocatorScriptFromDirectory(String directoryPath) {
        try {
            File dir = new File(directoryPath);
            if (!dir.exists() || !dir.isDirectory()) {
                log.error("目录不存在或不是有效目录: {}", directoryPath);
                return;
            }

            cleanHotspotLocatorScriptFromFiles(dir);

        } catch (Exception e) {
            log.error("清理目录中的热点定位脚本引用失败: {}", directoryPath, e);
        }
    }

    /**
     * 递归清理文件夹中的HTML文件
     */
    private void cleanHotspotLocatorScriptFromFiles(File dir) {
        File[] files = dir.listFiles();
        if (files == null) {
            return;
        }

        for (File file : files) {
            if (file.isFile() && file.getName().toLowerCase().endsWith(".html")) {
                cleanHotspotLocatorScriptFromHtmlFile(file);
            } else if (file.isDirectory()) {
                cleanHotspotLocatorScriptFromFiles(file);
            }
        }
    }

    /**
     * 清理单个HTML文件中的热点定位脚本引用
     */
    private void cleanHotspotLocatorScriptFromHtmlFile(File htmlFile) {
        try {
            String content = readFileContent(htmlFile);
            String cleanedContent = removeHotspotLocatorScriptReference(content);

            if (!content.equals(cleanedContent)) {
                writeFileContent(htmlFile, cleanedContent);
            }

        } catch (Exception e) {
            log.error("清理HTML文件失败: {}", htmlFile.getName(), e);
        }
    }

    /**
     * 从HTML内容中移除热点定位脚本引用
     */
    private String removeHotspotLocatorScriptReference(String htmlContent) {
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            return htmlContent;
        }

        // 检查是否包含热点定位脚本引用
        if (!htmlContent.contains("热点定位功能脚本") &&
            !htmlContent.contains("pano2vr-hotspot-locator.js")) {
            return htmlContent;
        }

        // 移除注释和脚本标签（支持多行匹配）
        Pattern pattern = Pattern.compile(
            "\\s*<!-- 热点定位功能脚本 -->\\s*\\n?" +
            "\\s*<script[^>]*src=\"[^\"]*pano2vr-hotspot-locator\\.js\"[^>]*></script>\\s*\\n?",
            Pattern.CASE_INSENSITIVE | Pattern.MULTILINE
        );

        String result = pattern.matcher(htmlContent).replaceAll("");

        // 清理可能的多余空行
        result = result.replaceAll("\\n\\s*\\n\\s*\\n", "\n\n");

        return result;
    }

    /**
     * 读取文件内容
     */
    private String readFileContent(File file) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }

    /**
     * 写入文件内容
     */
    private void writeFileContent(File file, String content) throws IOException {
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8))) {
            writer.write(content);
        }
    }
}
