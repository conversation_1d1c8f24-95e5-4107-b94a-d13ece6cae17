package com.cirpoint.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.cell.CellHandler;
import com.cirpoint.config.ThingworxConfig;
import com.cirpoint.model.PdfOptions;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellAddress;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;

@Slf4j
public class Util {

	/**
	 * 获取Thing的属性值
	 */

	public static Object getThingProperty(String thing, String property) {
		ThingworxConfig thingworxConfig = SpringContextUtil.getBean(ThingworxConfig.class);
		String domain = thingworxConfig.getDomain();
		String key = thingworxConfig.getKey();
		String path = domain + "/Thingworx/Things/" + thing + "/Properties/" + property
				+ "?method=GET&Accept=application/json&appKey=" + key;
		String rs = HttpRequest.post(path).header("Content-Type", "application/json").charset("utf-8").execute().body();
		return JSONUtil.parseObj(rs).getJSONArray("rows").getJSONObject(0).get(property);
	}

	public static int getLargeTableCol() {
		return Convert.toInt(getThingProperty("Thing.Util.HandsonTable", "LargeTableCol"));
	}

	public static void addCellContent(Table table, int row, int col, boolean hasColWidth, String headerRow, JSONObject cellObj,
									  com.itextpdf.layout.element.Cell cell, PdfFont font) {
		String rootPath = Util.getFileUploadPath();
		String className = cellObj.getStr("className", "htMiddle htCenter");
		if (className.contains("c-bold")) {
			cell.setBold();
		}
		// 默认居中
		cell.setTextAlignment(TextAlignment.CENTER)
				.setVerticalAlignment(com.itextpdf.layout.properties.VerticalAlignment.MIDDLE);

		if (className.contains("htLeft")) {
			cell.setTextAlignment(TextAlignment.LEFT);
		}
		if (className.contains("htCenter")) {
			cell.setTextAlignment(TextAlignment.CENTER);
		}
		if (className.contains("htRight")) {
			cell.setTextAlignment(TextAlignment.RIGHT);
		}
		if (className.contains("htJustify")) {
			cell.setTextAlignment(TextAlignment.JUSTIFIED);
		}
		if (className.contains("htTop")) {
			cell.setVerticalAlignment(com.itextpdf.layout.properties.VerticalAlignment.TOP);
		}
		if (className.contains("htMiddle")) {
			cell.setVerticalAlignment(com.itextpdf.layout.properties.VerticalAlignment.MIDDLE);
		}
		if (className.contains("htBottom")) {
			cell.setVerticalAlignment(com.itextpdf.layout.properties.VerticalAlignment.BOTTOM);
		}
		if (className.contains("font-size")) {
			int size = 13;
			String[] classes = className.split(" ");
			for (String cla : classes) {
				if (cla.contains("font-size")) {
					size = Convert.toInt(cla.split("-")[2]);
				}
			}
			cell.setFontSize(size);
		}

		if (className.contains("font-color")) {
			Color color = getFontColor(className);
			cell.setFontColor(color);
		}

//		cell.setKeepTogether(true);
		cell.add(new Paragraph(cellObj.getStr("text")).setMultipliedLeading(1.2f).setFont(font));

		JSONArray imgArr = cellObj.getJSONArray("img");
		if (!imgArr.isEmpty()) {
			try {
				JSONArray imgNumArr = new JSONArray();
				for (int x = 0; x < imgArr.size(); x++) {
					JSONObject imgObj = imgArr.getJSONObject(x);
					String src = imgObj.getStr("src");
					String imgType = imgObj.getStr("type");
					String imgClass = imgObj.getStr("class", "");
					String date = imgObj.getStr("date", "");
					if (imgType.equals("sign")) {
						Image img;
						if (src.contains("data:")) {
							img = new Image(
									ImageDataFactory.create(Base64.decodeBase64(src.substring(src.indexOf("base64,") + 7))));
						} else {
							img = new Image(ImageDataFactory.create(rootPath + src.substring(5)));
						}
						int maxWidth = 80;
						int maxHeight = 40;
						if (imgClass.contains("test-sign")) {
							maxWidth = 120;
							maxHeight = 60;
						}
						if (hasColWidth) {
							//842是页面宽度 减去20 是页面左右边距 822
							float pageWidth = 822;
							int colSpan = cell.getColspan();
							float cellTotalWidth = 0;
							for (int c = 0; c < colSpan; c++) {
								cellTotalWidth += table.getColumnWidth(col + c).getValue();
							}
							float cellWidth = Convert.toFloat(NumberUtil.div(NumberUtil.mul(pageWidth, cellTotalWidth), 100));
							if (cellWidth < maxWidth) {
//								img.setWidth(cellWidth);
								img.setAutoScale(true);
							} else {
								img.setMaxWidth(maxWidth);
								img.setMaxHeight(maxHeight);
							}
						} else {
							img.setMaxWidth(maxWidth);
							img.setMaxHeight(maxHeight);
						}
						cell.add(img);

						if (StrUtil.isNotEmpty(date)) {
							cell.add(new Paragraph(date).setMultipliedLeading(1.2f).setFont(font))
									.setTextAlignment(TextAlignment.LEFT);
						}
					} else {
						String imgNum = imgObj.getStr("imgNum");
						imgNumArr.add(imgNum);
					}
				}
				if (!imgNumArr.isEmpty()) {
					cell.add(new Paragraph(dealImgNum(imgNumArr)).setMultipliedLeading(1.2f).setFont(font).setFontColor(new DeviceRgb(255, 0, 0)));
				}
			} catch (MalformedURLException e) {
				log.error(e.getMessage());
			}
		}
		int min = 0;
		int max;
		if (headerRow.contains("-")) {
			min = Convert.toInt(headerRow.split("-")[0]);
			max = Convert.toInt(headerRow.split("-")[1]);
		} else {
			max = Convert.toInt(headerRow);
		}
		if (row <= (max - 1) && row >= (min - 1)) {
			Color bgColour = new DeviceRgb(230, 230, 230);
			table.addHeaderCell(cell.setBold().setBackgroundColor(bgColour));
		} else {
			table.addCell(cell);
		}
	}

	private static Color getFontColor(String className) {
		Color color = ColorConstants.BLACK;
		String[] classes = className.split(" ");
		for (String cla : classes) {
			if (cla.contains("font-color")) {
				String colorName = cla.split("-")[2];
				switch (colorName) {
					case "purple":
						color = new DeviceRgb(128, 0, 128);
						break;
					case "green":
						color = new DeviceRgb(0, 128, 0);
						break;
					case "orange":
						color = new DeviceRgb(255, 165, 0);
						break;
					case "deeppink":
						color = new DeviceRgb(255, 20, 147);
						break;
				}
			}
		}
		return color;
	}

	/**
	 * 获取实体文件总大小
	 */
	public static String getFileTotalSize() {
		String path = getFileUploadPath();
		return FileUtil.readableFileSize(FileUtil.size(FileUtil.file(path)));
	}


	public static String dealImgNum(JSONArray imgNums) {
		String tableNum = "";
		try {
			tableNum = imgNums.getStr(0).substring(0, imgNums.getStr(0).lastIndexOf('-'));
		} catch (Exception ignored) {

		}
		if (StrUtil.isNotBlank(tableNum)) {
			tableNum = tableNum + "-";
		}
		JSONArray arr = new JSONArray();
		for (int i = 0; i < imgNums.size(); i++) {
			String imgNum = imgNums.getStr(i);
			String[] tempArr = imgNum.split("-");
			int num = Convert.toInt(tempArr[tempArr.length - 1].substring(1));
			arr.add(num);
		}
		JSONArray newArr = new JSONArray();
		for (int i = 0; i < arr.size(); i++) {
			JSONArray tempArr = new JSONArray();
			tempArr.add(arr.getInt(i));
			while ((arr.getInt(i + 1, 0) - arr.getInt(i)) == 1) {
				tempArr.add(arr.getInt(i + 1));
				i++;
			}
			newArr.add(tempArr);
		}

		JSONArray textArr = new JSONArray();
		textArr.add("");

		for (int i = 0; i < newArr.size(); i++) {
			JSONArray childArr = newArr.getJSONArray(i);
			if (childArr.size() == 1) {
				textArr.add(tableNum + "图" + childArr.getStr(0));
			} else {
				textArr.add(tableNum + "图" + childArr.getStr(0) + "~图" + childArr.getStr(childArr.size() - 1));
			}
		}
		return StrUtil.join(" ", textArr);
	}

	/**
	 * 将二维数组中的元素为NULL的变为空字符串
	 */
	public static void dealJSONArray(JSONArray tableData) {
		for (int i = 0; i < tableData.size(); i++) {
			JSONArray row = tableData.getJSONArray(i);
			for (int j = 0; j < row.size(); j++) {
				if (StrUtil.isBlank(row.getStr(j, ""))) {
					row.set(j, "");
				}
			}
		}
	}

	public static JSONArray html2Images(String tableStr) {
		JSONArray images = new JSONArray();
		if (StrUtil.isNotBlank(tableStr)) {
			org.jsoup.nodes.Document doc = Jsoup.parseBodyFragment(tableStr);
			Element table = doc.getElementsByTag("table").get(0);
			Element tbody = table.getElementsByTag("tbody").get(0);
			Elements trs = tbody.getElementsByTag("tr");
			for (int i = 0; i < trs.size(); i++) {
				Element tr = trs.get(i);
				Elements tds = tr.getElementsByTag("td");
				for (int j = 0; j < tds.size(); j++) {
					Element td = tds.get(j);
					Elements imgs = td.getElementsByTag("img");
					if (!imgs.isEmpty()) {
						JSONObject imgsObj = JSONUtil.createObj();
						if (StrUtil.isNotBlank(td.attr("row"))) {
							imgsObj.set("row", td.attr("row"));
						} else {
							imgsObj.set("row", i);
						}
						if (StrUtil.isNotBlank(td.attr("col"))) {
							imgsObj.set("col", td.attr("col"));
						} else {
							imgsObj.set("col", j);
						}
						JSONArray imgArr = new JSONArray();
						for (Element img : imgs) {
							JSONObject imgObj = JSONUtil.createObj();
							String imgType = img.attr("type");
							imgObj.set("type", imgType);
							imgObj.set("src", img.attr("src"));
							imgObj.set("date", img.attr("date"));
							imgObj.set("class", img.attr("class"));
							if (imgType.equals("photo")) {
								imgObj.set("imgNum", img.attr("photoShowNum"));
							}
							imgArr.set(imgObj);
						}
						imgsObj.set("img", imgArr);
						images.add(imgsObj);
					}
				}
			}
		}
		return images;
	}


	/**
	 * 从JSON数组中获取所有文件路径和图片地址的列表。
	 *
	 * @param data 包含HTML数据和文件路径的JSON数组。
	 * @return 所有需要复制的文件路径和图片地址的列表。
	 */
	public static List<String> getCopyFiles(JSONArray data) {
		List<String> copyFiles = new ArrayList<>();
		for (int i = 0; i < data.size(); i++) {
			JSONObject node = data.getJSONObject(i);
			String htmlData = node.getStr("HTML_DATA");
			String filePath = node.getStr("FILE_PATH", "");
			// 处理文件路径
			if (StrUtil.isNotEmpty(filePath)) {
				if (filePath.contains("//")) {
					copyFiles.add(filePath);
				}
			}
			// 查找HTML中的图片
			JSONArray images = Util.html2Images(htmlData);
			for (int j = 0; j < images.size(); j++) {
				JSONObject imgObj = images.getJSONObject(j);
				JSONArray imgArr = imgObj.getJSONArray("img");
				// 处理图片地址
				if (ObjectUtil.isNotNull(imgArr)) {
					for (int k = 0; k < imgArr.size(); k++) {
						String src = imgArr.getJSONObject(k).getStr("src");
						String newSrc = StrUtil.replaceIgnoreCase(src, "File", "");
						copyFiles.add(newSrc);
					}
				}
			}
		}
		return copyFiles;
	}

	/**
	 * 获取对象的类型
	 */
	public static String getType(Object object) {
		String typeName = object.getClass().getName();
		int l = typeName.lastIndexOf(".");
		return typeName.substring(l + 1);
	}

	/**
	 * Excel横坐标的26进制转换为10进制
	 *
	 * @param str 例如:AZ
	 */
	public static int transferase_10(String str) {
		str = str.toUpperCase();
		char[] charArr = str.toCharArray();
		int res = 0;
		int exp = 0;
		for (int i = charArr.length - 1; i >= 0; i--) {
			int num = charArr[i] - 'A' + 1;
			res = (int) (res + num * Math.pow(26, exp));
			exp++;
		}
		return res;
	}

	/**
	 * 通过列的索引查找对应的列对象
	 *
	 * @param params  表属性集合
	 * @param address excel列的索引
	 */
	public static JSONObject getParam(JSONArray params, int address) {
		JSONObject res = new JSONObject();
		for (int i = 0; i < params.size(); i++) {
			JSONObject param = params.getJSONObject(i);
			String secondArea = param.getStr("SECOND_AREA", "");
			if (transferase_10(secondArea) == address) {
				res = param;
				break;
			}
		}
		return res;
	}

	/**
	 * 通过列的名称查找列的索引
	 *
	 * @param params  表属性集合
	 * @param colName excel列的索引
	 */
	public static int getColIndex(JSONArray params, String colName) {
		int res = 0;
		for (int i = 0; i < params.size(); i++) {
			JSONObject param = params.getJSONObject(i);

			String name = param.getStr("COL_NAME", "");
			if (StrUtil.equals(name, colName)) {
				String secondArea = param.getStr("SECOND_AREA", "");
				res = transferase_10(secondArea);
				break;
			}
		}
		return res;
	}


	/**
	 * 按文件修改时间顺序列出指定路径下的文件
	 *
	 * @param path 指定的文件路径
	 * @return JSONArray 包含文件信息的JSON数组，每个元素是一个包含文件名、文件路径、文件大小、文件前缀、文件格式、
	 * 文件修改时间（字符串和长整型两种形式）的JSONObject
	 */
	public static JSONArray listFilesOrderByTime(String path) {
		// 获取指定路径下的文件名列表
		List<String> fileNames = FileUtil.listFileNames(path);
		JSONArray fileNameArr = new JSONArray();
		for (String fileName : fileNames) {
			String filePath = path + File.separator + fileName;
			File thisFile = FileUtil.file(filePath);
			long modifiedTime = thisFile.lastModified();
			// 为每个文件创建包含详细信息的JSONObject
			JSONObject file = JSONUtil.createObj()
					.set("fileName", fileName)
					.set("filePath", filePath)
					.set("fileSize", FileUtil.readableFileSize(thisFile))
					.set("prefixFileName", FileNameUtil.getPrefix(fileName))
					.set("fileFormat", FileNameUtil.getSuffix(fileName))
					.set("fileModifiedTimeStr", cn.hutool.core.date.DateUtil.formatDateTime(new Date(modifiedTime)))
					.set("fileModifiedTime", modifiedTime);
			fileNameArr.add(file);
		}
		// 通过文件修改时间对文件信息进行排序
		Comparator<Object> comparator = Comparator
				.comparing(obj -> ((JSONObject) obj).getLong("fileModifiedTime"));
		fileNameArr.sort(comparator);

		return fileNameArr;
	}

	/**
	 * 获取文件上传的路径
	 */
	public static String getFileUploadPath() {
		JSONArray jar = postTwx("Thing.Fn.SystemDic", "getFileUploadPath", JSONUtil.createObj());
		return jar.getJSONObject(0).getStr("result");
	}

	/**
	 * 获取临时文件上传路径的方法。
	 */
	public static String getTempPath() {
		String tempPath = Convert.toStr(getThingProperty("Thing.Timer.DeleteTemps", "tempPath")) + "//";
		FileUtil.mkdir(tempPath);
		return tempPath;
	}

	/**
	 * 请求twx 返回数据
	 */
	public static JSONArray postTwx(String thing, String service, JSONObject params) {
		return postTwxForObject(thing, service, params).getJSONArray("rows");
	}

	public static JSONArray postTwx(String thing, String service) {
		return postTwx(thing, service, JSONUtil.createObj());
	}

	/**
	 * 请求twx 返回数据
	 */
	public static JSONArray postTwxForArray(String thing, String service, JSONObject params) {
		return postTwxForObject(thing, service, params).getJSONArray("array");
	}

	public static JSONArray postTwxForArray(String thing, String service) {
		return postTwxForArray(thing, service, JSONUtil.createObj());
	}

	/**
	 * 请求twx 返回对象
	 */
	public static JSONObject postTwxForObject(String thing, String service, JSONObject params) {
		return JSONUtil.parseObj(postTwxForString(thing, service, params));
	}

	public static JSONObject postTwxForObject(String thing, String service) {
		return postTwxForObject(thing, service, JSONUtil.createObj());
	}

	/**
	 * 查询sql
	 */
	public static JSONArray postQuerySql(String sql) {
		String result = postTwxForString("Thing.DB.Oracle", "RunQuery", JSONUtil.createObj().set("sql", sql));
		//判断result是不是json格式的字符串
		if (result.startsWith("{")) {
			return JSONUtil.parseObj(result).getJSONArray("rows");
		} else {
			log.error("查询sql{}出错：{}", sql, result);
			return new JSONArray();
		}
	}

	/**
	 * 执行sql
	 */
	public static int postCommandSql(String sql) {
		String result = postTwxForString("Thing.DB.Oracle", "RunCommand", JSONUtil.createObj().set("sql", sql));
		//判断result是不是json格式的字符串
		if (result.startsWith("{")) {
			return JSONUtil.parseObj(result).getJSONArray("rows").getJSONObject(0).getInt("result", 0);
		} else {
			log.error("执行sql{}出错：{}", sql, result);
			return 0;
		}
	}

	/**
	 * 请求twx 返回字符串
	 */
	public static String postTwxForString(String thing, String service, JSONObject params) {
		ThingworxConfig thingworxConfig = SpringContextUtil.getBean(ThingworxConfig.class);
		String domain = thingworxConfig.getDomain();
		String key = thingworxConfig.getKey();
		String path = domain + "/Thingworx/Things/" + thing + "/Services/" + service
				+ "?method=POST&Accept=application/json&appKey=" + key;
		return HttpRequest.post(path).header("Content-Type", "application/json").charset("utf-8")
				.body(params.toString()).execute().body();
	}

	public static byte[] readInputStream(InputStream inputStream) throws IOException {
		int BUFFER_SIZE = 1024;
		byte[] buffer = new byte[BUFFER_SIZE];

		int charsRead;
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		while ((charsRead = inputStream.read(buffer, 0, BUFFER_SIZE)) != -1) {
			bos.write(buffer, 0, charsRead);
		}
		bos.close();
		return bos.toByteArray();
	}

	/**
	 * 下载远程文件
	 * <p>
	 * Parameters: urlStr 请求的url path 目录 Returns: 下载的文件对象
	 */
	public static File downloadFromUrl(String urlStr, String path) throws Exception {

		File saveFile = null;
		URL url = new URL(urlStr);

		HttpURLConnection conn = (HttpURLConnection) url.openConnection();
		conn.setRequestProperty("User-Agent", "Mozilla/4.0(compatible MSIE 5.0; Windows NT; DigExt)");
		conn.setRequestMethod("GET");
		conn.setDoOutput(true);
		int code = conn.getResponseCode();
		if (code == 200) {
			String fileName = "";
			// 转换编码
			String contentDisposition = URLDecoder.decode(conn.getHeaderField("content-Disposition"), "UTF-8");
			if (!StrUtil.isEmpty(contentDisposition)) {
				if (contentDisposition.contains("filename=")) {
					fileName = contentDisposition.substring(contentDisposition.indexOf("filename=") + 9);
				}
			}
			InputStream is = conn.getInputStream();
			byte[] getData = readInputStream(is);
			File factoryFile = new File(path);
			FileUtil.mkdir(factoryFile);

			saveFile = new File(path + "//" + fileName);
			FileOutputStream fos = new FileOutputStream(saveFile);
			fos.write(getData);
			fos.close();
			is.close();
		}
		return saveFile;
	}

	/**
	 * 下载文件
	 */
	public static JSONObject downloadFromUrl(String urlStr) {
		JSONObject jsonObject = new JSONObject();
		try {
			urlStr = urlStr.replaceAll("amp;", "");
			// 文件上传路径
			String path = getFileUploadPath();
			// File srcFile = downloadFromUrl(urlStr, path);
			File srcFile;
			try {
				srcFile = HttpUtil.downloadFileFromUrl(urlStr, path);
			} catch (Exception e) {
				srcFile = downloadFromUrl(urlStr, path);
			}
			if (srcFile == null) {
				jsonObject.set("success", false);
				jsonObject.set("msg", "文件下载出错");
			} else {
				String fileName = srcFile.getName();
				String fileFormat = FileNameUtil.extName(srcFile);

				String month = new SimpleDateFormat("yyyy-MM").format(new Date());// 当前月份
				// 2019-10
				String paths = path + "//" + month;
				File file = new File(paths);
				FileUtil.mkdir(file);
				String uuid = UUID.randomUUID().toString();// 随机码
				File saveFile = new File(paths + "//" + uuid);
				FileUtil.copy(srcFile, saveFile, true);
				FileUtil.del(srcFile);
				jsonObject.set("success", true);
				jsonObject.set("fileFormat", fileFormat);// docx 、pdf
				jsonObject.set("fileName", fileName);// a.docx 、 b.pdf
				jsonObject.set("filePath", "//" + month + "//" + uuid);
			}

		} catch (Exception e) {
			jsonObject.set("success", false);
			jsonObject.set("msg", e.getLocalizedMessage());
			log.error(e.getMessage());
		}
		return jsonObject;
	}

	/**
	 * 字符串转成可插入的clob类型的sql
	 */
	public static String strToClobSql(String str) {
		int len = str.length();
		List<String> arr = new ArrayList<>();
		int maxLen = 1000;
		for (int i = 0; i < len; i += maxLen) {
			int next = i + maxLen;
			if (next > len) {
				next = len;
			}
			arr.add("to_clob('" + str.substring(i, next) + "')");
		}
		StringBuilder res = new StringBuilder();
		for (int i = 0; i < arr.size(); i++) {
			if (i < arr.size() - 1) {
				res.append(arr.get(i)).append("||");
			} else {
				res.append(arr.get(i));
			}

		}
		return res.toString();
	}

	/**
	 * 获取最大的列数量
	 */
	public static int getMaxColumnCount(ExcelReader reader) {
		int rowCount = reader.getRowCount();
		int colCount = 0;
		for (int i = 0; i < rowCount; i++) {
			int c = reader.getColumnCount(i);
			if (c > colCount) {
				colCount = c;
			}
		}
		return colCount;
	}

	/**
	 * 读取excel转换为JSONArray 其中空的单元格设置为""空字符串
	 */
	public static JSONArray readAll(ExcelReader reader) {
		int rowCount = reader.getRowCount();
		int colCount = getMaxColumnCount(reader);
		JSONArray readAll = new JSONArray();
		for (int r = 0; r < rowCount; r++) {
			JSONArray rowList = new JSONArray();
			for (int c = 0; c < colCount; c++) {
				rowList.add("");
			}
			readAll.add(rowList);
		}
		CellHandler c = (cell, value) -> {
			if (cell != null) {
				value = getCellValue(cell);
				readAll.getJSONArray(cell.getRowIndex()).set(cell.getColumnIndex(), StrUtil.trim(Convert.toStr(value)));
			}
		};
		reader.read(c);
		return readAll;
	}

	public static JSONArray clearExcelEmptyRow(JSONArray all, int maxRow) {
		for (int i = maxRow; i < all.size(); i++) {
			JSONArray row = all.getJSONArray(i);
			boolean isEmpty = true;
			for (int j = 0; j < row.size(); j++) {
				if (StrUtil.isNotBlank(row.getStr(j))) {
					isEmpty = false;
					break;
				}
			}
			if (isEmpty) {
				all.remove(i);
				i--;
			}
		}
		return all;
	}

	public static JSONArray clearExcelEmptyCol(JSONArray all, int maxCol) {
		int maxRowCol = 0;
		for (int i = 0; i < all.size(); i++) {
			JSONArray row = all.getJSONArray(i);
			for (int j = row.size() - 1; j >= 0; j--) {
				if (StrUtil.isNotBlank(row.getStr(j))) {
					if (maxRowCol < j) {
						maxRowCol = j;
					}
					break;
				}
			}
		}
		maxRowCol++;
		if (maxRowCol > maxCol) {
			maxCol = maxRowCol;
		}

		for (int i = 0; i < all.size(); i++) {
			JSONArray row = all.getJSONArray(i);
			for (int j = maxCol; j < row.size(); j++) {
				row.remove(j);
				j--;
			}
		}
		return all;
	}

	/**
	 * 读取excel转换为JSONArray 其中空的单元格设置为""空字符串
	 */
	public static JSONArray readAll(String filePath) {
		ExcelReader reader = ExcelUtil.getReader(filePath);
		return readAll(reader);
	}

	/**
	 * 描述：对表格中数值进行格式化
	 */
	public static String getCellValue(Cell cell) {
		String cellValue = "";
		if (ObjectUtil.isNull(cell)) {
			return cellValue;
		}
		// 以下是判断数据的类型
		switch (cell.getCellType()) {
			case NUMERIC: // 数字
				if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
					cellValue = sdf.format(DateUtil.getJavaDate(cell.getNumericCellValue()));
				} else {
					DataFormatter dataFormatter = new DataFormatter();
					cellValue = dataFormatter.formatCellValue(cell);
				}
				break;
			case STRING: // 字符串
				cellValue = cell.getStringCellValue();
				break;
			case BOOLEAN: // Boolean
				cellValue = cell.getBooleanCellValue() + "";
				break;
			case FORMULA: // 公式
				cellValue = cell.getCellFormula();
				break;
			case BLANK: // 空值
				cellValue = "";
				break;
			case ERROR: // 故障
				cellValue = "非法字符";
				break;
			default:
				cellValue = "未知类型";
				break;
		}
		return cellValue;
	}

	/**
	 * 判断两个结构化表格excel文件的表头格式是否相同
	 *
	 * @param tplPath  模板文件
	 * @param dataPath 用户上传的文件
	 */
	public static boolean compareExcelHeader(String tplPath, String dataPath) {
		boolean res = true;
		try {
			JSONArray tplArr = readAll(tplPath);
			JSONArray dataArr = readAll(dataPath);
			for (int i = 0; i < tplArr.size(); i++) {
				if (ObjectUtil.isNotNull(dataArr.getJSONArray(i))) {
					if (!ObjectUtil.equals(tplArr.getJSONArray(i), dataArr.getJSONArray(i))) {
						res = false;
						break;
					}
				} else {
					res = false;
					break;
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage());
			res = false;
		}
		return res;
	}

	/**
	 * 判断两个三级表的excel文件的格式是否相同
	 *
	 * @param tplPath  模板文件
	 * @param dataPath 用户上传的文件
	 */
	public static boolean compareThreeExcelTpl(String tplPath, String dataPath) {
		boolean res = true;
		try {
			JSONArray tplArr = readAll(tplPath);
			JSONArray dataArr = readAll(dataPath);
			for (int i = 0; i < tplArr.size(); i++) {
				JSONArray arr = tplArr.getJSONArray(i);

				for (int j = 0; j < arr.size(); j++) {
					String value = arr.getStr(j);
					if (!"".equals(value)) {
						if (ObjectUtil.isNotNull(dataArr.getJSONArray(i))) {
							if (ObjectUtil.isNotNull(dataArr.getJSONArray(i).getStr(j))) {
								if (!value.equals(dataArr.getJSONArray(i).getStr(j))) {
									res = false;
									break;
								}
							} else {
								res = false;
								break;
							}
						} else {
							res = false;
							break;
						}

					}
				}
			}
		} catch (Exception e) {
			res = false;
		}
		return res;
	}


	public static JSONObject getExcelPicData(Sheet sheet) {
		JSONObject res = new JSONObject();
		JSONArray arr = new JSONArray();
		int maxPicRow = 0, maxPicCol = 0;
		String date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
		String fileUploadPath = Util.getFileUploadPath();
		String month = new SimpleDateFormat("yyyy-MM").format(new Date());
		fileUploadPath = fileUploadPath + File.separator + month;
		FileUtil.mkdir(fileUploadPath);
		try {
			XSSFDrawing drawing = (XSSFDrawing) sheet.getDrawingPatriarch();
			for (XSSFShape shape : drawing.getShapes()) {
				if (shape instanceof XSSFPicture) {
					XSSFPicture picture = (XSSFPicture) shape;
					XSSFClientAnchor anchor = picture.getClientAnchor();
					XSSFPictureData pictureData = picture.getPictureData();
					short colNum = anchor.getCol1();
					int rowNum = anchor.getRow1();
					if (maxPicRow < rowNum + 1) {
						maxPicRow = rowNum + 1;
					}
					if (maxPicCol < colNum + 1) {
						maxPicCol = colNum + 1;
					}
					byte[] data = pictureData.getData();

					String photoName = (rowNum + 1) + "行," + (colNum + 1) + "列";
					//图片格式
					String photoFormat = pictureData.suggestFileExtension();
					String uuid = UUID.randomUUID().toString();//随机码

					String photoPath = fileUploadPath + File.separator + uuid;
					String src = "//" + month + "//" + uuid;
					FileUtil.writeBytes(data, photoPath);
					JSONObject photo = new JSONObject();
					photo.set("date", date);
					photo.set("col", colNum);
					photo.set("row", rowNum);
					photo.set("type", "photo");
					photo.set("photoPath", src);
					photo.set("photoName", photoName);
					photo.set("photoFormat", photoFormat);
					arr.add(photo);
				}
			}
		} catch (RuntimeException ignored) {

		}
		res.set("arr", arr);
		res.set("maxPicRow", maxPicRow);
		res.set("maxPicCol", maxPicCol);
		return res;
	}


	/**
	 * 提取出字符串中最后一个符合日期格式（yyyy-MM-dd）的子串，并返回一个包含原字符串去除最后一个日期后的字符串及该日期字符串的JSONObject
	 *
	 * @param input 输入的字符串
	 * @return JSONObject 包含 "extractedDate"（提取的日期）和 "remainingString"（剩余字符串）两个字段
	 */
	public static JSONObject extractAndRemoveLastDate(String input) {
		if (input == null || input.isEmpty()) {
			return new JSONObject().set("extractedDate", "").set("remainingString", "");
		}
		String DATE_PATTERN_REGEX = "(\\d{4}-\\d{2}-\\d{2})";
		Pattern pattern = Pattern.compile(DATE_PATTERN_REGEX);
		Matcher matcher = pattern.matcher(input);

		// 查找最后一个匹配项
		String lastMatchedDate = "";
		int lastIndex = -1;
		while (matcher.find()) {
			lastMatchedDate = matcher.group(1);
			lastIndex = matcher.end(1) - 10; // 记录最后一个匹配结束的位置
		}

		// 构造结果JSONObject
		JSONObject result = new JSONObject();
		// 如果找到了日期
		if (!lastMatchedDate.isEmpty() && lastIndex != -1) {
			// 确保找到了有效的日期并且获得了正确的索引位置
			String remainingString = input.substring(0, lastIndex) + input.substring(lastIndex + 10);
			result.set("extractedDate", lastMatchedDate);
			result.set("remainingString", remainingString);
		} else {
			result.set("extractedDate", "");
			result.set("remainingString", input);
		}

		return result;
	}

	public static JSONObject excel2HandsonTable(String excelPath) {
		return excel2HandsonTable(excelPath, 0);
	}

	/**
	 * 将excel文件读取为handson组件的json格式
	 */
	public static JSONObject excel2HandsonTable(String excelPath, int sheetIndex) {
		JSONObject res = new JSONObject();
		try (ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(excelPath)) {
			reader.setSheet(sheetIndex);
			Sheet sheet = reader.getSheet();
			// 获取合并单元格的坐标集合
			List<CellRangeAddress> listCombineCell = sheet.getMergedRegions();
			JSONArray merged = new JSONArray();

			int maxMergedRow = 0, maxMergedCol = 0;

			for (CellRangeAddress ca : listCombineCell) {
				// 获得合并单元格的起始行, 结束行, 起始列, 结束列
				int firstC = ca.getFirstColumn();
				int lastC = ca.getLastColumn();
				int firstR = ca.getFirstRow();
				int lastR = ca.getLastRow();

				if ((lastR + 1) > maxMergedRow) {
					maxMergedRow = lastR + 1;
				}
				if ((lastC + 1) > maxMergedCol) {
					maxMergedCol = lastC + 1;
				}

				int rowspan = lastR - firstR + 1;
				int colspan = lastC - firstC + 1;
				JSONObject m = new JSONObject();
				m.set("row", firstR).set("col", firstC).set("rowspan", rowspan).set("colspan", colspan).set("removed", false);
				merged.add(m);
			}
			JSONArray all = readAll(reader);

			//获取所有图片信息
			JSONObject picData = getExcelPicData(sheet);
			int maxPicRow = picData.getInt("maxPicRow");
			int maxPicCol = picData.getInt("maxPicCol");
			JSONArray picArr = picData.getJSONArray("arr");
			int maxRow = Math.max(maxPicRow, maxMergedRow);
			int maxCol = Math.max(maxPicCol, maxMergedCol);
			JSONArray clearRow = clearExcelEmptyRow(all, maxRow);
			JSONArray arr = clearExcelEmptyCol(clearRow, maxCol);
			int photoIndex = 0;
			JSONArray metas = new JSONArray();
			for (int i = 0; i < arr.size(); i++) {
				JSONArray row = arr.getJSONArray(i);
				for (int j = 0; j < row.size(); j++) {
					JSONArray eles = new JSONArray();
					JSONObject meta = JSONUtil.createObj().set("row", i).set("col", j);

					for (int x = 0; x < picArr.size(); x++) {
						JSONObject pic = picArr.getJSONObject(x);
						if (i == pic.getInt("row") && j == pic.getInt("col")) {
							String cellValue = row.getStr(j);
							JSONObject dateObj = extractAndRemoveLastDate(cellValue);
							String cellDate = dateObj.getStr("extractedDate");
							String remainingString = dateObj.getStr("remainingString");
							if (StrUtil.isNotBlank(cellDate)) {
								//这个单元格是日期
								DateTime dateTime = cn.hutool.core.date.DateUtil.parseDate(cellDate);
								cellDate = cn.hutool.core.date.DateUtil.format(dateTime, "yyyy-MM-dd");
								row.set(j, remainingString);
							}
							if (StrUtil.isNotBlank(cellDate)) {
								pic.set("date", cellDate);
								pic.set("type", "sign");
								pic.set("src", pic.getStr("photoPath"));
								pic.set("class", "sign-img test-sign");
								meta.set("readOnly", true);
							} else {
								photoIndex++;
								pic.set("src", "/File" + pic.getStr("photoPath"));
								pic.set("photoShowNum", "图" + photoIndex);
								pic.set("class", "sign-img photo");
							}
							eles.add(pic);
						}
					}
					metas.add(meta.set("eles", eles));
				}
			}

			JSONObject data = new JSONObject();
			data.set("tableData", arr);
			data.set("meta", metas);
			data.set("merged", merged);
			res.set("data", data);
			res.set("success", true);
		} catch (Exception e) {
			log.error(e.getMessage());
			res.set("success", false);
			res.set("msg", e.getLocalizedMessage());
		}
		return res;
	}

	/**
	 *
	 */
	public static CellStyle getCellStyle(ExcelWriter writer, boolean isHeader) {
		CellStyle style = writer.createCellStyle();
		// 设置单元格横向和纵向居中对齐
		style.setVerticalAlignment(VerticalAlignment.CENTER);
		style.setAlignment(HorizontalAlignment.CENTER);
		// 设置单元格四周边框
		style.setBorderBottom(BorderStyle.THIN);
		style.setBorderLeft(BorderStyle.THIN);
		style.setBorderTop(BorderStyle.THIN);
		style.setBorderRight(BorderStyle.THIN);
		style.setWrapText(true);
		if (isHeader) {
			// 设置标题字体 加粗
			Font font = writer.createFont();
			font.setBold(true);
			style.setFont(font);
			// 设置背景色
			style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
			style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		}
		return style;
	}

	public static File tableData2Excel(String saveDataStr, String path, String headerRow, String tableName) {
		JSONObject saveData = JSONUtil.parseObj(saveDataStr);
		JSONArray tableData = saveData.getJSONArray("tableData");
		int maxCol = tableData.getJSONArray(0).size();
		File f = new File(path);
		dealJSONArray(tableData);
		JSONArray header = new JSONArray();
		header.add(tableName);
		tableData.add(0, header);
		ExcelWriter writer = ExcelUtil.getWriter(path);
		// 一次性写出内容
		writer.write(tableData, false);
		// 设置列宽
//		for (int i = 0; i < writer.getColumnCount(); i++) {
//			writer.setColumnWidth(i, 20);
//		}
		// 设置行高
//		for (int i = 0; i < writer.getRowCount(); i++) {
//			writer.setRowHeight(i, 26);
//		}

		writer.autoSizeColumnAll();
		// 处理合并单元格
		JSONArray mergeds = saveData.getJSONArray("merged");
		//因为增加了一行 所以合并信息中的所有行的数值都要加1
		mergeds.add(0, new JSONObject()
				.set("row", 0)
				.set("col", 0)
				.set("rowspan", 1)
				.set("colspan", maxCol)
				.set("removed", false));
		for (int i = 1; i < mergeds.size(); i++) {
			JSONObject merged = mergeds.getJSONObject(i);
			int row = merged.getInt("row");
			merged.set("row", row + 1);
		}

		for (int i = 0; i < mergeds.size(); i++) {
			JSONObject merged = mergeds.getJSONObject(i);
			int row = merged.getInt("row");
			int col = merged.getInt("col");
			int rowspan = merged.getInt("rowspan");
			int colspan = merged.getInt("colspan");
			Cell cell = writer.getCell(col, row);
			String value = Convert.toStr(getCellValue(cell));
			if (!(rowspan == 1 && colspan == 1)) {
				writer.merge(row, row + rowspan - 1, col, col + colspan - 1, value, false);
			}
		}

		int min = 0;
		int max;
		if (headerRow.contains("-")) {
			min = Convert.toInt(headerRow.split("-")[0]);
			max = Convert.toInt(headerRow.split("-")[1]);
		} else {
			max = Convert.toInt(headerRow);
		}
		//因为增加了一行 数值要加1
		max++;
		CellStyle headerStyle = getCellStyle(writer, true);
		CellStyle cellStyle = getCellStyle(writer, false);
		for (int i = 0; i < writer.getRowCount(); i++) {
			if (i <= (max - 1) && i >= (min - 1)) {
				writer.setRowStyleIfHasData(i, headerStyle);
			} else {
				writer.setRowStyleIfHasData(i, cellStyle);
			}
		}
		writer.setFreezePane(max);
		writer.close();

		return f;
	}


	/**
	 * 将HandsonTable的表格数据导出excel
	 *
	 * @param data     JSON数据
	 * @param tempPath 临时文件目录
	 */
	public static File tableData2Excel(JSONObject data, String tempPath) {

		String saveDataStr = data.getStr("SAVE_DATA");
		String security = data.getStr("SECURITY_NAME");
		String headerRow = data.getStr("TABLE_HEADER", "0");
		String tableName = data.getStr("TABLE_NUM") + "：" + data.getStr("NAME");
		if (StrUtil.isEmpty(data.getStr("TABLE_NUM"))) {
			tableName = data.getStr("NAME");
		}
		tableName = tableName.replaceAll("/", "、").replaceAll("\\\\", "、");
		tableName += "（" + security + "）";
		String path = tempPath + "\\" + tableName + ".xlsx";
		return tableData2Excel(saveDataStr, path, headerRow, tableName);
	}

	/**
	 * 校验上传的excel文件 能否正常导出pdf
	 * 测试在有表头行的情况下才会导出失败
	 */
	public static boolean isExportPdf(String tableData, String tempPath, String fontPath) {
		JSONObject data = new JSONObject();
		data.set("SAVE_DATA", tableData);
		data.set("TABLE_HEADER", "1");
		data.set("NAME", System.currentTimeMillis() + "");
		JSONObject res = tableData2Pdf(data, tempPath, fontPath, new PdfOptions());
		return res.getBool("success");
	}

	/**
	 * 将HandsonTable的表格数据导出pdf
	 *
	 * @param data     JSON数据
	 * @param tempPath 临时文件目录
	 * @param fontPath pdf字体存放路径
	 */
	public static JSONObject tableData2Pdf(JSONObject data, String tempPath, String fontPath) {
		// 默认使用A4横向
		return tableData2Pdf(data, tempPath, fontPath, new PdfOptions());
	}

	public static UnitValue[] getColWidths(JSONArray colWidths) {
		UnitValue[] res = new UnitValue[colWidths.size()];
		float total = 0;
		for (int i = 0; i < colWidths.size(); i++) {
			float afloat = colWidths.getFloat(i);
			total += afloat;
		}
		for (int i = 0; i < colWidths.size(); i++) {
			float afloat = colWidths.getFloat(i);
			double div = NumberUtil.mul(NumberUtil.div(afloat, total), 100);

			UnitValue aUnitValue = new UnitValue(UnitValue.PERCENT, (float) div);
			res[i] = aUnitValue;
		}
		return res;
	}

	/**
	 * 优化表格列宽，根据内容动态计算合理的列宽分配
	 * 增强版算法，更智能地处理列宽分配问题
	 *
	 * @param tableData 表格数据
	 * @param originalColWidths 原始列宽配置
	 * @param headerRow 表头行范围，格式为"1"或"1-3"
	 * @return 优化后的列宽UnitValue数组
	 */
	public static UnitValue[] optimizeColWidths(JSONArray tableData, JSONArray originalColWidths, String headerRow) {
		if (tableData == null || tableData.isEmpty() || originalColWidths == null) {
			// 如果没有有效数据，使用默认处理方式
			return originalColWidths != null ? getColWidths(originalColWidths) : null;
		}

		int colCount = originalColWidths.size();

		// 解析表头行范围
		int headerMinRow = 0;
		int headerMaxRow = 0;
		if (StrUtil.isNotEmpty(headerRow)) {
			if (headerRow.contains("-")) {
				headerMinRow = Convert.toInt(headerRow.split("-")[0]) - 1;
				headerMaxRow = Convert.toInt(headerRow.split("-")[1]) - 1;
			} else {
				headerMaxRow = Convert.toInt(headerRow) - 1;
			}
		}
		boolean hasHeader = headerMaxRow >= headerMinRow;

		// 步骤1: 分析表格内容，计算每列的内容特征
		int[] maxContentLength = new int[colCount];
		int[] avgContentLength = new int[colCount];
		int[] contentCount = new int[colCount];
		int[] totalContentLength = new int[colCount]; // 总内容长度
		double[] contentDensity = new double[colCount]; // 内容密度
		int[] headerContentLength = new int[colCount]; // 表头内容长度
		boolean[] isDateColumn = new boolean[colCount];
		boolean[] isStatusColumn = new boolean[colCount];
		boolean[] isNumberColumn = new boolean[colCount];
		boolean[] isSerialColumn = new boolean[colCount];
		boolean[] isImageColumn = new boolean[colCount];
		boolean[] isMediumTextColumn = new boolean[colCount]; // 中等长度文本列 (10-30)
		boolean[] isLongTextColumn = new boolean[colCount];   // 长文本列 (30-100)
		boolean[] isVeryLongTextColumn = new boolean[colCount]; // 超长文本列 (>100)
		boolean[] isLinkColumn = new boolean[colCount]; // 包含链接的列
		boolean[] isCodeColumn = new boolean[colCount]; // 包含代码片段的列

		// 初始化列类型检测数组
		for (int i = 0; i < colCount; i++) {
			isNumberColumn[i] = true;  // 假设初始都是数字列，后续验证
			isDateColumn[i] = true;    // 假设初始都是日期列，后续验证
			isSerialColumn[i] = i == 0; // 第一列通常是序号列
			headerContentLength[i] = 4; // 默认至少能容纳两个中文字符
		}

		// 分析表格内容
		for (int i = 0; i < tableData.size(); i++) {
			JSONArray row = tableData.getJSONArray(i);
			boolean isHeaderRow = hasHeader && i >= headerMinRow && i <= headerMaxRow;

			for (int j = 0; j < Math.min(row.size(), colCount); j++) {
				Object cellObj = row.get(j);
				String cellContent = "";

				// 处理不同类型的单元格内容
				if (cellObj instanceof JSONObject) {
					JSONObject cellJson = (JSONObject) cellObj;
					if (cellJson.containsKey("text")) {
						cellContent = cellJson.getStr("text", "");
					}
					// 检测是否包含图片
					if (cellJson.containsKey("img") && !cellJson.getJSONArray("img").isEmpty()) {
						isImageColumn[j] = true;
					}
				} else {
					cellContent = row.getStr(j, "");
				}

				if (StrUtil.isNotEmpty(cellContent)) {
					// 计算内容长度（考虑中文字符占用更多宽度和内容复杂度）
					int contentLength = calculateContentWidth(cellContent);
					
					// 区分表头和普通内容的处理
					if (isHeaderRow) {
						// 表头内容长度处理
						headerContentLength[j] = Math.max(headerContentLength[j], contentLength);
						log.debug("列{}的表头文本宽度: {}, 内容: {}", j, contentLength, cellContent);
					} else {
						// 普通内容长度处理
						maxContentLength[j] = Math.max(maxContentLength[j], contentLength);
						totalContentLength[j] += contentLength;
						contentCount[j]++;

						// 检测是否为数字列
						if (isNumberColumn[j] && !isNumeric(cellContent)) {
							isNumberColumn[j] = false;
						}

						// 检测是否为日期列
						if (isDateColumn[j] && !isDateFormat(cellContent)) {
							isDateColumn[j] = false;
						}

						// 检测是否为状态列（通常是短文本，如"已完成"、"进行中"等）
						if (contentLength <= 4 && isStatusWord(cellContent)) {
							isStatusColumn[j] = true;
						}

						// 检测文本长度级别
						if (contentLength > 100) {
							isVeryLongTextColumn[j] = true;
						} else if (contentLength > 30) {
							isLongTextColumn[j] = true;
						} else if (contentLength > 10) {
							isMediumTextColumn[j] = true;
						}
						
						// 检测是否包含链接
						if (cellContent.contains("http://") || cellContent.contains("https://") || cellContent.contains("www.")) {
							isLinkColumn[j] = true;
						}
						
						// 检测是否包含代码片段
						if ((cellContent.contains("{") && cellContent.contains("}")) || 
							(cellContent.contains("<") && cellContent.contains(">"))) {
							isCodeColumn[j] = true;
						}
					}
				}
			}
		}

		// 计算平均内容长度和内容密度
		for (int i = 0; i < colCount; i++) {
			if (contentCount[i] > 0) {
				avgContentLength[i] = totalContentLength[i] / contentCount[i];
				contentDensity[i] = (double) totalContentLength[i] / contentCount[i];
			}
		}
		
		// 记录各类型列的统计
		int serialColumnCount = 0;
		int dateColumnCount = 0;
		int statusColumnCount = 0;
		int numberColumnCount = 0;
		int imageColumnCount = 0;
		int mediumTextColumnCount = 0;
		int longTextColumnCount = 0;
		int veryLongTextColumnCount = 0;
		int linkColumnCount = 0;
		int codeColumnCount = 0;
		
		for (int i = 0; i < colCount; i++) {
			if (isSerialColumn[i]) serialColumnCount++;
			if (isDateColumn[i]) dateColumnCount++;
			if (isStatusColumn[i]) statusColumnCount++;
			if (isNumberColumn[i]) numberColumnCount++;
			if (isImageColumn[i]) imageColumnCount++;
			if (isMediumTextColumn[i]) mediumTextColumnCount++;
			if (isLongTextColumn[i]) longTextColumnCount++;
			if (isVeryLongTextColumn[i]) veryLongTextColumnCount++;
			if (isLinkColumn[i]) linkColumnCount++;
			if (isCodeColumn[i]) codeColumnCount++;
		}
		
		log.debug("列类型统计 - 序号列: {}, 日期列: {}, 状态列: {}, 数字列: {}, 图片列: {}, " + 
				 "中等文本列: {}, 长文本列: {}, 超长文本列: {}, 链接列: {}, 代码列: {}",
				 serialColumnCount, dateColumnCount, statusColumnCount, numberColumnCount, imageColumnCount,
				 mediumTextColumnCount, longTextColumnCount, veryLongTextColumnCount, linkColumnCount, codeColumnCount);

		// 步骤2: 检测并修复异常列宽值
		boolean hasAbnormalWidths = false;
		double[] widths = new double[colCount];
		double totalOriginalWidth = 0;
		double maxWidth = Double.MIN_VALUE;
		double minWidth = Double.MAX_VALUE;

		for (int i = 0; i < colCount; i++) {
			widths[i] = originalColWidths.getDouble(i);
			totalOriginalWidth += widths[i];
			maxWidth = Math.max(maxWidth, widths[i]);
			minWidth = Math.min(minWidth, widths[i]);
		}

		// 计算平均列宽和标准差
		double avgWidth = totalOriginalWidth / colCount;
		double variance = 0;
		for (int i = 0; i < colCount; i++) {
			variance += Math.pow(widths[i] - avgWidth, 2);
		}
		double stdDev = Math.sqrt(variance / colCount);

		// 计算变异系数（标准差/平均值），用于评估分布的不均匀程度
		double coefficientOfVariation = stdDev / avgWidth;

		// 增强的异常检测条件
		// 1. 变异系数检测: 变异系数 > 0.4 表示分布不均匀
		// 2. 最大最小比例检测: 最大列宽/最小列宽 > 8
		// 3. 单列占比检测: 单个列宽占总宽度 > 25%
		// 4. 特定值检测: 检测是否存在特定的异常值（如829）
		double maxMinRatio = maxWidth / Math.max(0.1, minWidth);
		double maxWidthPercentage = maxWidth / totalOriginalWidth * 100;

		// 检测是否存在特定的异常值（如829）
		boolean hasSpecificAbnormalValue = false;
		for (int i = 0; i < colCount; i++) {
			if (widths[i] > 800) {  // 检测是否有超大值
				hasSpecificAbnormalValue = true;
				break;
			}
		}

		// 综合判断是否存在异常列宽
		if (coefficientOfVariation > 0.4 || maxMinRatio > 8 || maxWidthPercentage > 25 || hasSpecificAbnormalValue) {
			hasAbnormalWidths = true;
		}

		StringBuilder abnormalWidthsInfo = new StringBuilder();
		for (int i = 0; i < colCount; i++) {
			if (widths[i] > (avgWidth + 2 * stdDev) || widths[i] / totalOriginalWidth * 100 > 25 || widths[i] > 800) {
				abnormalWidthsInfo.append("列").append(i).append("=").append(widths[i]).append(", ");
			}
		}

				// 步骤3: 根据内容特征和列类型分配列宽
		double[] newWidthFactors = new double[colCount];
		
		// 计算自适应列宽上限（基于列数动态调整）
		// 列数越多，单列最大宽度占比越小
		double adaptiveMaxWidthPercentage = Math.min(40.0, Math.max(15.0, 100.0 / Math.sqrt(colCount)));
		log.debug("基于列数{}计算的自适应列宽上限: {}%", colCount, adaptiveMaxWidthPercentage);
		
		// 记录内容密度信息
		StringBuilder densityInfo = new StringBuilder();
		for (int i = 0; i < colCount; i++) {
			densityInfo.append("列").append(i).append("=").append(String.format("%.2f", contentDensity[i])).append(", ");
		}
		log.debug("内容密度分布: {}", densityInfo.toString());

		// 记录表头宽度分布
		if (hasHeader) {
			StringBuilder headerLengthInfo = new StringBuilder();
			for (int i = 0; i < colCount; i++) {
				headerLengthInfo.append("列").append(i).append("=").append(headerContentLength[i]).append(", ");
			}
			log.info("表头宽度分布: {}", headerLengthInfo.toString());
		}

		// 如果存在异常列宽，根据内容特征重新分配
		if (hasAbnormalWidths) {
			log.info("检测到异常列宽分布: 平均列宽={}, 标准差={}, 变异系数={}, 最大/最小比={}, 最大列占比={}%, 异常列: {}",
				avgWidth, stdDev, coefficientOfVariation, maxMinRatio, maxWidthPercentage, abnormalWidthsInfo.toString());

			// 记录列内容长度分布
			StringBuilder contentLengthInfo = new StringBuilder();
			for (int i = 0; i < colCount; i++) {
				contentLengthInfo.append("列").append(i).append("=").append(maxContentLength[i])
					.append("(avg:").append(avgContentLength[i]).append("), ");
			}
			log.info("列内容长度分布: {}", contentLengthInfo.toString());

			// 计算内容总长度（用于比例分配）
			int totalContentLengthSum = 0;
			// 找出最大内容密度和最小内容密度（用于归一化）
			double maxDensity = Double.MIN_VALUE;
			double minDensity = Double.MAX_VALUE;
			
			for (int i = 0; i < colCount; i++) {
				// 使用加权的内容长度，考虑最大长度、平均长度和表头长度
				int effectiveContentLength = hasHeader 
					? Math.max(headerContentLength[i], (int)(maxContentLength[i] * 0.7 + avgContentLength[i] * 0.3))
					: (int)(maxContentLength[i] * 0.7 + avgContentLength[i] * 0.3);
				totalContentLengthSum += effectiveContentLength;
				
				// 更新最大最小内容密度
				if (contentDensity[i] > maxDensity) {
					maxDensity = contentDensity[i];
				}
				if (contentDensity[i] < minDensity && contentDensity[i] > 0) {
					minDensity = contentDensity[i];
				}
						}						// 计算自适应列宽上限（基于列数动态调整）			// 列数越多，单列最大宽度占比越小			double adaptiveMaxWidthPercentage = Math.min(40.0, Math.max(15.0, 100.0 / Math.sqrt(colCount)));			log.debug("基于列数{}计算的自适应列宽上限: {}%", colCount, adaptiveMaxWidthPercentage);			// 基于内容特征的智能列宽分配
			for (int i = 0; i < colCount; i++) {
				// 特殊列类型处理
				if (isSerialColumn[i]) {
					// 序号列（通常是第一列）使用较小固定宽度，但确保能容纳表头
					newWidthFactors[i] = Math.max(4.0, headerContentLength[i] * 0.8);
					log.debug("列{}为序号列，分配固定宽度权重: {}", i, newWidthFactors[i]);
				} else if (isDateColumn[i]) {
					// 日期列使用固定宽度，但确保能容纳表头
					newWidthFactors[i] = Math.max(10.0, headerContentLength[i] * 0.8);
					log.debug("列{}为日期列，分配固定宽度权重: {}", i, newWidthFactors[i]);
				} else if (isStatusColumn[i]) {
					// 状态列使用较小宽度，但确保能容纳表头
					newWidthFactors[i] = Math.max(6.0, headerContentLength[i] * 0.8);
					log.debug("列{}为状态列，分配固定宽度权重: {}", i, newWidthFactors[i]);
				} else if (isNumberColumn[i]) {
					// 数字列使用较小宽度，但确保能容纳表头
					double contentFactor = (double) maxContentLength[i] / Math.max(1, totalContentLengthSum) * 100;
					newWidthFactors[i] = Math.max(Math.max(5.0, headerContentLength[i] * 0.8), contentFactor);
					log.debug("列{}为数字列，分配宽度权重: {}", i, newWidthFactors[i]);
				} else if (isImageColumn[i]) {
					// 图片列使用较大固定宽度
					newWidthFactors[i] = Math.max(15.0, headerContentLength[i] * 0.8);
					log.debug("列{}为图片列，分配固定宽度权重: {}", i, newWidthFactors[i]);
				} else if (isMediumTextColumn[i]) {
					// 中等长度文本列使用非线性宽度计算
					double contentFactor = applyNonLinearScaling((double) maxContentLength[i] / Math.max(1, totalContentLengthSum) * 100, 0.9);
					double headerFactor = headerContentLength[i] * 0.8;
					double densityFactor = 0;
					if (maxDensity > minDensity) {
						// 归一化内容密度到0-5范围，并应用到权重计算中
						densityFactor = 5.0 * (contentDensity[i] - minDensity) / (maxDensity - minDensity);
					}
					// 取内容因子和表头因子的最大值，并限制在合理范围内
					newWidthFactors[i] = Math.min(adaptiveMaxWidthPercentage, 
						Math.max(5.0, Math.max(contentFactor, headerFactor) + densityFactor));
					log.debug("列{}为中等长度文本列，内容因子:{}, 表头因子:{}, 密度因子:{}, 最终权重:{}", 
						i, contentFactor, headerFactor, densityFactor, newWidthFactors[i]);
				} else if (isLongTextColumn[i]) {
					// 长文本列使用非线性宽度计算，增加权重
					double contentFactor = applyNonLinearScaling((double) maxContentLength[i] / Math.max(1, totalContentLengthSum) * 100, 1.1);
					double headerFactor = headerContentLength[i] * 0.8;
					double densityFactor = 0;
					if (maxDensity > minDensity) {
						// 归一化内容密度到0-8范围，并应用到权重计算中
						densityFactor = 8.0 * (contentDensity[i] - minDensity) / (maxDensity - minDensity);
					}
					// 取内容因子和表头因子的最大值，并限制在合理范围内
					newWidthFactors[i] = Math.min(adaptiveMaxWidthPercentage, 
						Math.max(8.0, Math.max(contentFactor, headerFactor) + densityFactor));
					log.debug("列{}为长文本列，内容因子:{}, 表头因子:{}, 密度因子:{}, 最终权重:{}", 
						i, contentFactor, headerFactor, densityFactor, newWidthFactors[i]);
				} else if (isVeryLongTextColumn[i]) {
					// 超长文本列使用非线性宽度计算，使用对数缩放避免过大宽度
					// 使用对数缩放函数处理极长内容
					double contentFactor = applyNonLinearScaling((double) maxContentLength[i] / Math.max(1, totalContentLengthSum) * 100, 1.3);
					double headerFactor = headerContentLength[i] * 0.8;
					double densityFactor = 0;
					if (maxDensity > minDensity) {
						// 归一化内容密度到0-10范围，并应用到权重计算中
						densityFactor = 10.0 * (contentDensity[i] - minDensity) / (maxDensity - minDensity);
					}
					// 使用对数缩放处理极长内容，避免单列占用过多宽度
					contentFactor = logScaleForExtremeLength(contentFactor);
					// 取内容因子和表头因子的最大值，并限制在合理范围内
					newWidthFactors[i] = Math.min(adaptiveMaxWidthPercentage, 
						Math.max(10.0, Math.max(contentFactor, headerFactor) + densityFactor));
					log.debug("列{}为超长文本列，内容因子:{}, 表头因子:{}, 密度因子:{}, 最终权重:{}", 
						i, contentFactor, headerFactor, densityFactor, newWidthFactors[i]);
				} else if (isLinkColumn[i]) {
					// 链接列使用较大宽度，确保链接可见性
					double contentFactor = applyNonLinearScaling((double) maxContentLength[i] / Math.max(1, totalContentLengthSum) * 100, 1.0);
					double headerFactor = headerContentLength[i] * 0.8;
					// 取内容因子和表头因子的最大值，并限制在合理范围内
					newWidthFactors[i] = Math.min(adaptiveMaxWidthPercentage * 0.8, 
						Math.max(10.0, Math.max(contentFactor, headerFactor)));
					log.debug("列{}为链接列，分配宽度权重: {}", i, newWidthFactors[i]);
				} else if (isCodeColumn[i]) {
					// 代码片段列使用较大宽度，确保代码可读性
					double contentFactor = applyNonLinearScaling((double) maxContentLength[i] / Math.max(1, totalContentLengthSum) * 100, 1.1);
					double headerFactor = headerContentLength[i] * 0.8;
					// 取内容因子和表头因子的最大值，并限制在合理范围内
					newWidthFactors[i] = Math.min(adaptiveMaxWidthPercentage * 0.9, 
						Math.max(10.0, Math.max(contentFactor, headerFactor)));
					log.debug("列{}为代码片段列，分配宽度权重: {}", i, newWidthFactors[i]);
				} else {
					// 普通列基于内容长度分配宽度，同时考虑表头宽度
					int weightedContentLength = (int)(maxContentLength[i] * 0.7 + avgContentLength[i] * 0.3);
					double contentFactor = applyNonLinearScaling((double) weightedContentLength / Math.max(1, totalContentLengthSum) * 100, 0.8);
					double headerFactor = headerContentLength[i] * 0.8;
					double densityFactor = 0;
					if (maxDensity > minDensity) {
						// 归一化内容密度到0-3范围，并应用到权重计算中
						densityFactor = 3.0 * (contentDensity[i] - minDensity) / (maxDensity - minDensity);
					}

					// 确保最小宽度并限制最大宽度，同时考虑表头宽度
					newWidthFactors[i] = Math.min(adaptiveMaxWidthPercentage * 0.7, 
						Math.max(5.0, Math.max(contentFactor, headerFactor) + densityFactor));
					log.debug("列{}为普通列，内容因子:{}, 表头因子:{}, 密度因子:{}, 最终权重:{}", 
						i, contentFactor, headerFactor, densityFactor, newWidthFactors[i]);
				}
				
				// 限制表头过长导致的过宽列（不超过总宽度的自适应上限）
				if (newWidthFactors[i] > adaptiveMaxWidthPercentage && headerContentLength[i] > maxContentLength[i]) {
					newWidthFactors[i] = adaptiveMaxWidthPercentage;
					log.info("列{}的表头过长，限制最大宽度为{}%", i, adaptiveMaxWidthPercentage);
				}
			}
		} else {
			// 使用原始列宽比例，但仍进行一些优化，并考虑表头宽度
			for (int i = 0; i < colCount; i++) {
				// 计算原始比例
				double originalFactor = widths[i] / totalOriginalWidth * 100;

				// 根据列类型进行微调，并考虑表头宽度
				if (isSerialColumn[i] && originalFactor > 5.0) {
					// 序号列不需要太宽，但要确保能容纳表头
					originalFactor = Math.max(5.0, headerContentLength[i] * 0.8);
				} else if (isDateColumn[i] && (originalFactor < 8.0 || originalFactor > 12.0)) {
					// 日期列宽度调整到合理范围，但要确保能容纳表头
					originalFactor = Math.max(10.0, headerContentLength[i] * 0.8);
				} else if (isStatusColumn[i] && originalFactor > 8.0) {
					// 状态列不需要太宽，但要确保能容纳表头
					originalFactor = Math.max(6.0, headerContentLength[i] * 0.8);
				} else if (hasHeader) {
					// 其他列确保能容纳表头
					double headerFactor = headerContentLength[i] * 0.8;
					originalFactor = Math.max(originalFactor, headerFactor);
				}
				
				// 限制表头过长导致的过宽列（不超过总宽度的自适应上限）
				if (originalFactor > adaptiveMaxWidthPercentage && headerContentLength[i] > maxContentLength[i]) {
					originalFactor = adaptiveMaxWidthPercentage;
					log.info("列{}的表头过长，限制最大宽度为{}%", i, adaptiveMaxWidthPercentage);
				}

				newWidthFactors[i] = originalFactor;
			}
		}

		// 步骤4: 最小列宽限制和归一化
		double totalNewWidth = 0;
		
		// 应用最小列宽保证
		for (int i = 0; i < colCount; i++) {
			// 确保最小列宽，至少能容纳两个中文字符
			double minimumWidth = Math.max(4.0, headerContentLength[i] * 0.5);
			
			// 根据列类型调整最小宽度
			if (isSerialColumn[i]) {
				minimumWidth = Math.max(minimumWidth, 4.0);
			} else if (isDateColumn[i]) {
				minimumWidth = Math.max(minimumWidth, 8.0);
			} else if (isStatusColumn[i]) {
				minimumWidth = Math.max(minimumWidth, 5.0);
			} else if (isNumberColumn[i]) {
				minimumWidth = Math.max(minimumWidth, 5.0);
			} else if (isImageColumn[i]) {
				minimumWidth = Math.max(minimumWidth, 12.0);
			} else if (isMediumTextColumn[i] || isLongTextColumn[i] || isVeryLongTextColumn[i]) {
				minimumWidth = Math.max(minimumWidth, 8.0);
			}
			
			newWidthFactors[i] = Math.max(minimumWidth, newWidthFactors[i]);
			totalNewWidth += newWidthFactors[i];
		}

		// 记录原始分配的列宽情况
		StringBuilder originalWidthsInfo = new StringBuilder();
		for (int i = 0; i < colCount; i++) {
			originalWidthsInfo.append(String.format("%.1f", newWidthFactors[i])).append("%, ");
		}
		log.debug("列宽归一化前的分配情况: {}", originalWidthsInfo.toString());
		
		// 归一化到100%
		UnitValue[] result = new UnitValue[colCount];
		StringBuilder finalWidthsInfo = new StringBuilder();
		
		// 记录各类型列的宽度分布
		double serialColumnsWidth = 0;
		double dateColumnsWidth = 0;
		double statusColumnsWidth = 0;
		double numberColumnsWidth = 0;
		double imageColumnsWidth = 0;
		double mediumTextColumnsWidth = 0;
		double longTextColumnsWidth = 0;
		double veryLongTextColumnsWidth = 0;
		
		for (int i = 0; i < colCount; i++) {
			double normalizedWidth = newWidthFactors[i] / totalNewWidth * 100;
			result[i] = new UnitValue(UnitValue.PERCENT, (float) normalizedWidth);
			finalWidthsInfo.append(String.format("%.1f%%", normalizedWidth)).append(", ");
			
			// 累计各类型列的总宽度百分比
			if (isSerialColumn[i]) serialColumnsWidth += normalizedWidth;
			if (isDateColumn[i]) dateColumnsWidth += normalizedWidth;
			if (isStatusColumn[i]) statusColumnsWidth += normalizedWidth;
			if (isNumberColumn[i]) numberColumnsWidth += normalizedWidth;
			if (isImageColumn[i]) imageColumnsWidth += normalizedWidth;
			if (isMediumTextColumn[i]) mediumTextColumnsWidth += normalizedWidth;
			if (isLongTextColumn[i]) longTextColumnsWidth += normalizedWidth;
			if (isVeryLongTextColumn[i]) veryLongTextColumnsWidth += normalizedWidth;
		}

		// 记录最终的各类型列宽度占比
		log.info("列宽优化完成，最终列宽分布: {}", finalWidthsInfo.toString());
		
		// 记录各类型列的宽度占比
		StringBuilder columnTypeWidthInfo = new StringBuilder();
		if (serialColumnsWidth > 0) columnTypeWidthInfo.append("序号列: ").append(String.format("%.1f%%", serialColumnsWidth)).append(", ");
		if (dateColumnsWidth > 0) columnTypeWidthInfo.append("日期列: ").append(String.format("%.1f%%", dateColumnsWidth)).append(", ");
		if (statusColumnsWidth > 0) columnTypeWidthInfo.append("状态列: ").append(String.format("%.1f%%", statusColumnsWidth)).append(", ");
		if (numberColumnsWidth > 0) columnTypeWidthInfo.append("数字列: ").append(String.format("%.1f%%", numberColumnsWidth)).append(", ");
		if (imageColumnsWidth > 0) columnTypeWidthInfo.append("图片列: ").append(String.format("%.1f%%", imageColumnsWidth)).append(", ");
		if (mediumTextColumnsWidth > 0) columnTypeWidthInfo.append("中等文本列: ").append(String.format("%.1f%%", mediumTextColumnsWidth)).append(", ");
		if (longTextColumnsWidth > 0) columnTypeWidthInfo.append("长文本列: ").append(String.format("%.1f%%", longTextColumnsWidth)).append(", ");
		if (veryLongTextColumnsWidth > 0) columnTypeWidthInfo.append("超长文本列: ").append(String.format("%.1f%%", veryLongTextColumnsWidth)).append(", ");
		
		if (columnTypeWidthInfo.length() > 0) {
			log.info("各类型列宽度占比: {}", columnTypeWidthInfo.toString());
		}
		
		// 查找列宽最大和最小的列，用于评估分配均衡性
		double maxColWidth = Double.MIN_VALUE;
		double minColWidth = Double.MAX_VALUE;
		int maxWidthColIndex = 0;
		int minWidthColIndex = 0;
		
		for (int i = 0; i < colCount; i++) {
			double normalizedWidth = newWidthFactors[i] / totalNewWidth * 100;
			if (normalizedWidth > maxColWidth) {
				maxColWidth = normalizedWidth;
				maxWidthColIndex = i;
			}
			if (normalizedWidth < minColWidth) {
				minColWidth = normalizedWidth;
				minWidthColIndex = i;
			}
		}
		
		// 计算最大最小列宽比例，评估分配均衡性
		double maxMinWidthRatio = maxColWidth / Math.max(0.1, minColWidth);
		log.info("列宽分配均衡性 - 最大列({}): {}%, 最小列({}): {}%, 比例: {}", 
			maxWidthColIndex, String.format("%.1f", maxColWidth), 
			minWidthColIndex, String.format("%.1f", minColWidth), 
			String.format("%.2f", maxMinWidthRatio));
		
		// 如果分配极不均衡，记录警告信息
		if (maxMinWidthRatio > 10) {
			log.warn("检测到列宽分配极不均衡，最大/最小比例超过10倍，可能需要人工干预");
		}

		return result;
	}

	/**
	 * 优化表格列宽，根据内容动态计算合理的列宽分配
	 * 增强版算法，更智能地处理列宽分配问题
	 * 
	 * 此重载方法用于向后兼容，不考虑表头宽度优化
	 *
	 * @param tableData 表格数据
	 * @param originalColWidths 原始列宽配置
	 * @return 优化后的列宽UnitValue数组
	 */
	public static UnitValue[] optimizeColWidths(JSONArray tableData, JSONArray originalColWidths) {
		return optimizeColWidths(tableData, originalColWidths, null);
	}

	/**
	 * 计算内容宽度，考虑不同字符类型的宽度差异和内容复杂性
	 *
	 * @param content 内容字符串
	 * @return 估算的内容宽度
	 */
	private static int calculateContentWidth(String content) {
		if (StrUtil.isEmpty(content)) {
			return 0;
		}

		// 检测特殊内容类型
		boolean hasUrl = content.contains("http://") || content.contains("https://") || content.contains("www.");
		boolean hasCode = content.contains("{") && content.contains("}") || content.contains("(") && content.contains(")");
		boolean hasFormula = content.contains("=") && (content.contains("+") || content.contains("-") || content.contains("*") || content.contains("/"));
		boolean hasNewlines = content.contains("\n") || content.contains("\r");
		
		// 内容复杂度评分（初始为1.0）
		double complexityFactor = 1.0;
		
		// 特殊内容类型的复杂度加成
		if (hasUrl) complexityFactor += 0.2;
		if (hasCode) complexityFactor += 0.3;
		if (hasFormula) complexityFactor += 0.25;
		if (hasNewlines) complexityFactor += 0.1;
		
		// 检测字符多样性
		boolean hasUpperCase = false;
		boolean hasLowerCase = false;
		boolean hasDigit = false;
		boolean hasSymbol = false;
		boolean hasChinese = false;
		
		int width = 0;
		int specialSymbolCount = 0;
		
		// 字符计数数组，用于判断字符多样性
		for (int i = 0; i < content.length(); i++) {
			char c = content.charAt(i);
			
			if (Character.isUpperCase(c)) hasUpperCase = true;
			else if (Character.isLowerCase(c)) hasLowerCase = true;
			else if (Character.isDigit(c)) hasDigit = true;
			else if (isChinese(c)) hasChinese = true;
			else hasSymbol = true;
			
			// 根据字符类型计算宽度
			if (isChinese(c)) {
				// 中文字符宽度权重
				width += 2;
			} else if (Character.isDigit(c) || c == '.' || c == '-' || c == '+') {
				// 数字和常用符号宽度权重
				width += 1;
			} else if (c == ' ' || c == '\t') {
				// 空格和制表符宽度权重
				width += 1;
			} else if (c == '｜' || c == '—' || c == '～' || c == '…') {
				// 中文全角符号权重
				width += 2;
				specialSymbolCount++;
			} else if (c == '$' || c == '€' || c == '£' || c == '¥' || c == '₽') {
				// 货币符号权重
				width += 1;
				specialSymbolCount++;
			} else if (c == '%' || c == '°' || c == '§' || c == '©' || c == '®' || c == '™') {
				// 特殊符号权重
				width += 1;
				specialSymbolCount++;
			} else {
				// 其他字符宽度权重
				width += 1;
			}
		}
		
		// 计算字符多样性（0-5之间的值）
		int diversityScore = (hasUpperCase ? 1 : 0) + (hasLowerCase ? 1 : 0) + 
							 (hasDigit ? 1 : 0) + (hasChinese ? 1 : 0) + (hasSymbol ? 1 : 0);
		
		// 根据字符多样性和特殊符号数量增加复杂度
		complexityFactor += (diversityScore - 1) * 0.05;
		complexityFactor += specialSymbolCount * 0.02;
		
		// 应用复杂度因子到最终宽度
		return (int) Math.ceil(width * complexityFactor);
	}

	/**
	 * 判断字符是否为中文字符
	 *
	 * @param c 字符
	 * @return 是否为中文字符
	 */
	private static boolean isChinese(char c) {
		Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
		return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
				|| ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
				|| ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A;
	}

	/**
	 * 判断字符串是否为数字（整数或小数）
	 *
	 * @param str 字符串
	 * @return 是否为数字
	 */
	private static boolean isNumeric(String str) {
		if (StrUtil.isEmpty(str)) {
			return false;
		}

		// 尝试解析为数字
		try {
			Double.parseDouble(str);
			return true;
		} catch (NumberFormatException e) {
			// 检查是否为百分比格式
			if (str.endsWith("%")) {
				try {
					Double.parseDouble(str.substring(0, str.length() - 1));
					return true;
				} catch (NumberFormatException ex) {
					return false;
				}
			}
			return false;
		}
	}

	/**
	 * 判断字符串是否为日期格式
	 *
	 * @param str 字符串
	 * @return 是否为日期格式
	 */
	private static boolean isDateFormat(String str) {
		if (StrUtil.isEmpty(str)) {
			return false;
		}

		// 常见日期格式的正则表达式
		String[] datePatterns = {
			"\\d{4}-\\d{1,2}-\\d{1,2}", // yyyy-MM-dd
			"\\d{4}/\\d{1,2}/\\d{1,2}", // yyyy/MM/dd
			"\\d{1,2}-\\d{1,2}-\\d{4}", // dd-MM-yyyy
			"\\d{1,2}/\\d{1,2}/\\d{4}", // dd/MM/yyyy
			"\\d{4}年\\d{1,2}月\\d{1,2}日", // yyyy年MM月dd日
			"\\d{4}\\.\\d{1,2}\\.\\d{1,2}" // yyyy.MM.dd
		};

		for (String pattern : datePatterns) {
			if (str.matches(pattern)) {
				return true;
			}
		}

		// 尝试使用DateUtil解析
		try {
			 cn.hutool.core.date.DateUtil.parse(str);
			return true;
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 判断字符串是否为状态词
	 *
	 * @param str 字符串
	 * @return 是否为状态词
	 */
	private static boolean isStatusWord(String str) {
		if (StrUtil.isEmpty(str)) {
			return false;
		}

		// 常见状态词列表
		String[] statusWords = {
			"完成", "未完成", "进行中", "已完成", "待完成", "通过", "不通过", "合格", "不合格",
			"正常", "异常", "是", "否", "有", "无", "成功", "失败", "开始", "结束", "启用", "停用",
			"在线", "离线", "可用", "不可用", "激活", "未激活", "已审核", "未审核", "已签名", "未签名",
			"已闭环", "待检查", "需关注"
		};

		for (String statusWord : statusWords) {
			if (str.contains(statusWord)) {
				return true;
			}
		}

		return false;
	}

	/**
	 * 将表格数据转换为PDF表格
	 *
	 * @param doc         PDF文档对象
	 * @param tableStr    表格字符串
	 * @param saveDataStr 保存数据字符串
	 * @param headerRow   表头行
	 * @param font        PDF字体对象
	 */
	public static void tableDataToPdfTable(Document doc, String tableStr, String saveDataStr, String headerRow,
										   PdfFont font) {
		tableDataToPdfTable(doc, tableStr, saveDataStr, headerRow, font, true);
	}

	/**
	 * 将表格数据转换为PDF表格
	 *
	 * @param doc         PDF文档对象
	 * @param tableStr    表格字符串
	 * @param saveDataStr 保存数据字符串
	 * @param headerRow   表头行
	 * @param font        PDF字体对象
	 * @param isOutImg    是否输出图片
	 */
	public static void tableDataToPdfTable(Document doc, String tableStr, String saveDataStr, String headerRow,
										   PdfFont font, boolean isOutImg) {

		// 将HTML字符串转换为图片数组
		JSONArray images = Util.html2Images(tableStr);
		// 解析保存数据字符串为JSON对象
		JSONObject saveData = JSONUtil.parseObj(saveDataStr);
		// 获取表格数据数组
		JSONArray tableData = saveData.getJSONArray("tableData");
		// 获取列宽度数组
		JSONArray colWidths = saveData.getJSONArray("colWidths");
		int tableColNum = 0;
		for (int i = 0; i < tableData.size(); i++) {
			JSONArray row = tableData.getJSONArray(i);
			if (row.size() > tableColNum) {
				tableColNum = row.size();
			}
			for (int j = 0; j < row.size(); j++) {
				String text = row.getStr(j, "");
				row.set(j, JSONUtil.createObj().set("type", "text").set("text", text).set("img", new JSONArray()));
			}
		}
		// 获取合并单元格数组
		JSONArray mergeds = saveData.getJSONArray("merged");
		for (int i = 0; i < mergeds.size(); i++) {
			JSONObject merged = mergeds.getJSONObject(i);

			int row = merged.getInt("row");
			int col = merged.getInt("col");
			int rowspan = merged.getInt("rowspan");
			int colspan = merged.getInt("colspan");

			for (int r = row; r < rowspan + row; r++) {
				for (int c = col; c < colspan + col; c++) {
					if (!(r == row && c == col)) {
						tableData.getJSONArray(r).set(c,
								JSONUtil.createObj().set("type", "del").set("text", "").set("img", new JSONArray()));
					}
				}
			}
			JSONObject mergedObj = JSONUtil.createObj();
			mergedObj.set("type", "merged");
			mergedObj.set("rowspan", rowspan);
			mergedObj.set("colspan", colspan);
			mergedObj.set("img", new JSONArray());
			mergedObj.set("text", tableData.getJSONArray(row).getJSONObject(col).getStr("text"));
			tableData.getJSONArray(row).set(col, mergedObj);
		}
		// 获取元数据数组
		JSONArray metas = saveData.getJSONArray("meta");
		if (metas != null) {
			for (int i = 0; i < metas.size(); i++) {
				JSONObject meta = metas.getJSONObject(i);
				int row = meta.getInt("row");
				int col = meta.getInt("col");
				String className = meta.getStr("className", "htMiddle htCenter");
				JSONArray arr = tableData.getJSONArray(row);
				if (arr != null) {
					JSONObject obj = arr.getJSONObject(col);
					if (obj != null) {
						obj.set("className", className);
					}
				}
			}
		}

		// 将图片数组添加到表格数据中
		for (int i = 0; i < images.size(); i++) {
			JSONObject image = images.getJSONObject(i);
			int row = image.getInt("row");
			int col = image.getInt("col");
			JSONArray imgArr = image.getJSONArray("img");
			JSONObject cellObj = tableData.getJSONArray(row).getJSONObject(col);
			cellObj.set("img", imgArr);
		}
		// 获取最大列数
		int largeTableCol = Util.getLargeTableCol();
		// 创建表格对象
		Table table;
		boolean hasColWidth = ObjectUtil.isNotNull(colWidths);
		if (!hasColWidth) {
			if (tableColNum > largeTableCol) {
				table = new Table(tableColNum, true);
			} else {
				table = new Table(tableColNum);
			}
		} else {
			// 使用优化后的列宽度数组
			UnitValue[] widths = optimizeColWidths(tableData, colWidths, headerRow);
			if (tableColNum > largeTableCol) {
				table = new Table(widths, true);
			} else {
				table = new Table(widths);
			}
		}

		table.useAllAvailableWidth();
		for (int i = 0; i < tableData.size(); i++) {
			JSONArray row = tableData.getJSONArray(i);

			for (int j = 0; j < row.size(); j++) {

				JSONObject cellObj = row.getJSONObject(j);
				String cellType = cellObj.getStr("type");
				if (cellType.equals("text")) {
					// 创建单元格对象
					com.itextpdf.layout.element.Cell cell = new com.itextpdf.layout.element.Cell();
					// 添加单元格内容
					Util.addCellContent(table, i, j, hasColWidth, headerRow, cellObj, cell, font);
				} else if (cellType.equals("merged")) {
					// 创建合并单元格对象
					com.itextpdf.layout.element.Cell cell = new com.itextpdf.layout.element.Cell(
							cellObj.getInt("rowspan"), cellObj.getInt("colspan"));
					// 添加单元格内容
					Util.addCellContent(table, i, j, hasColWidth, headerRow, cellObj, cell, font);
				}
			}
		}

		doc.add(new Paragraph());
		doc.add(table);
		if (tableColNum > largeTableCol) {
			table.flush();
			table.complete();
		}
		if (!images.isEmpty() && isOutImg) {
			String rootPath = Util.getFileUploadPath();
			// 创建图片表格对象
			Table imgTable = new Table(new float[]{200, 200});
			imgTable.useAllAvailableWidth();
			for (int i = 0; i < images.size(); i++) {
				JSONObject image = images.getJSONObject(i);
				JSONArray imgArr = image.getJSONArray("img");
				for (int j = 0; j < imgArr.size(); j++) {
					JSONObject imgObj = imgArr.getJSONObject(j);
					String imgType = imgObj.getStr("type");
					if (imgType.equals("photo")) {
						String imgNum = imgObj.getStr("imgNum");
						String src = imgObj.getStr("src");
						src = src.substring(5);

						// 图片压缩
						//String tempPath = rootPath + File.separator + "pdfTempImage" + File.separator + src;
						//FileUtil.mkdir(tempPath);
						//Img.from(FileUtil.file(rootPath + src))
						//    .setQuality(0.3)//压缩比率
						//    .write(FileUtil.file(tempPath));
						try {
							if (FileUtil.exist(rootPath + src)) {
								Image img = new Image(ImageDataFactory.create(rootPath + src));
								img.setMaxWidth(380);
								img.setMaxHeight(500);
								com.itextpdf.layout.element.Cell cell = new com.itextpdf.layout.element.Cell();
								cell.setBorder(Border.NO_BORDER);
								cell.setTextAlignment(TextAlignment.CENTER);
								cell.add(img.setTextAlignment(TextAlignment.CENTER));
								cell.add(new Paragraph(imgNum).setTextAlignment(TextAlignment.CENTER).setMultipliedLeading(1.2f)
										.setFont(font));
								imgTable.addCell(cell);
							}
						} catch (Exception e) {
							log.error("插入图片出错 = {}", e.getMessage());
						}

					}
				}
			}
			doc.add(new Paragraph());
			doc.add(imgTable);
		}

	}

	/**
	 * 读取excel数据为JSONArray 相当于二维数组
	 *
	 * @param path excel文件路径
	 */
	public static JSONArray readExcelToArray(String path) {
		ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(path);
		List<List<Object>> allList = reader.read();
		return JSONUtil.parseArray(allList);
	}


	/**
	 * 读取excel数据为JSONArray 相当于[{},{}]
	 *
	 * @param path excel文件路径
	 */
	public static JSONArray readExcelToObject(String path) {
		ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(path);
		List<Map<String, Object>> allList = reader.readAll();
		return JSONUtil.parseArray(allList);
	}

	/**
	 * 读取excel数据为JSONArray 相当于[{},{}]
	 *
	 * @param path        excel文件路径
	 * @param headerAlias 对象属性名称别名Map
	 */
	public static JSONArray readExcelToObject(String path, Map<String, String> headerAlias) {
		JSONArray res;
		try (ExcelReader reader = ExcelUtil.getReader(path)) {
			reader.setHeaderAlias(headerAlias);
			List<Map<String, Object>> allList = reader.readAll();
			res = JSONUtil.parseArray(allList);
		}
		return res;
	}


	/**
	 * Base64转换为图片
	 *
	 * @param base64 Base64编码的图片字符串
	 * @return 返回相对路径，格式为 //yyyy-MM//uuid
	 */
	public static String base64ToImg(String base64) {
		if (StrUtil.isEmpty(base64)) {
			log.error("Base64字符串为空");
			return "";
		}

		// 获取文件上传路径
		String basePath = Util.getFileUploadPath();

		// 获取月份目录名
		String month = new SimpleDateFormat("yyyy-MM").format(new Date());
		String fileName = UUID.randomUUID().toString();

		// 创建基础目录
		File baseDir = new File(basePath);
		if (!baseDir.exists()) {
			boolean created = baseDir.mkdirs();
			if (!created) {
				log.error("创建基础目录失败: {}", basePath);
				return "";
			}
		}

		// 创建月份目录
		String monthPath = basePath + File.separator + month;
		File monthDir = new File(monthPath);
		if (!monthDir.exists()) {
			boolean created = monthDir.mkdirs();
			if (!created) {
				log.error("创建月份目录失败: {}", monthPath);
				return "";
			}
		}

		// 构建目标文件路径
		String targetPath = monthPath + File.separator + fileName;

		// 处理Base64字符串
		int commaIndex = base64.indexOf(",", 1);
		String base64Data = commaIndex > 0 ? base64.substring(commaIndex + 1) : base64;

		FileOutputStream fops = null;
		try {
			// 转换Base64并写入文件
			byte[] imageData = DatatypeConverter.parseBase64Binary(base64Data);
			fops = new FileOutputStream(targetPath);
			fops.write(imageData);
			fops.flush();

			// 返回相对路径
			return "//" + month + "//" + fileName;

		} catch (IOException e) {
			log.error("保存Base64图片失败: {}", e.getMessage());
			return "";
		} finally {
			if (fops != null) {
				try {
					fops.close();
				} catch (IOException e) {
					log.error("关闭文件输出流失败: {}", e.getMessage());
				}
			}
		}
	}


	public static File tableData2ImgZip(JSONObject data, String tempPath) {
		String saveDataStr = data.getStr("SAVE_DATA");
		String securityName = data.getStr("SECURITY_NAME");
		String tableName = data.getStr("TABLE_NUM") + "：" + data.getStr("NAME");
		if (StrUtil.isEmpty(data.getStr("TABLE_NUM"))) {
			tableName = data.getStr("NAME");
		}
		tableName = tableName.replaceAll("/", "、").replaceAll("\\\\", "、");
		String folderPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
		FileUtil.mkdir(folderPath);
		String zipFilePath = tempPath + File.separator + tableName + "照片（" + securityName + "）.zip";
		JSONObject saveData = JSONUtil.parseObj(saveDataStr);
		JSONArray metas = saveData.getJSONArray("meta");
		String uploadPath = Util.getFileUploadPath();
		if (ObjectUtil.isNotNull(metas)) {
			for (int i = 0; i < metas.size(); i++) {
				JSONObject meta = metas.getJSONObject(i);
				JSONArray eles = meta.getJSONArray("eles");
				if (ObjectUtil.isNotNull(eles)) {
					for (int j = 0; j < eles.size(); j++) {
						JSONObject ele = eles.getJSONObject(j);
						String type = ele.getStr("type");
						if ("photo".equals(type)) {
							String photoPath = uploadPath + ele.getStr("photoPath");
							String photoShowNum = ele.getStr("photoShowNum").replace("/", "_").replace("\\", "_");
							String photoFormat = ele.getStr("photoFormat");
							if (FileUtil.exist(photoPath)) {
								if (FileUtil.isFile(photoPath)) {
									FileUtil.copy(photoPath, folderPath + photoShowNum + "（" + securityName + "）." + photoFormat, true);
								}
							}
						}
					}
				}
			}
		}
		return ZipUtil.zip(folderPath, zipFilePath);
	}


	/**
	 * 加密文件
	 */
	public static void encryptFile(File inputFile, File encryptedFile) throws Exception {
		SecretKeySpec secretKey = new SecretKeySpec("cirpointpkg12345".getBytes(), "AES");
		Cipher cipher = Cipher.getInstance("AES");
		cipher.init(Cipher.ENCRYPT_MODE, secretKey);

		FileInputStream inputStream = new FileInputStream(inputFile);
		FileOutputStream outputStream = new FileOutputStream(encryptedFile);
		byte[] buffer = new byte[8192]; // 创建一个缓冲区，大小可按需调整，例如这里为8KB

		int readBytes;
		while ((readBytes = inputStream.read(buffer)) != -1) {
			byte[] encryptedChunk = cipher.update(buffer, 0, readBytes);
			if (encryptedChunk != null) {
				outputStream.write(encryptedChunk);
			}
		}

		// 处理最终块
		byte[] finalEncryptedBytes = cipher.doFinal();
		outputStream.write(finalEncryptedBytes);

		inputStream.close();
		outputStream.close();
	}

	/**
	 * 解密文件
	 */
	public static void decryptFile(File encryptedFile, File decryptedFile) throws Exception {
		SecretKeySpec secretKey = new SecretKeySpec("cirpointpkg12345".getBytes(), "AES");
		Cipher cipher = Cipher.getInstance("AES");
		cipher.init(Cipher.DECRYPT_MODE, secretKey);

		FileInputStream inputStream = new FileInputStream(encryptedFile);
		FileOutputStream outputStream = new FileOutputStream(decryptedFile);
		byte[] buffer = new byte[8192]; // 创建一个缓冲区，大小可按需调整，例如这里为8KB

		int readBytes;
		while ((readBytes = inputStream.read(buffer)) != -1) {
			byte[] decryptedChunk = cipher.update(buffer, 0, readBytes);
			if (decryptedChunk != null) {
				outputStream.write(decryptedChunk);
			}
		}

		// 处理最终块
		byte[] finalDecryptedBytes = cipher.doFinal();
		outputStream.write(finalDecryptedBytes);

		inputStream.close();
		outputStream.close();
	}


	public static JSONObject getNewUUIDPath() {
		JSONObject res = new JSONObject();
		String uploadPath = Util.getFileUploadPath();
		String month = new SimpleDateFormat("yyyy-MM").format(new Date());
		uploadPath = uploadPath + "//" + month;
		FileUtil.mkdir(uploadPath);
		String uuid = UUID.randomUUID().toString();
		//绝对路径
		res.set("fileAbsolutePath", uploadPath + "//" + uuid);
		//相对路径
		res.set("filePath", "//" + month + "//" + uuid);
		return res;
	}

	/**
	 * 判断cell是否为合并单元格，是的话返回合并行数和列数（只要在合并区域中的cell就会返回合同行列数，但只有左上角第一个有数据）
	 *
	 * @param listCombineCell 上面获取的合并区域列表
	 */
	public static Map<String, Object> isCombineCell(List<CellRangeAddress> listCombineCell, Cell cell) {
		int firstC;
		int lastC;
		int firstR;
		int lastR;
		Boolean flag = false;
		int mergedRow;
		int mergedCol;
		Map<String, Object> result = new HashMap<>();
		result.put("flag", flag);
		for (CellRangeAddress ca : listCombineCell) {
			// 获得合并单元格的起始行, 结束行, 起始列, 结束列
			firstC = ca.getFirstColumn();
			lastC = ca.getLastColumn();
			firstR = ca.getFirstRow();
			lastR = ca.getLastRow();
			// 判断cell是否在合并区域之内，在的话返回true和合并行列数
			if (cell.getRowIndex() >= firstR && cell.getRowIndex() <= lastR) {
				if (cell.getColumnIndex() >= firstC && cell.getColumnIndex() <= lastC) {
					mergedRow = lastR - firstR + 1;
					mergedCol = lastC - firstC + 1;
					result.put("flag", true);
					result.put("mergedRow", mergedRow);
					result.put("mergedCol", mergedCol);
					result.put("CellRangeAddress", ca);
					break;
				}
			}
		}
		return result;
	}

	/**
	 * 判断是否为EXCEL坐标
	 *
	 * @param str 传入的字符串
	 * @return 是整数返回true, 否则返回false
	 */
	public static boolean isCellAddress(String str) {
		boolean f;
		if (isNumeric(str) || containsChinese(str)) { // 数字或者包含中文
			f = false;
		} else {
			try {
				new CellAddress(str);
				f = true;
			} catch (Exception e) {
				f = false;
			}
		}
		return f;
	}


	/**
	 * 检查字符串是否包含中文字符或中文标点符号
	 * 使用Character.UnicodeBlock进行检测，性能更优
	 *
	 * @param str 待检查的字符串
	 * @return true-包含中文字符或标点，false-不包含中文字符或标点
	 */
	public static boolean containsChinese(String str) {
		if (str == null || str.isEmpty()) {
			return false;
		}

		for (char c : str.toCharArray()) {
			Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
			if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
					|| ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
					|| ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
					|| ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
					|| ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
					|| ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
					|| ub == Character.UnicodeBlock.GENERAL_PUNCTUATION) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 获取excel但坐标单元格的值
	 *
	 * @param filePath    excel文件路径
	 * @param sheetAt     表示第几个sheet页 从0开始
	 * @param addressArea 单元格坐标 A1 （单坐标）
	 * @return String 字符串类型的值
	 */
	public static String getAddressValue(String filePath, int sheetAt, String addressArea) {
		int x = getX(addressArea);
		int y = getY(addressArea);
		return getExcelValue(filePath, x, y, sheetAt);
	}

	/**
	 * 根据横纵坐标获取Excel单元格的值
	 *
	 * @param filePath Excel文件路径
	 * @param x        单元格横坐标（从1开始）
	 * @param y        单元格纵坐标（从1开始）
	 * @param sheetAt  工作表索引（从0开始）
	 * @return String  单元格的值，发生错误时返回带"error:"前缀的错误信息
	 */
	public static String getExcelValue(String filePath, int x, int y, int sheetAt) {
		// 参数校验
		if (x <= 0 || y <= 0) {
			return "error:请输入正确的坐标";
		}

		File file = new File(filePath);
		if (!file.exists()) {
			return "error:" + filePath + "文件不存在";
		}

		InputStream is = null;
		Workbook workbook = null;
		try {
			is = Files.newInputStream(file.toPath());

			// 检查Excel版本并获取工作簿
			if (isExcel2003(is)) {
				workbook = new HSSFWorkbook(is);
			} else if (isExcel2007(is)) {
				workbook = new XSSFWorkbook(is);
			} else {
				return "error:该文件不是Excel格式";
			}

			// 检查sheet索引是否有效
			int sheetNum = workbook.getNumberOfSheets();
			if (sheetAt >= sheetNum) {
				return "error:sheetAt大于实际sheet数";
			}

			// 获取指定的sheet
			Sheet sheet = workbook.getSheetAt(sheetAt);

			// 检查行坐标是否有效
			int lastRowNum = sheet.getLastRowNum();
			if (y > (lastRowNum + 1)) {
				return "error:输入坐标行数大于实际行数";
			}

			// 获取单元格值
			Row row = sheet.getRow(y - 1);
			if (row == null) {
				return "";
			}

			Cell cell = row.getCell(x - 1);
			return getCellValue(cell);

		} catch (IOException e) {
			log.error("读取Excel文件失败: {}", filePath, e);
			return "error:读取Excel文件失败";
		} catch (Exception e) {
			log.error("处理Excel数据异常: {}", filePath, e);
			return "error:处理Excel数据异常";
		} finally {
			// 关闭资源
			if (workbook != null) {
				try {
					workbook.close();
				} catch (IOException e) {
					log.error("关闭workbook失败", e);
				}
			}
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					log.error("关闭输入流失败", e);
				}
			}
		}
	}


	/**
	 * 判断excel文件是否为03版
	 *
	 * @param is 文件流
	 */
	public static boolean isExcel2003(InputStream is) throws IOException {
		try {
			new HSSFWorkbook(is);
		} catch (Exception e) {
			return false;
		} finally {
			is.close();
		}
		return true;
	}

	/**
	 * 判断excel文件是否为07版
	 *
	 * @param is 文件流
	 */
	public static boolean isExcel2007(InputStream is) throws IOException {
		try {
			new XSSFWorkbook(is);
		} catch (Exception e) {
			return false;
		} finally {
			is.close();
		}
		return true;
	}

	/**
	 * 获取横坐标x
	 *
	 * @param str 例如:A12
	 */
	public static int getX(String str) {
		String[] strs = str.split("\\d");
		StringBuilder res = new StringBuilder();
		for (String s : strs) {
			res.append(s);
		}
		return transferase_10(res.toString());
	}

	/**
	 * 获取纵坐标y
	 *
	 * @param str 例如:A12
	 */
	public static int getY(String str) {
		String[] strs = str.split("\\D");
		StringBuilder res = new StringBuilder();
		for (String s : strs) {
			res.append(s);
		}
		int r = 0;
		if (!res.toString().isEmpty()) {
			r = Integer.parseInt(res.toString());
		}
		return r;
	}

	/**
	 * 判断字符串是否只有大写字母
	 *
	 * @param str 传入的字符串
	 * @return 是整数返回true, 否则返回false
	 */
	public static boolean isCapital(String str) {
		if (StrUtil.isEmpty(str))
			return false;
		Pattern pattern = Pattern.compile("^[A-Z]+$");
		return pattern.matcher(str).matches();
	}

	/**
	 * 根据文件路径获取第一个sheet对象
	 */
	public static Sheet getSheet(String filePath) throws Exception {
		Workbook wb = getWorkbook(filePath);
		return wb.getSheetAt(0);
	}

	/**
	 * 描述：根据文件后缀，自适应上传文件的版本
	 */
	public static Workbook getWorkbook(String filePath) throws Exception {
		File file = new File(filePath);
		if (!file.exists()) {
			throw new Exception(filePath + "文件不存在");
		}
		FileInputStream is = new FileInputStream(file);
		Workbook wb;
		if (isExcel2003(Files.newInputStream(file.toPath()))) {
			wb = new HSSFWorkbook(is); // 2003-
		} else if (isExcel2007(Files.newInputStream(file.toPath()))) {
			wb = new XSSFWorkbook(is); // 2007+
		} else {
			throw new Exception("解析的文件格式有误！");
		}
		return wb;
	}

	/**
	 * 将Excel坐标转换为行列索引（从0开始）
	 * 例如：
	 * B8 -> {row: 7, col: 1}
	 * A4 -> {row: 3, col: 0}
	 *
	 * @param excelPos Excel坐标（例如：B8、A4）
	 * @return int数组，第一个元素为行索引，第二个元素为列索引，都从0开始计数
	 * @throws IllegalArgumentException 当输入的Excel坐标格式不正确时抛出异常
	 */
	public static int[] convertExcelPosToRowCol(String excelPos) {
		if (excelPos == null || excelPos.trim().isEmpty()) {
			throw new IllegalArgumentException("Excel坐标不能为空");
		}

		// 使用正则表达式分离列字母和行号
		if (!excelPos.matches("[A-Za-z]+\\d+")) {
			throw new IllegalArgumentException("Excel坐标格式不正确：" + excelPos);
		}

		// 分离列字母和行号
		String colStr = excelPos.replaceAll("\\d+", "").toUpperCase();
		String rowStr = excelPos.replaceAll("[A-Za-z]+", "");

		// 计算行号（减1是因为要从0开始）
		int row = Integer.parseInt(rowStr) - 1;

		// 计算列号（A=0, B=1, C=2, ...）
		int col = 0;
		for (int i = 0; i < colStr.length(); i++) {
			col = col * 26 + (colStr.charAt(i) - 'A');
		}

		return new int[]{row, col};
	}

	/**
	 * 将HandsonTable的表格数据导出pdf，支持使用PdfOptions对象
	 *
	 * @param data     JSON数据
	 * @param tempPath 临时文件目录
	 * @param fontPath pdf字体存放路径
	 * @param options  PDF导出选项对象
	 * @return 导出结果
	 */
	public static JSONObject tableData2Pdf(JSONObject data, String tempPath, String fontPath, PdfOptions options) {
		JSONObject res = new JSONObject();
		String tableStr = data.getStr("HTML_DATA", "");
		String saveDataStr = data.getStr("SAVE_DATA", "");
		String headerRow = data.getStr("TABLE_HEADER", "0");
		String tableNum = data.getStr("TABLE_NUM", "");
		String security = data.getStr("SECURITY_NAME", "");
		String tableName = data.getStr("TABLE_NUM") + "：" + data.getStr("NAME");
		if (StrUtil.isEmpty(tableNum)) {
			tableName = data.getStr("NAME");
		}
		tableName = tableName.replaceAll("/", "、").replaceAll("\\\\", "、");
		tableName += "（" + security + "）";
		FileUtil.mkdir(tempPath);
		String path = tempPath + "\\" + tableName + ".pdf";
		File f = new File(path);
		try {
			if (!f.exists()) {
				f.createNewFile();
			}
			PdfDocument pdfDoc = new PdfDocument(new PdfWriter(path));

			// 使用PdfOptions创建Document
			Document doc = options.createDocument(pdfDoc);

			PdfFont font = PdfFontFactory.createFont(fontPath + "\\simhei.ttf", PdfEncodings.IDENTITY_H);

			// 添加标题
			doc.add(new Paragraph(tableName).setFirstLineIndent(8).setMultipliedLeading(1.2f).setFont(font).setBold())
					.setTextAlignment(TextAlignment.LEFT);
			if (StrUtil.isNotEmpty(saveDataStr)) {
				tableDataToPdfTable(doc, tableStr, saveDataStr, headerRow, font);
			}

			doc.close();
			res.set("success", true);
			res.set("data", f.getAbsolutePath());
		} catch (Exception e) {
			log.error(e.getMessage());
			res.set("success", false);
			res.set("msg", e.getLocalizedMessage());
			res.set("nodeName", tableName);
		}
		return res;
	}

	/**
	 * 应用非线性缩放到列宽度系数
	 * 
	 * @param value 原始值
	 * @param power 指数系数（大于1增强差异，小于1减弱差异）
	 * @return 非线性缩放后的值
	 */
	private static double applyNonLinearScaling(double value, double power) {
		return Math.pow(value, power);
	}
	
	/**
	 * 对数缩放函数，用于处理极长内容的列宽度
	 * 避免单列占用过多空间
	 * 
	 * @param value 原始值
	 * @return 对数缩放后的值
	 */
	private static double logScaleForExtremeLength(double value) {
		if (value <= 10) {
			return value; // 小于阈值不做缩放
		}
		// 对数缩放公式: 10 + k*ln(value - 9)
		// 其中k是调节系数，可以根据需要调整
		double k = 4.0;
		return 10 + k * Math.log(value - 9);
	}

}
