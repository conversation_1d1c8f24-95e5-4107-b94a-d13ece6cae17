// Garden Gnome Software - Skin
// Pano2VR 7.1.9/20995
// Filename: danji.ggsk
// Generated 2025-06-16T16:09:21

function pano2vrSkin(player,base) {
	player.addVariable('opt_hotspot_preview', 2, true, { ignoreInState: 1  });
	player.addVariable('vis_userdata', 2, false, { ignoreInState: 0  });
	player.addVariable('vis_image_popup', 2, false, { ignoreInState: 0  });
	player.addVariable('vis_info_popup', 2, false, { ignoreInState: 0  });
	player.addVariable('vis_video_popup_file', 2, false, { ignoreInState: 0  });
	player.addVariable('vis_video_popup_url', 2, false, { ignoreInState: 0  });
	player.addVariable('vis_video_popup_vimeo', 2, false, { ignoreInState: 0  });
	player.addVariable('vis_video_popup_youtube', 2, false, { ignoreInState: 0  });
	player.addVariable('vis_website', 2, false, { ignoreInState: 0  });
	player.addVariable('vis_timer', 2, false, { ignoreInState: 0  });
	var me=this;
	var skin=this;
	var flag=false;
	var hotspotTemplates={};
	var skinKeyPressedKey = 0;
	var skinKeyPressedText = '';
	this.player=player;
	player.setApiVersion(7);
	this.player.skinObj=this;
	this.divSkin=player.divSkin;
	this.ggUserdata=player.userdata;
	this.lastSize={ w: -1,h: -1 };
	var basePath="";
	var cssPrefix="";
	// auto detect base path
	if (base=='?') {
		var scripts = document.getElementsByTagName('script');
		for(var i=0;i<scripts.length;i++) {
			var src=scripts[i].src;
			if (src.indexOf('skin.js')>=0) {
				var p=src.lastIndexOf('/');
				if (p>=0) {
					basePath=src.substr(0,p+1);
				}
			}
		}
	} else
	if (base) {
		basePath=base;
	}
	this.elementMouseDown={};
	this.elementMouseOver={};
	var i;
	var hs,el,els,elo,ela,elHorScrollFg,elHorScrollBg,elVertScrollFg,elVertScrollBg,elCornerBg;
	var prefixes='Webkit,Moz,O,ms,Ms'.split(',');
	for(var i=0;i<prefixes.length;i++) {
		if (typeof document.body.style[prefixes[i] + 'Transform'] !== 'undefined') {
			cssPrefix='-' + prefixes[i].toLowerCase() + '-';
		}
	}
	
	player.setMargins(0,0,0,0);
	
	this.updateSize=function(startElement) {
		var stack=[];
		stack.push(startElement);
		while(stack.length>0) {
			var e=stack.pop();
			if (e.ggUpdatePosition) {
				e.ggUpdatePosition();
			}
			if (e.hasChildNodes()) {
				for(var i=0;i<e.childNodes.length;i++) {
					stack.push(e.childNodes[i]);
				}
			}
		}
	}
	
	player.addListener('changenode', function() { me.ggUserdata=player.userdata; });
	
	var parameterToTransform=function(p) {
		return p.def + 'translate(' + p.rx + 'px,' + p.ry + 'px) rotate(' + p.a + 'deg) scale(' + p.sx + ',' + p.sy + ')';
	}
	
	this.findElements=function(id,regex) {
		var r=[];
		var stack=[];
		var pat=new RegExp(id,'');
		stack.push(me.divSkin);
		while(stack.length>0) {
			var e=stack.pop();
			if (regex) {
				if (pat.test(e.ggId)) r.push(e);
			} else {
				if (e.ggId==id) r.push(e);
			}
			if (e.hasChildNodes()) {
				for(var i=0;i<e.childNodes.length;i++) {
					stack.push(e.childNodes[i]);
				}
			}
		}
		return r;
	}
	
	this._=function(text, params) {
		return player._(text, params);
	}
	
	this.languageChanged=function() {
		var stack=[];
		stack.push(me.divSkin);
		while(stack.length>0) {
			var e=stack.pop();
			if (e.ggUpdateText) {
				e.ggUpdateText();
			}
			if (e.ggUpdateAria) {
				e.ggUpdateAria();
			}
			if (e.hasChildNodes()) {
				for(var i=0;i<e.childNodes.length;i++) {
					stack.push(e.childNodes[i]);
				}
			}
		}
	}
	player.addListener('sizechanged', function () { me.updateSize(me.divSkin);});
	player.addListener('languagechanged', this.languageChanged);
	
	this.addSkin=function() {
		var hs='';
		this.ggCurrentTime=new Date().getTime();
		player.addListener('configloaded', function(event) {
			if (hotspotTemplates.hasOwnProperty('stand-alone')) {
				for(var i = 0; i < hotspotTemplates['stand-alone'].length; i++) {
					hotspotTemplates['stand-alone'][i].ggEvent_configloaded();
				}
			}
			if (hotspotTemplates.hasOwnProperty('ht_node')) {
				for(var i = 0; i < hotspotTemplates['ht_node'].length; i++) {
					hotspotTemplates['ht_node'][i].ggEvent_configloaded();
				}
			}
		});
	};
	function SkinHotspotClass_ht_node(parentScope,hotspot) {
		var me=this;
		var flag=false;
		var hs='';
		me.parentScope=parentScope;
		me.hotspot=hotspot;
		var nodeId=String(hotspot.url);
		nodeId=(nodeId.charAt(0)=='{')?nodeId.substr(1, nodeId.length - 2):''; // }
		me.ggUserdata=skin.player.getNodeUserdata(nodeId);
		me.elementMouseDown={};
		me.elementMouseOver={};
		me.findElements=function(id,regex) {
			return skin.findElements(id,regex);
		}
		el=me._ht_node=document.createElement('div');
		el.ggId="ht_node";
		el.ggParameter={ rx:0,ry:0,a:0,sx:1,sy:1,def:'' };
		el.ggVisible=true;
		el.className="ggskin ggskin_hotspot ";
		el.ggType='hotspot';
		hs ='';
		hs+='height : 0px;';
		hs+='left : 393px;';
		hs+='position : absolute;';
		hs+='top : 48px;';
		hs+='visibility : inherit;';
		hs+='width : 0px;';
		hs+='pointer-events:auto;';
		hs+='cursor: pointer;';
		el.setAttribute('style',hs);
		el.style.transformOrigin='50% 50%';
		me._ht_node.ggIsActive=function() {
			return player.getCurrentNode()==this.ggElementNodeId();
		}
		el.ggElementNodeId=function() {
			if (me.hotspot.url!='' && me.hotspot.url.charAt(0)=='{') { // }
				return me.hotspot.url.substr(1, me.hotspot.url.length - 2);
			} else {
				if ((this.parentNode) && (this.parentNode.ggElementNodeId)) {
					return this.parentNode.ggElementNodeId();
				} else {
					return player.getCurrentNode();
				}
			}
		}
		me._ht_node.onclick=function (e) {
			player.openUrl(player._(me.hotspot.url),player._(me.hotspot.target));
			player.triggerEvent('hsproxyclick', {'id': me.hotspot.id, 'url': me.hotspot.url});
		}
		me._ht_node.ondblclick=function (e) {
			player.triggerEvent('hsproxydblclick', {'id': me.hotspot.id, 'url': me.hotspot.url});
		}
		me._ht_node.onmouseenter=function (e) {
			player.setActiveHotspot(me.hotspot);
			me.elementMouseOver['ht_node']=true;
			player.triggerEvent('hsproxyover', {'id': me.hotspot.id, 'url': me.hotspot.url});
		}
		me._ht_node.onmouseleave=function (e) {
			player.setActiveHotspot(null);
			me.elementMouseOver['ht_node']=false;
			player.triggerEvent('hsproxyout', {'id': me.hotspot.id, 'url': me.hotspot.url});
		}
		me._ht_node.ggUpdatePosition=function (useTransition) {
		}
		el=me._saimage=document.createElement('div');
		els=me._saimage__img=document.createElement('img');
		els.className='ggskin ggskin_svg';
		hs='data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0nMS4wJyBzdGFuZGFsb25lPSdubyc/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAnLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4nICdodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQnPgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgcC1pZD0iOTEzMyIgdmVyc2lvbj0iMS4xIiBoZWlnaHQ9IjMyIiBjbGFzcz0iaWNvbiIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIHdpZHRoPSIzMiIgdD0iMTc1MDA1NzYzOTAzOCI+CiA8cGF0aCBkPSJNNz'+
			'Y0LjQxNiA1NTguMDhjMC0xMDguMDMyLTg3LjU1Mi0xOTYuMDk2LTE5Ni4wOTYtMTk2LjA5NlMzNzIuMjI0IDQ1MC4wNDggMzcyLjIyNCA1NTguMDhjMCAxMDguMDMyIDg3LjU1MiAxOTYuMDk2IDE5Ni4wOTYgMTk2LjA5NiAxMDguMDMyIDAgMTk2LjA5Ni04OC4wNjQgMTk2LjA5Ni0xOTYuMDk2eiIgcC1pZD0iOTEzNCIgZmlsbD0iIzIxRThFRCIvPgogPHBhdGggZD0iTTUxMiA3NTkuODA4Yy0xMzYuNzA0IDAtMjQ3LjgwOC0xMTEuMTA0LTI0Ny44MDgtMjQ4LjMyczExMS4xMDQtMjQ3LjgwOCAyNDguMzItMjQ3LjgwOGMxNy40MDggMCAzNC4zMDQgMi4wNDggNTEuMiA1LjYzMiAxOS40NTYgMy41'+
			'ODQgMzIuMjU2IDIyLjAxNiAyOC42NzIgNDEuNDcyLTMuNTg0IDE5LjQ1Ni0yMi4wMTYgMzIuMjU2LTQxLjQ3MiAyOC42NzItMC41MTIgMC0xLjUzNi0wLjUxMi0yLjA0OC0wLjUxMi05NS4yMzItMjAuNDgtMTg4LjkyOCA0MC40NDgtMjA4Ljg5NiAxMzUuNjgtMjAuNDggOTUuMjMyIDQwLjQ0OCAxODguOTI4IDEzNS42OCAyMDguODk2czE4OC45MjgtNDAuNDQ4IDIwOC44OTYtMTM1LjY4YzUuMTItMjQuMDY0IDUuMTItNDkuMTUyIDAtNzMuNzI4LTQuNjA4LTE5LjQ1NiA3LjE2OC0zOC40IDI2LjYyNC00My4wMDggMTkuNDU2LTQuNjA4IDM4LjQgNy4xNjggNDMuMDA4IDI2LjYyNCAwIDAuNTEyID'+
			'AuNTEyIDEuMDI0IDAuNTEyIDIuMDQ4IDI4LjY3MiAxMzQuMTQ0LTU2LjgzMiAyNjUuNzI4LTE5MC45NzYgMjk0LjQtMTcuNDA4IDQuMDk2LTM0LjMwNCA1LjYzMi01MS43MTIgNS42MzJ6IG0wIDE5Ny42MzJjLTI0NS43NiAwLTQ0NS40NC0xOTkuNjgtNDQ1LjQ0LTQ0NS40NHMxOTkuNjgtNDQ1LjQ0IDQ0NS40NC00NDUuNDRjNjYuMDQ4IDAgMTMxLjA3MiAxNC44NDggMTkwLjQ2NCA0My4wMDggMTcuNDA4IDkuMjE2IDI0LjU3NiAzMC43MiAxNS44NzIgNDguMTI4LTguNzA0IDE2Ljg5Ni0yOS4xODQgMjQuMDY0LTQ2LjU5MiAxNi4zODQtNTAuMTc2LTI0LjA2NC0xMDQuOTYtMzUuODQtMTYwLjI1'+
			'Ni0zNS44NC0yMDYuMzM2IDAtMzczLjc2IDE2Ny40MjQtMzczLjc2IDM3My43NnMxNjcuNDI0IDM3My43NiAzNzMuNzYgMzczLjc2IDM3My43Ni0xNjcuNDI0IDM3My43Ni0zNzMuNzZjMC01OC44OC0xMy44MjQtMTE3LjI0OC00MC40NDgtMTY5LjQ3Mi05LjcyOC0xNy40MDgtMy4wNzItMzguOTEyIDE0LjMzNi00OC42NCAxNy40MDgtOS43MjggMzguOTEyLTMuMDcyIDQ4LjY0IDE0LjMzNiAwLjUxMiAwLjUxMiAwLjUxMiAxLjAyNCAxLjAyNCAyLjA0OCAzMS43NDQgNjIuNDY0IDQ4LjY0IDEzMS41ODQgNDguMTI4IDIwMi4yNCAwIDI0NS4yNDgtMTk5LjE2OCA0NDQuNDE2LTQ0NC45MjggNDQ0Lj'+
			'kyOHogbS0yLjA0OC00MTEuMTM2Yy0xOS45NjggMC0zNS44NC0xNS44NzItMzUuODQtMzUuODQgMC05LjcyOCAzLjU4NC0xOC40MzIgMTAuMjQtMjUuMDg4bDM2OC4xMjgtMzY4LjEyOGMxNC4zMzYtMTMuMzEyIDM2Ljg2NC0xMi44IDUwLjY4OCAxLjUzNiAxMi44IDEzLjgyNCAxMi44IDM1LjMyOCAwIDQ5LjE1MmwtMzY4LjEyOCAzNjguMTI4Yy02LjE0NCA2LjY1Ni0xNS4zNiAxMC4yNC0yNS4wODggMTAuMjR6IiBwLWlkPSI5MTM1IiBmaWxsPSIjMDU4OEZFIi8+Cjwvc3ZnPgo=';
		me._saimage__img.setAttribute('src',hs);
		hs ='';
		hs += 'position: absolute;top: 0px;left: 0px;width: 100%;height: 100%;-webkit-user-drag:none;pointer-events:none;;';
		els.setAttribute('style', hs);
		els['ondragstart']=function() { return false; };
		el.appendChild(els);
		el.ggSubElement = els;
		el.ggId="sa-image";
		el.ggDx=0;
		el.ggDy=0;
		el.ggParameter={ rx:0,ry:0,a:0,sx:1,sy:1,def:'' };
		el.ggVisible=true;
		el.className="ggskin ggskin_svg ";
		el.ggType='svg';
		hs ='';
		hs+='height : 32px;';
		hs+='left : calc(50% - ((32px + 0px) / 2) + 0px);';
		hs+='position : absolute;';
		hs+='top : calc(50% - ((32px + 0px) / 2) + 0px);';
		hs+='visibility : inherit;';
		hs+='width : 32px;';
		hs+='pointer-events:auto;';
		el.setAttribute('style',hs);
		el.style.transformOrigin='50% 50%';
		me._saimage.ggIsActive=function() {
			if ((this.parentNode) && (this.parentNode.ggIsActive)) {
				return this.parentNode.ggIsActive();
			}
			return false;
		}
		el.ggElementNodeId=function() {
			if ((this.parentNode) && (this.parentNode.ggElementNodeId)) {
				return this.parentNode.ggElementNodeId();
			}
			return me.ggNodeId;
		}
		me._saimage.ggUpdatePosition=function (useTransition) {
		}
		me._ht_node.appendChild(me._saimage);
		el=me._satext=document.createElement('div');
		els=me._satext__text=document.createElement('div');
		el.className='ggskin ggskin_textdiv';
		el.ggTextDiv=els;
		el.ggId="sa-text";
		el.ggDx=0;
		el.ggParameter={ rx:0,ry:0,a:0,sx:1,sy:1,def:'translate(-50%, 0px) ' };
		el.ggVisible=true;
		el.className="ggskin ggskin_text ";
		el.ggType='text';
		hs ='';
		hs+='color : rgba(255,255,255,1);';
		hs+='cursor : default;';
		hs+='height : auto;';
		hs+='left : calc(50% - ((0px + 2px) / 2) + 0px);';
		hs+='position : absolute;';
		hs+='top : 22px;';
		hs+='transform : translate(-50%, 0px);;';
		hs+='visibility : inherit;';
		hs+='width : auto;';
		hs+='pointer-events:auto;';
		hs+='text-shadow: 1px 1px 4px #000000;';
		el.setAttribute('style',hs);
		el.style.transformOrigin='50% 50%';
		hs ='';
		hs += 'box-sizing: border-box;';
		hs+='width: auto;';
		hs+='height: auto;';
		hs+='border : 1px solid #000000;';
		hs+='text-align: center;';
		hs+='white-space: pre;';
		hs+='padding: 2px 4px 2px 4px;';
		hs+='overflow: hidden;';
		els.setAttribute('style',hs);
		me._satext.ggUpdateText=function() {
			var params = [];
			params.push(String(player._(me.hotspot.title)));
			var hs = player._("%1", params);
			if (hs!=this.ggText) {
				this.ggText=hs;
				this.ggTextDiv.innerHTML=hs;
				if (this.ggUpdatePosition) this.ggUpdatePosition();
			}
		}
		me._satext.ggUpdateText();
		player.addListener('changenode', function() {
			me._satext.ggUpdateText();
		});
		el.appendChild(els);
		me._satext.ggIsActive=function() {
			if ((this.parentNode) && (this.parentNode.ggIsActive)) {
				return this.parentNode.ggIsActive();
			}
			return false;
		}
		el.ggElementNodeId=function() {
			if ((this.parentNode) && (this.parentNode.ggElementNodeId)) {
				return this.parentNode.ggElementNodeId();
			}
			return me.ggNodeId;
		}
		me._satext.logicBlock_position = function() {
			var newLogicStatePosition;
			if (
				((player.getIsMobile() == true))
			)
			{
				newLogicStatePosition = 0;
			}
			else {
				newLogicStatePosition = -1;
			}
			if (me._satext.ggCurrentLogicStatePosition != newLogicStatePosition) {
				me._satext.ggCurrentLogicStatePosition = newLogicStatePosition;
				me._satext.style.transition='left 0s, top 0s';
				if (me._satext.ggCurrentLogicStatePosition == 0) {
					me._satext.style.left = 'calc(50% - (0px / 2) - (2px / 2))';
					me._satext.style.top='-50px';
				}
				else {
					me._satext.style.left='calc(50% - ((0px + 2px) / 2) + 0px)';
					me._satext.style.top='22px';
				}
			}
		}
		me._satext.logicBlock_position();
		me._satext.ggUpdatePosition=function (useTransition) {
		}
		me._ht_node.appendChild(me._satext);
		me.elementMouseOver['ht_node']=false;
		me._satext.logicBlock_position();
			me.ggEvent_configloaded=function() {
				me._satext.logicBlock_position();
			};
			me.__div = me._ht_node;
	};
	function SkinHotspotClass_standalone(parentScope,hotspot) {
		var me=this;
		var flag=false;
		var hs='';
		me.parentScope=parentScope;
		me.hotspot=hotspot;
		var nodeId=String(hotspot.url);
		nodeId=(nodeId.charAt(0)=='{')?nodeId.substr(1, nodeId.length - 2):''; // }
		me.ggUserdata=skin.player.getNodeUserdata(nodeId);
		me.elementMouseDown={};
		me.elementMouseOver={};
		me.findElements=function(id,regex) {
			return skin.findElements(id,regex);
		}
		el=me._standalone=document.createElement('div');
		el.ggId="stand-alone";
		el.ggParameter={ rx:0,ry:0,a:0,sx:1,sy:1,def:'' };
		el.ggVisible=true;
		el.className="ggskin ggskin_hotspot ";
		el.ggType='hotspot';
		hs ='';
		hs+='height : 0px;';
		hs+='left : 250px;';
		hs+='position : absolute;';
		hs+='top : 50px;';
		hs+='visibility : inherit;';
		hs+='width : 0px;';
		hs+='pointer-events:auto;';
		hs+='cursor: pointer;';
		el.setAttribute('style',hs);
		el.style.transformOrigin='50% 50%';
		me._standalone.ggIsActive=function() {
			return player.getCurrentNode()==this.ggElementNodeId();
		}
		el.ggElementNodeId=function() {
			if (me.hotspot.url!='' && me.hotspot.url.charAt(0)=='{') { // }
				return me.hotspot.url.substr(1, me.hotspot.url.length - 2);
			} else {
				if ((this.parentNode) && (this.parentNode.ggElementNodeId)) {
					return this.parentNode.ggElementNodeId();
				} else {
					return player.getCurrentNode();
				}
			}
		}
		me._standalone.onclick=function (e) {
			player.triggerEvent('hsproxyclick', {'id': me.hotspot.id, 'url': me.hotspot.url});
		}
		me._standalone.ondblclick=function (e) {
			player.triggerEvent('hsproxydblclick', {'id': me.hotspot.id, 'url': me.hotspot.url});
		}
		me._standalone.onmouseenter=function (e) {
			player.setActiveHotspot(me.hotspot);
			me.elementMouseOver['standalone']=true;
			player.triggerEvent('hsproxyover', {'id': me.hotspot.id, 'url': me.hotspot.url});
		}
		me._standalone.onmouseleave=function (e) {
			player.setActiveHotspot(null);
			me.elementMouseOver['standalone']=false;
			player.triggerEvent('hsproxyout', {'id': me.hotspot.id, 'url': me.hotspot.url});
		}
		me._standalone.ggUpdatePosition=function (useTransition) {
		}
		el=me._saimage0=document.createElement('div');
		els=me._saimage0__img=document.createElement('img');
		els.className='ggskin ggskin_svg';
		hs='data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0nMS4wJyBzdGFuZGFsb25lPSdubyc/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAnLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4nICdodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQnPgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgcC1pZD0iNDA1NSIgdmVyc2lvbj0iMS4xIiBoZWlnaHQ9IjMyIiBjbGFzcz0iaWNvbiIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIHdpZHRoPSIzMiIgdD0iMTY5Nzc2NjkzNTU4NSI+CiA8cGF0aCBkPSJNND'+
			'k3LjM4OTc0NCA0MDkuNjA3MzczdjYxNC4zOTI2MjdsMjI1LjU5OTEyMS0yMDQuNjIyMDAyTDEwMjQuMDExOTk2IDc1MS4wMzQxMzN6IiBwLWlkPSI0MDU2IiBmaWxsPSIjMTI5NmRiIi8+CiA8cGF0aCBkPSJNNDIxLjc5MDE5NCA1MDQuODM4MjNsMjkwLjQwMjkxNSAxODguNTg5MjgtMTUyLjU0NDkxMiAzNC43Mjc4MTItMTguODcwNjMxIDQuMjcxNDkxLTEzLjU0NTg5NSAxMi4yODc4NTMtMTA1LjQ0MTQ3NyA5NS4wODQ1NzN2LTMzNC45OTAyNjZNMzUxLjEwNTc4NSAzODAuMzUwNTgxdjYxNC4zOTI2MjdsMjI1LjU5OTEyMS0yMDQuNjIyMDAxTDg3Ny43MjgwMzcgNzIxLjc3NzM0MSAzNTEuMTA1'+
			'Nzg1IDM4MC4zNTA1ODF6IiBwLWlkPSI0MDU3IiBmaWxsPSIjMTI5NmRiIi8+CiA8cGF0aCBkPSJNMzIyLjQzNDEyOSA1MjYuNjM0NTRDMjY2Ljg0NjIyNCA0OTQuNTY5MDk2IDIzMy40OTM0ODIgNDM4LjE2MjAwMSAyMzQuMDc4NjE4IDM3Ny4xMzIzMzQgMjM0LjA3ODYxOCAyODIuMzk4ODQyIDMxMi4zOTkwNDkgMjA0LjgwOTgzIDQwOS42MTkzNjggMjA0LjgwOTgzczE3NS41NDA3NTEgNzcuMDkxNjQ2IDE3NS41NDA3NTEgMTcyLjMyMjUwNGgtNjYuNjQ2OTcyYzAtNjAuNTYxNTU5LTQ4Ljg4ODA5OS0xMDkuNzEyOTY5LTEwOC4zMzc5LTEwOS43MTI5NjlzLTEwOC44NjQ1MjIgNDkuMTUxNDEtMT'+
			'A4Ljg2NDUyMiAxMDkuNzEyOTY5Yy0wLjU4NTEzNiAzOC43NjUyNDkgMjAuNTM4MjY4IDc1LjUxMTc4IDU2LjA4NTI3IDk2LjE5NjMzMUwzMjIuNDM0MTI5IDUyNi42MzQ1NHoiIHAtaWQ9IjQwNTgiIGZpbGw9IiMxMjk2ZGIiLz4KIDxwYXRoIGQ9Ik0yMDEuMzExMDExIDcwMi4xNzUyOUMxMS40MzQ0MzIgNjAwLjc3MTI1LTU2LjM1MzU1NCAzNzIuMjE3MTkzIDUwLjk4OTYxNSAxOTEuNDEwMjIgMTU4LjM2MjA0MSAxMC42MDMyNDYgMzk4LjQ0MzI3NC01My4yMzUwNzMgNTg4Ljg0NjQ3NSA0OC4xNjg5NjcgNzEzLjEyOTMyNiAxMTQuNjk4OTEyIDc4OS45NTc2NjEgMjM5LjcxMzE4MyA3ODkuOTU3'+
			'NjYxIDM3NC45MDg4MThoLTY3Ljc4Nzk4NmMwLTE3MS42Nzg4NTQtMTQ2LjMxMzIxNi0zMTAuNjQ4NjE1LTMyNy4wOTA5MzItMzEwLjY0ODYxNS0xODAuODA2OTczIDAtMzI3LjEyMDE4OSAxMzguOTY5NzYxLTMyNy4xMjAxODkgMzEwLjY0ODYxNSAwIDExMi4xNDEyODMgNjMuODM4MzIgMjE1LjY4MTA2OSAxNjYuNjQ2Njg2IDI3MC45MTc4OTJMMjAxLjMxMTAxMSA3MDIuMTc1Mjl6IiBwLWlkPSI0MDU5IiBmaWxsPSIjMTI5NmRiIi8+Cjwvc3ZnPgo=';
		me._saimage0__img.setAttribute('src',hs);
		hs ='';
		hs += 'position: absolute;top: 0px;left: 0px;width: 100%;height: 100%;-webkit-user-drag:none;pointer-events:none;;';
		els.setAttribute('style', hs);
		els['ondragstart']=function() { return false; };
		el.appendChild(els);
		el.ggSubElement = els;
		el.ggId="sa-image";
		el.ggDx=0;
		el.ggDy=0;
		el.ggParameter={ rx:0,ry:0,a:0,sx:1,sy:1,def:'' };
		el.ggVisible=true;
		el.className="ggskin ggskin_svg ";
		el.ggType='svg';
		hs ='';
		hs+='height : 32px;';
		hs+='left : calc(50% - ((32px + 0px) / 2) + 0px);';
		hs+='position : absolute;';
		hs+='top : calc(50% - ((32px + 0px) / 2) + 0px);';
		hs+='visibility : inherit;';
		hs+='width : 32px;';
		hs+='pointer-events:auto;';
		el.setAttribute('style',hs);
		el.style.transformOrigin='50% 50%';
		me._saimage0.ggIsActive=function() {
			if ((this.parentNode) && (this.parentNode.ggIsActive)) {
				return this.parentNode.ggIsActive();
			}
			return false;
		}
		el.ggElementNodeId=function() {
			if ((this.parentNode) && (this.parentNode.ggElementNodeId)) {
				return this.parentNode.ggElementNodeId();
			}
			return me.ggNodeId;
		}
		me._saimage0.ggUpdatePosition=function (useTransition) {
		}
		me._standalone.appendChild(me._saimage0);
		el=me._satext0=document.createElement('div');
		els=me._satext0__text=document.createElement('div');
		el.className='ggskin ggskin_textdiv';
		el.ggTextDiv=els;
		el.ggId="sa-text";
		el.ggDx=0;
		el.ggParameter={ rx:0,ry:0,a:0,sx:1,sy:1,def:'translate(-50%, 0px) ' };
		el.ggVisible=true;
		el.className="ggskin ggskin_text ";
		el.ggType='text';
		hs ='';
		hs+='color : rgba(255,255,255,1);';
		hs+='cursor : default;';
		hs+='height : auto;';
		hs+='left : calc(50% - ((0px + 2px) / 2) + 0px);';
		hs+='position : absolute;';
		hs+='top : 22px;';
		hs+='transform : translate(-50%, 0px);;';
		hs+='visibility : inherit;';
		hs+='width : auto;';
		hs+='pointer-events:auto;';
		hs+='text-shadow: 1px 1px 4px #000000;';
		el.setAttribute('style',hs);
		el.style.transformOrigin='50% 50%';
		hs ='';
		hs += 'box-sizing: border-box;';
		hs+='width: auto;';
		hs+='height: auto;';
		hs+='border : 1px solid #000000;';
		hs+='text-align: center;';
		hs+='white-space: pre;';
		hs+='padding: 2px 4px 2px 4px;';
		hs+='overflow: hidden;';
		els.setAttribute('style',hs);
		me._satext0.ggUpdateText=function() {
			var params = [];
			params.push(String(player._(me.hotspot.title)));
			var hs = player._("%1", params);
			if (hs!=this.ggText) {
				this.ggText=hs;
				this.ggTextDiv.innerHTML=hs;
				if (this.ggUpdatePosition) this.ggUpdatePosition();
			}
		}
		me._satext0.ggUpdateText();
		player.addListener('changenode', function() {
			me._satext0.ggUpdateText();
		});
		el.appendChild(els);
		me._satext0.ggIsActive=function() {
			if ((this.parentNode) && (this.parentNode.ggIsActive)) {
				return this.parentNode.ggIsActive();
			}
			return false;
		}
		el.ggElementNodeId=function() {
			if ((this.parentNode) && (this.parentNode.ggElementNodeId)) {
				return this.parentNode.ggElementNodeId();
			}
			return me.ggNodeId;
		}
		me._satext0.logicBlock_position = function() {
			var newLogicStatePosition;
			if (
				((player.getIsMobile() == true))
			)
			{
				newLogicStatePosition = 0;
			}
			else {
				newLogicStatePosition = -1;
			}
			if (me._satext0.ggCurrentLogicStatePosition != newLogicStatePosition) {
				me._satext0.ggCurrentLogicStatePosition = newLogicStatePosition;
				me._satext0.style.transition='left 0s, top 0s';
				if (me._satext0.ggCurrentLogicStatePosition == 0) {
					me._satext0.style.left = 'calc(50% - (0px / 2) - (2px / 2))';
					me._satext0.style.top='-50px';
				}
				else {
					me._satext0.style.left='calc(50% - ((0px + 2px) / 2) + 0px)';
					me._satext0.style.top='22px';
				}
			}
		}
		me._satext0.logicBlock_position();
		me._satext0.ggUpdatePosition=function (useTransition) {
		}
		me._standalone.appendChild(me._satext0);
		me.elementMouseOver['standalone']=false;
		me._satext0.logicBlock_position();
			me.ggEvent_configloaded=function() {
				me._satext0.logicBlock_position();
			};
			me.__div = me._standalone;
	};
	me.addSkinHotspot=function(hotspot) {
		var hsinst = null;
			if (hotspot.skinid=='stand-alone') {
				hotspot.skinid = 'stand-alone';
				hsinst = new SkinHotspotClass_standalone(me, hotspot);
			if (!hotspotTemplates.hasOwnProperty(hotspot.skinid)) {
				hotspotTemplates[hotspot.skinid] = [];
			}
			hotspotTemplates[hotspot.skinid].push(hsinst);
		} else
		{
				hotspot.skinid = 'ht_node';
				hsinst = new SkinHotspotClass_ht_node(me, hotspot);
			if (!hotspotTemplates.hasOwnProperty(hotspot.skinid)) {
				hotspotTemplates[hotspot.skinid] = [];
			}
			hotspotTemplates[hotspot.skinid].push(hsinst);
		}
		return hsinst;
	}
	me.removeSkinHotspots=function() {
		hotspotTemplates = {};
	}
	player.addListener('hotspotsremoved',function() {
			me.removeSkinHotspots();
	});
	player.addListener('changenode', function() {
		me.ggUserdata=player.userdata;
	});
	me.skinTimerEvent=function() {
		if (player.isInVR()) return;
		me.ggCurrentTime=new Date().getTime();
		for (const id in hotspotTemplates) {
			const tmpl=hotspotTemplates[id];
			tmpl.forEach(function(hotspot) {
				if (hotspot.hotspotTimerEvent) {
					hotspot.hotspotTimerEvent();
				}
			});
		};
	};
	player.addListener('timer', me.skinTimerEvent);
	me.addSkin();
	var style = document.createElement('style');
	style.type = 'text/css';
	style.appendChild(document.createTextNode('.ggskin { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 14px; line-height: normal; } .ggmarkdown p,.ggmarkdown h1,.ggmarkdown h2,.ggmarkdown h3,.ggmarkdown h4 { margin-top: 0px } .ggmarkdown { white-space:normal }'));
	document.head.appendChild(style);
	document.addEventListener('keyup', function(e) {
		if (e.key === 'Enter' || e.key === ' ') {
			let activeElement = document.activeElement;
			if (activeElement.classList.contains('ggskin') && activeElement.onclick) activeElement.onclick();
		}
	});
	document.addEventListener('keydown', function(e) {
		if (e.key === 'Enter' || e.key === ' ') {
			let activeElement = document.activeElement;
			if (activeElement.classList.contains('ggskin') && activeElement.onmousedown) activeElement.onmousedown();
		}
	});
	document.addEventListener('keyup', function(e) {
		if (e.key === 'Enter' || e.key === ' ') {
			let activeElement = document.activeElement;
			if (activeElement.classList.contains('ggskin') && activeElement.onmouseup) activeElement.onmouseup();
		}
	});
	me.skinTimerEvent();
	document.fonts.onloadingdone = () => {
		me.updateSize(me.divSkin);
	}
};