package com.cirpoint.controller;

import com.cirpoint.monitor.SystemMonitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 监控数据展示控制器
 */
@RestController
@RequestMapping("/metrics")
public class MonitorController {

    @Autowired
    private SystemMonitor systemMonitor;

    /**
     * 获取系统监控数据
     */
    @GetMapping(value = "/system", produces = MediaType.TEXT_HTML_VALUE)
    public String getSystemMetrics() {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>\n")
            .append("<html>\n")
            .append("<head>\n")
            .append("    <meta charset=\"UTF-8\">\n")
            .append("    <title>系统监控数据</title>\n")
            .append("    <style>\n")
            .append("        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }\n")
            .append("        .container { max-width: 800px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n")
            .append("        h1 { color: #333; text-align: center; margin-bottom: 30px; font-size: 24px; }\n")
            .append("        .metric { margin: 15px 0; padding: 10px; background-color: #f8f9fa; border-radius: 5px; }\n")
            .append("        .metric-title { color: #2c3e50; font-weight: bold; margin-bottom: 5px; }\n")
            .append("        .metric-value { color: #555; margin-left: 10px; }\n")
            .append("        .usage-bar { background-color: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden; margin-top: 5px; }\n")
            .append("        .usage-fill { height: 100%; transition: width 0.3s ease; }\n")
            .append("    </style>\n")
            .append("</head>\n")
            .append("<body>\n")
            .append("    <div class=\"container\">\n")
            .append("        <h1>💻 系统监控数据</h1>\n");

        // CPU使用率
        addMetricWithBar(html, "CPU使用率", String.format("%.2f%%", systemMonitor.getCpuUsage()));

        // 内存使用率
        addMetricWithBar(html, "系统内存使用率", String.format("%.2f%%", systemMonitor.getSystemMemoryUsage()));

        // JVM堆内存使用率
        addMetricWithBar(html, "JVM堆内存使用率", String.format("%.2f%%", systemMonitor.getHeapMemoryUsage()));

        // 磁盘使用率
        addMetricWithBar(html, "磁盘使用率", String.format("%.2f%%", systemMonitor.getDiskUsage()));

        html.append("    </div>\n")
            .append("    <script>\n")
            .append("        // 自动刷新页面\n")
            .append("        setTimeout(function() { location.reload(); }, 60000);\n")
            .append("    </script>\n")
            .append("</body>\n")
            .append("</html>");

        return html.toString();
    }

    private void addMetricWithBar(StringBuilder html, String title, String value) {
        double percentage = Double.parseDouble(value.replace("%", ""));
        String color = percentage >= 80 ? "#dc3545" : percentage >= 60 ? "#ffc107" : "#28a745";

        html.append("        <div class=\"metric\">\n")
            .append("            <div class=\"metric-title\">").append(title).append("</div>\n")
            .append("            <div class=\"metric-value\">").append(value).append("</div>\n")
            .append("            <div class=\"usage-bar\">\n")
            .append("                <div class=\"usage-fill\" style=\"width: ").append(value).append("; background-color: ").append(color).append(";\"></div>\n")
            .append("            </div>\n")
            .append("        </div>\n");
    }
}
