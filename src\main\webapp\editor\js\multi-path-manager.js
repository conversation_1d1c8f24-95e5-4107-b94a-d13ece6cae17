// multi-path-manager.js - 多路径管理相关功能

// 初始化多路径管理面板
function initMultiPathManager() {
    // 加载路径列表
    loadPathList();

    // 加载已启用路径列表
    loadEnabledPathList();

    // 绑定事件
    bindMultiPathManagerEvents();
}

// 加载路径列表
function loadPathList() {
    $.get('/multiPath/list', function (paths) {
        // 清空路径列表
        $('#pathList').empty();

        // 构建路径列表
        if (paths && paths.length > 0) {
            paths.forEach(function(pathEntry) {
                const pathItem = createPathItem(pathEntry);
                $('#pathList').append(pathItem);
            });
        } else {
            $('#pathList').append('<li class="path-item empty-list">暂无添加的路径</li>');
        }
    });
}

// 加载已启用路径列表
function loadEnabledPathList() {
    $.get('/multiPath/enabled', function (paths) {
        // 清空已启用路径列表
        $('#enabledPathList').empty();

        // 构建已启用路径列表
        if (paths && paths.length > 0) {
            paths.forEach(function(pathEntry) {
                const enabledPathItem = createEnabledPathItem(pathEntry);
                $('#enabledPathList').append(enabledPathItem);
            });
        } else {
            $('#enabledPathList').append('<li class="enabled-path-item empty-list">暂无已启用的路径</li>');
        }
    });
}

// 创建路径项
function createPathItem(pathEntry) {
    const isEnabled = pathEntry.enabled;
    const enableBtnClass = isEnabled ? 'layui-btn-disabled' : 'layui-btn-normal';

    return `
        <li class="path-item" data-path="${pathEntry.path}" data-id="${pathEntry.id}">
            <div class="path-item-text">${pathEntry.path}</div>
            <div class="path-item-actions">
                <button type="button" class="enable-path-btn layui-btn layui-btn-xs ${enableBtnClass}" ${isEnabled ? 'disabled' : ''} title="${isEnabled ? '已启用' : '启用'}">
                    <i class="layui-icon layui-icon-ok"></i>
                </button>
                <button type="button" class="delete-path-btn layui-btn layui-btn-xs layui-btn-danger" title="删除">
                    <i class="layui-icon layui-icon-close"></i>
                </button>
            </div>
        </li>
    `;
}

// 创建已启用路径项
function createEnabledPathItem(pathEntry) {
    return `
        <li class="enabled-path-item" data-path="${pathEntry.path}" data-id="${pathEntry.id}">
            <div class="path-item-text">${pathEntry.path}</div>
            <div class="path-item-actions">
                <button type="button" class="disable-path-btn layui-btn layui-btn-xs layui-btn-danger" title="删除">
                    <i class="layui-icon layui-icon-close"></i>
                </button>
            </div>
        </li>
    `;
}

// 添加新路径
function addPath(path) {
    if (!path) {
        layer.msg('请输入有效路径', { icon: 2 });
        return;
    }

    // 处理多行输入，按行分割
    const paths = path.split(/\r?\n/).filter(p => p.trim() !== '');

    if (paths.length === 0) {
        layer.msg('请输入有效路径', { icon: 2 });
        return;
    }

    // 显示加载层
    const loadingIndex = layer.load(2);
    let successCount = 0;
    let failCount = 0;
    let processedCount = 0;

    // 逐个添加路径
    paths.forEach(function(singlePath) {
        $.ajax({
            url: '/multiPath/add',
            method: 'POST',
            data: { path: singlePath.trim() },
            success: function (response) {
                processedCount++;
                if (response.success) {
                    successCount++;
                } else {
                    failCount++;
                }

                // 当所有路径都处理完毕时
                if (processedCount === paths.length) {
                    layer.close(loadingIndex);
                    if (successCount > 0) {
                        if (failCount > 0) {
                            layer.msg(`成功添加${successCount}个路径，${failCount}个路径添加失败`, { icon: 1 });
                        } else {
                            layer.msg(`成功添加${successCount}个路径`, { icon: 1 });
                        }
                        // 清空输入框
                        $('#newPathInput').val('');
                        // 重新加载路径列表
                        loadPathList();
                    } else {
                        layer.msg('所有路径添加失败', { icon: 2 });
                    }
                }
            },
            error: function () {
                processedCount++;
                failCount++;

                // 当所有路径都处理完毕时
                if (processedCount === paths.length) {
                    layer.close(loadingIndex);
                    if (successCount > 0) {
                        layer.msg(`成功添加${successCount}个路径，${failCount}个路径添加失败`, { icon: 1 });
                        // 清空输入框
                        $('#newPathInput').val('');
                        // 重新加载路径列表
                        loadPathList();
                    } else {
                        layer.msg('所有路径添加失败', { icon: 2 });
                    }
                }
            }
        });
    });
}

// 删除路径
function deletePath(id) {
    $.ajax({
        url: '/multiPath/remove',
        method: 'POST',
        data: { id: id },
        success: function (response) {
            if (response.success) {
                layer.msg('路径删除成功', { icon: 1 });
                // 重新加载路径列表
                loadPathList();
                // 重新加载已启用路径列表
                loadEnabledPathList();
            } else {
                layer.msg('路径删除失败: ' + response.message, { icon: 2 });
            }
        },
        error: function () {
            layer.msg('操作失败，请重试', { icon: 2 });
        }
    });
}

// 启用路径
function enablePath(id) {
    $.ajax({
        url: '/multiPath/enable',
        method: 'POST',
        data: { id: id },
        success: function (response) {
            if (response.success) {
                layer.msg('路径启用成功', { icon: 1 });
                // 重新加载路径列表
                loadPathList();
                // 重新加载已启用路径列表
                loadEnabledPathList();

                // 刷新文件树，显示新启用的路径
                if (window.treeManager && typeof window.treeManager.initZTree === 'function') {
                    window.treeManager.initZTree();
                }
            } else {
                layer.msg('路径启用失败: ' + response.message, { icon: 2 });
            }
        },
        error: function () {
            layer.msg('操作失败，请重试', { icon: 2 });
        }
    });
}

// 禁用路径
function disablePath(id) {
    $.ajax({
        url: '/multiPath/disable',
        method: 'POST',
        data: { id: id },
        success: function (response) {
            if (response.success) {
                layer.msg('路径已从启用列表中移除', { icon: 1 });
                // 重新加载路径列表
                loadPathList();
                // 重新加载已启用路径列表
                loadEnabledPathList();

                // 刷新文件树，移除已禁用的路径
                if (window.treeManager && typeof window.treeManager.initZTree === 'function') {
                    window.treeManager.initZTree();
                }

                // 关闭与该路径相关的标签页
                if (window.tabManager) {
                    // 获取被禁用的路径
                    $.get('/multiPath/path/' + id, function(pathEntry) {
                        if (pathEntry && pathEntry.path) {
                            // 关闭所有以该路径开头的标签页
                            window.tabManager.closePathRelatedTabs(pathEntry.path);
                        }
                    });
                }
            } else {
                layer.msg('操作失败: ' + response.message, { icon: 2 });
            }
        },
        error: function () {
            layer.msg('操作失败，请重试', { icon: 2 });
        }
    });
}

// 绑定多路径管理面板事件
function bindMultiPathManagerEvents() {
    // 添加路径按钮点击事件
    $('#addPathBtn').on('click', function() {
        const newPath = $('#newPathInput').val().trim();
        addPath(newPath);
    });

    // 新路径输入框回车事件
    $('#newPathInput').on('keypress', function(e) {
        if (e.which === 13) {
            const newPath = $(this).val().trim();
            addPath(newPath);
            return false; // 阻止默认行为
        }
    });

    // 启用路径按钮点击事件（使用事件委托）
    $('#pathList').on('click', '.enable-path-btn', function() {
        const pathItem = $(this).closest('.path-item');
        const id = pathItem.data('id');
        enablePath(id);
    });

    // 删除路径按钮点击事件（使用事件委托）
    $('#pathList').on('click', '.delete-path-btn', function() {
        const pathItem = $(this).closest('.path-item');
        const id = pathItem.data('id');
        const path = pathItem.data('path');

        layer.confirm('确定要删除此路径吗？', {
            btn: ['确定', '取消'],
            title: '删除确认'
        }, function(index) {
            deletePath(id);
            layer.close(index);
        });
    });

    // 禁用路径按钮点击事件（使用事件委托）
    $('#enabledPathList').on('click', '.disable-path-btn', function() {
        const pathItem = $(this).closest('.enabled-path-item');
        const id = pathItem.data('id');
        disablePath(id);
    });
}

// 显示路径设置对话框（兼容旧版API，切换到多路径管理标签页）
window.showPathDialog = function () {
    // 切换到多路径管理标签页
    window.uiUtils.switchSidebarTab('multiPathManager');
};

// 导出函数
window.multiPathManager = {
    initMultiPathManager: initMultiPathManager,
    loadPathList: loadPathList,
    loadEnabledPathList: loadEnabledPathList,
    addPath: addPath,
    deletePath: deletePath,
    enablePath: enablePath,
    disablePath: disablePath
};

// 兼容旧版路径管理器API
window.pathManager = {
    initPathManager: function() {}, // 空函数，不再需要初始化
    loadPathInfo: function() {}, // 空函数，不再需要加载
    updatePath: addPath, // 使用多路径管理器的添加路径功能
    switchPath: function(path) { // 使用多路径管理器的添加并启用路径功能
        if (path) {
            addPath(path);
        }
    }
};
