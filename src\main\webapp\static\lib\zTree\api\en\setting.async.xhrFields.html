<div class="apiDetail">
<div>
	<h2><span>Object</span><span class="path">setting.async.</span>xhrFields</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>It is valid when <span class="highlight_red">[setting.async.enable = true]</span></p>
			<p>Default："{}"</p>
      <p class="highlight_red">v3.5.36+</p>
		</div>
	</div>
	<h3>String Format</h3>
	<div class="desc">
	<p class="highlight_red">The 'xhrFields' in zTree and jQuery's ajax requests is same.</p>
	</div>
</div>
</div>