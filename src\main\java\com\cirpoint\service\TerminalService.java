package com.cirpoint.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 终端命令服务类
 */
@Service
@Slf4j
public class TerminalService {

    @Value("${terminal.enabled:true}")
    private boolean terminalEnabled;

    @Value("${terminal.blocked.commands:rm -rf,rm -f,mkfs,dd,> /dev/sda,:(){ :|:& };:,chmod -R 777 /," +
            "mv /* /dev/null,wget,curl,sudo,su,passwd,useradd,userdel,groupadd,groupdel," +
            "> /dev/null,/dev/zero,/dev/random,/etc/passwd,/etc/shadow," +
            "shutdown,reboot,halt,poweroff,init 0,init 6," +
            "chown,chmod,chgrp,mount,umount,fdisk,mkfs," +
            ":(){ :|: & };:,perl -e,python -c,ruby -e,php -r," +
            "nc,ncat,netcat,telnet,ftp,sftp,ssh," +
            "vim,vi,nano,emacs,pico,ed,sed,awk," +  // 文本编辑器
            ">,>>,2>,2>>,&>,&>>," +  // 重定向操作
            "tee,echo >,touch}")  // 文件创建和追加
    private String blockedCommands;

    private final Map<String, Process> runningProcesses = new ConcurrentHashMap<>();
    private final Map<String, String> workingDirectories = new ConcurrentHashMap<>();

    /**
     * 执行终端命令
     *
     * @param command 命令内容
     * @param sessionId 会话ID
     * @param workingDirectory 工作目录
     * @return 命令执行结果
     */
    public CompletableFuture<List<String>> executeCommand(String command, String sessionId, String workingDirectory) {
        if (!terminalEnabled) {
            List<String> result = new ArrayList<>();
            result.add("终端功能已禁用");
            return CompletableFuture.completedFuture(result);
        }

        // 检查命令是否在禁止列表中
        if (isCommandBlocked(command)) {
            List<String> result = new ArrayList<>();
            result.add("命令在禁止列表中，出于安全考虑，此命令已被禁止执行");
            return CompletableFuture.completedFuture(result);
        }

        // 获取工作目录
        final String effectiveWorkingDirectory = workingDirectory != null && !workingDirectory.isEmpty()
            ? workingDirectory
            : workingDirectories.getOrDefault(sessionId, System.getProperty("user.home"));

        return CompletableFuture.supplyAsync(() -> {
            List<String> output = new ArrayList<>();
            Process process = null;

            try {
                // 处理 cd 命令
                if (command.trim().startsWith("cd ")) {
                    String newPath = command.trim().substring(3).trim();
                    File newDir;

                    // 处理相对路径
                    if (newPath.startsWith("/")) {
                        newDir = new File(newPath);
                    } else {
                        newDir = new File(effectiveWorkingDirectory, newPath);
                    }

                    if (newDir.exists() && newDir.isDirectory()) {
                        workingDirectories.put(sessionId, newDir.getCanonicalPath());
                        output.add("当前工作目录: " + newDir.getCanonicalPath());
                        return output;
                    } else {
                        output.add("错误: 目录不存在 - " + newPath);
                        return output;
                    }
                }

                // 使用bash或cmd执行命令
                String[] cmdArray;
                if (System.getProperty("os.name").toLowerCase().contains("win")) {
                    cmdArray = new String[]{"cmd.exe", "/c", command};
                } else {
                    cmdArray = new String[]{"/bin/bash", "-c", command};
                }

                ProcessBuilder processBuilder = new ProcessBuilder(cmdArray);
                processBuilder.directory(new File(effectiveWorkingDirectory));
                processBuilder.redirectErrorStream(true);

                process = processBuilder.start();
                runningProcesses.put(sessionId, process);

                // 读取命令输出
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        output.add(line);
                    }
                }

                // 等待命令执行完成，最多等待30秒
                if (!process.waitFor(30, TimeUnit.SECONDS)) {
                    process.destroyForcibly();
                    output.add("命令执行超时，已强制终止");
                }

            } catch (IOException | InterruptedException e) {
                log.error("执行命令出错: {}", e.getMessage(), e);
                output.add("执行命令出错: " + e.getMessage());
            } finally {
                if (process != null) {
                    process.destroyForcibly();
                    runningProcesses.remove(sessionId);
                }
            }

            return output;
        });
    }

    /**
     * 终止正在执行的命令
     *
     * @param sessionId 会话ID
     * @return 是否成功终止
     */
    public boolean terminateCommand(String sessionId) {
        Process process = runningProcesses.get(sessionId);
        if (process != null && process.isAlive()) {
            process.destroyForcibly();
            runningProcesses.remove(sessionId);
            return true;
        }
        return false;
    }

    /**
     * 获取当前工作目录
     *
     * @param sessionId 会话ID
     * @return 当前工作目录
     */
    public String getWorkingDirectory(String sessionId) {
        return workingDirectories.getOrDefault(sessionId, System.getProperty("user.home"));
    }

    /**
     * 设置工作目录
     *
     * @param sessionId 会话ID
     * @param directory 工作目录
     */
    public void setWorkingDirectory(String sessionId, String directory) {
        File dir = new File(directory);
        if (dir.exists() && dir.isDirectory()) {
            try {
                workingDirectories.put(sessionId, dir.getCanonicalPath());
            } catch (IOException e) {
                log.error("设置工作目录出错: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 检查命令是否在禁止列表中
     *
     * @param command 命令内容
     * @return 是否禁止执行
     */
    private boolean isCommandBlocked(String command) {
        if (command == null || command.trim().isEmpty()) {
            return true;
        }

        // 获取命令的第一个部分（主命令）和完整命令
        String trimmedCommand = command.trim().toLowerCase();
        String mainCommand = trimmedCommand.split("\\s+")[0];

        // 检查命令是否在禁止列表中
        String[] blockedCommandsArray = blockedCommands.split(",");
        for (String blocked : blockedCommandsArray) {
            blocked = blocked.trim().toLowerCase();
            if (blocked.isEmpty()) {
                continue;
            }

            // 检查是否完全匹配某个禁止命令
            if (trimmedCommand.equals(blocked)) {
                return true;
            }

            // 检查是否以某个禁止命令开头
            if (trimmedCommand.startsWith(blocked + " ")) {
                return true;
            }

            // 检查主命令是否被禁止
            if (mainCommand.equals(blocked)) {
                return true;
            }

            // 检查命令中是否包含危险的管道操作
            if (trimmedCommand.contains("|") && (
                trimmedCommand.contains("rm ") ||
                trimmedCommand.contains("mv ") ||
                trimmedCommand.contains("> ") ||
                trimmedCommand.contains("dd ")
            )) {
                return true;
            }

            // 检查命令中是否包含通配符删除操作
            if (mainCommand.equals("rm") && (
                trimmedCommand.contains("*") ||
                trimmedCommand.contains("?") ||
                trimmedCommand.contains("[") ||
                trimmedCommand.contains("]")
            )) {
                return true;
            }

            // 检查命令中是否包含重定向操作
            if (trimmedCommand.contains(">") ||
                trimmedCommand.contains(">>") ||
                trimmedCommand.contains("2>") ||
                trimmedCommand.contains("&>")) {
                return true;
            }

            // 检查是否包含管道到文本编辑器的操作
            if (trimmedCommand.contains("|") && (
                trimmedCommand.contains(" vi") ||
                trimmedCommand.contains(" vim") ||
                trimmedCommand.contains(" nano") ||
                trimmedCommand.contains(" emacs") ||
                trimmedCommand.contains(" sed") ||
                trimmedCommand.contains(" awk")
            )) {
                return true;
            }
        }

        return false;
    }
}
