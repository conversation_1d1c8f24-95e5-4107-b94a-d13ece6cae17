<?xml version="1.0" encoding="UTF-8"?>
<configuration encoding="UTF-8">
    <!-- 新增框架日志级别控制 -->
    <logger name="org.springframework.web" level="WARN"/>
    <logger name="org.springframework.boot" level="WARN"/>
    <logger name="org.apache.catalina" level="WARN"/>
    <logger name="org.springframework.web.servlet.DispatcherServlet" level="ERROR"/>
    <logger name="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping" level="ERROR"/>
    <logger name="org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor" level="WARN"/>

    <property name="LOG_PATH" value="logs" />
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n" />

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/application.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/archived/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 异步输出 -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="FILE"/>
    </appender>

    <!-- 根日志级别调整为WARN -->
    <root level="WARN">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="ASYNC" />
    </root>

    <!-- 应用日志级别保持DEBUG -->
    <logger name="com.cirpoint" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="ASYNC" />
    </logger>
</configuration>
