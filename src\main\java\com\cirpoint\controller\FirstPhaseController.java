package com.cirpoint.controller;

import cn.hutool.json.JSONObject;
import com.cirpoint.model.Result;
import com.cirpoint.service.FirstPhaseService;
import com.cirpoint.util.FileDownloadUtil;
import java.io.File;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 第一期相关操作的控制器
 */
@Slf4j
@RestController
@RequestMapping("/first/phase")
public class FirstPhaseController {

	private final FirstPhaseService firstPhaseService;

	@Autowired
	public FirstPhaseController(FirstPhaseService firstPhaseService) {
		this.firstPhaseService = firstPhaseService;
	}

	/**
	 * 导出Excel文件
	 *
	 * @param type 统计类型
	 * @param rsId 记录ID
	 * @return Excel文件下载响应
	 */
	@PostMapping("/export/excel")
	public ResponseEntity<?> exportExcel(
			@RequestParam String type,
			@RequestParam String rsId) {
		File excelFile = firstPhaseService.exportExcel(type, rsId);
		return FileDownloadUtil.fileResponseAndDelete(excelFile);
	}

	/**
	 * 下载文件
	 *
	 * @param fileName 文件名
	 * @param filePath 文件路径
	 * @return 文件下载响应
	 */
	@RequestMapping(value = "/download/file", method = {RequestMethod.POST, RequestMethod.GET})
	public ResponseEntity<?> downloadFile(
			@RequestParam String fileName,
			@RequestParam String filePath) {
		String processedFileName = firstPhaseService.getProcessedFileName(fileName);
		return FileDownloadUtil.fileResponse(filePath, processedFileName);
	}

	/**
	 * 导出二级表Excel
	 *
	 * @param treeId 树节点ID
	 * @param type   类型
	 * @param fi     第一个索引
	 * @param si     第二个索引
	 * @return Excel文件下载响应
	 */
	@PostMapping("/export/excel2")
	public ResponseEntity<?> exportExcel2(
			@RequestParam String treeId,
			@RequestParam String type,
			@RequestParam(required = false, defaultValue = "-1") String fi,
			@RequestParam(required = false, defaultValue = "-1") String si) {
		File excelFile = firstPhaseService.exportExcel2(type, treeId, fi, si);
		String fileName = getExcel2FileName(type);
		return FileDownloadUtil.fileResponseAndDelete(excelFile, fileName);

	}

	/**
	 * 获取二级表文件名
	 */
	private String getExcel2FileName(String type) {
		switch (type) {
			case "1":
				return "多层加工二级表";
			case "2":
				return "热管二级表";
			case "3":
				return "热控喷涂二级表";
			case "4":
				return "OSR粘贴二级表";
			case "5":
				return "热敏电阻加工二级表";
			case "6":
				return "一般结构件汇总表";
			default:
				return "导出表";
		}
	}

	/**
	 * 读取Excel文件
	 *
	 * @param filePath  文件路径
	 * @param resultId  结果ID
	 * @param dataId    数据ID
	 * @param productId 产品ID
	 * @param refDpid   参考ID
	 * @param model     型号
	 * @return 处理结果
	 */
	@PostMapping("/read/excel")
	public ResponseEntity<?> readExcel(
			@RequestParam String filePath,
			@RequestParam String resultId,
			@RequestParam String dataId,
			@RequestParam String productId,
			@RequestParam String refDpid,
			@RequestParam String model) {
		try {
			Map<String, String> params = new HashMap<>();
			params.put("filePath", filePath);
			params.put("result_id", resultId);
			params.put("data_id", dataId);
			params.put("product_id", productId);
			params.put("ref_dpid", refDpid);
			params.put("model", model);

			int result = firstPhaseService.readExcelFile(params);
			return Result.ok(result);
		} catch (Exception e) {
			log.error("读取Excel文件失败", e);
			return Result.error("读取Excel文件失败：" + e.getMessage());
		}
	}

	/**
	 * 检查图片是否存在
	 *
	 * @param url 图片URL地址
	 * @return 检查结果
	 */
	@PostMapping("/check/photo/exist")
	public ResponseEntity<?> checkPhotoExistence(@RequestParam String url) {
		return ResponseEntity.ok(firstPhaseService.checkPhotoExistence(url));
	}

	/**
	 * 导入Excel文件
	 *
	 * @param file          上传的文件
	 * @param type          文件类型
	 * @param nodeCode      树节点id
	 * @param fileType      文件类型
	 * @param fileTypeValue 文件类型值
	 * @param fileName      文件名称
	 * @param fileFormat    文件形式
	 * @param securityLevel 密级
	 * @param creator       创建者
	 * @return 处理结果
	 */
	@PostMapping("/import/excel")
	public ResponseEntity<?> importExcel(
			@RequestParam("excelFile") MultipartFile file,
			@RequestParam String type,
			@RequestParam String nodeCode,
			@RequestParam String fileType,
			@RequestParam String fileTypeValue,
			@RequestParam String fileName,
			@RequestParam String fileFormat,
			@RequestParam String securityLevel,
			@RequestParam String creator) {

		try {
			JSONObject result = firstPhaseService.importExcel(file, type, nodeCode,
					fileType, fileTypeValue, fileName, fileFormat,
					securityLevel, creator);
			return ResponseEntity.ok(result);
		} catch (Exception e) {
			log.error("导入Excel失败", e);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
					.body(new JSONObject().set("success", false)
							.set("message", "导入Excel失败：" + e.getMessage()));
		}
	}


	@PostMapping("/import/excel2")
	public ResponseEntity<?> importExcel2(
			@RequestParam("uploadFile") MultipartFile file,
			@RequestParam String type,
			@RequestParam String ctype,
			@RequestParam String treeId,
			@RequestParam String creator) {

		JSONObject result = firstPhaseService.importExcel2(file, type, ctype, treeId, creator);
		return ResponseEntity.ok(result);
	}

	/**
	 * 导出二级表Excel
	 *
	 * @param treeId  树节点ID
	 * @param tableId 表格ID
	 * @return Excel文件下载响应
	 */
	@PostMapping("/export/second/excel")
	public ResponseEntity<?> exportSecondExcel(@RequestParam String treeId, @RequestParam String tableId) {
		File excelFile = firstPhaseService.exportSecondExcel(treeId, tableId);
		return FileDownloadUtil.fileResponseAndDelete(excelFile, "二级表");
	}
}