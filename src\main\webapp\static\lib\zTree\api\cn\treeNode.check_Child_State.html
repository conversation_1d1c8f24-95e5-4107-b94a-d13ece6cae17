<div class="apiDetail">
<div>
	<h2><span>Number</span><span class="path">treeNode.</span>check_Child_State</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.excheck</span> 扩展 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>用于设置节点的子节点的 checkBox / radio 的半选状态。<span class="highlight_red">[setting.check.enable = true 时有效]</span></p>
			<p class="highlight_red">v3.x 针对节点数据对象提供 treeNode.getCheckStatus() 方法获取标准的半选状态</p>
			<p class="highlight_red">zTree 内部使用，请勿进行初始化 或 随意修改</p>
			<p>默认值：true</p>
		</div>
	</div>
	<h3>Number 格式说明</h3>
	<div class="desc">
	<p>规则如下：</p>
	<table width="100%" border="0" cellspacing="1" cellpadding="0">
		<thead>
			<tr><td colspan="4">setting.check.checkType = "checkbox"</td></tr>
			<tr><td>treeNode.check_Child_State</td><td>勾选状态说明</td></tr>
		</thead>
		<tbody>
			<tr><td>-1</td><td>不存在子节点 或 子节点全部设置为 nocheck = true</td></tr>
			<tr><td>0</td><td>无 子节点被勾选</td></tr>
			<tr><td>1</td><td>部分 子节点被勾选</td></tr>
			<tr><td>2</td><td>全部 子节点被勾选</td></tr>
		</tbody>
	</table>
	<br/>
	<table width="100%" border="0" cellspacing="1" cellpadding="0">
		<thead>
			<tr><td colspan="4">setting.check.checkType = "radio"</td></tr>
			<tr><td>treeNode.check_Child_State</td><td>勾选状态说明</td></tr>
		</thead>
		<tbody>
			<tr><td>-1</td><td>不存在子节点 或 子节点全部设置为 nocheck = true</td></tr>
			<tr><td>0</td><td>无 子节点被勾选</td></tr>
			<tr><td>2</td><td>有 子节点被勾选</td></tr>
		</tbody>
	</table>
	</div>
</div>
</div>