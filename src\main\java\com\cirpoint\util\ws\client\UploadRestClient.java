package com.cirpoint.util.ws.client;

import cn.hutool.core.io.FileUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.bind.DatatypeConverter;

public class UploadRestClient {

	private static Logger logger = LoggerFactory.getLogger(UploadRestClient.class);

	private int fileSize;    //每个文件大小
	private String restUrl;    //WS的地址

	private String auth;    //调用凭证
	private String dataPackage;//采集数据转换器

	public int getFileSize() {
		return fileSize;
	}

	public void setFileSize(int fileSize) {
		this.fileSize = fileSize;
	}

	public String getRestUrl() {
		return restUrl;
	}

	public void setRestUrl(String restUrl) {
		this.restUrl = restUrl;
	}

	public String getAuth() {
		return auth;
	}

	public void setAuth(String auth) {
		this.auth = auth;
	}

	public String getDataPackage() {
		return dataPackage;
	}

	public void setDataPackage(String dataPackage) {
		this.dataPackage = dataPackage;
	}

	public static void main(String[] args) {
		String filePath = "C:\\TestOut\\1678861141671\\card-22-1678861142574.zip";

		UploadRestClient client = new UploadRestClient();
		client.setFileSize(1024 * 1024 * 50);
		client.setRestUrl("http://***********:8085/avfms/collect/rest");
		//client.setRestUrl("http://127.0.0.1:2030/avfms/collect/rest");

		client.setAuth("avfms1");
		client.setDataPackage("avfms");

		long begin = System.currentTimeMillis();
		client.upload(filePath);
		long end = System.currentTimeMillis();
		logger.info("调用服务服务【{}】,耗时【{}】毫秒", client.getRestUrl(), end - begin);

	}

	public List<String> upload(String filePath) {
		File file = FileUtil.file(filePath);
		return this.splitFile(file);
	}

	private List<String> splitFile(File file) {
		List<String> result = new ArrayList<>();
		int size = this.getFileSize();
		String md5 = SecureUtil.md5(file);

		try {
			try (FileInputStream fis = new FileInputStream(file)) {
				byte[] byteArray = new byte[size];
				int len = 0;
				int i = 0;
				while ((len = fis.read(byteArray)) != -1) {
					logger.info("offset={},size={}", i * size, len);

					//最后一块
					if (len < size) {
						//截取长度len的字节
						byte[] byteArray2 = new byte[len];
						System.arraycopy(byteArray, 0, byteArray2, 0, len);
						result.add(this.upload(file.getName(), "", byteArray2, 1L * i * size, true, md5));
					} else {
						result.add(this.upload(file.getName(), "", byteArray, 1L * i * size, false, md5));
					}
					i++;
				}
			}

		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return result;
	}

	private String upload(String fileName, String originalFileDir, byte[] buffer, long offset, boolean isLastBolck,
						  String fileMD5) {

		logger.info("file={},offset={},len={},isLastBolck={}", fileName, offset, buffer.length, isLastBolck);

		try {

			JSONObject paramMap = new JSONObject();
			paramMap.set("auth", this.getAuth());
			paramMap.set("dataPackage", this.getDataPackage());
			paramMap.set("originalFileDir", "");
			paramMap.set("fileName", fileName);
			paramMap.set("buffer", DatatypeConverter.printBase64Binary(buffer));
			paramMap.set("offset", offset);
			paramMap.set("isLastBolck", isLastBolck);
			paramMap.set("fileMD5", fileMD5);

			String result = HttpUtil.post(this.getRestUrl(), paramMap.toString());

			return result;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getLocalizedMessage());
			return "E0000";
		}

	}
}
