package com.cirpoint.service.tools;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.cirpoint.constant.WorkTimeConstants;
import com.cirpoint.exception.WorkTimeProcessException;
import com.cirpoint.model.worktime.AttendanceRecord;
import com.cirpoint.model.worktime.LateEarlyResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工时统计服务类
 * 负责处理Excel文件上传、数据解析、工时计算和结果生成
 */
@Slf4j
@Service
public class WorkTimeService {

    @Value("${file.temp.path}")
    private String tempDir;

    private final CrossDayValidator crossDayValidator;
    private final DuplicateRecordProcessor duplicateRecordProcessor;

    public WorkTimeService(CrossDayValidator crossDayValidator,
                          DuplicateRecordProcessor duplicateRecordProcessor) {
        this.crossDayValidator = crossDayValidator;
        this.duplicateRecordProcessor = duplicateRecordProcessor;
    }



    /**
     * 验证Excel文件
     * @param file 上传的文件
     * @throws WorkTimeProcessException 验证失败异常
     */
    private void validateExcelFile(MultipartFile file) throws WorkTimeProcessException {
        if (file == null || file.isEmpty()) {
            throw WorkTimeProcessException.fileFormatError("文件为空");
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw WorkTimeProcessException.fileFormatError("文件名无效");
        }

        if (!originalFilename.toLowerCase().endsWith(WorkTimeConstants.EXCEL_EXTENSION)) {
            throw WorkTimeProcessException.fileFormatError(WorkTimeConstants.ERROR_FILE_NOT_EXCEL);
        }

        if (file.getSize() > WorkTimeConstants.MAX_FILE_SIZE) {
            throw WorkTimeProcessException.fileSizeError(WorkTimeConstants.ERROR_FILE_TOO_LARGE);
        }

        if (file.getSize() == 0) {
            throw WorkTimeProcessException.fileFormatError(WorkTimeConstants.ERROR_FILE_EMPTY);
        }
    }

    /**
     * 读取Excel数据并转换为AttendanceRecord列表
     * @param file Excel文件
     * @return 打卡记录列表
     * @throws WorkTimeProcessException 读取异常
     */
    private List<AttendanceRecord> readExcelData(MultipartFile file) throws WorkTimeProcessException {
        try {
            // 创建临时文件
            String tempFileName = System.currentTimeMillis() + "_" + file.getOriginalFilename();
            String tempFilePath = tempDir + File.separator + tempFileName;
            FileUtil.mkdir(tempDir);

            File tempFile = new File(tempFilePath);
            file.transferTo(tempFile);

            try (ExcelReader reader = ExcelUtil.getReader(tempFile)) {
                // 验证必需列是否存在
                validateRequiredColumns(reader);

                // 设置列别名映射
                Map<String, String> headerAlias = createHeaderAlias();
                reader.setHeaderAlias(headerAlias);

                // 读取所有数据
                List<Map<String, Object>> rawData = reader.readAll();

                // 转换为AttendanceRecord对象
                List<AttendanceRecord> records = convertToAttendanceRecords(rawData);

                // 清理临时文件
                FileUtil.del(tempFile);

                return records;

            } catch (Exception e) {
                // 清理临时文件
                FileUtil.del(tempFile);
                throw e;
            }

        } catch (IOException e) {
            throw WorkTimeProcessException.ioError("读取Excel文件失败", e);
        } catch (Exception e) {
            throw WorkTimeProcessException.dataValidationError("Excel数据解析失败: " + e.getMessage());
        }
    }

    /**
     * 验证Excel文件是否包含必需的列
     * @param reader Excel读取器
     * @throws WorkTimeProcessException 验证失败异常
     */
    private void validateRequiredColumns(ExcelReader reader) throws WorkTimeProcessException {
        List<Object> headers = reader.readRow(0);
        if (headers == null || headers.isEmpty()) {
            throw WorkTimeProcessException.dataValidationError("Excel文件没有表头");
        }

        Set<String> headerSet = headers.stream()
                .map(Object::toString)
                .collect(Collectors.toSet());

        String[] requiredColumns = {
            WorkTimeConstants.COLUMN_EMPLOYEE_NAME,
            WorkTimeConstants.COLUMN_ACCESS_POINT,
            WorkTimeConstants.COLUMN_DIRECTION,
            WorkTimeConstants.COLUMN_EVENT_TIME
        };

        for (String column : requiredColumns) {
            if (!headerSet.contains(column)) {
                throw WorkTimeProcessException.dataValidationError(
                    WorkTimeConstants.ERROR_MISSING_COLUMNS + ": " + column);
            }
        }
    }

    /**
     * 创建列别名映射
     * @return 列别名映射
     */
    private Map<String, String> createHeaderAlias() {
        Map<String, String> headerAlias = new HashMap<>();
        headerAlias.put(WorkTimeConstants.COLUMN_EMPLOYEE_NAME, "employeeName");
        headerAlias.put(WorkTimeConstants.COLUMN_EMPLOYEE_ID, "employeeId");
        headerAlias.put(WorkTimeConstants.COLUMN_DEPARTMENT, "department");
        headerAlias.put(WorkTimeConstants.COLUMN_ACCESS_POINT, "accessPoint");
        headerAlias.put(WorkTimeConstants.COLUMN_CONTROLLER, "controller");
        headerAlias.put(WorkTimeConstants.COLUMN_DIRECTION, "direction");
        headerAlias.put(WorkTimeConstants.COLUMN_EVENT_TIME, "eventTime");
        return headerAlias;
    }

    /**
     * 将原始数据转换为AttendanceRecord对象列表
     * @param rawData 原始数据
     * @return AttendanceRecord列表
     * @throws WorkTimeProcessException 转换异常
     */
    private List<AttendanceRecord> convertToAttendanceRecords(List<Map<String, Object>> rawData)
            throws WorkTimeProcessException {
        List<AttendanceRecord> records = new ArrayList<>();
        int rowIndex = 1; // 从第二行开始（第一行是表头）

        for (Map<String, Object> row : rawData) {
            rowIndex++;
            try {
                AttendanceRecord record = convertRowToRecord(row, rowIndex);
                if (record.isValid()) {
                    records.add(record);
                }
            } catch (Exception e) {
                log.warn("第{}行数据转换失败: {}", rowIndex, e.getMessage());
                // 继续处理其他行，不中断整个过程
            }
        }

        if (records.isEmpty()) {
            throw WorkTimeProcessException.dataValidationError(WorkTimeConstants.ERROR_NO_VALID_RECORDS);
        }

        return records;
    }

    /**
     * 将单行数据转换为AttendanceRecord对象
     * @param row 行数据
     * @param rowIndex 行索引
     * @return AttendanceRecord对象
     */
    private AttendanceRecord convertRowToRecord(Map<String, Object> row, int rowIndex) {
        AttendanceRecord record = new AttendanceRecord();

        // 设置基本字段
        record.setEmployeeName(getStringValue(row, "employeeName"));
        record.setEmployeeId(getStringValue(row, "employeeId"));
        record.setDepartment(getStringValue(row, "department"));
        record.setAccessPoint(getStringValue(row, "accessPoint")); // 这会自动设置location
        record.setController(getStringValue(row, "controller"));
        record.setDirection(getStringValue(row, "direction"));

        // 解析事件时间
        Object eventTimeObj = row.get("eventTime");
        if (eventTimeObj != null) {
            LocalDateTime eventTime = parseDateTime(eventTimeObj.toString(), rowIndex);
            record.setEventTime(eventTime);
        }

        return record;
    }

    /**
     * 从Map中获取字符串值
     * @param row 数据行
     * @param key 键
     * @return 字符串值
     */
    private String getStringValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        return value != null ? StrUtil.trim(value.toString()) : null;
    }

    /**
     * 解析日期时间字符串
     * @param dateTimeStr 日期时间字符串
     * @param rowIndex 行索引（用于错误提示）
     * @return LocalDateTime对象
     */
    private LocalDateTime parseDateTime(String dateTimeStr, int rowIndex) {
        if (StrUtil.isBlank(dateTimeStr)) {
            return null;
        }

        try {
            // 尝试多种日期时间格式
            String[] patterns = {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy/MM/dd HH:mm:ss",
                "yyyy-MM-dd HH:mm",
                "yyyy/MM/dd HH:mm"
            };

            for (String pattern : patterns) {
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                    return LocalDateTime.parse(dateTimeStr, formatter);
                } catch (DateTimeParseException ignored) {
                    // 继续尝试下一个格式
                }
            }

            // 如果所有格式都失败，尝试使用Hutool的日期解析
            Date date = DateUtil.parse(dateTimeStr);
            return DateUtil.toLocalDateTime(date);

        } catch (Exception e) {
            log.warn("第{}行日期时间解析失败: {}", rowIndex, dateTimeStr);
            return null;
        }
    }



    /**
     * 按员工复合标识符、场所对记录进行分组（不包含日期，以支持跨天工时计算）
     * @param records 打卡记录列表
     * @return 分组后的记录
     */
    private Map<String, List<AttendanceRecord>> groupRecordsByEmployeeLocation(List<AttendanceRecord> records) {
        if (records == null || records.isEmpty()) {
            return new HashMap<>();
        }

        return records.stream()
            .filter(record -> record != null &&
                             record.getEmployeeName() != null &&
                             !record.getEmployeeName().trim().isEmpty() &&
                             record.getLocation() != null &&
                             !record.getLocation().trim().isEmpty() &&
                             record.getEventTime() != null)
            .collect(Collectors.groupingBy(
                record -> record.getEmployeeCompositeKey() + "|" + record.getLocation(),
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    list -> {
                        // 按时间排序
                        list.sort(Comparator.comparing(AttendanceRecord::getEventTime));
                        return list;
                    }
                )
            ));
    }

    /**
     * 计算单个分组（员工+场所）的跨天工时
     * @param records 该分组的打卡记录
     * @return 按日期分组的工时数据
     */
    private Map<LocalDate, Double> calculateCrossDayWorkTime(List<AttendanceRecord> records) {
        Map<LocalDate, Double> dailyHours = new HashMap<>();

        if (CollUtil.isEmpty(records)) {
            return dailyHours;
        }

        LocalDateTime entryTime = null;

        for (AttendanceRecord record : records) {
            if (WorkTimeConstants.DIRECTION_IN.equals(record.getDirection())) {
                // 入场记录
                if (entryTime == null) {
                    entryTime = record.getEventTime();
                } else {
                    // 连续的入场记录，忽略后续的
                }
            } else if (WorkTimeConstants.DIRECTION_OUT.equals(record.getDirection())) {
                // 出场记录
                if (entryTime != null) {
                    // 找到配对的入-出记录，计算并分配工时
                    calculateAndDistributeWorkTime(entryTime, record.getEventTime(), dailyHours);
                    entryTime = null; // 重置入场时间
                } else {
                    // 悬挂的出场记录，忽略
                }
            }
        }

        // 如果最后还有未配对的入场记录，忽略（悬挂的入场记录）

        return dailyHours;
    }

    /**
     * 计算并分配跨天工时到对应的日期
     * @param startTime 入场时间
     * @param endTime 出场时间
     * @param dailyHours 日工时累计Map
     */
    private void calculateAndDistributeWorkTime(LocalDateTime startTime, LocalDateTime endTime,
                                              Map<LocalDate, Double> dailyHours) {
        if (startTime == null || endTime == null || !endTime.isAfter(startTime)) {
            return;
        }

        // 检查时间间隔是否合理（不超过24小时）
        Duration totalDuration = Duration.between(startTime, endTime);
        if (totalDuration.toHours() > 24) {
            log.warn("检测到异常长的工作时间: {} 到 {}, 时长: {} 小时",
                    startTime, endTime, totalDuration.toHours());
        }

        LocalDate startDate = startTime.toLocalDate();
        LocalDate endDate = endTime.toLocalDate();

        if (startDate.equals(endDate)) {
            // 同一天，直接计算
            double hours = totalDuration.toMinutes() / 60.0;
            dailyHours.merge(startDate, hours, Double::sum);
        } else {
            // 跨天情况，拆分计算
            // 第一天的工时：从入场时间到当天23:59:59
            LocalDateTime endOfFirstDay = startDate.atTime(23, 59, 59);
            double firstDayHours = Duration.between(startTime, endOfFirstDay).toMinutes() / 60.0;
            dailyHours.merge(startDate, firstDayHours, Double::sum);

            // 第二天的工时：从00:00:00到出场时间
            LocalDateTime startOfSecondDay = endDate.atStartOfDay();
            double secondDayHours = Duration.between(startOfSecondDay, endTime).toMinutes() / 60.0;
            dailyHours.merge(endDate, secondDayHours, Double::sum);

            log.info("跨天工作检测: {} 到 {}, 第一天({})工时: {:.2f}小时, 第二天({})工时: {:.2f}小时",
                    startTime, endTime, startDate, firstDayHours, endDate, secondDayHours);
        }
    }






    /**
     * 比较两个中文字符串的拼音顺序
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 比较结果
     */
    private int comparePinyin(String str1, String str2) {
        if (str1 == null && str2 == null) return 0;
        if (str1 == null) return -1;
        if (str2 == null) return 1;

        // 使用Java的Collator进行中文排序
        java.text.Collator collator = java.text.Collator.getInstance(java.util.Locale.CHINA);
        return collator.compare(str1, str2);
    }



    /**
     * 处理迟到早退统计文件的主要方法
     * @param file 上传的Excel文件
     * @return 处理后的Excel文件字节数组
     * @throws WorkTimeProcessException 处理异常
     */
    public byte[] processLateEarlyFile(MultipartFile file) throws WorkTimeProcessException {
        try {
            log.info("开始处理迟到早退统计文件: {}", file.getOriginalFilename());

            // 1. 验证文件
            validateExcelFile(file);

            // 2. 读取Excel数据
            List<AttendanceRecord> records = readExcelData(file);
            log.info("成功读取{}条打卡记录", records.size());

            // 3. 计算迟到早退统计
            List<LateEarlyResult> results = calculateLateEarlyStatistics(records);
            log.info("成功计算{}条迟到早退记录", results.size());

            // 4. 排序结果
            List<LateEarlyResult> sortedResults = sortLateEarlyByPinyin(results);

            // 5. 生成输出Excel
            byte[] excelBytes = generateLateEarlyExcel(sortedResults);

            log.info("迟到早退统计处理完成");
            return excelBytes;

        } catch (WorkTimeProcessException e) {
            throw e;
        } catch (Exception e) {
            log.error("处理迟到早退统计文件失败", e);
            throw WorkTimeProcessException.unknownError("处理文件时发生未知错误", e);
        }
    }

    /**
     * 计算迟到早退统计的核心方法
     * @param records 打卡记录列表
     * @return 迟到早退统计结果列表
     * @throws WorkTimeProcessException 计算异常
     */
    private List<LateEarlyResult> calculateLateEarlyStatistics(List<AttendanceRecord> records)
            throws WorkTimeProcessException {
        try {
            // 1. 门禁数据去重处理
            List<AttendanceRecord> deduplicatedRecords = duplicateRecordProcessor.removeDuplicateRecords(records);
            log.info("门禁数据去重完成，{}",
                    duplicateRecordProcessor.getDeduplicationStats(records, deduplicatedRecords));

            // 2. 跨日验证，获取需要忽略的异常记录
            Set<AttendanceRecord> invalidRecords = crossDayValidator.validateCrossDayRecords(deduplicatedRecords);

            // 3. 过滤掉异常记录
            List<AttendanceRecord> validRecords = deduplicatedRecords.stream()
                .filter(record -> !invalidRecords.contains(record))
                .collect(Collectors.toList());

            log.info("跨日验证完成，去重后记录数: {}, 有效记录数: {}, 异常记录数: {}",
                    deduplicatedRecords.size(), validRecords.size(), invalidRecords.size());

            // 4. 过滤910/920厂房记录并合并处理
            List<AttendanceRecord> factoryRecords = filterAndMergeFactoryRecords(validRecords);
            log.info("910/920厂房记录过滤完成，有效厂房记录数: {}", factoryRecords.size());

            // 5. 按员工姓名和日期分组
            Map<String, List<AttendanceRecord>> groupedRecords = groupRecordsByEmployeeDate(factoryRecords);

            // 6. 计算每个分组的迟到早退情况
            List<LateEarlyResult> results = new ArrayList<>();
            for (Map.Entry<String, List<AttendanceRecord>> entry : groupedRecords.entrySet()) {
                String groupKey = entry.getKey();
                List<AttendanceRecord> groupRecords = entry.getValue();

                // 解析分组键：员工复合标识符|日期
                String[] keyParts = groupKey.split("\\|");
                String employeeCompositeKey = keyParts[0];
                LocalDate workDate = LocalDate.parse(keyParts[1]);

                // 从记录中获取员工姓名和工号（因为复合键可能只包含姓名）
                AttendanceRecord firstRecord = groupRecords.get(0);
                String employeeName = firstRecord.getEmployeeName();
                String employeeId = firstRecord.getEmployeeId();

                // 计算该分组的迟到早退情况
                LateEarlyResult result = calculateDailyLateEarly(employeeName, employeeId, workDate, groupRecords);
                if (result != null) {
                    results.add(result);
                }
            }

            return results;

        } catch (Exception e) {
            log.error("计算迟到早退统计失败", e);
            throw WorkTimeProcessException.calculationError("迟到早退统计计算失败");
        }
    }

    /**
     * 过滤并合并910/920厂房记录
     * @param records 所有有效记录
     * @return 910/920厂房记录列表
     */
    private List<AttendanceRecord> filterAndMergeFactoryRecords(List<AttendanceRecord> records) {
        return records.stream()
            .filter(record -> "910厂房".equals(record.getLocation()) || "920厂房".equals(record.getLocation()))
            .map(record -> {
                // 将910厂房和920厂房统一标记为"910/920厂房"以便合并处理
                AttendanceRecord mergedRecord = new AttendanceRecord();
                mergedRecord.setEmployeeName(record.getEmployeeName());
                mergedRecord.setEmployeeId(record.getEmployeeId());
                mergedRecord.setDepartment(record.getDepartment());
                mergedRecord.setAccessPoint(record.getAccessPoint());
                mergedRecord.setController(record.getController());
                mergedRecord.setDirection(record.getDirection());
                mergedRecord.setEventTime(record.getEventTime());
                mergedRecord.setLocation("910/920厂房"); // 合并标记
                return mergedRecord;
            })
            .collect(Collectors.toList());
    }

    /**
     * 按员工复合标识符和日期分组记录
     * @param records 记录列表
     * @return 分组后的记录Map，键格式：员工姓名+工号|日期
     */
    private Map<String, List<AttendanceRecord>> groupRecordsByEmployeeDate(List<AttendanceRecord> records) {
        return records.stream()
            .collect(Collectors.groupingBy(record ->
                record.getEmployeeCompositeKey() + "|" + record.getEventTime().toLocalDate().toString()));
    }

    /**
     * 计算单个员工单日的迟到早退情况
     * @param employeeName 员工姓名
     * @param employeeId 工号
     * @param workDate 工作日期
     * @param records 该员工当日的打卡记录
     * @return 迟到早退结果
     */
    private LateEarlyResult calculateDailyLateEarly(String employeeName, String employeeId, LocalDate workDate,
                                                   List<AttendanceRecord> records) {
        if (records == null || records.isEmpty()) {
            return null;
        }

        // 按时间排序
        records.sort(Comparator.comparing(AttendanceRecord::getEventTime));

        // 计算净工时（复用现有逻辑）
        Map<LocalDate, Double> dailyHours = calculateCrossDayWorkTime(records);
        double totalHours = dailyHours.getOrDefault(workDate, 0.0);

        // 创建结果对象
        LateEarlyResult result = new LateEarlyResult(employeeName, employeeId, workDate, totalHours);

        // 分离上午和下午记录
        List<AttendanceRecord> morningRecords = new ArrayList<>();
        List<AttendanceRecord> afternoonRecords = new ArrayList<>();

        for (AttendanceRecord record : records) {
            LocalDateTime eventTime = record.getEventTime();
            if (eventTime.getHour() < 12) {
                morningRecords.add(record);
            } else {
                afternoonRecords.add(record);
            }
        }

        // 计算上午迟到早退
        calculateMorningLateEarly(result, morningRecords);

        // 计算下午迟到早退
        calculateAfternoonLateEarly(result, afternoonRecords);

        return result;
    }

    /**
     * 计算上午迟到早退情况
     * @param result 结果对象
     * @param morningRecords 上午记录（00:00-12:00）
     */
    private void calculateMorningLateEarly(LateEarlyResult result, List<AttendanceRecord> morningRecords) {
        if (morningRecords.isEmpty()) {
            return; // 上午未进厂，不统计
        }

        // 找到上午的进入和离开记录
        List<AttendanceRecord> morningEntries = morningRecords.stream()
            .filter(r -> WorkTimeConstants.DIRECTION_IN.equals(r.getDirection()))
            .collect(Collectors.toList());

        List<AttendanceRecord> morningExits = morningRecords.stream()
            .filter(r -> WorkTimeConstants.DIRECTION_OUT.equals(r.getDirection()))
            .collect(Collectors.toList());

        // 计算上午迟到：第一次进厂时间晚于08:05（周六上午为08:30）
        if (!morningEntries.isEmpty()) {
            LocalDateTime firstEntry = morningEntries.get(0).getEventTime();
            result.setMorningFirstEntry(firstEntry);

            // 判断是否为周六，周六上午迟到时间为08:30，其他时间为08:05
            boolean isSaturday = firstEntry.getDayOfWeek().getValue() == 6; // 周六为6
            int lateMinute = isSaturday ? WorkTimeConstants.MORNING_LATE_MINUTE_SATURDAY : WorkTimeConstants.MORNING_LATE_MINUTE_WEEKDAY;
            LocalDateTime lateThreshold = firstEntry.toLocalDate().atTime(WorkTimeConstants.MORNING_LATE_HOUR, lateMinute);

            if (firstEntry.isAfter(lateThreshold)) {
                result.setMorningLateCount(1);
            }
        }

        // 计算上午早退：10:00-12:00时段最后一次出厂时间早于11:00
        if (!morningExits.isEmpty()) {
            // 过滤10:00-12:00时段的出厂记录
            List<AttendanceRecord> lateExits = morningExits.stream()
                .filter(r -> r.getEventTime().getHour() >= 10)
                .collect(Collectors.toList());

            if (!lateExits.isEmpty()) {
                LocalDateTime lastExit = lateExits.get(lateExits.size() - 1).getEventTime();
                result.setMorningLastExit(lastExit);

                LocalDateTime earlyThreshold = lastExit.toLocalDate().atTime(WorkTimeConstants.MORNING_EARLY_HOUR, WorkTimeConstants.MORNING_EARLY_MINUTE);
                if (lastExit.isBefore(earlyThreshold)) {
                    result.setMorningEarlyCount(1);
                }
            }
        }
    }

    /**
     * 计算下午迟到早退情况
     * @param result 结果对象
     * @param afternoonRecords 下午记录（12:00-23:59）
     */
    private void calculateAfternoonLateEarly(LateEarlyResult result, List<AttendanceRecord> afternoonRecords) {
        if (afternoonRecords.isEmpty()) {
            return; // 下午未进厂，不统计
        }

        // 找到下午的进入和离开记录
        List<AttendanceRecord> afternoonEntries = afternoonRecords.stream()
            .filter(r -> WorkTimeConstants.DIRECTION_IN.equals(r.getDirection()))
            .collect(Collectors.toList());

        List<AttendanceRecord> afternoonExits = afternoonRecords.stream()
            .filter(r -> WorkTimeConstants.DIRECTION_OUT.equals(r.getDirection()))
            .collect(Collectors.toList());

        // 计算下午迟到：12:00后第一次进厂时间晚于12:30
        if (!afternoonEntries.isEmpty()) {
            LocalDateTime firstEntry = afternoonEntries.get(0).getEventTime();
            result.setAfternoonFirstEntry(firstEntry);

            LocalDateTime lateThreshold = firstEntry.toLocalDate().atTime(WorkTimeConstants.AFTERNOON_LATE_HOUR, WorkTimeConstants.AFTERNOON_LATE_MINUTE);
            if (firstEntry.isAfter(lateThreshold)) {
                result.setAfternoonLateCount(1);
            }
        }

        // 计算下午早退：12:30后最后一次出厂时间早于16:30
        if (!afternoonExits.isEmpty()) {
            // 过滤12:30后的出厂记录
            List<AttendanceRecord> lateExits = afternoonExits.stream()
                .filter(r -> r.getEventTime().isAfter(r.getEventTime().toLocalDate().atTime(WorkTimeConstants.AFTERNOON_LATE_HOUR, WorkTimeConstants.AFTERNOON_LATE_MINUTE)))
                .collect(Collectors.toList());

            if (!lateExits.isEmpty()) {
                LocalDateTime lastExit = lateExits.get(lateExits.size() - 1).getEventTime();
                result.setAfternoonLastExit(lastExit);

                LocalDateTime earlyThreshold = lastExit.toLocalDate().atTime(WorkTimeConstants.AFTERNOON_EARLY_HOUR, WorkTimeConstants.AFTERNOON_EARLY_MINUTE);
                if (lastExit.isBefore(earlyThreshold)) {
                    result.setAfternoonEarlyCount(1);
                }
            }
        }
    }

    /**
     * 按拼音排序迟到早退结果
     * @param results 结果列表
     * @return 排序后的结果列表
     */
    private List<LateEarlyResult> sortLateEarlyByPinyin(List<LateEarlyResult> results) {
        if (CollUtil.isEmpty(results)) {
            return new ArrayList<>();
        }

        return results.stream()
            .sorted((r1, r2) -> {
                // 首先按员工姓名排序
                int nameCompare = comparePinyin(r1.getEmployeeName(), r2.getEmployeeName());
                if (nameCompare != 0) {
                    return nameCompare;
                }
                // 姓名相同时按日期排序
                if (r1.getWorkDate() != null && r2.getWorkDate() != null) {
                    return r1.getWorkDate().compareTo(r2.getWorkDate());
                }
                return 0;
            })
            .collect(Collectors.toList());
    }

    /**
     * 生成迟到早退统计Excel文件
     * @param results 迟到早退统计结果列表
     * @return Excel文件字节数组
     * @throws WorkTimeProcessException 生成异常
     */
    private byte[] generateLateEarlyExcel(List<LateEarlyResult> results) throws WorkTimeProcessException {
        try {
            // 创建临时文件
            String tempFileName = System.currentTimeMillis() + "_late_early_result.xlsx";
            String tempFilePath = tempDir + File.separator + tempFileName;

            try (ExcelWriter writer = ExcelUtil.getWriter(tempFilePath)) {
                // 生成汇总工作表
                generateSummarySheet(writer, results);

                // 生成明细工作表
                generateDetailSheet(writer, results);

                // 设置默认工作表为汇总
                writer.setSheet(0);
            }

            // 读取文件内容
            File tempFile = new File(tempFilePath);
            byte[] fileBytes = FileUtil.readBytes(tempFile);

            // 清理临时文件
            FileUtil.del(tempFile);

            return fileBytes;

        } catch (Exception e) {
            log.error("生成迟到早退统计Excel失败", e);
            throw WorkTimeProcessException.ioError("生成结果文件失败", e);
        }
    }

    /**
     * 生成汇总工作表
     * @param writer Excel写入器
     * @param results 明细结果列表
     */
    private void generateSummarySheet(ExcelWriter writer, List<LateEarlyResult> results) {
        // 设置汇总工作表
        writer.setSheet(0);
        writer.renameSheet("汇总");

        // 写入表头
        List<String> summaryHeaders = Arrays.asList(
            "序号", "姓名", "工号", "出勤天数", "净时长", "平均时长",
            "上午迟到次数", "上午早退次数", "下午迟到次数", "下午早退次数"
        );
        writer.writeRow(summaryHeaders);

        // 按员工复合标识符分组并汇总数据
        Map<String, List<LateEarlyResult>> employeeGroups = results.stream()
            .collect(Collectors.groupingBy(LateEarlyResult::getEmployeeCompositeKey));

        List<LateEarlyResult> summaryResults = new ArrayList<>();
        for (Map.Entry<String, List<LateEarlyResult>> entry : employeeGroups.entrySet()) {
            String employeeCompositeKey = entry.getKey();
            List<LateEarlyResult> employeeResults = entry.getValue();

            // 从第一条记录中获取员工姓名和工号
            LateEarlyResult firstResult = employeeResults.get(0);
            String employeeName = firstResult.getEmployeeName();
            String employeeId = firstResult.getEmployeeId();

            // 计算汇总数据
            int attendanceDays = employeeResults.size();
            double totalHours = employeeResults.stream().mapToDouble(LateEarlyResult::getDailyHours).sum();
            double averageHours = attendanceDays > 0 ? totalHours / attendanceDays : 0.0;

            int totalMorningLate = employeeResults.stream().mapToInt(LateEarlyResult::getMorningLateCount).sum();
            int totalMorningEarly = employeeResults.stream().mapToInt(LateEarlyResult::getMorningEarlyCount).sum();
            int totalAfternoonLate = employeeResults.stream().mapToInt(LateEarlyResult::getAfternoonLateCount).sum();
            int totalAfternoonEarly = employeeResults.stream().mapToInt(LateEarlyResult::getAfternoonEarlyCount).sum();

            // 创建汇总记录
            LateEarlyResult summary = new LateEarlyResult(employeeName, employeeId, attendanceDays, averageHours);
            summary.setDailyHours(totalHours); // 用于显示净时长
            summary.setMorningLateCount(totalMorningLate);
            summary.setMorningEarlyCount(totalMorningEarly);
            summary.setAfternoonLateCount(totalAfternoonLate);
            summary.setAfternoonEarlyCount(totalAfternoonEarly);

            summaryResults.add(summary);
        }

        // 按姓名排序
        summaryResults.sort((r1, r2) -> comparePinyin(r1.getEmployeeName(), r2.getEmployeeName()));

        // 写入汇总数据
        for (int i = 0; i < summaryResults.size(); i++) {
            LateEarlyResult summary = summaryResults.get(i);
            summary.setSequence(i + 1);

            List<Object> row = Arrays.asList(
                summary.getSequence(),
                summary.getEmployeeName(),
                summary.getEmployeeId() != null ? summary.getEmployeeId() : "",
                summary.getAttendanceDays(),
                summary.getFormattedDailyHours(),
                summary.getFormattedAverageHours(),
                summary.getMorningLateCount(),
                summary.getMorningEarlyCount(),
                summary.getAfternoonLateCount(),
                summary.getAfternoonEarlyCount()
            );
            writer.writeRow(row);
        }

        // 设置列宽
        writer.setColumnWidth(0, 8);   // 序号
        writer.setColumnWidth(1, 15);  // 姓名
        writer.setColumnWidth(2, 12);  // 工号
        writer.setColumnWidth(3, 12);  // 出勤天数
        writer.setColumnWidth(4, 12);  // 净时长
        writer.setColumnWidth(5, 12);  // 平均时长
        writer.setColumnWidth(6, 15);  // 上午迟到次数
        writer.setColumnWidth(7, 15);  // 上午早退次数
        writer.setColumnWidth(8, 15);  // 下午迟到次数
        writer.setColumnWidth(9, 15);  // 下午早退次数
    }

    /**
     * 生成明细工作表
     * @param writer Excel写入器
     * @param results 明细结果列表
     */
    private void generateDetailSheet(ExcelWriter writer, List<LateEarlyResult> results) {
        // 创建明细工作表
        writer.setSheet(1);
        writer.renameSheet("明细");

        // 写入表头
        List<String> detailHeaders = Arrays.asList(
            "序号", "姓名", "工号", "日期", "净时长",
            "上午迟到次数", "上午早退次数", "下午迟到次数", "下午早退次数",
            "上午第一次进入时间", "上午最后一次出时间", "下午第一次进入时间", "下午最后一次出时间"
        );
        writer.writeRow(detailHeaders);

        // 写入明细数据
        for (int i = 0; i < results.size(); i++) {
            LateEarlyResult result = results.get(i);
            result.setSequence(i + 1);

            List<Object> row = Arrays.asList(
                result.getSequence(),
                result.getEmployeeName(),
                result.getEmployeeId() != null ? result.getEmployeeId() : "",
                result.getWorkDateString(),
                result.getFormattedDailyHours(),
                result.getMorningLateCount(),
                result.getMorningEarlyCount(),
                result.getAfternoonLateCount(),
                result.getAfternoonEarlyCount(),
                result.getMorningFirstEntryString(),
                result.getMorningLastExitString(),
                result.getAfternoonFirstEntryString(),
                result.getAfternoonLastExitString()
            );
            writer.writeRow(row);
        }

        // 设置列宽
        writer.setColumnWidth(0, 8);   // 序号
        writer.setColumnWidth(1, 15);  // 姓名
        writer.setColumnWidth(2, 12);  // 工号
        writer.setColumnWidth(3, 12);  // 日期
        writer.setColumnWidth(4, 12);  // 净时长
        writer.setColumnWidth(5, 15);  // 上午迟到次数
        writer.setColumnWidth(6, 15);  // 上午早退次数
        writer.setColumnWidth(7, 15);  // 下午迟到次数
        writer.setColumnWidth(8, 15);  // 下午早退次数
        writer.setColumnWidth(9, 18);  // 上午第一次进入时间
        writer.setColumnWidth(10, 18); // 上午最后一次出时间
        writer.setColumnWidth(11, 18); // 下午第一次进入时间
        writer.setColumnWidth(12, 18); // 下午最后一次出时间
    }

    /**
     * 处理综合统计文件的主要方法（工时统计 + 迟到早退统计）
     * @param file 上传的Excel文件
     * @return 处理后的Excel文件字节数组
     * @throws WorkTimeProcessException 处理异常
     */
    public byte[] processComprehensiveStatistics(MultipartFile file) throws WorkTimeProcessException {
        try {
            log.info("开始处理综合统计文件: {}", file.getOriginalFilename());

            // 1. 验证文件
            validateExcelFile(file);

            // 2. 读取Excel数据
            List<AttendanceRecord> records = readExcelData(file);
            log.info("成功读取{}条打卡记录", records.size());

            // 3. 计算综合统计
            List<LateEarlyResult> results = calculateComprehensiveStatistics(records);
            log.info("成功计算{}条综合统计记录", results.size());

            // 4. 排序结果
            List<LateEarlyResult> sortedResults = sortLateEarlyByPinyin(results);

            // 5. 生成输出Excel
            byte[] excelBytes = generateComprehensiveExcel(sortedResults);

            log.info("综合统计处理完成");
            return excelBytes;

        } catch (WorkTimeProcessException e) {
            throw e;
        } catch (Exception e) {
            log.error("处理综合统计文件失败", e);
            throw WorkTimeProcessException.unknownError("处理文件时发生未知错误", e);
        }
    }

    /**
     * 计算综合统计的核心方法（工时统计 + 迟到早退统计）
     * @param records 打卡记录列表
     * @return 综合统计结果列表
     * @throws WorkTimeProcessException 计算异常
     */
    private List<LateEarlyResult> calculateComprehensiveStatistics(List<AttendanceRecord> records)
            throws WorkTimeProcessException {
        try {
            // 1. 门禁数据去重处理
            List<AttendanceRecord> deduplicatedRecords = duplicateRecordProcessor.removeDuplicateRecords(records);
            log.info("门禁数据去重完成，{}",
                    duplicateRecordProcessor.getDeduplicationStats(records, deduplicatedRecords));

            // 2. 跨日验证，获取需要忽略的异常记录
            Set<AttendanceRecord> invalidRecords = crossDayValidator.validateCrossDayRecords(deduplicatedRecords);

            // 3. 过滤掉异常记录
            List<AttendanceRecord> validRecords = deduplicatedRecords.stream()
                .filter(record -> !invalidRecords.contains(record))
                .collect(Collectors.toList());

            log.info("跨日验证完成，去重后记录数: {}, 有效记录数: {}, 异常记录数: {}",
                    deduplicatedRecords.size(), validRecords.size(), invalidRecords.size());

            // 4. 计算全厂房工时统计并同时处理迟到早退统计
            List<LateEarlyResult> comprehensiveResults = calculateAllFactoryWorkTimeWithLateEarly(validRecords);
            log.info("综合统计完成，记录数: {}", comprehensiveResults.size());

            return comprehensiveResults;

        } catch (Exception e) {
            log.error("计算综合统计失败", e);
            throw WorkTimeProcessException.calculationError("综合统计计算失败");
        }
    }

    /**
     * 计算全厂房工时统计并同时处理迟到早退统计
     * @param records 有效记录列表
     * @return 综合统计结果（包含工时和迟到早退）
     */
    private List<LateEarlyResult> calculateAllFactoryWorkTimeWithLateEarly(List<AttendanceRecord> records) {
        // 按员工姓名、场所分组（不包含日期，以支持跨天工时计算）
        Map<String, List<AttendanceRecord>> groupedRecords = groupRecordsByEmployeeLocation(records);

        // 计算每个分组的跨天工时
        Map<String, Map<LocalDate, Double>> employeeDailyHours = new HashMap<>();

        for (Map.Entry<String, List<AttendanceRecord>> entry : groupedRecords.entrySet()) {
            String groupKey = entry.getKey();
            List<AttendanceRecord> groupRecords = entry.getValue();

            // 解析分组键：员工复合标识符|场所
            String[] keyParts = groupKey.split("\\|");
            String employeeCompositeKey = keyParts[0];
            String location = keyParts[1];

            // 计算该分组的跨天工时
            Map<LocalDate, Double> dailyHours = calculateCrossDayWorkTime(groupRecords);

            // 累加到员工的日工时中（使用复合标识符作为键）
            Map<LocalDate, Double> employeeHours = employeeDailyHours.computeIfAbsent(employeeCompositeKey, k -> new HashMap<>());
            for (Map.Entry<LocalDate, Double> dailyEntry : dailyHours.entrySet()) {
                employeeHours.merge(dailyEntry.getKey(), dailyEntry.getValue(), Double::sum);
            }
        }

        // 生成综合统计结果（工时 + 迟到早退）
        List<LateEarlyResult> results = new ArrayList<>();
        for (Map.Entry<String, Map<LocalDate, Double>> entry : employeeDailyHours.entrySet()) {
            String employeeCompositeKey = entry.getKey();
            Map<LocalDate, Double> dailyHours = entry.getValue();

            for (Map.Entry<LocalDate, Double> dailyEntry : dailyHours.entrySet()) {
                LocalDate date = dailyEntry.getKey();
                double hours = dailyEntry.getValue();

                // 从记录中获取员工姓名和工号
                AttendanceRecord sampleRecord = records.stream()
                    .filter(r -> employeeCompositeKey.equals(r.getEmployeeCompositeKey()))
                    .findFirst()
                    .orElse(null);

                if (sampleRecord == null) {
                    continue; // 跳过无法找到对应记录的情况
                }

                String employeeName = sampleRecord.getEmployeeName();
                String employeeId = sampleRecord.getEmployeeId();

                // 确定主要厂房（取当天工时最多的厂房）
                String primaryLocation = determinePrimaryLocation(employeeCompositeKey, date, records);

                // 创建基础结果对象
                LateEarlyResult result = new LateEarlyResult(employeeName, employeeId, date, hours, primaryLocation);

                // 如果是910/920厂房员工，计算迟到早退统计
                if ("910厂房".equals(primaryLocation) || "920厂房".equals(primaryLocation)) {
                    // 获取该员工当天的910/920厂房记录
                    List<AttendanceRecord> dayRecords = records.stream()
                        .filter(r -> employeeCompositeKey.equals(r.getEmployeeCompositeKey()) &&
                                    date.equals(r.getEventTime().toLocalDate()) &&
                                    ("910厂房".equals(r.getLocation()) || "920厂房".equals(r.getLocation())))
                        .collect(Collectors.toList());

                    if (!dayRecords.isEmpty()) {
                        // 计算迟到早退情况
                        calculateDailyLateEarlyForResult(result, dayRecords);
                        result.setParticipateInLateEarlyStats(true);
                    }
                }

                results.add(result);
            }
        }

        return results;
    }

    /**
     * 为结果对象计算迟到早退情况
     * @param result 结果对象
     * @param dayRecords 当天的打卡记录
     */
    private void calculateDailyLateEarlyForResult(LateEarlyResult result, List<AttendanceRecord> dayRecords) {
        if (result == null || dayRecords == null || dayRecords.isEmpty()) {
            return;
        }

        // 按时间排序
        dayRecords.sort(Comparator.comparing(AttendanceRecord::getEventTime));

        // 分离上午和下午记录
        List<AttendanceRecord> morningRecords = new ArrayList<>();
        List<AttendanceRecord> afternoonRecords = new ArrayList<>();

        for (AttendanceRecord record : dayRecords) {
            LocalDateTime eventTime = record.getEventTime();
            if (eventTime.getHour() < 12) {
                morningRecords.add(record);
            } else {
                afternoonRecords.add(record);
            }
        }

        // 计算上午迟到早退
        calculateMorningLateEarly(result, morningRecords);

        // 计算下午迟到早退
        calculateAfternoonLateEarly(result, afternoonRecords);
    }

    /**
     * 确定员工在指定日期的主要厂房位置
     * @param employeeCompositeKey 员工复合标识符
     * @param date 日期
     * @param records 所有记录
     * @return 主要厂房位置
     */
    private String determinePrimaryLocation(String employeeCompositeKey, LocalDate date, List<AttendanceRecord> records) {
        if (employeeCompositeKey == null || date == null || records == null || records.isEmpty()) {
            return "未知厂房";
        }

        Map<String, Double> locationHours = new HashMap<>();

        // 按厂房分组计算工时
        Map<String, List<AttendanceRecord>> locationGroups = records.stream()
            .filter(r -> r != null &&
                        employeeCompositeKey.equals(r.getEmployeeCompositeKey()) &&
                        r.getEventTime() != null &&
                        date.equals(r.getEventTime().toLocalDate()) &&
                        r.getLocation() != null)
            .collect(Collectors.groupingBy(AttendanceRecord::getLocation));

        for (Map.Entry<String, List<AttendanceRecord>> entry : locationGroups.entrySet()) {
            String location = entry.getKey();
            List<AttendanceRecord> locationRecords = entry.getValue();

            if (location != null && !locationRecords.isEmpty()) {
                // 计算该厂房的工时
                Map<LocalDate, Double> dailyHours = calculateCrossDayWorkTime(locationRecords);
                double hours = dailyHours.getOrDefault(date, 0.0);
                locationHours.put(location, hours);
            }
        }

        // 返回工时最多的厂房
        return locationHours.entrySet().stream()
            .filter(entry -> entry.getKey() != null && entry.getValue() != null)
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse("未知厂房");
    }





    /**
     * 生成综合统计Excel文件（包含工时统计和迟到早退统计）
     * @param results 综合统计结果列表
     * @return Excel文件字节数组
     * @throws WorkTimeProcessException 生成异常
     */
    private byte[] generateComprehensiveExcel(List<LateEarlyResult> results) throws WorkTimeProcessException {
        try {
            // 创建临时文件
            String tempFileName = System.currentTimeMillis() + "_comprehensive_result.xlsx";
            String tempFilePath = tempDir + File.separator + tempFileName;

            try (ExcelWriter writer = ExcelUtil.getWriter(tempFilePath)) {
                // 生成综合汇总工作表
                generateComprehensiveSummarySheet(writer, results);

                // 生成综合明细工作表
                generateComprehensiveDetailSheet(writer, results);

                // 设置默认工作表为汇总
                writer.setSheet(0);
            }

            // 读取文件内容
            File tempFile = new File(tempFilePath);
            byte[] fileBytes = FileUtil.readBytes(tempFile);

            // 清理临时文件
            FileUtil.del(tempFile);

            return fileBytes;

        } catch (Exception e) {
            log.error("生成综合统计Excel失败", e);
            throw WorkTimeProcessException.ioError("生成结果文件失败", e);
        }
    }

    /**
     * 生成综合汇总工作表（包含工时和迟到早退统计）
     * @param writer Excel写入器
     * @param results 综合统计结果列表
     */
    private void generateComprehensiveSummarySheet(ExcelWriter writer, List<LateEarlyResult> results) {
        if (writer == null || results == null) {
            return;
        }

        // 设置汇总工作表
        writer.setSheet(0);
        writer.renameSheet("汇总");

        // 写入表头
        List<String> headers = Arrays.asList(
            "序号", "姓名", "工号", "出勤天数", "净时长", "平均时长",
            "上午迟到次数", "上午早退次数", "下午迟到次数", "下午早退次数"
        );
        writer.writeRow(headers);

        // 按员工复合标识符分组并汇总数据
        Map<String, List<LateEarlyResult>> employeeGroups = results.stream()
            .collect(Collectors.groupingBy(LateEarlyResult::getEmployeeCompositeKey));

        List<LateEarlyResult> summaryResults = new ArrayList<>();
        for (Map.Entry<String, List<LateEarlyResult>> entry : employeeGroups.entrySet()) {
            String employeeCompositeKey = entry.getKey();
            List<LateEarlyResult> employeeResults = entry.getValue();

            // 从第一条记录中获取员工姓名和工号
            LateEarlyResult firstResult = employeeResults.get(0);
            String employeeName = firstResult.getEmployeeName();
            String employeeId = firstResult.getEmployeeId();

            // 计算汇总数据
            int attendanceDays = employeeResults.size();
            double totalHours = employeeResults.stream().mapToDouble(LateEarlyResult::getDailyHours).sum();
            double averageHours = attendanceDays > 0 ? totalHours / attendanceDays : 0.0;

            // 计算迟到早退总次数
            int totalMorningLate = employeeResults.stream().mapToInt(LateEarlyResult::getMorningLateCount).sum();
            int totalMorningEarly = employeeResults.stream().mapToInt(LateEarlyResult::getMorningEarlyCount).sum();
            int totalAfternoonLate = employeeResults.stream().mapToInt(LateEarlyResult::getAfternoonLateCount).sum();
            int totalAfternoonEarly = employeeResults.stream().mapToInt(LateEarlyResult::getAfternoonEarlyCount).sum();

            // 创建汇总记录
            LateEarlyResult summary = new LateEarlyResult(employeeName, employeeId, attendanceDays, totalHours, averageHours);
            summary.setMorningLateCount(totalMorningLate);
            summary.setMorningEarlyCount(totalMorningEarly);
            summary.setAfternoonLateCount(totalAfternoonLate);
            summary.setAfternoonEarlyCount(totalAfternoonEarly);
            summaryResults.add(summary);
        }

        // 按姓名排序
        summaryResults.sort((r1, r2) -> comparePinyin(r1.getEmployeeName(), r2.getEmployeeName()));

        // 写入汇总数据
        for (int i = 0; i < summaryResults.size(); i++) {
            LateEarlyResult summary = summaryResults.get(i);
            summary.setSequence(i + 1);

            List<Object> row = Arrays.asList(
                summary.getSequence(),
                summary.getEmployeeName(),
                summary.getEmployeeId() != null ? summary.getEmployeeId() : "",
                summary.getAttendanceDays(),
                summary.getFormattedTotalHours(),
                summary.getFormattedAverageHours(),
                summary.getMorningLateCount(),
                summary.getMorningEarlyCount(),
                summary.getAfternoonLateCount(),
                summary.getAfternoonEarlyCount()
            );
            writer.writeRow(row);
        }

        // 设置列宽
        writer.setColumnWidth(0, 8);   // 序号
        writer.setColumnWidth(1, 15);  // 姓名
        writer.setColumnWidth(2, 12);  // 工号
        writer.setColumnWidth(3, 12);  // 出勤天数
        writer.setColumnWidth(4, 12);  // 净时长
        writer.setColumnWidth(5, 12);  // 平均时长
        writer.setColumnWidth(6, 15);  // 上午迟到次数
        writer.setColumnWidth(7, 15);  // 上午早退次数
        writer.setColumnWidth(8, 15);  // 下午迟到次数
        writer.setColumnWidth(9, 15);  // 下午早退次数
    }

    /**
     * 生成综合明细工作表（包含工时和迟到早退统计）
     * @param writer Excel写入器
     * @param results 综合统计结果列表
     */
    private void generateComprehensiveDetailSheet(ExcelWriter writer, List<LateEarlyResult> results) {
        if (writer == null || results == null) {
            return;
        }

        // 创建明细工作表
        writer.setSheet(1);
        writer.renameSheet("明细");

        // 写入表头
        List<String> headers = Arrays.asList(
            "序号", "姓名", "工号", "日期", "净时长",
            "上午迟到次数", "上午早退次数", "下午迟到次数", "下午早退次数",
            "上午第一次进入时间", "上午最后一次出时间", "下午第一次进入时间", "下午最后一次出时间"
        );
        writer.writeRow(headers);

        // 写入明细数据
        for (int i = 0; i < results.size(); i++) {
            LateEarlyResult result = results.get(i);
            result.setSequence(i + 1);

            List<Object> row = Arrays.asList(
                result.getSequence(),
                result.getEmployeeName(),
                result.getEmployeeId() != null ? result.getEmployeeId() : "",
                result.getWorkDateString(),
                result.getFormattedDailyHours(),
                result.getMorningLateCount(),
                result.getMorningEarlyCount(),
                result.getAfternoonLateCount(),
                result.getAfternoonEarlyCount(),
                result.getMorningFirstEntryString(),
                result.getMorningLastExitString(),
                result.getAfternoonFirstEntryString(),
                result.getAfternoonLastExitString()
            );
            writer.writeRow(row);
        }

        // 设置列宽
        writer.setColumnWidth(0, 8);   // 序号
        writer.setColumnWidth(1, 15);  // 姓名
        writer.setColumnWidth(2, 12);  // 工号
        writer.setColumnWidth(3, 12);  // 日期
        writer.setColumnWidth(4, 12);  // 净时长
        writer.setColumnWidth(5, 15);  // 上午迟到次数
        writer.setColumnWidth(6, 15);  // 上午早退次数
        writer.setColumnWidth(7, 15);  // 下午迟到次数
        writer.setColumnWidth(8, 15);  // 下午早退次数
        writer.setColumnWidth(9, 18);  // 上午第一次进入时间
        writer.setColumnWidth(10, 18); // 上午最后一次出时间
        writer.setColumnWidth(11, 18); // 下午第一次进入时间
        writer.setColumnWidth(12, 18); // 下午最后一次出时间
    }



}