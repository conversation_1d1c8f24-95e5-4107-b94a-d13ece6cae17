package com.cirpoint.service;

import cn.hutool.json.JSONArray;
import com.cirpoint.service.archive.ArchiveService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2025-01-17
 * @description 档案系统服务测试类
 */
@SpringBootTest
public class ArchiveServiceTest {

	@Autowired
	private ArchiveService archiveService;

	@BeforeEach
	void setUp() {
//		FileUtil.clean("E:/DpkgPushTemp/");
//		ReflectionTestUtils.setField(archiveService, "pushTempPath", "E:/DpkgPushTemp/");
//		ReflectionTestUtils.setField(archiveService, "fileUploadPath", "D:/DataPkgFile/");
	}

	/**
	 * 测试收集过程节点数据包功能
	 */
	@Test
	void testCollectProcess() {
		// 调用测试方法
		String phaseTreeId = "3";
		archiveService.collectPackage(phaseTreeId, "adm", "管理员");
	}

	/**
	 * 测试查询数据包状态功能
	 */
	@Test
	void testQueryStatus() {
		// 调用测试方法
		String phaseTreeId = "4";
		JSONArray objects = archiveService.queryPackageStatus(phaseTreeId);
		System.out.println(objects);
	}

}
