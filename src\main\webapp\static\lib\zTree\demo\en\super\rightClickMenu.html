<!DOCTYPE html>
<HTML>
<HEAD>
	<TITLE> ZTREE DEMO - select menu</TITLE>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<link rel="stylesheet" href="../../../css/demo.css" type="text/css">
	<link rel="stylesheet" href="../../../css/zTreeStyle/zTreeStyle.css" type="text/css">
	<script type="text/javascript" src="../../../js/jquery-1.4.4.min.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.core.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.excheck.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.exedit.js"></script>
	<SCRIPT type="text/javascript">
		<!--
		var setting = {
			view: {
				dblClickExpand: false
			},
			check: {
				enable: true
			},
			callback: {
				onRightClick: OnRightClick
			}
		};

		var zNodes =[
			{id:1, name:"No right-click menu 1", open:true, noR:true,
				children:[
					   {id:11, name:"Leaf Node 1-1", noR:true},
					   {id:12, name:"Leaf Node 1-2", noR:true}

				]},
			{id:2, name:"Right-click 2", open:true,
				children:[
					   {id:21, name:"Leaf Node 2-1"},
					   {id:22, name:"Leaf Node 2-2"},
					   {id:23, name:"Leaf Node 2-3"},
					   {id:24, name:"Leaf Node 2-4"}
				]},
			{id:3, name:"Right-click 3", open:true,
				children:[
					   {id:31, name:"Leaf Node 3-1"},
					   {id:32, name:"Leaf Node 3-2"},
					   {id:33, name:"Leaf Node 3-3"},
					   {id:34, name:"Leaf Node 3-4"}
				]}
  	 	];

		function OnRightClick(event, treeId, treeNode) {
			if (!treeNode && event.target.tagName.toLowerCase() != "button" && $(event.target).parents("a").length == 0) {
				zTree.cancelSelectedNode();
				showRMenu("root", event.clientX, event.clientY);
			} else if (treeNode && !treeNode.noR) {
				zTree.selectNode(treeNode);
				showRMenu("node", event.clientX, event.clientY);
			}
		}

		function showRMenu(type, x, y) {
			$("#rMenu ul").show();
			if (type=="root") {
				$("#m_del").hide();
				$("#m_check").hide();
				$("#m_unCheck").hide();
			} else {
				$("#m_del").show();
				$("#m_check").show();
				$("#m_unCheck").show();
			}

            y += document.body.scrollTop;
            x += document.body.scrollLeft;
            rMenu.css({"top":y+"px", "left":x+"px", "visibility":"visible"});

			$("body").bind("mousedown", onBodyMouseDown);
		}
		function hideRMenu() {
			if (rMenu) rMenu.css({"visibility": "hidden"});
			$("body").unbind("mousedown", onBodyMouseDown);
		}
		function onBodyMouseDown(event){
			if (!(event.target.id == "rMenu" || $(event.target).parents("#rMenu").length>0)) {
				rMenu.css({"visibility" : "hidden"});
			}
		}
		var addCount = 1;
		function addTreeNode() {
			hideRMenu();
			var newNode = { name:"newNode " + (addCount++)};
			if (zTree.getSelectedNodes()[0]) {
				newNode.checked = zTree.getSelectedNodes()[0].checked;
				zTree.addNodes(zTree.getSelectedNodes()[0], newNode);
			} else {
				zTree.addNodes(null, newNode);
			}
		}
		function removeTreeNode() {
			hideRMenu();
			var nodes = zTree.getSelectedNodes();
			if (nodes && nodes.length>0) {
				if (nodes[0].children && nodes[0].children.length > 0) {
					var msg = "If you delete this node will be deleted along with sub-nodes. \n\nPlease confirm!";
					if (confirm(msg)==true){
						zTree.removeNode(nodes[0]);
					}
				} else {
					zTree.removeNode(nodes[0]);
				}
			}
		}
		function checkTreeNode(checked) {
			var nodes = zTree.getSelectedNodes();
			if (nodes && nodes.length>0) {
				zTree.checkNode(nodes[0], checked, true);
			}
			hideRMenu();
		}
		function resetTree() {
			hideRMenu();
			$.fn.zTree.init($("#treeDemo"), setting, zNodes);
		}

		var zTree, rMenu;
		$(document).ready(function(){
			$.fn.zTree.init($("#treeDemo"), setting, zNodes);
			zTree = $.fn.zTree.getZTreeObj("treeDemo");
			rMenu = $("#rMenu");
		});
		//-->
	</SCRIPT>
	<style type="text/css">
div#rMenu {position:absolute; visibility:hidden; top:0; background-color: #555;text-align: left;padding: 2px;}
div#rMenu ul li{
	margin: 1px 0;
	padding: 0 5px;
	cursor: pointer;
	list-style: none outside none;
	background-color: #DFDFDF;
}
	</style>
 </HEAD>

<BODY>
<h1>Right-click Menu</h1>
<h6>[ File Path: super/rightClickMenu.html ]</h6>
<div class="content_wrap">
	<div class="zTreeDemoBackground left">
		<ul id="treeDemo" class="ztree"></ul>
	</div>
	<div class="right">
		<ul class="info">
			<li class="title"><h2>Explanation of implementation method</h2>
				<ul class="list">
				<li>Use 'beforeRightClick / onRightClick' callback function achieve right-click menu.</li>
				<li class="highlight_red">Demo's menu is ugly, you can use a custom menu fine style.</li>
				</ul>
			</li>
		</ul>
	</div>
</div>
<div id="rMenu">
	<ul>
		<li id="m_add" onclick="addTreeNode();">Add Node</li>
		<li id="m_del" onclick="removeTreeNode();">Delete Node</li>
		<li id="m_check" onclick="checkTreeNode(true);">Check Node</li>
		<li id="m_unCheck" onclick="checkTreeNode(false);">Uncheck Node</li>
		<li id="m_reset" onclick="resetTree();">Resume zTree</li>
	</ul>
</div>
</BODY>
</HTML>