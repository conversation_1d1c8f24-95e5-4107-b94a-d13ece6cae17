package com.cirpoint.service.archive;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.util.ArchiveLogger;
import com.cirpoint.util.CommonUtil;
import com.cirpoint.util.Util;
import java.io.File;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 照片档案服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ArchivePhotoService extends ArchiveBaseService {

	@Autowired
	private ArchivePackageService archivePackageService;

	/**
	 * 收集照片的数据包
	 */
	public void collectPhotoPackage(String phaseTreeId, String pushUserFullname, JSONObject phaseModelObj, ArchiveLogger logger, GlobalRollSortManager globalSortManager) {
		try {
			String packageType = "PHOTO";
			logger.info("开始查询阶段下的自动采集照片记录");
			//查询阶段下面所有的自动采集的照片
			String sql = "select * " +
					"from PROCESS_CONTROL_RESULT " +
					"where FILE_TYPE = '影像记录' " +
					"  AND GATHERING_METHOD = '自动采集' " +
					"  AND FILEPATH IS NOT NULL " +
					"  AND NODECODE in (select ID " +
					"                   from DATA_PACKAGE " +
					"                   where REFTREEID in " +
					"                         (select TREEID from DATAPACKAGETREE start with TREEID = " + phaseTreeId + " connect by prior TREEID = PARENTID)) " +
					"ORDER BY CREATE_TIMESTAMP";

			logger.info("=== 执行SQL查询 [collectPhotoPackage] ===");
			logger.info("SQL语句: {}", sql);
			logger.info("参数: phaseTreeId = {}", phaseTreeId);
			logger.info("参数: pushUserFullname = {}", pushUserFullname);
			logger.info("参数: phaseModelObj.NODENAME = {}", phaseModelObj.getStr("NODENAME", ""));

			JSONArray photos = Util.postQuerySql(sql);

			if (photos.isEmpty()) {
				logger.info(String.format("阶段[%s]没有自动采集的照片记录，跳过处理", phaseTreeId));
				return;
			}

			logger.info(String.format("找到 %d 条照片记录", photos.size()));
			String nodename = phaseModelObj.getStr("NODENAME");
			String pkgPath = generateUniquePath(1, packageType);
			logger.info(String.format("开始创建照片数据包，路径: %s, 数据包名称: %s_照片", pkgPath, nodename));

			String logId = insertArchivePushLog(phaseTreeId,
					phaseModelObj,
					packageType,
					nodename + "_照片",
					pkgPath);

			// 如果logId为null，表示该数据包已成功收集和推送，直接返回
			if (logId == null) {
				logger.info("照片数据包已存在且成功推送，跳过处理");
				return;
			}

			try {
				logger.info("开始创建照片数据包XML文件");
				createPackageXml(pkgPath, (roll, archive) -> {
					try {
						String rollInnerId = phaseTreeId + "_roll_photo";
						String rollPath = pkgPath + File.separator + "照片" + File.separator + "案卷" + File.separator;
						logger.info(String.format("创建照片案卷目录: %s, 数据包名称: %s", rollPath, nodename + "_照片"));
						FileUtil.mkdir(rollPath);
						String rollNo = "12010" + nodename + "-001";
						int rollSort = globalSortManager.getNextPhotoSort();
						logger.info(String.format("创建照片案卷XML，案卷号: %s, 排序号: %d", rollNo, rollSort));
						createPhotoRollXml(rollInnerId, nodename, pushUserFullname, rollPath, pkgPath, roll, rollNo, nodename, rollSort);

						String archPath = pkgPath + File.separator + "照片" + File.separator + "档案" + File.separator;
						logger.info(String.format("创建照片档案目录: %s, 数据包名称: %s", archPath, nodename + "_照片"));
						FileUtil.mkdir(archPath);

						//组装照片附件
						logger.info("开始组装照片附件信息");
						
						// 构建Excel表头
						JSONArray headers = new JSONArray();
						headers.add("序号");
						headers.add("名称");
						headers.add("地址");
						
						// 构建Excel数据
						JSONArray data = new JSONArray();
						for (int i = 0; i < photos.size(); i++) {
							JSONObject photo = photos.getJSONObject(i);
							String fileName = photo.getStr("FILE_NAME");
							String filePath = photo.getStr("FILEPATH");
							
							JSONArray row = new JSONArray();
							row.add(i + 1);  // 序号，从1开始
							row.add(fileName);  // 名称
							row.add(filePath);  // 地址
							data.add(row);
							
							logger.info(String.format("添加照片文件: %s, 路径: %s", fileName, filePath));
						}
						
						// 设置Excel格式参数
						JSONArray columnWidths = new JSONArray();
						columnWidths.add(5);   // 序号列宽
						columnWidths.add(60);  // 名称列宽（修复：不超过255限制）
						columnWidths.add(120);  // 地址列宽（修复：设为最大允许值255）
						int rowHeight = 25;
						
						//将Excel文件写入到名称为型号质量记录照片.xlsx的文件中
						String xlsxFileName = "型号质量记录照片.xlsx";
						logger.info(String.format("创建型号质量记录照片Excel文件: %s", xlsxFileName));
						File excelFile = CommonUtil.createExcelFile("型号质量记录照片", headers, data, columnWidths, rowHeight);
						
						// 将生成的Excel文件复制到目标路径
						FileUtil.copy(excelFile, new File(archPath + xlsxFileName), true);
						int archSort = 1; // 照片档案只有一个，排序号为1
						logger.info(String.format("创建照片档案XML，文件名: %s, 排序号: %d", xlsxFileName, archSort));
						createPhotoArchXml(phaseTreeId + "_arch_photo", rollInnerId, photos.size(), xlsxFileName, nodename, archPath, archive, pkgPath, rollNo, archSort);
						logger.info("照片档案XML创建完成");
					} catch (Exception e) {
						logger.error("创建照片数据包XML文件失败", e);
						throw e;
					}
				});

				// 更新收集状态
				logger.info("照片数据包收集完成，开始更新状态");
				updateCollectStatus(logId, "SUCCESS", null,
						countPdfFiles(pkgPath), 1, countArchiveFiles(pkgPath));

				// 压缩并推送数据包
				logger.info("开始推送照片数据包");
				archivePackageService.zipAndPushPackage(pkgPath, logId);
				logger.info("照片数据包推送完成");

			} catch (Exception e) {
				// 更新失败状态
				logger.error("照片数据包处理失败", e);
				updateCollectStatus(logId, "FAILED", e.getMessage(), 0, 0, 0);
				throw new RuntimeException("收集照片数据包失败", e);
			}
		} catch (Exception e) {
			logger.error("收集照片数据包过程中发生错误", e);
			throw e;
		}
	}

	/**
	 * 创建照片案卷的xml
	 */
	private void createPhotoRollXml(String innerId, String nodeName, String rollBuilderName, String rollPath, String pkgPath, Element rollEl, String rollNo, String xinghao, int rollSort) {
		// 创建案卷的xml文档
		Document rollDoc = DocumentHelper.createDocument();
		// obj: 根节点，表示一个案卷对象
		Element objEl = rollDoc.addElement("obj");
		// innerId: 案卷的唯一标识
		objEl.addElement("innerId").addText(innerId);
		// classId: 案卷类型，固定值为航天产品案卷
		objEl.addElement("classId").addText(ROLL_CLASS_ID);
		// attrs: 案卷属性信息节点
		Element attrsEl = objEl.addElement("attrs");
		// rollName: 案卷名称
		attrsEl.addElement("案卷名称").addText(nodeName + "型号质量记录照片");
		// rollId: 案卷号
		attrsEl.addElement("案卷号").addText(rollNo);
		// secretId: 密级，默认20表示内部
		attrsEl.addElement("密级").addText("10");
		// retentionPeriod: 保管期限，默认30年
		attrsEl.addElement("保管期限").addText("30");
		// rollBuilderName: 立卷人
		attrsEl.addElement("立卷人").addText(rollBuilderName);
		// rollBuildTime: 立卷日期
		attrsEl.addElement("立卷日期").addText(DateUtil.today());
		// rollXH: 案卷序号
		attrsEl.addElement("序号").addText("1");
		attrsEl.addElement("备注").addText(nodeName + "型号照片案卷");
		attrsEl.addElement("项目代号").addText(nodeName);
		attrsEl.addElement("总件数").addText("1");
		attrsEl.addElement("总页数").addText("1");
		attrsEl.addElement("xinghao").addText(nodeName);
		// sort: 排序字段
		attrsEl.addElement("sort").addText(String.valueOf(rollSort));

		// 保存案卷xml文件
		writeDocumentToXml(rollDoc, rollPath, "案卷");
		rollEl.addElement("path").addText(FileUtil.subPath(pkgPath, rollPath) + "案卷.xml");
	}

	/**
	 * 创建照片档案的xml
	 */
	private void createPhotoArchXml(String innerId, String rollInnerId, int photoCount, String fileName, String nodename, String archiveFolderPath, Element archiveEl, String pkgPath, String rollNo, int archSort) {
		//创建档案的xml文件
		Document doc = DocumentHelper.createDocument();
		// obj: 根节点，表示一个档案对象
		Element objEl = doc.addElement("obj");
		// innerId: 档案的唯一标识，
		objEl.addElement("innerId").addText(innerId);
		// classId: 档案类型，由档案系统提供
		objEl.addElement("classId").addText(ARCH_CLASS_ID);
		// attrs: 档案属性信息节点
		Element attrsEl = objEl.addElement("attrs");
		attrsEl.addElement("题名").addText(nodename + "型号质量记录照片");
		attrsEl.addElement("密级").addText("10");
		attrsEl.addElement("保管期限").addText("30");
		attrsEl.addElement("编制日期").addText(DateUtil.today());
		attrsEl.addElement("型号代号").addText(nodename);
		attrsEl.addElement("序号").addText("1");
		attrsEl.addElement("案卷InnerId").addText(rollInnerId);
		attrsEl.addElement("借阅方式").addText("digitization");
		attrsEl.addElement("页数").addText(String.valueOf(photoCount));
		attrsEl.addElement("归档单位").addText("质量技术处");
		attrsEl.addElement("案卷号").addText(rollNo);
		// sort: 排序字段
		attrsEl.addElement("sort").addText(String.valueOf(archSort));

		Element filesEl = objEl.addElement("files");
		Element fileEl = filesEl.addElement("file");
		Element fileAttrsEl = fileEl.addElement("attrs");
		fileAttrsEl.addElement("path").addText(fileName);
		fileAttrsEl.addElement("题名").addText(nodename + "型号质量记录照片");
		// 保存档案xml文件
		writeDocumentToXml(doc, archiveFolderPath, "档案");
		archiveEl.addElement("path").addText(FileUtil.subPath(pkgPath, archiveFolderPath) + "档案.xml");
	}
}
