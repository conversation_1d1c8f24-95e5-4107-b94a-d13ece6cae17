package com.cirpoint.util;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class AccessToOracleImporterTest {

	@Autowired
	private AccessToOracleImporter importer;

	@Test
	public void importData() throws Exception {


// 使用示例
		importer.importData(
				"C:\\TestOut\\Database1.mdb",
				"D:\\eclipse-work\\FileHandle\\src\\main\\resources\\template\\field_mapping.json"
		);
	}
}
