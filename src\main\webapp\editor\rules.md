# 在线代码编辑器修改指南（v1.0.1）

## 项目概述

这是一个基于Web的在线代码编辑器，主要功能包括（v1.0.1）：
- 文件树浏览和管理
- 代码编辑（基于Monaco编辑器）
- 文件操作（创建、删除、重命名等）
- 密码保护访问
- 多标签页编辑
- 多路径管理

## 项目结构

```
src/main/webapp/
├── editor.html              # 主HTML页面
├── static/
│   ├── css/
│   │   └── main.css         # 主样式文件
│   ├── js/
│   │   └── main.js          # 主JavaScript文件
│   └── lib/                 # 第三方库
│       ├── monaco-editor    # 代码编辑器核心
│       ├── layui            # UI框架
│       ├── zTree            # 文件树组件
│       ├── jquery           # jQuery库
│       └── crypto-js        # 加密库
└── ...

src/main/java/com/cirpoint/
├── controller/
│   └── EditorController.java # 后端控制器
├── service/
│   └── EditorService.java    # 后端服务
└── ...
```

## 核心组件

### 1. 前端组件

#### 1.1 文件树 (ZTree)
- 位于左侧面板
- 实现文件和目录的浏览
- 支持右键菜单操作

#### 1.2 Monaco编辑器
- 位于右侧主面板
- 支持多种编程语言的语法高亮
- 代码自动完成和提示

#### 1.3 标签页管理
- 支持多文件同时打开
- 可拖拽排序
- 右键菜单支持关闭操作

#### 1.4 密码验证
- 初始访问时需要密码验证
- 使用CryptoJS进行密码验证

### 2. 后端组件

#### 2.1 EditorController
- 处理前端请求
- 提供文件操作API
- 管理密码验证

#### 2.2 EditorService
- 实现文件系统操作
- 处理文件内容的读写
- 提供文件搜索和导出功能

## 修改指南

### 添加新功能

#### 1. 添加新的文件操作
如需添加新的文件操作（如复制文件），请遵循以下步骤：

1. 在`EditorService.java`中添加相应的服务方法
2. 在`EditorController.java`中添加对应的API端点
3. 在`main.js`中添加前端处理逻辑
4. 如需要，在右键菜单中添加新选项（`editor.html`和`main.js`）

#### 2. 扩展编辑器功能
如需扩展Monaco编辑器功能：

1. 在`main.js`的`initMonacoEditor()`函数中添加配置
2. 添加新的编辑器事件处理在`bindEditorEvents()`函数中

#### 3. 修改UI样式
修改UI样式时：

1. 主要样式定义在`main.css`中
2. 布局结构在`editor.html`中定义
3. 注意保持与Layui框架的兼容性

### 修改现有功能

#### 1. 修改文件树行为
文件树相关代码位于：
- `main.js`中的`initZTree()`函数
- 事件处理函数：`onClick`, `onRightClick`, `beforeExpand`等

#### 2. 修改标签页行为
标签页相关代码位于：
- `main.js`中的标签页处理函数
- 右键菜单处理在`showTabRightMenu()`和`bindTabRightMenuEvents()`

#### 3. 修改密码验证
密码验证相关代码：
- 前端：`main.js`中的`bindPasswordEvents()`
- 后端：`EditorController.java`中的`verifyPassword()`方法


## API参考

### 前端API

#### 文件树操作
- `refreshFileTree()`: 刷新文件树
- `addFile()`: 添加新文件
- `addFolder()`: 添加新文件夹
- `deleteNode()`: 删除节点
- `renameNode()`: 重命名节点

#### 编辑器操作
- `saveFile()`: 保存当前文件
- `getLanguageByExt()`: 根据扩展名获取语言

### 后端API

#### 文件操作
- `GET /editor/tree`: 获取文件树
- `GET /editor/content`: 获取文件内容
- `POST /editor/save`: 保存文件内容
- `POST /editor/create`: 创建文件或文件夹
- `DELETE /editor/delete`: 删除文件或文件夹
- `POST /editor/rename`: 重命名文件或文件夹
- `GET /editor/download`: 下载文件

#### 路径管理
- `POST /editor/addAndEnablePath`: 添加并启用路径
- `GET /multiPath/list`: 获取所有路径
- `GET /multiPath/enabled`: 获取已启用的路径
- `POST /multiPath/add`: 添加新路径
- `POST /multiPath/remove`: 删除路径
- `POST /multiPath/enable`: 启用路径
- `POST /multiPath/disable`: 禁用路径
- `GET /multiPath/path/{id}`: 获取单个路径信息

#### 安全
- `POST /editor/verify`: 验证密码