package com.cirpoint.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.util.Util;
import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import static com.cirpoint.util.Util.getCellValue;

public class ParsingExcel2 {
	private static final String FILEPATH_KEY = "FILEPATH";
	private static final String TREEID_KEY = "TREEID";
	private static final String CREATOR_KEY = "CREATOR";
	private static final String FILENAME_KEY = "FILENAME";

	public static int parsingExcel1(String filePath, String filePath1, String treeId, String creator, String filename) {
		return parseExcel(filePath, filePath1, treeId, creator, filename, "Thing.Fn.ProductQuality", "insert_P_MULTILAYERMACHINING",
				new String[]{"B2", "F2", "I2", "H6", "H10", "H5", "H12", "H13", "H14", "H15", "H16", "H17", "H18", "C19", "C20", "G20", "J20"},
				new String[]{"型号", "多层名称", "多层代号", "多层物料编号", "件数", "隔热层层数", "重量(g)", "底膜", "面膜", "包边要求<√/×>", "二次表面镜电阻(kΩ)", "最上层镀铝膜到接地线末端导通阻值(Ω)", "多层接地点到接地线末端导通阻值(Ω)", "影像记录", "结论", "检验人员", "检验时间"},
				"热控多层");
	}

	public static int parsingExcel2(String filePath, String filePath1, String treeId, String creator, String filename) {
		return parseExcel(filePath, filePath1, treeId, creator, filename, "Thing.Fn.ProductQuality", "insert_P_THERMOTUBEMACHINING",
				new String[]{"B2", "G2", "K2", "I2", "J13", "H18", "J10", "C15", "G15", "D15", "H15", "E15", "I15", "C16", "G16", "C17", "G17", "C8", "G8", "C9", "G9", "C7", "G7", "B20", "B19", "G20", "J20", "C6", "G6"},
				new String[]{"型号", "产品名称", "产品代号", "产品编号", "外观", "重量(g)", "X射线探伤", "长度(mm)-设计값", "长度(mm)-实测값", "宽度(mm)-设计값", "宽度(mm)-实测값", "高度(mm)-设计값", "高度(mm)-实测값", "平面度(mm)-设计값", "平面度(mm)-实测값", "直线度(mm)-设计값", "直线度(mm)-实测값", "耐温性能(℃)-设计값", "耐温性能(℃)-实测값", "等温性能(℃)-设计값", "等温性能(℃)-实测값", "管壳漏率(Pa·m3/s)-设计값", "管壳漏率(Pa·m3/s)-实测값", "结论", "影像记录", "检验人员", "检验时间", "充氨量-设计값", "充氨量-实测값"},
				"热管");
	}

	public static int parsingExcel3(String filePath, String filePath1, String treeId, String creator, String filename) {
		return parseExcel(filePath, filePath1, treeId, creator, filename, "Thing.Fn.ProductQuality", "insert_P_ThermalControlSpraying",
				new String[]{"B2", "H6", "F2", "G3", "I13", "H9", "H18", "D14", "H14", "D15", "H15", "D17", "H17", "B20", "B19", "F20", "I20"},
				new String[]{"型号", "试片号", "零件名称", "涂层", "外观", "产品重量(g)", "喷涂增重(g)", "发射率ε-设计값", "发射率ε-实测값", "太阳吸收率α-设计값", "太阳吸收率α-实测값", "厚度(um)-设计값", "厚度(um)-实测값", "结论", "影像记录", "检验人员", "检验时间"},
				"热控喷涂");
	}

	public static int parsingExcel4(String filePath, String filePath1, String treeId, String creator, String filename) {
		return parseExcel(filePath, filePath1, treeId, creator, filename, "Thing.Fn.ProductQuality", "insert_P_OSRPASTE",
				new String[]{"B2", "K3", "F2", "J15", "I11", "H21", "J16", "C18", "I18", "C19", "I19", "C20", "I20", "I6", "I7", "B23", "B22", "G23", "J23"},
				new String[]{"型号", "试片号", "产品名称", "外观", "重量(g)", "OSR增量(g)", "粘贴位置及尺寸", "太阳吸收比-设计값", "太阳吸收比-实测값", "半球发射率-设计값", "半球发射率-实测값", "电阻值-设计값", "电阻값-实测값", "规格-40*40mm", "规格-40*40mm", "结论", "影像记录", "检验人员", "检验时间"},
				"OSR粘贴");
	}

	public static XSSFSheet getSheet(String filePath) {
		XSSFSheet sheet = null;
		File file = new File(filePath);
		if (file.exists()) {
			InputStream is = null;
			XSSFWorkbook workbook = null;
			try {
				is = Files.newInputStream(file.toPath());
				workbook = new XSSFWorkbook(is);
				sheet = workbook.getSheetAt(0);//获取第一个sheet
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return sheet;
	}

	public static int parsingExcel5(String filePath, String filePath1, String treeId, String creator, String filename) {
		int result = 0;
		XSSFSheet sheet = getSheet(filePath);
		if (sheet != null) {
			int lastRowNum = sheet.getLastRowNum();
			JSONArray jsonArray = new JSONArray();
			XSSFRow row0 = sheet.getRow(0);
			String val1 = getCellValue(row0.getCell(3));
			String val2 = getCellValue(row0.getCell(11));

			for (int i = 3; i <= lastRowNum; i++) {
				XSSFRow row = sheet.getRow(i);
				int cellLast = 20;
				JSONObject jsonObject = new JSONObject();
				jsonObject.set("val1", val1); // 型号
				jsonObject.set("val2", val2); // 产品类别
				jsonObject.set(FILEPATH_KEY, filePath1); // 存放的相对路径
				jsonObject.set(TREEID_KEY, treeId); // 关联的树节点
				jsonObject.set(CREATOR_KEY, creator); // 创建人
				jsonObject.set(FILENAME_KEY, filename); // 文件名称

				for (int j = 0; j < cellLast; j++) {
					XSSFCell cell = row.getCell(j);
					String value = getCellValue(cell);
					jsonObject.set("val" + (j + 3), value);
				}

				jsonArray.add(jsonObject);
			}

			String jsons = jsonArray.toString();
			JSONObject json = new JSONObject();
			json.set("jsons", jsons);
			JSONArray list = Util.postTwx("Thing.Fn.ProductQuality", "insert_P_THERMISTORMACHINING", json);
			result = list.getJSONObject(0).getInt("result");
		}
		return result;
	}

	public static int parsingExcel6_1(String filePath, String filePath1, String treeId, String creator, String filename) {
		return parseExcel(filePath, filePath1, treeId, creator, filename, "Thing.Fn.ProductQuality", "insert_P_GENERALSTRUCTURE",
				new String[]{"B2", "H2", "F2", "J2", "C14", "G5", "D7", "G6", "F14", "I14", "C13"},
				new String[]{"型号", "图号", "构件名称", "数量", "结论", "重量(g)", "表面状态", "标识", "检验人", "检验时间", "影像记录"},
				"结构件", "1");
	}

	public static int parsingExcel6_2(String filePath, String filePath1, String treeId, String creator, String filename) {
		return parseExcel(filePath, filePath1, treeId, creator, filename, "Thing.Fn.ProductQuality", "insert_P_GENERALSTRUCTURE",
				new String[]{"B2", "H2", "F2", "J2", "C13", "G5", "G6", "F13", "I13", "C12"},
				new String[]{"型号", "图号", "构件名称", "数量", "结论", "重量(g)", "标识", "检验人", "检验时间", "影像记录"},
				"结构件", "2");
	}

	public static int parsingExcel7(String filePath, String filePath1, String treeId, String creator, String filename) {
		return parseExcel(filePath, filePath1, treeId, creator, filename, "Thing.Fn.ProductQuality", "insert_P_STARARROWLINK",
				new String[]{"B1", "D1", "F1", "H1", "J1", "J2", "C25", "F25", "I25", "C26", "C27", "F27", "I27"},
				new String[]{"型号", "阶段", "产品名称", "产品图号", "数量", "入所复验号", "环境温度", "环境湿度", "跟踪卡号", "影像记录", "验收结论", "检验员", "日期"},
				"星箭连接环");
	}

	public static int parsingExcel8(String filePath, String filePath1, String treeId, String creator, String filename) {
		return parseExcel(filePath, filePath1, treeId, creator, filename, "Thing.Fn.ProductQuality", "insert_P_LOWFREQUENCYCABLE",
				new String[]{"D1", "K1"},
				new String[]{"型号", "产品类别"},
				"低频电缆加工");
	}

	private static int parseExcel(String filePath, String filePath1, String treeId, String creator, String filename, String thingName, String serviceName, String[] addresses, String[] keys, String productCategory) {
		return parseExcel(filePath, filePath1, treeId, creator, filename, thingName, serviceName, addresses, keys, productCategory, null);
	}

	private static int parseExcel(String filePath, String filePath1, String treeId, String creator, String filename, String thingName, String serviceName, String[] addresses, String[] keys, String productCategory, String type) {
		int result = 0;
		JSONObject json = new JSONObject();
		JSONArray arr = new JSONArray();
		JSONObject obj = new JSONObject();

		for (int i = 0; i < addresses.length; i++) {
			obj.set(keys[i], Util.getAddressValue(filePath, 0, addresses[i]));
		}

		obj.set(FILEPATH_KEY, filePath1);
		obj.set(TREEID_KEY, treeId);
		obj.set(CREATOR_KEY, creator);
		obj.set(FILENAME_KEY, filename);
		obj.set("val2", productCategory); // 产品类别

		if (type != null) {
			obj.set("val14", type); // 类型
		}

		arr.add(obj);
		json.set("jsons", arr);
		JSONArray list = Util.postTwx(thingName, serviceName, json);
		result = list.getJSONObject(0).getInt("result");
		return result;
	}
}

