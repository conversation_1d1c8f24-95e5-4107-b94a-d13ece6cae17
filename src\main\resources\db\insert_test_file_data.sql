-- 为TEST_FILE表插入模拟数据
DECLARE
  v_id NUMBER := 1;
  v_categories VARCHAR2(1000) := '鉴定审查类,研制依据类,试验评估类,研制总结类,图样技术类,其他文件';
  v_category VARCHAR2(255);
  v_reviewer VARCHAR2(255);
  v_security VARCHAR2(255);
  v_file_format VARCHAR2(255);
  v_status VARCHAR2(255);
  v_create_time VARCHAR2(255);
  v_review_time VARCHAR2(255);
  v_random_days NUMBER;
  v_file_count NUMBER;
BEGIN
  -- 遍历TEST_PRODUCT_TREE中所有TYPE_为product的记录
  FOR product IN (SELECT ID_, NAME_ FROM TEST_PRODUCT_TREE WHERE TYPE_ = 'product' ORDER BY ID_)
  LOOP
    -- 为每个product随机生成10-20个文件记录
    SELECT CEIL(DBMS_RANDOM.VALUE(10, 20)) INTO v_file_count FROM DUAL;
    
    FOR i IN 1..v_file_count
    LOOP
      -- 随机选择文件类别
      SELECT TRIM(REGEXP_SUBSTR(v_categories, '[^,]+', 1, CEIL(DBMS_RANDOM.VALUE(1, 6))))
      INTO v_category
      FROM DUAL;
      
      -- 随机生成审核人
      SELECT CASE CEIL(DBMS_RANDOM.VALUE(1, 5))
        WHEN 1 THEN '张工'
        WHEN 2 THEN '李工'
        WHEN 3 THEN '王工'
        WHEN 4 THEN '赵工'
        ELSE '钱工'
      END INTO v_reviewer
      FROM DUAL;
      
      -- 随机生成密级
      SELECT CASE CEIL(DBMS_RANDOM.VALUE(1, 4))
        WHEN 1 THEN '公开'
        WHEN 2 THEN '内部'
        WHEN 3 THEN '秘密'
        ELSE '机密'
      END INTO v_security
      FROM DUAL;
      
      -- 随机生成文件格式
      SELECT CASE CEIL(DBMS_RANDOM.VALUE(1, 5))
        WHEN 1 THEN 'doc'
        WHEN 2 THEN 'pdf'
        WHEN 3 THEN 'xlsx'
        WHEN 4 THEN 'ppt'
        ELSE 'txt'
      END INTO v_file_format
      FROM DUAL;
      
      -- 随机生成状态
      SELECT CASE CEIL(DBMS_RANDOM.VALUE(1, 3))
        WHEN 1 THEN '待审核'
        WHEN 2 THEN '已通过'
        ELSE '已驳回'
      END INTO v_status
      FROM DUAL;
      
      -- 随机生成创建时间（2024年内）
      v_random_days := CEIL(DBMS_RANDOM.VALUE(1, 365));
      SELECT TO_CHAR(TO_DATE('2024-01-01', 'YYYY-MM-DD') + v_random_days, 'YYYY-MM-DD HH24:MI:SS')
      INTO v_create_time
      FROM DUAL;
      
      -- 生成审核时间（创建时间后1-30天）
      SELECT TO_CHAR(TO_DATE(v_create_time, 'YYYY-MM-DD HH24:MI:SS') + CEIL(DBMS_RANDOM.VALUE(1, 30)), 'YYYY-MM-DD HH24:MI:SS')
      INTO v_review_time
      FROM DUAL;
      
      -- 插入数据
      INSERT INTO TEST_FILE (
        ID_,
        TREE_ID_,
        NAME_,
        CATEGORY_,
        REVIEWER_,
        SECURITY_,
        CREATOR_,
        CREATE_TIME_,
        FILE_NAME_,
        FILE_PATH_,
        FILE_FORMAT_,
        STATUS_,
        REVIEW_TIME_
      ) VALUES (
        v_id,
        product.ID_,
        product.NAME_ || '-' || v_category || '-' || i,
        v_category,
        v_reviewer,
        v_security,
        'adm',
        v_create_time,
        product.NAME_ || '-' || v_category || '-' || i || '.' || v_file_format,
        '/files/' || TO_CHAR(product.ID_) || '/' || v_id || '.' || v_file_format,
        v_file_format,
        v_status,
        CASE WHEN v_status = '待审核' THEN NULL ELSE v_review_time END
      );
      
      v_id := v_id + 1;
    END LOOP;
  END LOOP;
  
  COMMIT;
END;
/
