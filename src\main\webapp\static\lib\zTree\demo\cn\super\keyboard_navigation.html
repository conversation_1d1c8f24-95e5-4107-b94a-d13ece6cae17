<!DOCTYPE html>
<HTML>
<HEAD>
	<TITLE> ZTREE DEMO - Keyboard navigation </TITLE>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<link rel="stylesheet" href="../../../css/demo.css" type="text/css">
	<link rel="stylesheet" href="../../../css/zTreeStyle/zTreeStyle.css" type="text/css">
	<script type="text/javascript" src="../../../js/jquery-1.4.4.min.js"></script>
	<script type="text/javascript" src="../../js/keyboard_navigation.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.core.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.exedit.js"></script>
	<SCRIPT type="text/javascript">

		var setting = {
			data: {
				simpleData: {
					enable: true
				}
			}
		};

		var zNodes =[
			{ id:1, pId:0, name:"Custom Icon 01", open:true, iconSkin:"pIcon01", accesskey: 'c'},
			{ id:11, pId:1, name:"leaf node 01", iconSkin:"icon01", accesskey: 'l'},
			{ id:12, pId:1, name:"leaf node 02", iconSkin:"icon02"},
			{ id:13, pId:1, name:"parent node 03", iconSkin:"pIcon01", accesskey: 'p'},
			{ id:131, pId:13, name:"leaf node 01", iconSkin:"icon01"},
			{ id:132, pId:13, name:"leaf node 02", iconSkin:"icon02"},
			{ id:133, pId:13, name:"leaf node 03", iconSkin:"icon03"},
			{ id:2, pId:0, name:"Custom Icon 02", open:true, iconSkin:"pIcon02"},
			{ id:21, pId:2, name:"leaf node 01", iconSkin:"icon04"},
			{ id:22, pId:2, name:"leaf node 02", iconSkin:"icon05"},
			{ id:23, pId:2, name:"leaf node 03", iconSkin:"icon06"},
			{ id:3, pId:0, name:"no Custom Icon", open:true, accesskey: 'n' },
			{ id:31, pId:3, name:"leaf node 01"},
			{ id:32, pId:3, name:"leaf node 02"},
			{ id:33, pId:3, name:"leaf node 03"}
		];

		var $ = jQuery;
		$(document).ready(function()
		{
			var element = "#treeDemo";
			var zTree = $.fn.zTree.init($(element), setting, zNodes);
			// Initialize keyboard navigation
			$.fn.zTreeKeyboardNavigation(zTree, document.body);
		});

	</SCRIPT>
	<style type="text/css">
		.ztree li > a
		{
			border-left: 1px solid white;
		}

		.ztree li > a.curSelectedNode {
			border-radius: 3px;
		}

		.ztree li span.button.pIcon01_ico_open{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/1_open.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.pIcon01_ico_close{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/1_close.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.pIcon02_ico_open, .ztree li span.button.pIcon02_ico_close{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/2.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.icon01_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/3.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.icon02_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/4.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.icon03_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/5.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.icon04_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/6.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.icon05_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/7.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.icon06_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/8.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
	</style>
</HEAD>

<BODY>
<h1>设置快捷键</h1>
<h6>[ 文件路径: super/keyboard_navigation.html ]</h6>
<div class="content_wrap">
	<div class="zTreeDemoBackground left">
		<ul id="treeDemo" class="ztree"></ul>
	</div>
	<div class="right">
		<ul class="info">
			<li class="title"><h2>1、设置说明</h2>
				<ul class="list">
				<li>键盘导航不需要特殊配置。</li>
				<li>只需要加载 keyboard_navigation.js 文件并初始化。</li>
				</ul>
			</li>
			<li class="title"><h2>2、快捷键说明</h2>
				<ul class="list">
					<li>Home (keycode 36)：定位到第一个根节点</li>
					<li>End (keycode 35)：定位到树上最后一个节点</li>
					<li>下 (keycode 39)：定位到下一个同级节点</li>
					<li>右 (keycode 40)：定位到下一个可视节点（包括子节点/同级节点 以及后面的父级节点）</li>
					<li>上 (keycode 37)：定位到前一个同级节点</li>
					<li>左 (keycode 38)：定位到前一个可视节点（包括子节点/同级节点 以及后面的父级节点）</li>
					<li>空格 (keycode 32)：切换当前父节点 折叠、展开状态</li>
				</ul>
			</li>			
		</ul>
	</div>
</div>
</BODY>
</HTML>