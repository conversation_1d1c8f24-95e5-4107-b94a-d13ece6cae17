package com.cirpoint.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.model.PdfOptions;
import java.io.File;
import java.nio.charset.Charset;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

/**
 * description
 *
 * <AUTHOR> 2022/05/25 20:31
 */
public class HandSonTableUtil {


	public static void main(String[] args) {
		System.out.println(dealSignFile("Thing.Fn.LaunchOnlineConfirm", "2383504"));
	}

	/**
	 * 将之前版本的signHtml中单元格中的图片和签名提取到 tableData中的meta对象中
	 *
	 */
	public static JSONArray signHtmlToMetas(String thing, String id) {
		JSONObject res = Util.postTwxForObject(thing, "QueryNodeById", JSONUtil.createObj().set("id", id));
		String signHtml = res.getJSONObject("data").getStr("SIGN_HTML");
		JSONArray metas = new JSONArray();
		if (StrUtil.isNotBlank(signHtml)) {
			org.jsoup.nodes.Document doc = Jsoup.parseBodyFragment(signHtml);
			Element table = doc.getElementsByTag("table").get(0);
			Element tbody = table.getElementsByTag("tbody").get(0);
			Elements trs = tbody.getElementsByTag("tr");
			for (int i = 0; i < trs.size(); i++) {
				Element tr = trs.get(i);
				Elements tds = tr.getElementsByTag("td");
				for (int j = 0; j < tds.size(); j++) {
					Element td = tds.get(j);
					Elements imgs = td.getElementsByTag("img");
					JSONObject meta = JSONUtil.createObj();
					if (StrUtil.isNotBlank(td.attr("row"))) {
						meta.set("row", td.attr("row"));
					} else {
						meta.set("row", i);
					}
					if (StrUtil.isNotBlank(td.attr("col"))) {
						meta.set("col", td.attr("col"));
					} else {
						meta.set("col", j);
					}
					JSONArray eles = new JSONArray();
					if (!imgs.isEmpty()) {
						for (Element img : imgs) {
							JSONObject ele = JSONUtil.createObj();
							String imgType = img.attr("type");
							ele.set("type", imgType);
							ele.set("src", img.attr("src"));
							ele.set("date", img.attr("date"));
							if (imgType.equals("photo")) {
								ele.set("id", img.attr("id"));
								ele.set("photoPath", img.attr("photoShowNum"));
								ele.set("photoName", img.attr("photoname"));
								ele.set("photoShowNum", img.attr("photoshownum"));
								ele.set("photoFormat", img.attr("photoformat"));
								ele.set("class", img.attr("class"));
							}
							eles.set(ele);
						}
					}
					meta.set("eles", eles);
					metas.add(meta);
				}
			}
		}

		return metas;
	}


		/**
	 * 生成节点以及所有子节点的单个pdf并打包成压缩包（使用默认的A4横向格式）
	 *
	 * @param postId    节点id
	 * @param tempPath  生成文件的临时目录
	 * @param fontPath  pdf字体文件路径
	 * @param thing     请求的thing
	 * @return 生成结果
	 */
	public static JSONObject exportPdfZip(String postId, String tempPath, String fontPath, String thing) {
		// 使用默认的A4横向格式
		return exportPdfZip(postId, tempPath, fontPath, thing, new PdfOptions());
	}

	/**
	 * 生成节点以及所有子节点的单个pdf并打包成压缩包（使用单独的页面大小和方向参数）
	 *
	 * @param postId          节点id
	 * @param tempPath        生成文件的临时目录
	 * @param fontPath        pdf字体文件路径
	 * @param thing           请求的thing
	 * @param pageSize        纸张大小，如A4、A3等
	 * @param pageOrientation 页面方向，portrait纵向或landscape横向
	 * @return 生成结果
	 */
	public static JSONObject exportPdfZip(String postId, String tempPath, String fontPath, String thing, String pageSize, String pageOrientation) {
		// 创建PdfOptions对象并调用新的重载方法
		PdfOptions options = new PdfOptions(pageSize, pageOrientation);
		return exportPdfZip(postId, tempPath, fontPath, thing, options);
	}

	/**
	 * 生成节点以及所有子节点的单个pdf并打包成压缩包（使用PdfOptions对象）
	 *
	 * @param postId    节点id
	 * @param tempPath  生成文件的临时目录
	 * @param fontPath  pdf字体文件路径
	 * @param thing     请求的thing
	 * @param options   PDF导出选项对象
	 * @return 生成结果
	 */
	public static JSONObject exportPdfZip(String postId, String tempPath, String fontPath, String thing, PdfOptions options) {
		JSONObject obj = Util.postTwxForObject(thing, "QueryTreeById",
				JSONUtil.createObj().set("id", postId));
		JSONObject res = new JSONObject();
		boolean success = obj.getBool("success");
		if (success) {
			JSONArray errorNode = new JSONArray();
			JSONArray data = obj.getJSONArray("data");
			String folderName = System.currentTimeMillis() + "";
			tempPath = tempPath + File.separator + folderName;
			FileUtil.mkdir(tempPath);
			for (int i = 0; i < data.size(); i++) {
				JSONObject node = data.getJSONObject(i);
				if ("a".equals(node.getStr("TYPE")) || "b".equals(node.getStr("TYPE")) || "report".equals(node.getStr("TYPE")) || StrUtil.contains(node.getStr("TYPE"), "table")) {
					// 使用传入的PdfOptions对象
					JSONObject nodeRes = Util.tableData2Pdf(node, tempPath, fontPath, options);
					if (!nodeRes.getBool("success")) {
						String nodeName = nodeRes.getStr("nodeName");
						errorNode.add(nodeName);
					}
				}
			}
			File resFile = ZipUtil.zip(tempPath, Charset.forName("GBK"));
			if (!errorNode.isEmpty()) {
				res.set("success", false);
				res.set("msg", exportPdfErrorMsg(errorNode));
				FileUtil.del(resFile);
				FileUtil.del(tempPath);
			} else {
				res.set("data", resFile.getAbsolutePath());
				res.set("success", true);
				res.set("msg", "导出成功！");
			}

		} else {
			String msg = obj.getStr("msg");
			res.set("success", false);
			res.set("msg", msg);
		}
		return res;
	}

	public static String exportPdfErrorMsg(JSONArray errorNode) {
		StringBuilder errorNodes = new StringBuilder();
		for (int i = 0; i < errorNode.size(); i++) {
			errorNodes.append(errorNode.getStr(i)).append(",");
		}
		errorNodes = new StringBuilder(errorNodes.substring(0, errorNodes.length() - 1));
		return "部分节点导出失败，如下：" + errorNodes;
	}

	/**
	 * 处理有过签名的表格 将之前的签名保存为图片存储
	 *
	 */
	public static JSONObject dealSignFile(String thing, String id) {
		JSONObject obj = new JSONObject();
		try {
			JSONObject res = Util.postTwxForObject(thing, "QueryNodeById", JSONUtil.createObj().set("id", id));
			JSONObject tableData = res.getJSONObject("data").getJSONObject("SAVE_DATA");
			JSONArray metas = tableData.getJSONArray("meta");
			JSONArray signs = Util.postTwxForObject(thing, "QuerySignByPid", JSONUtil.createObj().set("id", id)).getJSONArray("data");
			System.out.println("signs.size() = " + signs.size());
			for (int i = 0; i < metas.size(); i++) {
				JSONObject meta = metas.getJSONObject(i);
				JSONArray eles = meta.getJSONArray("eles");
				System.out.println("eles = " + eles);
				if (eles != null) {
					for (int j = 0; j < eles.size(); j++) {
						JSONObject ele = eles.getJSONObject(j);
						if (ele.getStr("type").equals("sign")) {
							String src = ele.getStr("src");
							if (src.contains("data:")) {
								String imgPath = Util.base64ToImg(ele.getStr("src"));
								for (int x = 0; x < signs.size(); x++) {
									String signStr = signs.getJSONObject(x).getStr("IMG");
									if (signStr.equals(src)) {
										//更新签名表的文件路径
										Util.postTwxForObject(thing, "UpdateSign", JSONUtil.createObj().set("id", signs.getJSONObject(x).getStr("ID")).set("path", imgPath));
									}
								}
								ele.set("src", imgPath);
							}
						}
					}
				}
			}
			//最后更新确认表的所有数据
			Util.postTwxForObject(thing, "UpdateTableData", JSONUtil.createObj().set("id", id).set("tableData", tableData.toString()));
			obj.set("success", true);
		} catch (Exception e) {
			obj.set("success", false);
			obj.set("msg", thing + ":" + id + "更新失败，原因：" + e);
		}
		return obj;
	}

	public static void GenerateFileComplete(String downloadId, String fileUploadPath, JSONObject res) {
		JSONObject param = new JSONObject();
		param.set("downloadId", downloadId);
		if (res.getBool("success")) {
			String filePath = res.getStr("data");
			File file = new File(filePath);
			String fileName = file.getName();
			File newFile = FileUtil.rename(file, System.currentTimeMillis() + "", true, true);

			String fileSize = FileUtil.readableFileSize(newFile);
			String childPath = FileUtil.subPath(fileUploadPath, newFile.getAbsolutePath());
			param.set("filePath", childPath);
			param.set("fileName", fileName);
			param.set("fileSize", fileSize);
			param.set("downloadId", downloadId);
			param.set("isComplete", 1);
			param.set("msg", "文件已生成");

		} else {
			param.set("isComplete", 2);
			param.set("msg", "文件生成失败，原因：" + res.getStr("msg"));
		}
		Util.postTwxForObject("Thing.Util.HandsonTable", "GenerateFileComplete", param);
	}
}
