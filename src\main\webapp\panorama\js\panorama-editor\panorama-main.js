/**
 * 全景图热点编辑系统 - 主入口文件
 * 
 * <AUTHOR>
 * @date 2025-06-10
 * @description 系统主入口，负责初始化和事件绑定
 * @requires panorama-core.js, panorama-ui.js, panorama-task.js, panorama-device.js
 */

/**
 * 全景图编辑器主模块
 * 负责系统初始化和事件绑定
 */
layui.use(['layer', 'form', 'table', 'upload', 'element'], function () {
    'use strict';
    
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var upload = layui.upload;
    var element = layui.element;
    var $ = layui.$;

    // ==================== 系统初始化 ====================

    /**
     * 解析URL参数中的用户名并存储到sessionStorage
     * 支持URL编码的用户名参数
     */
    function parseUrlAndSetUsername() {
        var search = window.location.search;
        if (search) {
            var params = search.substring(1);
            var paramPairs = params.split('&');

            for (var i = 0; i < paramPairs.length; i++) {
                var pair = paramPairs[i].split('=');
                if (pair.length === 2 && pair[0] === 'username') {
                    var username = decodeURIComponent(pair[1]);
                    if (username && username.trim() !== '') {
                        sessionStorage.setItem('username', username.trim());
                        break;
                    }
                }
            }
        }
    }

    /**
     * 页面初始化
     */
    function initPage() {
        // 检查必要的模块是否已加载
        if (typeof PanoramaCore === 'undefined') {
            console.error('PanoramaCore模块未加载');
            return;
        }
        
        if (typeof PanoramaUI === 'undefined') {
            console.error('PanoramaUI模块未加载');
            return;
        }
        
        if (typeof PanoramaTask === 'undefined') {
            console.error('PanoramaTask模块未加载');
            return;
        }

        if (typeof PanoramaDevice === 'undefined') {
            console.error('PanoramaDevice模块未加载');
            return;
        }

        // 初始化热点表格
        initHotspotTable();

        // 初始化文件上传
        initFileUpload();

        // 初始化拖拽功能
        PanoramaUI.initResizeHandle();
    }

    /**
     * 绑定事件
     */
    function bindEvents() {
        // 任务管理按钮
        var taskManagementBtn = document.getElementById('taskManagementBtn');
        if (taskManagementBtn) {
            taskManagementBtn.addEventListener('click', function () {
                PanoramaTask.showTaskManagementDialog();
            });
        }

        // 创建任务按钮
        var createTaskBtn = document.getElementById('createTaskBtn');
        if (createTaskBtn) {
            createTaskBtn.addEventListener('click', function () {
                PanoramaTask.showCreateTaskDialog();
            });
        }

        // 导出按钮 - 点击时检查条件并给出明确提示
        var exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            // 移除之前的事件监听器
            exportBtn.removeEventListener('click', handleExportClick);
            exportBtn.addEventListener('click', handleExportClick);
        }

        // 刷新表格按钮
        var refreshTableBtn = document.getElementById('refreshTableBtn');
        if (refreshTableBtn) {
            refreshTableBtn.addEventListener('click', function () {
                var currentTaskId = PanoramaCore.getCurrentTaskId();
                var hotspotTable = PanoramaCore.getHotspotTable();
                if (currentTaskId && hotspotTable) {
                    hotspotTable.reload();
                }
            });
        }

        // 批量删除按钮
        var batchDeleteBtn = document.getElementById('batchDeleteBtn');
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', function () {
                if (typeof PanoramaHotspot !== 'undefined' && PanoramaHotspot.showBatchDeleteDialog) {
                    PanoramaHotspot.showBatchDeleteDialog();
                }
            });
        }

        // 刷新预览按钮
        var refreshPreviewBtn = document.getElementById('refreshPreviewBtn');
        if (refreshPreviewBtn) {
            refreshPreviewBtn.addEventListener('click', function () {
                var currentTaskId = PanoramaCore.getCurrentTaskId();
                if (currentTaskId && typeof PanoramaPreview !== 'undefined' && PanoramaPreview.loadPreview) {
                    PanoramaPreview.loadPreview();
                }
            });
        }

        // 任务选择监听
        form.on('select(taskSelect)', function (data) {
            handleTaskSelect(data);
        });

        // 查看设备数据按钮事件
        var viewDeviceBtn = document.getElementById('viewDeviceBtn');
        if (viewDeviceBtn) {
            viewDeviceBtn.addEventListener('click', function() {
                var currentTaskId = PanoramaCore.getCurrentTaskId();
                if (!currentTaskId) {
                    layer.msg('请先选择任务', { icon: 2 });
                    return;
                }
                if (typeof PanoramaDevice !== 'undefined' && PanoramaDevice.showDeviceListDialog) {
                    PanoramaDevice.showDeviceListDialog();
                }
            });
        }

        // 初始化导出按钮状态
        PanoramaUI.updateExportButtonState();
    }
    
    /**
     * 处理导出按钮点击
     */
    function handleExportClick() {
        // 检查是否选择了任务
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) {
            layer.msg('请先选择一个任务', { icon: 2 });
            return;
        }

        // 检查任务状态和条件
        if (typeof PanoramaExport !== 'undefined' && PanoramaExport.checkExportConditions) {
            PanoramaExport.checkExportConditions();
        } else {
            layer.msg('导出功能模块未加载', { icon: 2 });
        }
    }
    
    /**
     * 处理任务选择
     * @param {Object} data 选择数据
     */
    function handleTaskSelect(data) {
        var taskId = data.value;
        var currentTaskId = PanoramaCore.getCurrentTaskId();

        if (taskId) {
            // 如果当前已有任务，提示用户确认切换
            if (currentTaskId && currentTaskId != taskId) {
                layer.confirm('请确保已保存所有修改。是否继续切换？', {
                    icon: 3,
                    title: '确认切换任务',
                    btn: ['确认切换', '取消']
                }, function (index) {
                    // 用户确认切换
                    layer.close(index);
                    PanoramaTask.selectTask(taskId);
                }, function (index) {
                    // 用户取消切换，恢复原选择
                    layer.close(index);
                    var taskSelect = document.getElementById('taskSelect');
                    if (taskSelect) {
                        taskSelect.value = currentTaskId;
                        form.render('select');
                    }
                });
            } else {
                // 首次选择任务，直接切换
                PanoramaTask.selectTask(taskId);
            }
        } else {
            // 选择空值，清空任务信息
            if (currentTaskId) {
                layer.confirm('确定要取消选择当前任务吗？这将清空所有工作状态。', {
                    icon: 3,
                    title: '确认取消选择',
                    btn: ['确认', '取消']
                }, function (index) {
                    layer.close(index);
                    PanoramaTask.clearTaskInfo();
                }, function (index) {
                    layer.close(index);
                    var taskSelect = document.getElementById('taskSelect');
                    if (taskSelect) {
                        taskSelect.value = currentTaskId;
                        form.render('select');
                    }
                });
            } else {
                PanoramaTask.clearTaskInfo();
            }
        }
    }
    
    // ==================== 热点表格初始化 ====================
    
    /**
     * 初始化热点表格
     */
    function initHotspotTable() {
        var hotspotTable = table.render({
            elem: '#hotspotTable',
            url: '/panorama/hotspot/list',
            where: {
                taskId: 0 // 初始为0，不加载数据
            },
            page: true,
            limit: PanoramaCore.CONFIG.TABLE.DEFAULT_LIMIT,
            limits: PanoramaCore.CONFIG.TABLE.LIMITS,
            height: PanoramaCore.CONFIG.TABLE.HEIGHT,
            cols: [[
                { type: 'numbers', title: '序号', width: 40 },
                { title: '', width: 15, minWidth: 15, unresize: true, templet: '#statusDotTpl' },
                {
                    field: 'TITLE_DISPLAY',
                    title: '热点标题',
                    width: 180,
                    templet: '#titleDisplayTpl'
                },
                { field: 'DESCRIPTION_DISPLAY', title: '热点描述', width: 180, templet: '#descriptionDisplayTpl' },
                { title: '操作', width: 140, toolbar: '#hotspotTableBar', fixed: 'right' }
            ]],
            done: function () {
                // 表格渲染完成回调
            }
        });
        
        // 保存表格实例到核心模块
        PanoramaCore.setHotspotTable(hotspotTable);

        // 监听表格工具条
        table.on('tool(hotspotTable)', function (obj) {
            var data = obj.data;
            if (obj.event === 'edit') {
                if (typeof PanoramaHotspot !== 'undefined' && PanoramaHotspot.editHotspot) {
                    PanoramaHotspot.editHotspot(data);
                }
            } else if (obj.event === 'locate') {
                if (typeof PanoramaHotspot !== 'undefined' && PanoramaHotspot.locateHotspot) {
                    PanoramaHotspot.locateHotspot(data.HOTSPOT_ID);
                }
            } else if (obj.event === 'delete') {
                if (typeof PanoramaHotspot !== 'undefined' && PanoramaHotspot.deleteHotspot) {
                    PanoramaHotspot.deleteHotspot(data);
                }
            }
        });
    }
    
    // ==================== 文件上传初始化 ====================
    
    /**
     * 初始化文件上传
     */
    function initFileUpload() {
        // ZIP文件上传
        var zipUploadInst = upload.render({
            elem: '#uploadZipBtn',
            url: '/panorama/upload/zip',
            accept: 'file',
            exts: 'zip',
            data: {
                taskId: function () {
                    return PanoramaCore.getCurrentTaskId();
                }
            },
            before: function (obj) {
                var currentTaskId = PanoramaCore.getCurrentTaskId();
                if (!currentTaskId) {
                    layer.msg('请先选择任务', { icon: 2 });
                    return false;
                }

                // 检查是否已存在热点数据
                if (typeof PanoramaUpload !== 'undefined' && PanoramaUpload.checkExistingHotspotsBeforeUpload) {
                    PanoramaUpload.checkExistingHotspotsBeforeUpload(obj, zipUploadInst);
                }
                return false; // 阻止默认上传，由检查结果决定是否继续
            },
            done: function (res) {
                layer.closeAll('loading');
                if (res.success) {
                    layer.msg('ZIP文件上传成功', { icon: 1 });
                    var zipStatus = document.getElementById('zipStatus');
                    if (zipStatus) {
                        zipStatus.textContent = '已上传';
                    }
                    
                    // 重新加载相关数据
                    if (typeof PanoramaHotspot !== 'undefined' && PanoramaHotspot.loadHotspotData) {
                        PanoramaHotspot.loadHotspotData();
                    }
                    if (typeof PanoramaPreview !== 'undefined' && PanoramaPreview.loadPreview) {
                        PanoramaPreview.loadPreview();
                    }
                } else {
                    layer.msg('上传失败: ' + res.msg, { icon: 2 });
                }
            },
            error: function () {
                layer.closeAll('loading');
                layer.msg('上传失败', { icon: 2 });
            }
        });
    }
    
    // ==================== 系统启动 ====================
    
    // 页面准备就绪时初始化
    $(document).ready(function () {
        // 解析URL参数设置用户名
        parseUrlAndSetUsername();

        initPage();
        bindEvents();
        PanoramaTask.loadTaskList();
        // 显示任务选择蒙版
        PanoramaUI.showTaskSelectionMask();
    });
    
    // ==================== 向后兼容性支持 ====================
    // 保持原有函数的全局访问方式
    
    window.initPage = initPage;
    window.bindEvents = bindEvents;
    window.initHotspotTable = initHotspotTable;
    window.initFileUpload = initFileUpload;
});
