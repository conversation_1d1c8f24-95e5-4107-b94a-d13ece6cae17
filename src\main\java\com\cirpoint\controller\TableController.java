package com.cirpoint.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.annotation.DisableLogging;
import com.cirpoint.service.TableService;
import com.cirpoint.util.FileDownloadUtil;
import com.cirpoint.util.FileUploadUtil;
import java.io.File;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/table")
@Slf4j
public class TableController {

	private final TableService tableService;

	@Autowired
	public TableController(TableService tableService) {
		this.tableService = tableService;
	}


	/**
	 * 导出二级表数据
	 *
	 * @param productTreeId 产品树ID（可选，默认-1）
	 * @param processTreeId 工序树ID（可选，默认0）
	 * @param tableId       表格ID
	 * @param tableName     表格名称
	 * @param query         查询条件
	 * @param dlwIsAll      是否导出全部（可选，默认2）
	 * @return Excel文件下载响应
	 */
	@PostMapping("/second/export")
	public ResponseEntity<?> exportSecondExcel(
			@RequestParam(value = "productTreeId", required = false) String productTreeId,
			@RequestParam(value = "processTreeId", required = false) String processTreeId,
			@RequestParam("tableId") String tableId,
			@RequestParam(required = false) String tableName,
			@RequestParam(required = false) String query,
			@RequestParam(defaultValue = "2") Integer dlwIsAll) {

		File file = tableService.exportSecondTableExcel(
				productTreeId, processTreeId, tableId, tableName, query, dlwIsAll);

		String fileName = tableName == null || tableName.isEmpty() ? "二级表数据" : tableName;
		return FileDownloadUtil.fileResponseAndDelete(file, fileName + ".xlsx");
	}

	/**
	 * 导出质量数据确认表
	 *
	 * @param treeId        树节点ID
	 * @param tableConfigId 表配置ID
	 * @param fileName      文件名
	 * @param username      用户名
	 * @return 文件下载响应
	 */
	@PostMapping("/quality/export")
	public ResponseEntity<?> exportQualityExcel(
			@RequestParam("treeId") String treeId,
			@RequestParam("tableConfigId") String tableConfigId,
			@RequestParam("fileName") String fileName,
			@RequestParam("username") String username) {

		File file = tableService.exportQualityExcel(treeId, tableConfigId, username);
		return FileDownloadUtil.fileResponseAndDelete(file, fileName + ".xlsx");
	}

	/**
	 * 导出自定义模板
	 *
	 * @param tableId   表格ID
	 * @param tableName 表格名称
	 * @return Excel文件
	 */
	@PostMapping("/export/custom/tpl")
	public ResponseEntity<?> exportCustomTemplate(
			@RequestParam("tableId") String tableId,
			@RequestParam("tableName") String tableName) {

		File file = tableService.exportCustomTemplate(tableId);
		return FileDownloadUtil.fileResponseAndDelete(file, tableName + "自定义导出模板.xlsx");
	}

	/**
	 * 导出自定义Excel数据
	 *
	 * @param tableId       表格ID
	 * @param tableName     表格名称
	 * @param username      用户名
	 * @param isCurve       是否包含曲线
	 * @param isRelatedData 是否关联数据（仅对热敏电阻加工有效）
	 * @param file          上传的Excel文件
	 * @return 导出结果
	 */
	@PostMapping("/custom/export")
	public ResponseEntity<JSONObject> exportCustomExcel(
			@RequestParam("tableId") String tableId,
			@RequestParam("tableName") String tableName,
			@RequestParam("username") String username,
			@RequestParam(value = "isCurve", defaultValue = "false") Boolean isCurve,
			@RequestParam(value = "isRelatedData", defaultValue = "true") Boolean isRelatedData,
			@RequestParam("uploadFile") MultipartFile file) throws IOException {

		JSONObject result = tableService.exportCustomExcel(
				file.getInputStream(), tableId, tableName, username, isCurve, isRelatedData);
		return ResponseEntity.ok(result);
	}

	/**
	 * 获取二级表表头
	 *
	 * @param filePath Excel文件路径
	 * @param endY     数据起始行
	 * @param tableId  表格配置ID
	 * @return 表头数据
	 */
	@DisableLogging
	@GetMapping("/second/header")
	public ResponseEntity<JSONObject> getSecondTableHeader(
			@RequestParam("filePath") String filePath,
			@RequestParam("endY") int endY,
			@RequestParam("tableId") String tableId) {

		try {
			String headerJson = tableService.getSecondTableHeader(filePath, endY, tableId);
			return ResponseEntity.ok(JSONUtil.createObj()
					.set("success", true)
					.set("result", headerJson));
		} catch (Exception e) {
			log.error("获取二级表表头失败", e);
			return ResponseEntity.ok(JSONUtil.createObj()
					.set("success", false)
					.set("result", e.toString()));
		}
	}

	/**
	 * 创建证书
	 *
	 * @param certificateFilePath 证书模板文件路径
	 * @param objs                证书数据对象
	 * @return ResponseEntity
	 */
	@PostMapping("/create/certificate")
	public ResponseEntity<?> createCertificate(
			@RequestParam String certificateFilePath,
			@RequestParam String objs) {
		try {
			String path = tableService.createCertificate(objs, certificateFilePath);
			return ResponseEntity.ok(path);
		} catch (Exception e) {
			log.error("创建合格证失败", e);
			return ResponseEntity.badRequest().body("创建合格证失败：" + e.getMessage());
		}
	}

	/**
	 * 删除照片文件
	 *
	 * @param filePath 文件路径
	 * @return ResponseEntity
	 */
	@PostMapping("/delete/photo")
	public ResponseEntity<JSONObject> deletePhoto(@RequestParam String filePath) {
		JSONObject result = tableService.deletePhoto(filePath);
		return ResponseEntity.ok(result);
	}

	/**
	 * 生成策划表中的所有图片
	 *
	 * @param downloadId    下载ID
	 * @param tableConfigId 表格配置ID
	 * @param treeId        树节点ID
	 * @param creator       创建者
	 * @param type          类型
	 * @return ResponseEntity
	 */
	@PostMapping("/generate/all/photos")
	public ResponseEntity<Void> generateAllPhotos(
			@RequestParam String downloadId,
			@RequestParam String tableConfigId,
			@RequestParam String treeId,
			@RequestParam String creator,
			@RequestParam("type_") String type) {
		tableService.generateAllPhotos(downloadId, tableConfigId, treeId, creator, type);
		return ResponseEntity.ok().build();
	}

	/**
	 * 下载MES文件
	 *
	 * @param url 下载URL
	 * @return ResponseEntity
	 */
	@GetMapping("/download/mes/file")
	public ResponseEntity<JSONObject> downloadMesFile(@RequestParam String url) {
		return ResponseEntity.ok(tableService.downloadMesFile(url));
	}

	/**
	 * 下载单元格的图片
	 *
	 * @param tableConfigId 表格配置ID
	 * @param treeId        树节点ID
	 * @param onlyValue     唯一值
	 * @param paramId       参数ID
	 * @return ResponseEntity
	 */
	@PostMapping("/download/td/photos")
	public ResponseEntity<?> downloadTdPhotos(
			@RequestParam String tableConfigId,
			@RequestParam String treeId,
			@RequestParam String onlyValue,
			@RequestParam String paramId) {
		File resultFile = tableService.downloadTdPhotos(tableConfigId, treeId, onlyValue, paramId);
		return FileDownloadUtil.fileResponseAndDelete(resultFile);
	}

	/**
	 * Excel转HTML
	 *
	 * @param filepath Excel文件路径
	 * @return ResponseEntity
	 */
	@PostMapping("/excel/to/html")
	public ResponseEntity<String> excelToHtml(@RequestParam String filepath) {
		try {
			String html = tableService.excelToHtml(filepath);
			return ResponseEntity.ok()
					.contentType(MediaType.TEXT_HTML)
					.header("Content-Type", "text/html;charset=UTF-8")
					.body(html);
		} catch (Exception e) {
			log.error("Excel转HTML失败", e);
			throw new RuntimeException("Excel转HTML失败：" + e.getMessage());
		}
	}

	/**
	 * 导出质量影像记录策划表
	 *
	 * @param processTreeId 工序树ID
	 * @param tableId       表格ID
	 * @return ResponseEntity
	 */
	@PostMapping("/export/photo/plan")
	public ResponseEntity<?> exportPhotoPlanExcel(
			@RequestParam String processTreeId,
			@RequestParam String tableId) {
		File resultFile = tableService.exportPhotoPlanExcel(processTreeId, tableId);
		return FileDownloadUtil.fileResponseAndDelete(resultFile);
	}

	/**
	 * 导出质量影像记录确认表以及照片
	 *
	 * @param treeId        树节点ID
	 * @param tableConfigId 表格配置ID
	 * @param fileName      文件名称
	 * @return ResponseEntity
	 */
	@PostMapping("/quality/photo/export")
	public ResponseEntity<?> exportQualityPhotoExcel(
			@RequestParam String treeId,
			@RequestParam String tableConfigId,
			@RequestParam String fileName) {
		File resultFile = tableService.exportQualityPhotoExcel(treeId, tableConfigId, fileName);
		return FileDownloadUtil.fileResponseAndDelete(resultFile);
	}

	/**
	 * 生成质量影像记录确认表以及照片
	 *
	 * @param downloadId    下载ID
	 * @param treeId        树节点ID
	 * @param tableConfigId 表格配置ID
	 * @param creator       创建者
	 * @param type          类型
	 * @param fileName      文件名称
	 * @return ResponseEntity
	 */
	@PostMapping("/generate/quality/photo")
	public ResponseEntity<Void> generateQualityPhotoExcel(
			@RequestParam String downloadId,
			@RequestParam String treeId,
			@RequestParam String tableConfigId,
			@RequestParam String creator,
			@RequestParam("type_") String type,
			@RequestParam String fileName) {
		try {
			tableService.generateQualityPhotoExcel(downloadId, treeId, tableConfigId,
					creator, type, fileName);
			return ResponseEntity.ok().build();
		} catch (Exception e) {
			log.error("生成质量影像记录失败", e);
			throw new RuntimeException("生成质量影像记录失败：" + e.getMessage());
		}
	}

	/**
	 * 导入MES三级表数据
	 *
	 * @param type         数据来源类型
	 * @param tableId      配置表ID
	 * @param treeId       树节点ID
	 * @param creator      创建者
	 * @param endY         数据起始行
	 * @param productId    产品ID
	 * @param relativePath 相对路径
	 * @param filename     文件名称
	 * @return ResponseEntity
	 */
	@PostMapping("/import/mes")
	public ResponseEntity<JSONObject> importMesThreeExcel(
			@RequestParam String type,
			@RequestParam String tableId,
			@RequestParam String treeId,
			@RequestParam String creator,
			@RequestParam(required = false, defaultValue = "0") Integer endY,
			@RequestParam(required = false) String productId,
			@RequestParam String relativePath,
			@RequestParam String filename) {
		try {
			JSONObject result = tableService.importMesThreeExcel(
					type, tableId, treeId, creator, endY,
					productId, relativePath, filename);
			return ResponseEntity.ok(result);
		} catch (Exception e) {
			log.error("导入MES三级表数据失败", e);
			return ResponseEntity.ok(new JSONObject()
					.set("success", false)
					.set("result", e.toString()));
		}
	}

	/**
	 * 导入三级表数据
	 *
	 * @param type     数据来源类型
	 * @param fileType 文件类型(2或3)
	 * @param tableId  配置表ID
	 * @param treeId   树节点ID
	 * @param creator  创建者
	 * @param endY     数据起始行
	 * @param file     上传的文件
	 * @return ResponseEntity
	 */
	@PostMapping("/import/three")
	public ResponseEntity<JSONObject> importThreeExcel(
			@RequestParam String type,
			@RequestParam String fileType,
			@RequestParam String tableId,
			@RequestParam String treeId,
			@RequestParam String creator,
			@RequestParam(required = false, defaultValue = "0") Integer endY,
			@RequestParam("uploadFile") MultipartFile file) {
		try {
			// 获取原始文件名
			String filename = file.getOriginalFilename();

			// 保存文件并获取结果
			JSONObject result = tableService.importThreeExcel(
					type, fileType, tableId, treeId, creator, endY, file, filename);

			return ResponseEntity.ok(result);
		} catch (Exception e) {
			log.error("导入三级表数据失败", e);
			return ResponseEntity.ok(new JSONObject()
					.set("success", false)
					.set("msg", e.toString())
					.set("result", e.toString()));
		}
	}

	/**
	 * 导入质量数据策划表
	 *
	 * @param type          策划表类型(1:质量数据 2:影像记录)
	 * @param tableConfigId 配置表ID
	 * @param treeId        树节点ID
	 * @param creator       创建者
	 * @param file          上传的文件
	 * @return ResponseEntity
	 */
	@PostMapping("/import/plan")
	public ResponseEntity<JSONObject> importPlanTable(
			@RequestParam String type,
			@RequestParam String tableConfigId,
			@RequestParam String treeId,
			@RequestParam String creator,
			@RequestParam("uploadFile") MultipartFile file) {
		try {
			// 获取原始文件名
			String filename = file.getOriginalFilename();

			// 保存文件并获取结果
			JSONObject result = tableService.importPlanTable(
					type, tableConfigId, treeId, creator, file, filename);

			return ResponseEntity.ok(result);
		} catch (Exception e) {
			log.error("导入质量数据策划表失败", e);
			return ResponseEntity.ok(new JSONObject()
					.set("success", false)
					.set("msg", "上传失败：原因：" + e));
		}
	}

	/**
	 * 上传360度全景照片
	 *
	 * @param file   上传的文件
	 * @param chunk  当前分片序号
	 * @param chunks 总分片数
	 * @param name   文件名
	 * @return ResponseEntity
	 */
	@PostMapping("/upload/360/photo")
	public ResponseEntity<JSONObject> upload360Photo(
			@RequestParam(value = "file") MultipartFile file,
			@RequestParam(value = "chunk", required = false) Integer chunk,
			@RequestParam(value = "chunks", required = false) Integer chunks,
			@RequestParam("name") String name,
			@RequestParam("reqIdent") String reqIdent,
			@RequestParam(value = "extraData", required = false) String extraData) {
		try {
			// 使用FileUploadUtil处理文件上传
			JSONObject uploadResult = FileUploadUtil.handleFileUpload(file, chunk, chunks, name, reqIdent, extraData);

			// 如果不是最后一片，直接返回上传结果
			if (!uploadResult.getBool("isAll", false)) {
				return ResponseEntity.ok(uploadResult);
			}

			// 处理完整文件
			JSONObject result = tableService.handle360Photo(uploadResult.getStr("file"), name);
			return ResponseEntity.ok(result);
		} catch (Exception e) {
			log.error("上传360度全景照片失败", e);
			return ResponseEntity.ok(new JSONObject()
					.set("success", false)
					.set("msg", "上传失败：" + e.getMessage()));
		}
	}

	/**
	 * 上传照片文件
	 *
	 * @param photos 上传的照片文件数组
	 * @return ResponseEntity
	 */
	@PostMapping("/upload/photo")
	public ResponseEntity<JSONObject> uploadPhoto(@RequestParam("photo") MultipartFile[] photos) {
		// 处理文件上传
		JSONObject result = tableService.uploadPhoto(photos);
		return ResponseEntity.ok(result);
	}

	/**
	 * Web上传照片文件
	 *
	 * @return ResponseEntity
	 */
	@PostMapping("/web/upload/photo")
	public ResponseEntity<JSONObject> webUploadPhoto(
			@RequestParam(value = "file") MultipartFile file,
			@RequestParam(value = "chunk", required = false) Integer chunk,
			@RequestParam(value = "chunks", required = false) Integer chunks,
			@RequestParam("name") String name,
			@RequestParam("reqIdent") String reqIdent,
			@RequestParam("extraData") String extraData) {
		JSONObject result = tableService.webUploadPhoto(file, chunk, chunks, name, reqIdent, extraData);
		return ResponseEntity.ok(result);
	}

}
