package com.cirpoint.controller;

import com.cirpoint.model.Result;
import com.cirpoint.service.BpmDataService;
import com.cirpoint.util.BpmDataUtil;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * BPM系统模拟数据控制器
 * 提供技术状态更改单和不合格品审理单的模拟数据接口
 *
 * <AUTHOR>
 * @date 2025年5月28日09:27:07
 */
@Slf4j
@RestController
@RequestMapping("/bpm")
public class BpmDataController {

	@Autowired
	private BpmDataService bpmDataService;

	/**
	 * 查询BO表数据（简化版）
	 * 仅接收BO表名称参数
	 * 支持GET和POST请求
	 *
	 * @param boName BO表名称
	 * @return 包含查询结果的JSON响应
	 */
	@RequestMapping(value = "/query/simple", method = {RequestMethod.GET, RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<?> querySimple(@RequestParam("boName") String boName) {
		return Result.ok("请求成功", BpmDataUtil.post(boName));
	}

	/**
	 * 查询BO表数据（完整版）
	 * 接收所有可选参数
	 * 支持GET和POST请求
	 *
	 * @param boName       BO表名称（必填）
	 * @param querys       查询条件（JSON字符串格式）
	 * @param selectClause 自定义查询语句
	 * @param orderBy      排序字段，多个字段用逗号分隔
	 * @param firstRow     首记录行号（>=0）
	 * @param rowCount     返回记录数
	 * @return 包含查询结果的JSON响应
	 */
	@RequestMapping(value = "/query", method = {RequestMethod.GET, RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<?> query(
			@RequestParam("boName") String boName,
			@RequestParam(value = "querys", required = false) String querys,
			@RequestParam(value = "selectClause", required = false) String selectClause,
			@RequestParam(value = "orderBy", required = false) String orderBy,
			@RequestParam(value = "firstRow", required = false) String firstRow,
			@RequestParam(value = "rowCount", required = false) String rowCount) {
		return Result.ok("请求成功", BpmDataUtil.post(boName, querys, selectClause, orderBy, firstRow, rowCount));
	}

	/**
	 * 获取所有用户信息
	 * 模拟从BPM系统获取用户信息的接口
	 * 支持GET请求
	 *
	 * @return 包含用户信息列表的JSON响应
	 */
	@RequestMapping(value = "/users", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<?> getAllUsers() throws Exception {
		return Result.ok("请求成功", bpmDataService.getAllUsers());
	}

	@RequestMapping(value = "/get/files", method = {RequestMethod.GET, RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<?> getFiles(@RequestParam("boId") String boId, @RequestParam("fieldName") String fieldName) {
		return Result.ok("请求成功", BpmDataUtil.getFiles(boId, fieldName));
	}

	@RequestMapping(value = "/get/department", method = {RequestMethod.GET, RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<?> getDepartmentName(@RequestParam("id") String id) {
		return Result.ok("请求部门名称成功", BpmDataUtil.getDepartmentName(id));
	}

	/**
	 * 创建签名任务
	 * 通过向BPM服务发送请求创建一个签名任务
	 * 支持POST请求，接收JSON数据
	 *
	 * @param requestData 包含任务信息的JSON数据
	 * @return 包含任务创建结果的JSON响应
	 */
	@RequestMapping(value = "/task/create", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<?> createSignTask(@RequestBody Map<String, String> requestData) {
		String taskTitle = requestData.get("taskTitle");
		String taskDescription = requestData.get("taskDescription");
		String assigneeWorkno = requestData.get("assigneeWorkno");
		String assigneeName = requestData.get("assigneeName");
		String pushWorkno = requestData.get("pushWorkno");
		String pushName = requestData.get("pushName");
		String callbackUrl = requestData.get("callbackUrl");

		// 参数验证
		if (taskTitle == null || taskTitle.trim().isEmpty()) {
			return Result.error("任务标题不能为空");
		}
		if (assigneeWorkno == null || assigneeWorkno.trim().isEmpty()) {
			return Result.error("任务分配人工号不能为空");
		}

		String result = BpmDataUtil.createSignTask(taskTitle, taskDescription, assigneeWorkno, assigneeName, pushWorkno, pushName, callbackUrl);
		return Result.ok("创建签名任务成功", result);
	}


	/**
	 * 完成签名任务
	 * 标记指定的签名任务为完成状态
	 * 支持GET和POST请求
	 *
	 * @return 包含任务完成结果的JSON响应
	 */
	@RequestMapping(value = "/task/complete", method = {RequestMethod.GET, RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<?> completeSignTask(@RequestBody Map<String, String> requestData) {
		String taskInstId = requestData.get("taskInstId");
		String assigneeWorkno = requestData.get("assigneeWorkno");

		// 获取isBranch参数，默认为true
		boolean isBranch = true;
		if (requestData.get("isBranch") != null) {
			isBranch = Boolean.parseBoolean(requestData.get("isBranch"));
		}

		// 获取isBreakUserTask参数，默认为false
		boolean isBreakUserTask = false;
		if (requestData.get("isBreakUserTask") != null) {
			isBreakUserTask = Boolean.parseBoolean(requestData.get("isBreakUserTask"));
		}

		boolean result = BpmDataUtil.completeSignTask(taskInstId, assigneeWorkno, isBranch, isBreakUserTask);
		if (result) {
			return Result.ok("完成签名任务成功", true);
		} else {
			return Result.error("完成签名任务失败");
		}
	}

	@RequestMapping(value = "/query/user", method = {RequestMethod.GET, RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<?> queryUser() {
		return Result.ok("查询用户成功", BpmDataUtil.queryUser());
	}

}
