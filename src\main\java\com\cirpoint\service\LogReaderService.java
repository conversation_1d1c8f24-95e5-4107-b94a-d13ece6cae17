package com.cirpoint.service;

import com.cirpoint.config.LogViewerConfig;
import com.cirpoint.model.LogEntry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 日志读取服务
 * 负责实时读取日志文件并通过SSE推送给客户端
 */
@Service
public class LogReaderService {
    
    private static final Logger logger = LoggerFactory.getLogger(LogReaderService.class);
    
    @Autowired
    private LogViewerConfig config;
    
    // SSE客户端连接管理
    private final ConcurrentHashMap<SseEmitter, Long> sseClients = new ConcurrentHashMap<>();
    
    // 文件读取状态
    private volatile long lastReadPosition = 0;
    private volatile long lastFileSize = 0;
    private volatile String currentLogFilePath;
    
    // 最近日志缓存（用于新连接的客户端）
    private final CopyOnWriteArrayList<LogEntry> recentLogs = new CopyOnWriteArrayList<>();
    
    /**
     * 服务初始化
     */
    @PostConstruct
    public void init() {
        currentLogFilePath = config.getCurrentLogFilePath();
        logger.info("LogReaderService initialized, monitoring file: {}", currentLogFilePath);
        
        // 初始化文件读取位置
        File logFile = new File(currentLogFilePath);
        if (logFile.exists()) {
            lastFileSize = logFile.length();
            lastReadPosition = lastFileSize; // 从文件末尾开始读取
            logger.info("Initial file size: {} bytes, starting from end", lastFileSize);
        }
    }
    
    /**
     * 定时任务：读取最新日志并推送给所有客户端
     */
    @Scheduled(fixedDelayString = "${log.viewer.push-interval-ms:2000}")
    public void readAndPushLogs() {
        if (!config.isEnabled() || sseClients.isEmpty()) {
            return;
        }
        
        try {
            List<LogEntry> newLogs = readIncrementalLogs();
            if (!newLogs.isEmpty()) {
                // 更新最近日志缓存
                updateRecentLogsCache(newLogs);
                
                // 推送给所有客户端
                pushToClients(newLogs);
                
                logger.debug("Pushed {} new log entries to {} clients", 
                    newLogs.size(), sseClients.size());
            }
        } catch (Exception e) {
            logger.error("Error in readAndPushLogs", e);
        }
    }
    
    /**
     * 增量读取日志文件
     */
    public List<LogEntry> readIncrementalLogs() {
        List<LogEntry> newEntries = new ArrayList<>();
        File logFile = new File(currentLogFilePath);
        
        if (!logFile.exists()) {
            logger.warn("Log file does not exist: {}", currentLogFilePath);
            return newEntries;
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(logFile, "r")) {
            long currentSize = raf.length();
            
            // 检查文件是否被轮转（文件变小了）
            if (currentSize < lastReadPosition) {
                logger.info("Log file rotated, resetting read position. Old size: {}, New size: {}", 
                    lastReadPosition, currentSize);
                lastReadPosition = 0;
                recentLogs.clear(); // 清空缓存
            }
            
            // 如果文件没有新内容，直接返回
            if (currentSize <= lastReadPosition) {
                return newEntries;
            }
            
            // 移动到上次读取的位置
            raf.seek(lastReadPosition);
            
            String line;
            while ((line = readLine(raf)) != null) {
                if (!line.trim().isEmpty()) {
                    LogEntry entry = parseLogLine(line);
                    newEntries.add(entry);
                }
            }
            
            // 更新读取位置
            lastReadPosition = raf.getFilePointer();
            lastFileSize = currentSize;
            
        } catch (IOException e) {
            logger.error("Failed to read log file: " + currentLogFilePath, e);
        }
        
        return newEntries;
    }
    
    /**
     * 读取一行，处理UTF-8编码
     */
    private String readLine(RandomAccessFile raf) throws IOException {
        StringBuilder line = new StringBuilder();
        int c;
        
        while ((c = raf.read()) != -1) {
            if (c == '\n') {
                break;
            }
            if (c != '\r') {
                line.append((char) c);
            }
        }
        
        if (line.length() == 0 && c == -1) {
            return null;
        }
        
        // 处理UTF-8编码
        byte[] bytes = line.toString().getBytes(StandardCharsets.ISO_8859_1);
        return new String(bytes, StandardCharsets.UTF_8);
    }
    
    /**
     * 解析日志行为LogEntry对象
     */
    public LogEntry parseLogLine(String line) {
        return new LogEntry(line);
    }
    
    /**
     * 添加SSE客户端
     */
    public boolean addSseClient(SseEmitter emitter) {
        if (sseClients.size() >= config.getMaxSseClients()) {
            logger.warn("Maximum SSE clients reached: {}", config.getMaxSseClients());
            return false;
        }
        
        long connectTime = System.currentTimeMillis();
        sseClients.put(emitter, connectTime);
        
        // 设置超时和完成回调
        emitter.onTimeout(() -> removeSseClient(emitter));
        emitter.onCompletion(() -> removeSseClient(emitter));
        emitter.onError((ex) -> {
            logger.warn("SSE client error", ex);
            removeSseClient(emitter);
        });
        
        logger.info("SSE client connected. Total clients: {}", sseClients.size());
        
        // 发送最近的日志给新客户端
        sendRecentLogsToClient(emitter);
        
        return true;
    }
    
    /**
     * 移除SSE客户端
     */
    public void removeSseClient(SseEmitter emitter) {
        Long connectTime = sseClients.remove(emitter);
        if (connectTime != null) {
            long duration = System.currentTimeMillis() - connectTime;
            logger.info("SSE client disconnected after {} ms. Remaining clients: {}", 
                duration, sseClients.size());
        }
        
        try {
            emitter.complete();
        } catch (Exception e) {
            // 忽略完成时的异常
        }
    }
    
    /**
     * 推送日志到所有客户端
     */
    private void pushToClients(List<LogEntry> logs) {
        if (logs.isEmpty() || sseClients.isEmpty()) {
            return;
        }
        
        Iterator<SseEmitter> iterator = sseClients.keySet().iterator();
        while (iterator.hasNext()) {
            SseEmitter emitter = iterator.next();
            try {
                emitter.send(SseEmitter.event()
                    .name("logs")
                    .data(logs));
            } catch (Exception e) {
                logger.warn("Failed to send to SSE client, removing", e);
                iterator.remove();
                try {
                    emitter.complete();
                } catch (Exception ex) {
                    // 忽略完成时的异常
                }
            }
        }
    }
    
    /**
     * 发送最近的日志给新连接的客户端
     */
    private void sendRecentLogsToClient(SseEmitter emitter) {
        if (recentLogs.isEmpty()) {
            return;
        }
        
        try {
            // 发送最近的日志（最多100条）
            int startIndex = Math.max(0, recentLogs.size() - 100);
            List<LogEntry> recentLogList = new ArrayList<>(
                recentLogs.subList(startIndex, recentLogs.size()));
            
            if (!recentLogList.isEmpty()) {
                emitter.send(SseEmitter.event()
                    .name("recent-logs")
                    .data(recentLogList));
            }
        } catch (Exception e) {
            logger.warn("Failed to send recent logs to new client", e);
            removeSseClient(emitter);
        }
    }
    
    /**
     * 更新最近日志缓存
     */
    private void updateRecentLogsCache(List<LogEntry> newLogs) {
        recentLogs.addAll(newLogs);
        
        // 保持缓存大小在限制范围内
        int maxCache = config.getMaxLogLineCache();
        while (recentLogs.size() > maxCache) {
            recentLogs.remove(0);
        }
    }
    
    /**
     * 获取当前连接的客户端数量
     */
    public int getConnectedClientCount() {
        return sseClients.size();
    }
    
    /**
     * 获取当前连接数（别名方法，为了与测试保持一致）
     */
    public int getConnectionCount() {
        return getConnectedClientCount();
    }
    
    /**
     * 获取当前文件读取位置
     */
    public long getCurrentReadPosition() {
        return lastReadPosition;
    }
    
    /**
     * 获取当前文件大小
     */
    public long getCurrentFileSize() {
        return lastFileSize;
    }
    
    /**
     * 获取最近日志缓存大小
     */
    public int getRecentLogsCacheSize() {
        return recentLogs.size();
    }
    
    /**
     * 清理无效的SSE连接
     */
    public void cleanupInvalidConnections() {
        long now = System.currentTimeMillis();
        long timeoutMs = config.getSseTimeoutMs();
        
        sseClients.entrySet().removeIf(entry -> {
            long connectTime = entry.getValue();
            if (now - connectTime > timeoutMs) {
                logger.info("Removing timeout SSE client");
                try {
                    entry.getKey().complete();
                } catch (Exception e) {
                    // 忽略异常
                }
                return true;
            }
            return false;
        });
    }
} 