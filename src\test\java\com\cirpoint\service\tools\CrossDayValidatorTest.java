package com.cirpoint.service.tools;

import com.cirpoint.constant.WorkTimeConstants;
import com.cirpoint.model.worktime.AttendanceRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 跨日验证器测试类
 */
public class CrossDayValidatorTest {

    private CrossDayValidator validator;
    private DateTimeFormatter formatter;

    @BeforeEach
    public void setUp() {
        validator = new CrossDayValidator();
        formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    }

    @Test
    @DisplayName("测试正常的跨日工作记录")
    public void testNormalCrossDayWork() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 张三正常跨日工作：21日22:00入，22日06:00出
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-21 22:00:00"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-22 06:00:00"));
        
        Set<AttendanceRecord> invalidRecords = validator.validateCrossDayRecords(records);
        
        // 正常跨日工作不应该有异常记录
        assertTrue(invalidRecords.isEmpty(), "正常跨日工作不应该产生异常记录");
    }

    @Test
    @DisplayName("测试前置出场记录验证")
    public void testLeadingOutRecordValidation() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 张三22日第一条记录是出场，但21日没有入场记录
        AttendanceRecord invalidOutRecord = createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-22 06:00:00");
        records.add(invalidOutRecord);
        
        Set<AttendanceRecord> invalidRecords = validator.validateCrossDayRecords(records);
        
        // 应该检测到一条异常记录
        assertEquals(1, invalidRecords.size(), "应该检测到一条前置出场异常记录");
        assertTrue(invalidRecords.contains(invalidOutRecord), "异常记录应该包含前置出场记录");
    }

    @Test
    @DisplayName("测试后置入场记录验证")
    public void testTrailingInRecordValidation() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 张三21日最后一条记录是入场，但22日没有出场记录
        AttendanceRecord invalidInRecord = createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-21 22:00:00");
        records.add(invalidInRecord);
        
        Set<AttendanceRecord> invalidRecords = validator.validateCrossDayRecords(records);
        
        // 应该检测到一条异常记录
        assertEquals(1, invalidRecords.size(), "应该检测到一条后置入场异常记录");
        assertTrue(invalidRecords.contains(invalidInRecord), "异常记录应该包含后置入场记录");
    }

    @Test
    @DisplayName("测试有效的跨日配对")
    public void testValidCrossDayPairing() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 张三21日入场，22日出场（有效配对）
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-21 22:00:00"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-22 06:00:00"));
        
        // 李四22日入场，23日出场（有效配对）
        records.add(createRecord("李四", "920厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-22 20:00:00"));
        records.add(createRecord("李四", "920厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-23 04:00:00"));
        
        Set<AttendanceRecord> invalidRecords = validator.validateCrossDayRecords(records);
        
        // 有效配对不应该产生异常记录
        assertTrue(invalidRecords.isEmpty(), "有效的跨日配对不应该产生异常记录");
    }

    @Test
    @DisplayName("测试多员工多场所混合场景")
    public void testMultiEmployeeMultiLocationScenario() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 张三910厂房：正常跨日工作
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-21 22:00:00"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-22 06:00:00"));
        
        // 张三920厂房：悬挂入场记录
        AttendanceRecord zhangSanInvalid = createRecord("张三", "920厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-22 14:00:00");
        records.add(zhangSanInvalid);
        
        // 李四910厂房：悬挂出场记录
        AttendanceRecord lisiInvalid = createRecord("李四", "910厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-22 08:00:00");
        records.add(lisiInvalid);
        
        // 王五二号楼：正常单日工作
        records.add(createRecord("王五", "二号楼", WorkTimeConstants.DIRECTION_IN, "2025-06-22 08:00:00"));
        records.add(createRecord("王五", "二号楼", WorkTimeConstants.DIRECTION_OUT, "2025-06-22 17:00:00"));
        
        Set<AttendanceRecord> invalidRecords = validator.validateCrossDayRecords(records);
        
        // 应该检测到2条异常记录
        assertEquals(2, invalidRecords.size(), "应该检测到2条异常记录");
        assertTrue(invalidRecords.contains(zhangSanInvalid), "应该包含张三的悬挂入场记录");
        assertTrue(invalidRecords.contains(lisiInvalid), "应该包含李四的悬挂出场记录");
    }

    @Test
    @DisplayName("测试同日多次进出不受影响")
    public void testSameDayMultipleInOut() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 张三同日多次进出
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-22 08:00:00"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-22 12:00:00"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-22 13:00:00"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-22 17:00:00"));
        
        Set<AttendanceRecord> invalidRecords = validator.validateCrossDayRecords(records);
        
        // 同日多次进出不应该产生异常记录
        assertTrue(invalidRecords.isEmpty(), "同日多次进出不应该产生异常记录");
    }

    @Test
    @DisplayName("测试空记录列表")
    public void testEmptyRecordList() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        Set<AttendanceRecord> invalidRecords = validator.validateCrossDayRecords(records);
        
        assertTrue(invalidRecords.isEmpty(), "空记录列表应该返回空的异常记录集合");
    }

    @Test
    @DisplayName("测试单条记录")
    public void testSingleRecord() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 单条入场记录
        AttendanceRecord singleRecord = createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-22 08:00:00");
        records.add(singleRecord);
        
        Set<AttendanceRecord> invalidRecords = validator.validateCrossDayRecords(records);
        
        // 单条入场记录应该被标记为异常（后置入场记录）
        assertEquals(1, invalidRecords.size(), "单条入场记录应该被标记为异常");
        assertTrue(invalidRecords.contains(singleRecord), "异常记录应该包含该单条记录");
    }

    /**
     * 创建测试用的打卡记录
     */
    private AttendanceRecord createRecord(String employeeName, String location, String direction, String eventTimeStr) {
        AttendanceRecord record = new AttendanceRecord();
        record.setEmployeeName(employeeName);
        record.setLocation(location);
        record.setDirection(direction);
        record.setEventTime(LocalDateTime.parse(eventTimeStr, formatter));
        
        // 根据location设置accessPoint
        if ("910厂房".equals(location)) {
            record.setAccessPoint("910" + direction);
        } else if ("920厂房".equals(location)) {
            record.setAccessPoint("920" + direction);
        } else if ("二号楼".equals(location)) {
            record.setAccessPoint("二号楼" + direction);
        }
        
        return record;
    }
}
