package com.cirpoint.service.panorama;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.util.Iterator;
import java.util.List;

/**
 * 全景图XML处理服务
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@Slf4j
@Service
public class PanoramaXmlService {

    /**
     * 更新XML文件中的热点信息
     */
    public void updateHotspotInXml(String extractPath, String pan, String tilt, String editedTitle, String editedDescription) {
        try {
            // 查找pano.xml文件
            File panoXmlFile = PanoramaFileUtils.findPanoXmlFile(new File(extractPath));
            if (panoXmlFile == null) {
                log.warn("未找到pano.xml文件，无法更新XML中的热点信息");
                return;
            }

            // 读取XML文件
            SAXReader reader = new SAXReader();
            Document document = reader.read(panoXmlFile);
            Element root = document.getRootElement();

            // 查找所有panorama节点
            List<Element> panoramas = root.elements("panorama");
            boolean updated = false;
            int matchCount = 0;

            for (Element panorama : panoramas) {
                Element hotspotsElement = panorama.element("hotspots");
                if (hotspotsElement != null) {
                    List<Element> hotspots = hotspotsElement.elements("hotspot");

                    for (Element hotspot : hotspots) {
                        String xmlPan = hotspot.attributeValue("pan");
                        String xmlTilt = hotspot.attributeValue("tilt");
                        String skinid = hotspot.attributeValue("skinid");
                        String currentTitle = hotspot.attributeValue("title");
                        String xmlId = hotspot.attributeValue("id");

                        // 使用pan、tilt和skinid进行精确匹配
                        if ("stand-alone".equals(skinid) &&
                            pan != null && pan.equals(xmlPan) &&
                            tilt != null && tilt.equals(xmlTilt)) {

                            // 更新title和description属性
                            if (editedTitle != null && !editedTitle.trim().isEmpty()) {
                                hotspot.addAttribute("title", editedTitle);
                            }
                            if (editedDescription != null && !editedDescription.trim().isEmpty()) {
                                hotspot.addAttribute("description", editedDescription);
                            }
                            updated = true;
                            matchCount++;
                            // 理论上pan+tilt组合是唯一的，但为了安全起见，继续检查
                        }
                    }
                }
            }

            if (updated) {
                // 写回XML文件
                writeXmlToFile(document, panoXmlFile);
            } else {
                log.warn("未找到XML中的热点 - 定位: pan={}, tilt={}", pan, tilt);
            }

        } catch (Exception e) {
            log.error("更新XML文件中的热点信息失败", e);
        }
    }

    /**
     * 将Document写入XML文件
     */
    public void writeXmlToFile(Document document, File xmlFile) throws Exception {
        OutputFormat format = OutputFormat.createPrettyPrint();
        format.setEncoding("UTF-8");

        try (FileOutputStream fos = new FileOutputStream(xmlFile)) {
            XMLWriter writer = new XMLWriter(fos, format);
            writer.write(document);
            writer.close();
        }
    }

    /**
     * 获取当前全景节点ID
     * @param taskId 任务ID
     * @return 当前节点信息
     */
    public JSONObject getCurrentNode(Long taskId) {
        try {
            // 获取任务的XML文件路径
            String taskSql = "SELECT EXTRACT_PATH FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray taskResult = Util.postQuerySql(taskSql);

            if (taskResult == null || taskResult.size() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            JSONObject taskInfo = (JSONObject) taskResult.get(0);
            String extractPath = taskInfo.getStr("EXTRACT_PATH");

            if (extractPath == null || extractPath.trim().isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务没有XML文件路径");
            }

            // 查找并解析XML文件
            File panoXmlFile = PanoramaFileUtils.findPanoXmlFile(new File(extractPath));
            if (panoXmlFile == null) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到pano.xml文件");
            }

            // 解析XML获取当前节点
            SAXReader reader = new SAXReader();
            Document document = reader.read(panoXmlFile);
            Element root = document.getRootElement();

            // 获取tour节点的start属性
            Element tourElement = root.element("tour");
            String currentNodeId = "node1"; // 默认值

            if (tourElement != null) {
                String startAttribute = tourElement.attributeValue("start");
                if (startAttribute != null && !startAttribute.trim().isEmpty()) {
                    currentNodeId = startAttribute;
                }
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", JSONUtil.createObj()
                            .set("currentNodeId", currentNodeId)
                            .set("taskId", taskId));

        } catch (Exception e) {
            log.error("获取当前节点失败: taskId={}", taskId, e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取当前节点失败: " + e.getMessage());
        }
    }

    /**
     * 添加热点到XML文件
     */
    public void addHotspotToXml(String extractPath, String nodeId, String hotspotXmlId,
                                String pan, String tilt, String title, String description) {
        try {
            // 查找pano.xml文件
            File panoXmlFile = PanoramaFileUtils.findPanoXmlFile(new File(extractPath));
            if (panoXmlFile == null) {
                log.warn("未找到pano.xml文件，无法添加热点到XML");
                return;
            }

            // 读取XML文件
            SAXReader reader = new SAXReader();
            Document document = reader.read(panoXmlFile);
            Element root = document.getRootElement();

            // 查找对应的panorama节点
            List<Element> panoramas = root.elements("panorama");
            boolean added = false;

            for (Element panorama : panoramas) {
                String panoramaId = panorama.attributeValue("id");
                if (nodeId.equals(panoramaId)) {
                    // 找到对应的节点，添加热点
                    Element hotspotsElement = panorama.element("hotspots");
                    if (hotspotsElement != null) {
                        // 创建新的热点元素
                        Element newHotspot = hotspotsElement.addElement("hotspot");
                        newHotspot.addAttribute("tilt", tilt);
                        newHotspot.addAttribute("pan", pan);
                        newHotspot.addAttribute("url", "");
                        newHotspot.addAttribute("title", title);
                        newHotspot.addAttribute("target", "");
                        newHotspot.addAttribute("skinid", "stand-alone");
                        newHotspot.addAttribute("id", hotspotXmlId);
                        newHotspot.addAttribute("description", description != null ? description : title);

                        added = true;
                        break;
                    }
                }
            }

            if (added) {
                // 保存XML文件
                OutputFormat format = OutputFormat.createPrettyPrint();
                format.setEncoding("UTF-8");
                XMLWriter writer = new XMLWriter(new FileWriter(panoXmlFile), format);
                writer.write(document);
                writer.close();
            } else {
                log.warn("未找到对应的panorama节点: {}", nodeId);
            }

        } catch (Exception e) {
            log.error("添加热点到XML失败", e);
        }
    }

    /**
     * 从XML文件中删除热点
     */
    public void removeHotspotFromXml(String extractPath, String nodeId, String pan, String tilt, String hotspotXmlId) {
        try {
            // 查找pano.xml文件
            File panoXmlFile = PanoramaFileUtils.findPanoXmlFile(new File(extractPath));
            if (panoXmlFile == null) {
                log.warn("未找到pano.xml文件，无法从XML中删除热点");
                return;
            }

            // 读取XML文件
            SAXReader reader = new SAXReader();
            Document document = reader.read(panoXmlFile);
            Element root = document.getRootElement();

            // 查找所有panorama节点
            List<Element> panoramas = root.elements("panorama");
            boolean removed = false;
            int removeCount = 0;

            for (Element panorama : panoramas) {
                String panoramaId = panorama.attributeValue("id");

                // 如果指定了节点ID，只在对应节点中查找；否则在所有节点中查找
                if (nodeId == null || nodeId.equals(panoramaId)) {
                    Element hotspotsElement = panorama.element("hotspots");
                    if (hotspotsElement != null) {
                        List<Element> hotspots = hotspotsElement.elements("hotspot");

                        // 使用迭代器安全删除元素
                        Iterator<Element> iterator = hotspots.iterator();
                        while (iterator.hasNext()) {
                            Element hotspot = iterator.next();
                            String xmlPan = hotspot.attributeValue("pan");
                            String xmlTilt = hotspot.attributeValue("tilt");
                            String xmlId = hotspot.attributeValue("id");
                            String skinid = hotspot.attributeValue("skinid");

                            // 使用多重匹配条件确保准确删除
                            boolean matchByCoordinates = pan != null && pan.equals(xmlPan) &&
                                                       tilt != null && tilt.equals(xmlTilt) &&
                                                       "stand-alone".equals(skinid);
                            boolean matchByXmlId = hotspotXmlId != null && hotspotXmlId.equals(xmlId);

                            if (matchByCoordinates || matchByXmlId) {
                                // 从父元素中移除热点
                                hotspotsElement.remove(hotspot);
                                removed = true;
                                removeCount++;

                                // 如果是通过坐标匹配的，理论上应该是唯一的，但为了安全继续检查
                                if (matchByCoordinates) {
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            if (removed) {
                // 写回XML文件
                writeXmlToFile(document, panoXmlFile);
            } else {
                log.warn("未在XML中找到要删除的热点 - 节点: {}, 定位: pan={}, tilt={}, xmlId={}",
                        nodeId, pan, tilt, hotspotXmlId);
            }

        } catch (Exception e) {
            log.error("从XML文件中删除热点失败", e);
        }
    }
}
