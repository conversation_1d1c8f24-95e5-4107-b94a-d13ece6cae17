package com.cirpoint.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Random;

/**
 * 测试控制器 - 用于生成测试日志
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    private final Random random = new Random();
    private final String[] LEVELS = {"INFO", "WARN", "ERROR"};
    private final String[] OPERATIONS = {"用户登录", "文件上传", "数据处理", "系统检查", "任务执行"};

    @GetMapping("/")
    public String test() {
        return "Hello, World!";
    }

    /**
     * 生成测试日志
     */
    @GetMapping("/generate-logs")
    public String generateLogs() {
        log.info("开始生成测试日志...");
        
        for (int i = 0; i < 10; i++) {
            String operation = OPERATIONS[random.nextInt(OPERATIONS.length)];
            String level = LEVELS[random.nextInt(LEVELS.length)];
            
            switch (level) {
                case "INFO":
                    log.info("操作 [{}] 执行成功，处理时间: {}ms", operation, random.nextInt(1000));
                    break;
                case "WARN":
                    log.warn("操作 [{}] 执行缓慢，处理时间: {}ms，超过预期时间", operation, random.nextInt(2000) + 1000);
                    break;
                case "ERROR":
                    log.error("操作 [{}] 执行失败，错误代码: {}, 错误信息: 系统异常", operation, random.nextInt(500) + 500);
                    break;
            }
            
            try {
                Thread.sleep(500); // 每条日志间隔500ms
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("生成测试日志被中断", e);
            }
        }
        
        log.info("测试日志生成完成");
        return "日志生成完成，请查看日志监控页面";
    }

    /**
     * 生成性能测试日志
     */
    @GetMapping("/performance")
    public String testPerformance() {
        log.info("开始性能测试...");
        
        try {
            // 模拟CPU密集操作
            long result = 0;
            for (int i = 0; i < 1000000; i++) {
                result += Math.sqrt(i);
            }
            log.info("CPU密集操作完成，结果: {}", result);
            
            // 模拟内存操作
			// 分配50MB内存
			log.info("内存分配完成，大小: 50MB");
            Thread.sleep(2000);
			System.gc();
            log.info("内存释放完成");
            
            // 模拟IO操作
            Thread.sleep(1500);
            log.info("IO操作模拟完成");
            
        } catch (Exception e) {
            log.error("性能测试过程中发生错误", e);
            return "性能测试失败";
        }
        
        log.info("性能测试完成");
        return "性能测试完成，请查看监控数据";
    }
}
