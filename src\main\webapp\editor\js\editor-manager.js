// editor-manager.js - 编辑器管理相关功能

// 初始化Monaco编辑器
function initMonacoEditor() {
    // 配置 Monaco 基础路径
    var baseUrl = 'static/lib/monaco-editor-0.34/min';

    // 配置 require.js
    require.config({
        baseUrl: baseUrl,
        paths: {
            'vs': 'vs'
        }
    });

    // 配置 Monaco 环境
    window.MonacoEnvironment = {
        getWorkerUrl: function (moduleId, label) {
            return baseUrl + '/vs/base/worker/workerMain.js';
        }
    };

    // 加载中文语言包配置
    require.config({
        'vs/nls': {
            availableLanguages: {
                '*': 'zh-cn'
            }
        }
    });

    // 初始化编辑器
    require(['vs/editor/editor.main', 'vs/basic-languages/log/log'], function () {
        // 注册日志文件语言
        if (monaco && self.MonacoEnvironment && self.MonacoEnvironment.Contrib && self.MonacoEnvironment.Contrib.log) {
            var logContrib = self.MonacoEnvironment.Contrib.log;
            monaco.languages.register({ id: 'log' });
            monaco.languages.setMonarchTokensProvider('log', logContrib.language);
            monaco.languages.setLanguageConfiguration('log', logContrib.conf);
        }
        
        // 加载XML语言支持
        if (monaco && monaco.languages) {
            monaco.languages.register({ id: 'xml' });
        }
        
        // 加载Properties文件语言支持
        if (monaco && monaco.languages) {
            monaco.languages.register({ id: 'properties' });
            // 为Properties文件设置语法高亮规则
            monaco.languages.setMonarchTokensProvider('properties', {
                tokenizer: {
                    root: [
                        // 注释
                        [/^\s*[#!].*$/, 'comment'],
                        // 键值对：key=value 或 key:value
                        [/^([^=:]+)(=|:)(.*)$/, ['key', 'delimiter', 'value']]
                    ]
                }
            });
            // 设置Properties文件的语言配置
            monaco.languages.setLanguageConfiguration('properties', {
                comments: {
                    lineComment: '#'
                },
                brackets: [],
                autoClosingPairs: [],
                surroundingPairs: [],
                folding: {
                    markers: {
                        start: new RegExp("^\\s*#\\s*region\\b"),
                        end: new RegExp("^\\s*#\\s*endregion\\b")
                    }
                }
            });
        }
        
        var container = document.getElementById('editor');
        if (!container) {
            console.error('找不到编辑器容器');
            return;
        }

        // 创建编辑器实例
        window.editor = monaco.editor.create(container, {
            value: '',
            language: 'plaintext',
            theme: 'vs-dark',
            automaticLayout: true,
            minimap: {
                enabled: true
            },
            fontSize: 14,
            tabSize: 4,
            insertSpaces: true,
            wordWrap: 'on',
            lineNumbers: 'on',
            glyphMargin: true,
            folding: true,
            contextmenu: true,
            scrollbar: {
                verticalScrollbarSize: 10,
                horizontalScrollbarSize: 10
            },
        });

        // -----------自定义右键菜单：压缩SQL功能-----------
        // 注册SQL压缩命令（必须在editor实例创建后）
        window.editor.addAction({
            id: 'compress-sql',
            label: '压缩 SQL',
            contextMenuGroupId: '1_modification', // 与格式化文档同组
            contextMenuOrder: 1.6, // 排在格式化文档(1.5)之后
            precondition: 'editorLangId == sql',
            run: function(ed) {
                var selection = ed.getSelection();
                var model = ed.getModel();
                var isSql = model && model.getLanguageId && model.getLanguageId() === 'sql';
                if (!isSql) {
                    layer.msg('仅支持SQL文件压缩', {icon:2});
                    return;
                }
                var text = selection && !selection.isEmpty() ? model.getValueInRange(selection) : model.getValue();
                // 1. 先压缩为一行
                var compressed = text.replace(/[\r\n]+/g, ' ').replace(/\s+/g, ' ').trim();
                // 2. ; 后加换行
                compressed = compressed.replace(/;\s*/g, ";\n");
                // 3. 保证/独占一行（前后都换行，且去除周围空白）
                compressed = compressed.replace(/\s*\/\s*/g, '\n/\n');
                // 4. 去除多余空行
                compressed = compressed.replace(/\n{2,}/g, '\n').trim();
                if (selection && !selection.isEmpty()) {
                    ed.executeEdits('compress-sql', [{
                        range: selection,
                        text: compressed,
                        forceMoveMarkers: true
                    }]);
                } else {
                    ed.setValue(compressed);
                }
                layer.msg('SQL已压缩且每条语句换行', {icon:1});
            }
        });
        // -----------自定义右键菜单：压缩SQL功能 END-----------

        // 创建编辑器后立即绑定编辑器相关事件
        bindEditorEvents();

        // 监听窗口大小变化，自动调整编辑器大小
        window.addEventListener('resize', function () {
            window.editor.layout();
        });

        // 注册SQL语言格式化支持
        monaco.languages.registerDocumentFormattingEditProvider('sql', {
            provideDocumentFormattingEdits: function (model) {
                try {
                    let sql = model.getValue();

                    // 预处理简单查询
                    if (/^select\s+\*?\s+from\s+\w+/i.test(sql)) {
                        sql = sql.replace(/\s+/g, ' ').trim();
                    }

                    // 先用sqlFormatter格式化
                    const formatted = sqlFormatter.format(sql, {
                        language: 'sql',
                        indent: '  ',
                        uppercase: true,
                        params: {
                            layout: {
                                breaks: {
                                    select_clause: 0,
                                    from_clause: 0,
                                    where_clause: 0,
                                    groupby_clause: 0,
                                    having_clause: 0,
                                    orderby_clause: 0
                                },
                                comma: 'before',
                                linesBetweenQueries: 1
                            },
                            columnCount: 999,
                            columnWidth: 999
                        }
                    });

                    // 处理 ; 换行
                    let finalSql = formatted.replace(/;\s*/g, ";\n");
                    // 保证/独占一行（前后都换行，且去除周围空白）
                    finalSql = finalSql.replace(/\s*\/\s*/g, '\n/\n');
                    // 去除多余空行
                    finalSql = finalSql.replace(/\n{2,}/g, '\n').trim();

                    return [{
                        text: finalSql,
                        range: model.getFullModelRange()
                    }];
                } catch (e) {
                    console.error('SQL格式化失败:', e);
                    return [];
                }
            }
        });
        
        // 注册XML语言格式化支持
        monaco.languages.registerDocumentFormattingEditProvider('xml', {
            provideDocumentFormattingEdits: function(model) {
                try {
                    const xml = model.getValue();
                    const formatted = formatXML(xml);
                    
                    return [{
                        text: formatted,
                        range: model.getFullModelRange()
                    }];
                } catch (e) {
                    console.error('XML格式化失败:', e);
                    return [];
                }
            }
        });
        
        // 注册Properties文件格式化支持
        monaco.languages.registerDocumentFormattingEditProvider('properties', {
            provideDocumentFormattingEdits: function(model) {
                try {
                    const properties = model.getValue();
                    const formatted = formatProperties(properties);
                    
                    return [{
                        text: formatted,
                        range: model.getFullModelRange()
                    }];
                } catch (e) {
                    console.error('Properties格式化失败:', e);
                    return [];
                }
            }
        });

        // 初始化调整器
        initResizer();

        // 在编辑器完全加载后创建标签页管理器
        window.tabManager = window.tabManagerModule.createTabManager(window.editor);

        console.log('Monaco编辑器初始化完成');
    });
}

// 新增编辑器事件绑定函数
function bindEditorEvents() {
    window.editor.onDidChangeModelContent(function () {
        if (window.tabManager.activeTab) {
            window.tabManager.activeTab.modified = true;
        }
    });

    // 添加滚动事件监听，实时保存滚动位置
    window.editor.onDidScrollChange(function() {
        // 确保当前有活动标签页
        if (window.tabManager && window.tabManager.activeTab) {
            // 保存当前视图状态，包含滚动位置
            window.tabManager.activeTab.viewState = window.editor.saveViewState();
        }
    });
}

// 保存文件
function saveFile() {
    if (!window.tabManager.activeTab) {
        layer.msg('没有活动的文件', { icon: 2 });
        return;
    }

    // 如果是图片文件，不需要保存
    if (window.tabManager.activeTab.isImage) {
        layer.msg('图片文件无需保存', { icon: 1 });
        return;
    }

    // 获取当前编辑器内容
    const content = window.editor.getValue();
    const path = window.tabManager.activeTab.path;

    // 调用保存API
    $.ajax({
        url: '/editor/save',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            path: path,
            content: content
        }),
        success: function () {
            layer.msg('保存成功', { icon: 1 });
            // 更新标签页状态
            window.tabManager.activeTab.modified = false;
        },
        error: function (xhr) {
            layer.msg('保存失败: ' + xhr.responseText, { icon: 2 });
        }
    });
}

// 初始化拖动调整宽度功能
function initResizer() {
    const resizer = document.getElementById('resizer');
    const sidebar = document.querySelector('.layui-side');
    const body = document.querySelector('.layui-body');

    if (!resizer || !sidebar || !body) return;

    let isResizing = false;
    let lastDownX = 0;

    // 最小和最大宽度限制
    const minWidth = 200;
    const maxWidth = 600;

    // 鼠标按下事件
    resizer.addEventListener('mousedown', function(e) {
        isResizing = true;
        lastDownX = e.clientX;

        // 添加活动状态样式
        resizer.classList.add('active');
        document.body.classList.add('resizing');

        // 阻止默认事件和冒泡
        e.preventDefault();
        e.stopPropagation();
    });

    // 鼠标移动事件
    document.addEventListener('mousemove', function(e) {
        if (!isResizing) return;

        // 计算新宽度
        const newWidth = Math.max(minWidth, Math.min(maxWidth, e.clientX));

        // 更新侧边栏宽度
        sidebar.style.width = newWidth + 'px';

        // 更新分隔线位置
        resizer.style.left = newWidth + 'px';

        // 更新编辑区域左边距
        body.style.left = newWidth + 'px';

        // 阻止默认事件
        e.preventDefault();
    });

    // 鼠标松开事件
    document.addEventListener('mouseup', function() {
        if (!isResizing) return;

        // 重置状态
        isResizing = false;

        // 移除活动状态样式
        resizer.classList.remove('active');
        document.body.classList.remove('resizing');

        // 重新布局编辑器
        if (window.editor) {
            window.editor.layout();
        }
    });
}

// Properties文件格式化函数
function formatProperties(text) {
    if (!text || typeof text !== 'string') {
        return text;
    }

    try {
        // 按行分割
        const lines = text.split(/\r?\n/);
        const formattedLines = [];
        
        // 查找最长的键名，用于对齐等号
        let maxKeyLength = 0;
        const nonCommentLines = lines.filter(line => {
            // 忽略空行和注释行
            const trimmedLine = line.trim();
            return trimmedLine && !trimmedLine.startsWith('#') && !trimmedLine.startsWith('!');
        });
        
        // 计算最长键名
        for (const line of nonCommentLines) {
            const match = line.match(/^([^=:]+)(=|:)/);
            if (match) {
                const keyLength = match[1].trim().length;
                if (keyLength > maxKeyLength) {
                    maxKeyLength = keyLength;
                }
            }
        }
        
        // 格式化每一行
        for (const line of lines) {
            const trimmedLine = line.trim();
            
            // 处理空行
            if (!trimmedLine) {
                formattedLines.push('');
                continue;
            }
            
            // 处理注释行
            if (trimmedLine.startsWith('#') || trimmedLine.startsWith('!')) {
                formattedLines.push(trimmedLine);
                continue;
            }
            
            // 处理键值对
            const match = line.match(/^([^=:]+)(=|:)(.*)$/);
            if (match) {
                const key = match[1].trim();
                const delimiter = match[2]; // = 或 :
                const value = match[3].trim();
                
                // 对齐等号/冒号
                const paddedKey = key.padEnd(maxKeyLength, ' ');
                formattedLines.push(`${paddedKey} ${delimiter} ${value}`);
            } else {
                // 无法解析的行，保持原样
                formattedLines.push(trimmedLine);
            }
        }
        
        return formattedLines.join('\n');
    } catch (e) {
        console.error('Properties格式化错误:', e);
        // 发生错误时返回原始字符串
        return text;
    }
}

// XML格式化函数
function formatXML(xml) {
    // 验证XML字符串是否为空
    if (!xml || typeof xml !== 'string') {
        return xml;
    }

    try {
        // 保存原XML中的空行位置索引（从0开始）
        const originalLines = xml.split(/\r?\n/);
        const emptyLineIndices = [];
        
        for (let i = 0; i < originalLines.length; i++) {
            if (originalLines[i].trim() === '') {
                emptyLineIndices.push(i);
            }
        }
        
        // 执行常规XML格式化
        let formattedXml = formatXmlContent(xml);
        
        // 现在根据原始空行位置重新插入空行
        const formattedLines = formattedXml.split(/\r?\n/);
        const resultLines = [];
        
        // 计数插入了多少空行
        let insertedEmptyLines = 0;
        
        // 遍历每一行
        for (let i = 0; i < formattedLines.length; i++) {
            // 当前行的实际位置（考虑插入的空行）
            const actualLineNumber = i + insertedEmptyLines;
            
            // 检查是否需要在当前行前插入空行
            while (emptyLineIndices.length > 0 && emptyLineIndices[0] === actualLineNumber) {
                resultLines.push('');
                emptyLineIndices.shift(); // 移除已处理的索引
                insertedEmptyLines++;
            }
            
            // 添加当前行
            resultLines.push(formattedLines[i]);
        }
        
        // 处理文件末尾的空行
        while (emptyLineIndices.length > 0 && emptyLineIndices[0] === resultLines.length) {
            resultLines.push('');
            emptyLineIndices.shift();
        }
        
        return resultLines.join('\n');
    } catch (e) {
        console.error('XML格式化错误:', e);
        // 发生错误时返回原始XML字符串
        return xml;
    }
}

// 格式化XML内容（不考虑空行）
function formatXmlContent(xml) {
    if (!xml || typeof xml !== 'string') {
        return xml;
    }

    try {
        // 移除多余的空格但保留标签内容进行格式化处理
        let cleanXml = xml.replace(/>\s+</g, '><').trim();
        
        // 检查是否有XML声明行
        const hasXmlDeclaration = cleanXml.startsWith('<?xml');
        let xmlDeclaration = '';
        let mainXml = cleanXml;

        // 如果有XML声明，将其提取出来单独处理
        if (hasXmlDeclaration) {
            const declarationEndIndex = cleanXml.indexOf('?>') + 2;
            xmlDeclaration = cleanXml.substring(0, declarationEndIndex);
            mainXml = cleanXml.substring(declarationEndIndex).trim();
        }

        // 保存CDATA和注释内容，避免被格式化
        const preservedTokens = [];
        
        // 替换CDATA内容为占位符
        mainXml = mainXml.replace(/<!\[CDATA\[[\s\S]*?\]\]>/g, function(match) {
            preservedTokens.push(match);
            return '___PRESERVED_TOKEN_' + (preservedTokens.length - 1) + '___';
        });
        
        // 替换注释内容为占位符
        mainXml = mainXml.replace(/<!--[\s\S]*?-->/g, function(match) {
            preservedTokens.push(match);
            return '___PRESERVED_TOKEN_' + (preservedTokens.length - 1) + '___';
        });

        // 初始化缩进配置
        const INDENT = '    '; // 四个空格的缩进
        let formatted = '';
        let depth = 0;
        
        // 分析标签的类型和内容
        const analyzeXml = function() {
            const result = [];
            let currentPos = 0;
            let currentTagStart = -1;
            let inTag = false;
            
            const processRemainingContent = () => {
                if (currentPos < mainXml.length) {
                    result.push({
                        type: 'text',
                        content: mainXml.substring(currentPos)
                    });
                }
            };
            
            for (let i = 0; i < mainXml.length; i++) {
                const char = mainXml[i];
                
                if (char === '<' && !inTag) {
                    // 处理标签前的文本内容
                    if (i > currentPos) {
                        result.push({
                            type: 'text',
                            content: mainXml.substring(currentPos, i)
                        });
                    }
                    
                    inTag = true;
                    currentTagStart = i;
                } else if (char === '>' && inTag) {
                    inTag = false;
                    const tagContent = mainXml.substring(currentTagStart, i + 1);
                    result.push({
                        type: 'tag',
                        content: tagContent
                    });
                    currentPos = i + 1;
                }
            }
            
            processRemainingContent();
            return result;
        };
        
        const xmlParts = analyzeXml();
        
        const formatElement = function(parts, startIndex) {
            let localDepth = depth;
            let currentIndex = startIndex;
            let output = '';
            
            if (currentIndex >= parts.length) {
                return { output, endIndex: currentIndex };
            }
            
            // 获取开始标签
            const startTag = parts[currentIndex];
            currentIndex++;
            
            // 检查是否是自闭合标签
            if (startTag.content.endsWith('/>')) {
                output += '\n' + INDENT.repeat(localDepth) + startTag.content;
                return { output, endIndex: currentIndex };
            }
            
            // 检查后面是否紧跟文本内容
            if (currentIndex < parts.length && parts[currentIndex].type === 'text') {
                const textContent = parts[currentIndex].content.trim();
                currentIndex++;
                
                // 查找匹配的结束标签
                if (currentIndex < parts.length && parts[currentIndex].type === 'tag' && parts[currentIndex].content.startsWith('</')) {
                    // 这是一个只包含文本的元素，不换行处理
                    output += '\n' + INDENT.repeat(localDepth) + startTag.content + textContent + parts[currentIndex].content;
                    currentIndex++;
                    return { output, endIndex: currentIndex };
                } else {
                    // 不是简单的文本元素，回退处理
                    currentIndex--;
                }
            }
            
            // 正常处理带有子元素的标签
            output += '\n' + INDENT.repeat(localDepth) + startTag.content;
            depth++;
            
            // 处理子元素
            while (currentIndex < parts.length) {
                const part = parts[currentIndex];
                
                if (part.type === 'tag' && part.content.startsWith('</')) {
                    // 找到结束标签
                    depth--;
                    output += '\n' + INDENT.repeat(depth) + part.content;
                    currentIndex++;
                    break;
                } else if (part.type === 'text' && part.content.trim() === '') {
                    // 忽略空白文本
                    currentIndex++;
                } else if (part.type === 'tag') {
                    // 递归处理子元素
                    const result = formatElement(parts, currentIndex);
                    output += result.output;
                    currentIndex = result.endIndex;
                } else {
                    // 处理其他内容（文本等）
                    output += '\n' + INDENT.repeat(depth) + part.content.trim();
                    currentIndex++;
                }
            }
            
            return { output, endIndex: currentIndex };
        };
        
        // 开始格式化
        let index = 0;
        while (index < xmlParts.length) {
            const part = xmlParts[index];
            
            if (part.type === 'tag' && !part.content.startsWith('</')) {
                // 处理开始标签
                const result = formatElement(xmlParts, index);
                formatted += result.output;
                index = result.endIndex;
            } else {
                // 处理其他内容
                formatted += '\n' + INDENT.repeat(depth) + part.content;
                index++;
            }
        }
        
        // 移除第一个换行符
        if (formatted.startsWith('\n')) {
            formatted = formatted.substring(1);
        }
        
        // 恢复CDATA和注释内容
        for (let i = 0; i < preservedTokens.length; i++) {
            const token = preservedTokens[i];
            formatted = formatted.replace('___PRESERVED_TOKEN_' + i + '___', token);
        }
        
        // 如果有XML声明，将其添加到结果前面
        if (hasXmlDeclaration) {
            return xmlDeclaration + '\n' + formatted;
        }
        
        return formatted;
    } catch (e) {
        console.error('XML内容格式化错误:', e);
        return xml;
    }
}

// 导出函数
window.editorManager = {
    initMonacoEditor: initMonacoEditor,
    bindEditorEvents: bindEditorEvents,
    saveFile: saveFile,
    initResizer: initResizer,
    formatXML: formatXML,
    formatProperties: formatProperties
};