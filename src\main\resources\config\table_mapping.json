{"tableMapping": {"标定参数": {"tableName": "TX_CALIBRATION_PARAMETERS", "description": "标定参数表", "fields": [{"accessColName": "编号", "fieldName": "ID", "description": "唯一标识符"}, {"accessColName": "SD_传感器到原点距离", "fieldName": "SENSOR_DISTANCE_TO_ORIGIN", "description": "传感器到原点的距离"}, {"accessColName": "SD_标定等级", "fieldName": "CALIBRATION_LEVEL", "description": "标定等级"}, {"accessColName": "SD_平台重量", "fieldName": "PLATFORM_WEIGHT", "description": "平台重量"}, {"accessColName": "SD_平台P1系数", "fieldName": "PLATFORM_P1_COEF", "description": "平台P1系数"}, {"accessColName": "SD_平台P2系数", "fieldName": "PLATFORM_P2_COEF", "description": "平台P2系数"}, {"accessColName": "SD_平台P3系数", "fieldName": "PLATFORM_P3_COEF", "description": "平台P3系数"}, {"accessColName": "SD_砝码1重量", "fieldName": "WEIGHT1", "description": "砝码1重量"}, {"accessColName": "SD_砝码1P1系数", "fieldName": "WEIGHT1_P1_COEF", "description": "砝码1P1系数"}, {"accessColName": "SD_砝码1P2系数", "fieldName": "WEIGHT1_P2_COEF", "description": "砝码1P2系数"}, {"accessColName": "SD_砝码1P3系数", "fieldName": "WEIGHT1_P3_COEF", "description": "砝码1P3系数"}, {"accessColName": "SD_砝码2重量", "fieldName": "WEIGHT2", "description": "砝码2重量"}, {"accessColName": "SD_砝码2P1系数", "fieldName": "WEIGHT2_P1_COEF", "description": "砝码2P1系数"}, {"accessColName": "SD_砝码2P2系数", "fieldName": "WEIGHT2_P2_COEF", "description": "砝码2P2系数"}, {"accessColName": "SD_砝码2P3系数", "fieldName": "WEIGHT2_P3_COEF", "description": "砝码2P3系数"}, {"accessColName": "SD_砝码3重量", "fieldName": "WEIGHT3", "description": "砝码3重量"}, {"accessColName": "SD_砝码3P1系数", "fieldName": "WEIGHT3_P1_COEF", "description": "砝码3P1系数"}, {"accessColName": "SD_砝码3P2系数", "fieldName": "WEIGHT3_P2_COEF", "description": "砝码3P2系数"}, {"accessColName": "SD_砝码3P3系数", "fieldName": "WEIGHT3_P3_COEF", "description": "砝码3P3系数"}, {"accessColName": "SD_砝码4重量", "fieldName": "WEIGHT4", "description": "砝码4重量"}, {"accessColName": "SD_砝码4P1系数", "fieldName": "WEIGHT4_P1_COEF", "description": "砝码4P1系数"}, {"accessColName": "SD_砝码4P2系数", "fieldName": "WEIGHT4_P2_COEF", "description": "砝码4P2系数"}, {"accessColName": "SD_砝码4P3系数", "fieldName": "WEIGHT4_P3_COEF", "description": "砝码4P3系数"}, {"accessColName": "SD_砝码5重量", "fieldName": "WEIGHT5", "description": "砝码5重量"}, {"accessColName": "SD_砝码5P1系数", "fieldName": "WEIGHT5_P1_COEF", "description": "砝码5P1系数"}, {"accessColName": "SD_砝码5P2系数", "fieldName": "WEIGHT5_P2_COEF", "description": "砝码5P2系数"}, {"accessColName": "SD_砝码5P3系数", "fieldName": "WEIGHT5_P3_COEF", "description": "砝码5P3系数"}, {"accessColName": "SD_砝码6重量", "fieldName": "WEIGHT6", "description": "砝码6重量"}, {"accessColName": "SD_砝码6P1系数", "fieldName": "WEIGHT6_P1_COEF", "description": "砝码6P1系数"}, {"accessColName": "SD_砝码6P2系数", "fieldName": "WEIGHT6_P2_COEF", "description": "砝码6P2系数"}, {"accessColName": "SD_砝码6P3系数", "fieldName": "WEIGHT6_P3_COEF", "description": "砝码6P3系数"}, {"accessColName": "SD_砝码7重量", "fieldName": "WEIGHT7", "description": "砝码7重量"}, {"accessColName": "SD_砝码7P1系数", "fieldName": "WEIGHT7_P1_COEF", "description": "砝码7P1系数"}, {"accessColName": "SD_砝码7P2系数", "fieldName": "WEIGHT7_P2_COEF", "description": "砝码7P2系数"}, {"accessColName": "SD_砝码7P3系数", "fieldName": "WEIGHT7_P3_COEF", "description": "砝码7P3系数"}, {"accessColName": "SD_砝码8重量", "fieldName": "WEIGHT8", "description": "砝码8重量"}, {"accessColName": "SD_砝码8P1系数", "fieldName": "WEIGHT8_P1_COEF", "description": "砝码8P1系数"}, {"accessColName": "SD_砝码8P2系数", "fieldName": "WEIGHT8_P2_COEF", "description": "砝码8P2系数"}, {"accessColName": "SD_砝码8P3系数", "fieldName": "WEIGHT8_P3_COEF", "description": "砝码8P3系数"}, {"accessColName": "SD_砝码9重量", "fieldName": "WEIGHT9", "description": "砝码9重量"}, {"accessColName": "SD_砝码9P1系数", "fieldName": "WEIGHT9_P1_COEF", "description": "砝码9P1系数"}, {"accessColName": "SD_砝码9P2系数", "fieldName": "WEIGHT9_P2_COEF", "description": "砝码9P2系数"}, {"accessColName": "SD_砝码9P3系数", "fieldName": "WEIGHT9_P3_COEF", "description": "砝码9P3系数"}, {"accessColName": "SD_砝码10重量", "fieldName": "WEIGHT10", "description": "砝码10重量"}, {"accessColName": "SD_砝码10P1系数", "fieldName": "WEIGHT10_P1_COEF", "description": "砝码10P1系数"}, {"accessColName": "SD_砝码10P2系数", "fieldName": "WEIGHT10_P2_COEF", "description": "砝码10P2系数"}, {"accessColName": "SD_砝码10P3系数", "fieldName": "WEIGHT10_P3_COEF", "description": "砝码10P3系数"}, {"accessColName": "SD_平台P1码值", "fieldName": "PLATFORM_P1_CODE", "description": "平台P1码值"}, {"accessColName": "SD_平台P2码值", "fieldName": "PLATFORM_P2_CODE", "description": "平台P2码值"}, {"accessColName": "SD_平台P3码值", "fieldName": "PLATFORM_P3_CODE", "description": "平台P3码值"}, {"accessColName": "SD_砝码1P1码值", "fieldName": "WEIGHT1_P1_CODE", "description": "砝码1P1码值"}, {"accessColName": "SD_砝码1P2码值", "fieldName": "WEIGHT1_P2_CODE", "description": "砝码1P2码值"}, {"accessColName": "SD_砝码1P3码值", "fieldName": "WEIGHT1_P3_CODE", "description": "砝码1P3码值"}, {"accessColName": "SD_砝码2P1码值", "fieldName": "WEIGHT2_P1_CODE", "description": "砝码2P1码值"}, {"accessColName": "SD_砝码2P2码值", "fieldName": "WEIGHT2_P2_CODE", "description": "砝码2P2码值"}, {"accessColName": "SD_砝码2P3码值", "fieldName": "WEIGHT2_P3_CODE", "description": "砝码2P3码值"}, {"accessColName": "SD_砝码3P1码值", "fieldName": "WEIGHT3_P1_CODE", "description": "砝码3P1码值"}, {"accessColName": "SD_砝码3P2码值", "fieldName": "WEIGHT3_P2_CODE", "description": "砝码3P2码值"}, {"accessColName": "SD_砝码3P3码值", "fieldName": "WEIGHT3_P3_CODE", "description": "砝码3P3码值"}, {"accessColName": "SD_砝码4P1码值", "fieldName": "WEIGHT4_P1_CODE", "description": "砝码4P1码值"}, {"accessColName": "SD_砝码4P2码值", "fieldName": "WEIGHT4_P2_CODE", "description": "砝码4P2码值"}, {"accessColName": "SD_砝码4P3码值", "fieldName": "WEIGHT4_P3_CODE", "description": "砝码4P3码值"}, {"accessColName": "SD_砝码5P1码值", "fieldName": "WEIGHT5_P1_CODE", "description": "砝码5P1码值"}, {"accessColName": "SD_砝码5P2码值", "fieldName": "WEIGHT5_P2_CODE", "description": "砝码5P2码值"}, {"accessColName": "SD_砝码5P3码值", "fieldName": "WEIGHT5_P3_CODE", "description": "砝码5P3码值"}, {"accessColName": "SD_砝码6P1码值", "fieldName": "WEIGHT6_P1_CODE", "description": "砝码6P1码值"}, {"accessColName": "SD_砝码6P2码值", "fieldName": "WEIGHT6_P2_CODE", "description": "砝码6P2码值"}, {"accessColName": "SD_砝码6P3码值", "fieldName": "WEIGHT6_P3_CODE", "description": "砝码6P3码值"}, {"accessColName": "SD_砝码7P1码值", "fieldName": "WEIGHT7_P1_CODE", "description": "砝码7P1码值"}, {"accessColName": "SD_砝码7P2码值", "fieldName": "WEIGHT7_P2_CODE", "description": "砝码7P2码值"}, {"accessColName": "SD_砝码7P3码值", "fieldName": "WEIGHT7_P3_CODE", "description": "砝码7P3码值"}, {"accessColName": "SD_砝码8P1码值", "fieldName": "WEIGHT8_P1_CODE", "description": "砝码8P1码值"}, {"accessColName": "SD_砝码8P2码值", "fieldName": "WEIGHT8_P2_CODE", "description": "砝码8P2码值"}, {"accessColName": "SD_砝码8P3码值", "fieldName": "WEIGHT8_P3_CODE", "description": "砝码8P3码值"}, {"accessColName": "SD_砝码9P1码值", "fieldName": "WEIGHT9_P1_CODE", "description": "砝码9P1码值"}, {"accessColName": "SD_砝码9P2码值", "fieldName": "WEIGHT9_P2_CODE", "description": "砝码9P2码值"}, {"accessColName": "SD_砝码9P3码值", "fieldName": "WEIGHT9_P3_CODE", "description": "砝码9P3码值"}, {"accessColName": "SD_砝码10P1码值", "fieldName": "WEIGHT10_P1_CODE", "description": "砝码10P1码值"}, {"accessColName": "SD_砝码10P2码值", "fieldName": "WEIGHT10_P2_CODE", "description": "砝码10P2码值"}, {"accessColName": "SD_砝码10P3码值", "fieldName": "WEIGHT10_P3_CODE", "description": "砝码10P3码值"}, {"accessColName": "SD_标定日期", "fieldName": "CALIBRATION_DATE", "description": "标定日期"}, {"accessColName": "MD_八点标定结论", "fieldName": "MD_EIGHT_POINT_CONCLUSION", "description": "八点标定结论"}, {"accessColName": "MD_八点标定日期", "fieldName": "EIGHT_POINT_CALIB_DATE", "description": "八点标定日期"}, {"accessColName": "MD_质量校正结论", "fieldName": "MD_MASS_CORRECT_CONCLUSION", "description": "质量校正结论"}, {"accessColName": "MD_质量校正日期", "fieldName": "MASS_CORRECTION_DATE", "description": "质量校正日期"}, {"accessColName": "Plat_P1重量", "fieldName": "PLATFORM_P1_WEIGHT", "description": "平台P1重量"}, {"accessColName": "Plat_P2重量", "fieldName": "PLATFORM_P2_WEIGHT", "description": "平台P2重量"}, {"accessColName": "Plat_P3重量", "fieldName": "PLATFORM_P3_WEIGHT", "description": "平台P3重量"}, {"accessColName": "Plat_PTotal重量", "fieldName": "PLATFORM_TOTAL_WEIGHT", "description": "平台总重量"}]}, "测试数据表": {"tableName": "TX_TEST_DATA", "description": "测试数据表", "fields": [{"accessColName": "测试编号", "fieldName": "TEST_ID", "description": ""}, {"accessColName": "产品型号", "fieldName": "PRODUCT_MODEL", "description": ""}, {"accessColName": "批次", "fieldName": "BATCH", "description": ""}, {"accessColName": "产品名称", "fieldName": "PRODUCT_NAME", "description": ""}, {"accessColName": "操作人员1", "fieldName": "OPERATOR1", "description": ""}, {"accessColName": "操作人员2", "fieldName": "OPERATOR2", "description": ""}, {"accessColName": "操作人员3", "fieldName": "OPERATOR3", "description": ""}, {"accessColName": "操作人员4", "fieldName": "OPERATOR4", "description": ""}, {"accessColName": "测试日期", "fieldName": "TEST_DATE", "description": ""}, {"accessColName": "HPlatP1Weight", "fieldName": "H_PLAT_P1_WEIGHT", "description": "水平测试时，平台P1"}, {"accessColName": "HPlatP2Weight", "fieldName": "H_PLAT_P2_WEIGHT", "description": "水平测试时，平台P2"}, {"accessColName": "HPlatP3Weight", "fieldName": "H_PLAT_P3_WEIGHT", "description": "水平测试时，平台P3"}, {"accessColName": "HPlatPTotalWeight", "fieldName": "H_PLAT_TOTAL_WEIGHT", "description": "水平测试时，平台总重量"}, {"accessColName": "H_Clamp_P1", "fieldName": "H_CLAMP_P1", "description": "水平测试时，夹具P1"}, {"accessColName": "H_Clamp_P2", "fieldName": "H_CLAMP_P2", "description": "水平测试时，夹具P2"}, {"accessColName": "H_Clamp_P3", "fieldName": "H_CLAMP_P3", "description": "水平测试时，夹具P3"}, {"accessColName": "H_Clamp_G", "fieldName": "H_CLAMP_G", "description": "水平测试时，夹具总重"}, {"accessColName": "H_Clamp_Yc", "fieldName": "H_CLAMP_YC", "description": "水平测试时，夹具Y向质心"}, {"accessColName": "H_Clamp_Zc", "fieldName": "H_CLAMP_ZC", "description": "水平测试时，夹具Z向质心"}, {"accessColName": "H_Clamp_R", "fieldName": "H_CLAMP_R", "description": "水平测试时，夹具质心与坐标原点距离"}, {"accessColName": "H_Clamp_Alfa", "fieldName": "H_CLAMP_ALFA", "description": "水平测试时，夹具质心与坐标原点连线与+Y轴间角度"}, {"accessColName": "H_Clamp_My", "fieldName": "H_CLAMP_MY", "description": "水平测试时，夹具Y向力矩"}, {"accessColName": "H_Clamp_Mz", "fieldName": "H_CLAMP_MZ", "description": "水平测试时，夹具Z向力矩"}, {"accessColName": "H_Clamp_BalanceAngle", "fieldName": "H_CLAMP_BALANCE_ANGLE", "description": "水平测试时，夹具配平角度"}, {"accessColName": "H_Clamp_BalanceMoment", "fieldName": "H_CLAMP_BALANCE_MOMENT", "description": "水平测试时，夹具配平力矩"}, {"accessColName": "H_ClampSatellite_P1", "fieldName": "H_CLAMP_SATELLITE_P1", "description": "水平测试时，夹具+卫星P1"}, {"accessColName": "H_ClampSatellite_P2", "fieldName": "H_CLAMP_SATELLITE_P2", "description": "水平测试时，夹具+卫星P2"}, {"accessColName": "H_ClampSatellite_P3", "fieldName": "H_CLAMP_SATELLITE_P3", "description": "水平测试时，夹具+卫星P3"}, {"accessColName": "H_ClampSatellite_G", "fieldName": "H_CLAMP_SATELLITE_G", "description": "水平测试时，夹具+卫星总重"}, {"accessColName": "H_ClampSatellite_Yc", "fieldName": "H_CLAMP_SATELLITE_YC", "description": "水平测试时，夹具+卫星Y向质心"}, {"accessColName": "H_ClampSatellite_Zc", "fieldName": "H_CLAMP_SATELLITE_ZC", "description": "水平测试时，夹具+卫星Z向质心"}, {"accessColName": "H_ClampSatellite_R", "fieldName": "H_CLAMP_SATELLITE_R", "description": "水平测试时，夹具+卫星质心与坐标原点距离"}, {"accessColName": "H_ClampSatellite_Alfa", "fieldName": "H_CLAMP_SATELLITE_ALFA", "description": "水平测试时，夹具+卫星质心与坐标原点连线与+Y轴间角度"}, {"accessColName": "H_ClampSatellite_My", "fieldName": "H_CLAMP_SATELLITE_MY", "description": "水平测试时，夹具+卫星Y向力矩"}, {"accessColName": "H_ClampSatellite_Mz", "fieldName": "H_CLAMP_SATELLITE_MZ", "description": "水平测试时，夹具+卫星Z向力矩"}, {"accessColName": "H_ClampSatellite_BalanceAngle", "fieldName": "H_CLAMP_SAT_BAL_ANGLE", "description": "水平测试时，夹具+卫星配平角度"}, {"accessColName": "H_ClampSatellite_BalanceMoment", "fieldName": "H_CLAMP_SAT_BAL_MOMENT", "description": "水平测试时，夹具+卫星配平力矩"}, {"accessColName": "H_Satellite_P1", "fieldName": "H_SATELLITE_P1", "description": "水平测试时，卫星P1"}, {"accessColName": "H_Satellite_P2", "fieldName": "H_SATELLITE_P2", "description": "水平测试时，卫星P2"}, {"accessColName": "H_Satellite_P3", "fieldName": "H_SATELLITE_P3", "description": "水平测试时，卫星P3"}, {"accessColName": "H_Satellite_G", "fieldName": "H_SATELLITE_G", "description": "水平测试时，卫星总重"}, {"accessColName": "H_Satellite_Yc", "fieldName": "H_SATELLITE_YC", "description": "水平测试时，卫星Y向质心"}, {"accessColName": "H_Satellite_Zc", "fieldName": "H_SATELLITE_ZC", "description": "水平测试时，卫星Z向质心"}, {"accessColName": "H_Satellite_R", "fieldName": "H_SATELLITE_R", "description": "水平测试时，卫星质心与坐标原点距离"}, {"accessColName": "H_Satellite_Alfa", "fieldName": "H_SATELLITE_ALFA", "description": "水平测试时，卫星质心与坐标原点连线与+X轴间角度"}, {"accessColName": "H_Satellite_My", "fieldName": "H_SATELLITE_MY", "description": "水平测试时，卫星Y向力矩"}, {"accessColName": "H_Satellite_Mz", "fieldName": "H_SATELLITE_MZ", "description": "水平测试时，卫星Z向力矩"}, {"accessColName": "H_Satellite_BalanceAngle", "fieldName": "H_SATELLITE_BALANCE_ANGLE", "description": "水平测试时，卫星配平角度"}, {"accessColName": "H_Satellite_BalanceMoment", "fieldName": "H_SATELLITE_BALANCE_MOMENT", "description": "水平测试时，卫星配平力矩"}, {"accessColName": "VPlatP1Weight", "fieldName": "V_PLAT_P1_WEIGHT", "description": "倾斜测试时，平台P1"}, {"accessColName": "VPlatP2Weight", "fieldName": "V_PLAT_P2_WEIGHT", "description": "倾斜测试时，平台P2"}, {"accessColName": "VPlatP3Weight", "fieldName": "V_PLAT_P3_WEIGHT", "description": "倾斜测试时，平台P3"}, {"accessColName": "VPlatPTotalWeight", "fieldName": "V_PLAT_TOTAL_WEIGHT", "description": "倾斜测试时，平台总重量"}, {"accessColName": "V_Clamp_P1", "fieldName": "V_CLAMP_P1", "description": "倾斜测试时，夹具P1"}, {"accessColName": "V_Clamp_P2", "fieldName": "V_CLAMP_P2", "description": "倾斜测试时，夹具P2"}, {"accessColName": "V_Clamp_P3", "fieldName": "V_CLAMP_P3", "description": "倾斜测试时，夹具P3"}, {"accessColName": "V_Clamp_G", "fieldName": "V_CLAMP_G", "description": "倾斜测试时，夹具总重"}, {"accessColName": "V_Clamp_Xc", "fieldName": "V_CLAMP_XC", "description": "倾斜测试时，夹具Y向质心"}, {"accessColName": "V_Clamp_Zc", "fieldName": "V_CLAMP_ZC", "description": "倾斜测试时，夹具Z向质心"}, {"accessColName": "V_Clamp_R", "fieldName": "V_CLAMP_R", "description": "倾斜测试时，夹具质心与坐标原点距离"}, {"accessColName": "V_Clamp_Alfa", "fieldName": "V_CLAMP_ALFA", "description": "倾斜测试时，夹具质心与坐标原点连线与+Y轴间角度"}, {"accessColName": "V_Clamp_Mx", "fieldName": "V_CLAMP_MX", "description": "倾斜测试时，夹具Y向力矩"}, {"accessColName": "V_Clamp_Mz", "fieldName": "V_CLAMP_MZ", "description": "倾斜测试时，夹具Z向力矩"}, {"accessColName": "V_Clamp_BalanceAngle", "fieldName": "V_CLAMP_BALANCE_ANGLE", "description": "倾斜测试时，夹具配平角度"}, {"accessColName": "V_Clamp_BalanceMoment", "fieldName": "V_CLAMP_BALANCE_MOMENT", "description": "倾斜测试时，夹具配平力矩"}, {"accessColName": "V_Satellite_P1", "fieldName": "V_SATELLITE_P1", "description": "倾斜测试时，卫星P1"}, {"accessColName": "V_Satellite_P2", "fieldName": "V_SATELLITE_P2", "description": "倾斜测试时，卫星P2"}, {"accessColName": "V_Satellite_P3", "fieldName": "V_SATELLITE_P3", "description": "倾斜测试时，卫星P3"}, {"accessColName": "V_Satellite_G", "fieldName": "V_SATELLITE_G", "description": "倾斜测试时，卫星总重"}, {"accessColName": "V_Satellite_Xc", "fieldName": "V_SATELLITE_XC", "description": "倾斜测试时，卫星Y向质心"}, {"accessColName": "V_Satellite_Zc", "fieldName": "V_SATELLITE_ZC", "description": "倾斜测试时，卫星Z向质心"}, {"accessColName": "V_Satellite_R", "fieldName": "V_SATELLITE_R", "description": "倾斜测试时，卫星质心与坐标原点距离"}, {"accessColName": "V_Satellite_Alfa", "fieldName": "V_SATELLITE_ALFA", "description": "倾斜测试时，卫星质心与坐标原点连线与+X轴间角度"}, {"accessColName": "V_Satellite_Mx", "fieldName": "V_SATELLITE_MX", "description": "倾斜测试时，卫星Y向力矩"}, {"accessColName": "V_Satellite_Mz", "fieldName": "V_SATELLITE_MZ", "description": "倾斜测试时，卫星Z向力矩"}, {"accessColName": "V_Satellite_BalanceAngle", "fieldName": "V_SATELLITE_BALANCE_ANGLE", "description": "倾斜测试时，卫星配平角度"}, {"accessColName": "V_Satellite_BalanceMoment", "fieldName": "V_SATELLITE_BALANCE_MOMENT", "description": "倾斜测试时，卫星配平力矩"}, {"accessColName": "V_ClampSatellite_P1", "fieldName": "V_CLAMP_SATELLITE_P1", "description": "倾斜测试时，夹具+卫星P1"}, {"accessColName": "V_ClampSatellite_P2", "fieldName": "V_CLAMP_SATELLITE_P2", "description": "倾斜测试时，夹具+卫星P2"}, {"accessColName": "V_ClampSatellite_P3", "fieldName": "V_CLAMP_SATELLITE_P3", "description": "倾斜测试时，夹具+卫星P3"}, {"accessColName": "V_ClampSatellite_G", "fieldName": "V_CLAMP_SATELLITE_G", "description": "倾斜测试时，夹具+卫星总重"}, {"accessColName": "V_ClampSatellite_Xc", "fieldName": "V_CLAMP_SATELLITE_XC", "description": "倾斜测试时，夹具+卫星Y向质心"}, {"accessColName": "V_ClampSatellite_Zc", "fieldName": "V_CLAMP_SATELLITE_ZC", "description": "倾斜测试时，夹具+卫星Z向质心"}, {"accessColName": "V_ClampSatellite_R", "fieldName": "V_CLAMP_SATELLITE_R", "description": "倾斜测试时，夹具+卫星质心与坐标原点距离"}, {"accessColName": "V_ClampSatellite_Alfa", "fieldName": "V_CLAMP_SATELLITE_ALFA", "description": "倾斜测试时，夹具+卫星质心与坐标原点连线与+Y轴间角度"}, {"accessColName": "V_ClampSatellite_Mx", "fieldName": "V_CLAMP_SATELLITE_MX", "description": "倾斜测试时，夹具+卫星Y向力矩"}, {"accessColName": "V_ClampSatellite_Mz", "fieldName": "V_CLAMP_SATELLITE_MZ", "description": "倾斜测试时，夹具+卫星Z向力矩"}, {"accessColName": "V_ClampSatellite_BalanceAngle", "fieldName": "V_CLAMP_SAT_BAL_ANGLE", "description": "倾斜测试时，夹具+卫星配平角度"}, {"accessColName": "V_ClampSatellite_BalanceMoment", "fieldName": "V_CLAMP_SAT_BAL_MOMENT", "description": "倾斜测试时，夹具+卫星配平力矩"}]}, "计算结果": {"tableName": "TX_CALCULATION_RESULTS", "description": "计算结果表", "fields": [{"accessColName": "测试编号", "fieldName": "TEST_ID", "description": ""}, {"accessColName": "产品型号", "fieldName": "PRODUCT_MODEL", "description": ""}, {"accessColName": "测试日期", "fieldName": "TEST_DATE", "description": ""}, {"accessColName": "质心Xctest", "fieldName": "XC_TEST", "description": "理论质心坐标系质心坐标X"}, {"accessColName": "质心Yctest", "fieldName": "YC_TEST", "description": "理论质心坐标系质心坐标Y"}, {"accessColName": "质心Zctest", "fieldName": "ZC_TEST", "description": "理论质心坐标系质心坐标Z"}, {"accessColName": "质心Xcsat", "fieldName": "XC_SAT", "description": "卫星坐标系质心坐标X"}, {"accessColName": "质心Ycsat", "fieldName": "YC_SAT", "description": "卫星坐标系质心坐标Y"}, {"accessColName": "质心Zcsat", "fieldName": "ZC_SAT", "description": "卫星坐标系质心坐标Z"}, {"accessColName": "转动惯量Ixtest", "fieldName": "IX_TEST", "description": "测试坐标系Ix"}, {"accessColName": "转动惯量Iytest", "fieldName": "IY_TEST", "description": "测试坐标系Iy"}, {"accessColName": "转动惯量Iztest", "fieldName": "IZ_TEST", "description": "测试坐标系Iz"}, {"accessColName": "惯性积Ixytest", "fieldName": "IXY_TEST", "description": "测试坐标系Ixy"}, {"accessColName": "惯性积Ixztest", "fieldName": "IXZ_TEST", "description": "测试坐标系Ixz"}, {"accessColName": "惯性积Iyztest", "fieldName": "IYZ_TEST", "description": "测试坐标系Iyz"}, {"accessColName": "转动惯量IxWcent", "fieldName": "IX_WCENT", "description": "质心坐标系Ix"}, {"accessColName": "转动惯量IyWcent", "fieldName": "IY_WCENT", "description": "质心坐标系Iy"}, {"accessColName": "转动惯量IzWcent", "fieldName": "IZ_WCENT", "description": "质心坐标系Iz"}, {"accessColName": "惯性积IxyWcent", "fieldName": "IXY_WCENT", "description": "质心坐标系Ixy"}, {"accessColName": "惯性积IxzWcent", "fieldName": "IXZ_WCENT", "description": "质心坐标系Ixz"}, {"accessColName": "惯性积IyzWcent", "fieldName": "IYZ_WCENT", "description": "质心坐标系Iyz"}, {"accessColName": "质心Xc", "fieldName": "XC", "description": "旋转中心为坐标原点计算得到的Xc"}, {"accessColName": "转动惯量IxBottom", "fieldName": "IX_BOTTOM", "description": "旋转中心为坐标原点计算得到的Ix"}, {"accessColName": "转动惯量IyBottom", "fieldName": "IY_BOTTOM", "description": "旋转中心为坐标原点计算得到的Iy"}, {"accessColName": "转动惯量IzBottom", "fieldName": "IZ_BOTTOM", "description": "旋转中心为坐标原点计算得到的Iz"}, {"accessColName": "惯性积IxyBottom", "fieldName": "IXY_BOTTOM", "description": "旋转中心为坐标原点计算得到的Ixy"}, {"accessColName": "惯性积IxzBottom", "fieldName": "IXZ_BOTTOM", "description": "旋转中心为坐标原点计算得到的Ixz"}, {"accessColName": "惯性积IyzBottom", "fieldName": "IYZ_BOTTOM", "description": "旋转中心为坐标原点计算得到的Iyz"}, {"accessColName": "倾倒角度RotatAngle", "fieldName": "ROTATE_ANGLE", "description": "倾倒角度"}, {"accessColName": "卫星质量Weight", "fieldName": "WEIGHT", "description": "卫星质量"}, {"accessColName": "testXc", "fieldName": "TEST_XC", "description": "测试坐标系（两轴交点坐标系）下X坐标"}]}, "转动惯量标定表": {"tableName": "TX_MOMENT_CALIBRATION", "description": "转动惯量标定表", "fields": [{"accessColName": "编号", "fieldName": "ID", "description": ""}, {"accessColName": "测量周期数", "fieldName": "MEASURE_CYCLE_NUM", "description": ""}, {"accessColName": "测量次数", "fieldName": "MEASURE_COUNT", "description": ""}, {"accessColName": "PlatT1", "fieldName": "PLAT_T1", "description": "空载周期1"}, {"accessColName": "PlatT2", "fieldName": "PLAT_T2", "description": "空载周期2"}, {"accessColName": "PlatT3", "fieldName": "PLAT_T3", "description": "空载周期3"}, {"accessColName": "PlatT4", "fieldName": "PLAT_T4", "description": "空载周期4"}, {"accessColName": "PlatTa", "fieldName": "PLAT_TA", "description": "空载平均周期"}, {"accessColName": "Standard1MI", "fieldName": "STANDARD1_MI", "description": "一级标定标准惯量值"}, {"accessColName": "Standard1T1", "fieldName": "STANDARD1_T1", "description": "一级标定周期1"}, {"accessColName": "Standard1T2", "fieldName": "STANDARD1_T2", "description": "一级标定周期2"}, {"accessColName": "Standard1T3", "fieldName": "STANDARD1_T3", "description": "一级标定周期3"}, {"accessColName": "Standard1T4", "fieldName": "STANDARD1_T4", "description": "一级标定周期4"}, {"accessColName": "Standard1Ta", "fieldName": "STANDARD1_TA", "description": "一级标定平均周期"}, {"accessColName": "Standard1C", "fieldName": "STANDARD1_C", "description": "一级标定扭摆系数"}, {"accessColName": "Standard2MI", "fieldName": "STANDARD2_MI", "description": "二级标定标准惯量值"}, {"accessColName": "Standard2T1", "fieldName": "STANDARD2_T1", "description": "二级标定周期1"}, {"accessColName": "Standard2T2", "fieldName": "STANDARD2_T2", "description": "二级标定周期2"}, {"accessColName": "Standard2T3", "fieldName": "STANDARD2_T3", "description": "二级标定周期3"}, {"accessColName": "Standard2T4", "fieldName": "STANDARD2_T4", "description": "二级标定周期4"}, {"accessColName": "Standard2Ta", "fieldName": "STANDARD2_TA", "description": "二级标定平均周期"}, {"accessColName": "Standard2C", "fieldName": "STANDARD2_C", "description": "二级标定扭摆系数"}, {"accessColName": "Standard3MI", "fieldName": "STANDARD3_MI", "description": "三级标定标准惯量值"}, {"accessColName": "Standard3T1", "fieldName": "STANDARD3_T1", "description": "三级标定周期1"}, {"accessColName": "Standard3T2", "fieldName": "STANDARD3_T2", "description": "三级标定周期2"}, {"accessColName": "Standard3T3", "fieldName": "STANDARD3_T3", "description": "三级标定周期3"}, {"accessColName": "Standard3T4", "fieldName": "STANDARD3_T4", "description": "三级标定周期4"}, {"accessColName": "Standard3Ta", "fieldName": "STANDARD3_TA", "description": "三级标定平均周期"}, {"accessColName": "Standard3C", "fieldName": "STANDARD3_C", "description": "三级标定扭摆系数"}, {"accessColName": "标定日期", "fieldName": "CALIBRATION_DATE", "description": ""}]}, "转动惯量测试记录表": {"tableName": "TX_MOMENT_TEST_RECORD", "description": "转动惯量测试记录表", "fields": [{"accessColName": "测试编号", "fieldName": "TEST_ID", "description": ""}, {"accessColName": "产品型号", "fieldName": "PRODUCT_MODEL", "description": ""}, {"accessColName": "产品批次", "fieldName": "PRODUCT_BATCH", "description": ""}, {"accessColName": "产品名称", "fieldName": "PRODUCT_NAME", "description": ""}, {"accessColName": "操作人员1", "fieldName": "OPERATOR1", "description": ""}, {"accessColName": "操作人员2", "fieldName": "OPERATOR2", "description": ""}, {"accessColName": "操作人员3", "fieldName": "OPERATOR3", "description": ""}, {"accessColName": "测试日期", "fieldName": "TEST_DATE", "description": ""}, {"accessColName": "TestPeriodCount", "fieldName": "TEST_PERIOD_COUNT", "description": "空载平均周期"}, {"accessColName": "TestTimes", "fieldName": "TEST_TIMES", "description": ""}, {"accessColName": "PlatTAV", "fieldName": "PLAT_TAV", "description": "夹具平均周期"}, {"accessColName": "ClampTAV-X", "fieldName": "CLAMP_TAV_X", "description": "夹具和卫星平均周期"}, {"accessColName": "J0-KValue-X", "fieldName": "J0_KVALUE_X", "description": "夹具计算时扭摆系数"}, {"accessColName": "ClmpSatI-X", "fieldName": "CLMP_SAT_I_X", "description": "夹具转动惯量"}, {"accessColName": "ClampAndSatTAV-X", "fieldName": "CLAMP_AND_SAT_TAV_X", "description": "夹具和卫星计算时扭摆系数"}, {"accessColName": "JD-KValue-X", "fieldName": "JD_KVALUE_X", "description": "夹具和卫星转动惯量"}, {"accessColName": "ClampAndSatSatI-X", "fieldName": "CLAMP_AND_SAT_I_X", "description": "夹具和卫星转动惯量"}, {"accessColName": "SatI-X", "fieldName": "SAT_I_X", "description": "卫星转动惯量"}, {"accessColName": "ClampTAV-Y", "fieldName": "CLAMP_TAV_Y", "description": "夹具平均周期"}, {"accessColName": "J0-KValue-Y", "fieldName": "J0_KVALUE_Y", "description": "夹具计算时扭摆系数"}, {"accessColName": "ClmpSatI-Y", "fieldName": "CLMP_SAT_I_Y", "description": "夹具转动惯量"}, {"accessColName": "ClampAndSatTAV-Y", "fieldName": "CLAMP_AND_SAT_TAV_Y", "description": "夹具和卫星平均周期"}, {"accessColName": "JD-KValue-Y", "fieldName": "JD_KVALUE_Y", "description": "夹具和卫星计算时扭摆系数"}, {"accessColName": "ClampAndSatSatI-Y", "fieldName": "CLAMP_AND_SAT_I_Y", "description": "夹具和卫星转动惯量"}, {"accessColName": "SatI-Y", "fieldName": "SAT_I_Y", "description": "卫星转动惯量"}, {"accessColName": "ClampTAV-Z", "fieldName": "CLAMP_TAV_Z", "description": "夹具平均周期"}, {"accessColName": "J0-KValue-Z", "fieldName": "J0_KVALUE_Z", "description": "夹具计算时扭摆系数"}, {"accessColName": "ClmpSatI-Z", "fieldName": "CLMP_SAT_I_Z", "description": "夹具转动惯量"}, {"accessColName": "ClampAndSatTAV-Z", "fieldName": "CLAMP_AND_SAT_TAV_Z", "description": "夹具和卫星平均周期"}, {"accessColName": "JD-KValue-Z", "fieldName": "JD_KVALUE_Z", "description": "夹具和卫星计算时扭摆系数"}, {"accessColName": "ClampAndSatSatI-Z", "fieldName": "CLAMP_AND_SAT_I_Z", "description": "夹具和卫星转动惯量"}, {"accessColName": "SatI-Z", "fieldName": "SAT_I_Z", "description": "卫星转动惯量"}, {"accessColName": "Work_Number", "fieldName": "WORK_NUMBER", "description": ""}, {"accessColName": "ClampTAV-Statu1", "fieldName": "CLAMP_TAV_STATU1", "description": "夹具平均周期"}, {"accessColName": "J0-KValue-Statu1", "fieldName": "J0_KVALUE_STATU1", "description": "夹具计算时扭摆系数"}, {"accessColName": "ClmpSatI-Statu1", "fieldName": "CLMP_SAT_I_STATU1", "description": "夹具转动惯量"}, {"accessColName": "ClampAndSatTAV-Statu1", "fieldName": "CLAMP_AND_SAT_TAV_STATU1", "description": "夹具和卫星平均周期"}, {"accessColName": "JD-KValue-Statu1", "fieldName": "JD_KVALUE_STATU1", "description": "夹具和卫星计算时扭摆系数"}, {"accessColName": "ClampAndSatSatI-Statu1", "fieldName": "CLAMP_AND_SAT_I_STATU1", "description": "夹具和卫星转动惯量"}, {"accessColName": "SatI-Statu1", "fieldName": "SAT_I_STATU1", "description": "卫星转动惯量"}, {"accessColName": "ClampTAV-Statu2", "fieldName": "CLAMP_TAV_STATU2", "description": "夹具平均周期"}, {"accessColName": "J0-KValue-Statu2", "fieldName": "J0_KVALUE_STATU2", "description": "夹具计算时扭摆系数"}, {"accessColName": "ClmpSatI-Statu2", "fieldName": "CLMP_SAT_I_STATU2", "description": "夹具转动惯量"}, {"accessColName": "ClampAndSatTAV-Statu2", "fieldName": "CLAMP_AND_SAT_TAV_STATU2", "description": "夹具和卫星平均周期"}, {"accessColName": "JD-KValue-Statu2", "fieldName": "JD_KVALUE_STATU2", "description": "夹具和卫星计算时扭摆系数"}, {"accessColName": "ClampAndSatSatI-Statu2", "fieldName": "CLAMP_AND_SAT_I_STATU2", "description": "夹具和卫星转动惯量"}, {"accessColName": "SatI-Statu2", "fieldName": "SAT_I_STATU2", "description": "卫星转动惯量"}, {"accessColName": "ClampTAV-Statu3", "fieldName": "CLAMP_TAV_STATU3", "description": "夹具平均周期"}, {"accessColName": "J0-KValue-Statu3", "fieldName": "J0_KVALUE_STATU3", "description": "夹具计算时扭摆系数"}, {"accessColName": "ClmpSatI-Statu3", "fieldName": "CLMP_SAT_I_STATU3", "description": "夹具转动惯量"}, {"accessColName": "ClampAndSatTAV-Statu3", "fieldName": "CLAMP_AND_SAT_TAV_STATU3", "description": "夹具和卫星平均周期"}, {"accessColName": "JD-KValue-Statu3", "fieldName": "JD_KVALUE_STATU3", "description": "夹具和卫星计算时扭摆系数"}, {"accessColName": "ClampAndSatSatI-Statu3", "fieldName": "CLAMP_AND_SAT_I_STATU3", "description": "夹具和卫星转动惯量"}, {"accessColName": "SatI-Statu3", "fieldName": "SAT_I_STATU3", "description": "卫星转动惯量"}, {"accessColName": "ClampTAV-Statu4", "fieldName": "CLAMP_TAV_STATU4", "description": "夹具平均周期"}, {"accessColName": "J0-KValue-Statu4", "fieldName": "J0_KVALUE_STATU4", "description": "夹具计算时扭摆系数"}, {"accessColName": "ClmpSatI-Statu4", "fieldName": "CLMP_SAT_I_STATU4", "description": "夹具转动惯量"}, {"accessColName": "ClampAndSatTAV-Statu4", "fieldName": "CLAMP_AND_SAT_TAV_STATU4", "description": "夹具和卫星平均周期"}, {"accessColName": "JD-KValue-Statu4", "fieldName": "JD_KVALUE_STATU4", "description": "夹具和卫星计算时扭摆系数"}, {"accessColName": "ClampAndSatSatI-Statu4", "fieldName": "CLAMP_AND_SAT_I_STATU4", "description": "夹具和卫星转动惯量"}, {"accessColName": "SatI-Statu4", "fieldName": "SAT_I_STATU4", "description": "卫星转动惯量"}, {"accessColName": "ClampTAV-Statu5", "fieldName": "CLAMP_TAV_STATU5", "description": "夹具平均周期"}, {"accessColName": "J0-KValue-Statu5", "fieldName": "J0_KVALUE_STATU5", "description": "夹具计算时扭摆系数"}, {"accessColName": "ClmpSatI-Statu5", "fieldName": "CLMP_SAT_I_STATU5", "description": "夹具转动惯量"}, {"accessColName": "ClampAndSatTAV-Statu5", "fieldName": "CLAMP_AND_SAT_TAV_STATU5", "description": "夹具和卫星平均周期"}, {"accessColName": "JD-KValue-Statu5", "fieldName": "JD_KVALUE_STATU5", "description": "夹具和卫星计算时扭摆系数"}, {"accessColName": "ClampAndSatSatI-Statu5", "fieldName": "CLAMP_AND_SAT_I_STATU5", "description": "夹具和卫星转动惯量"}, {"accessColName": "SatI-Statu5", "fieldName": "SAT_I_STATU5", "description": "卫星转动惯量"}, {"accessColName": "Sat-<PERSON><PERSON>", "fieldName": "SAT_ANGLE", "description": "卫星倾倒角度"}, {"accessColName": "ClampTAV-Statu6", "fieldName": "CLAMP_TAV_STATU6", "description": "夹具平均周期"}, {"accessColName": "J0-KValue-Statu6", "fieldName": "J0_KVALUE_STATU6", "description": "夹具计算时扭摆系数"}, {"accessColName": "ClmpSatI-Statu6", "fieldName": "CLMP_SAT_I_STATU6", "description": "夹具转动惯量"}, {"accessColName": "ClampAndSatTAV-Statu6", "fieldName": "CLAMP_AND_SAT_TAV_STATU6", "description": "夹具和卫星平均周期"}, {"accessColName": "JD-KValue-Statu6", "fieldName": "JD_KVALUE_STATU6", "description": "夹具和卫星计算时扭摆系数"}, {"accessColName": "ClampAndSatSatI-Statu6", "fieldName": "CLAMP_AND_SAT_I_STATU6", "description": "夹具和卫星转动惯量"}, {"accessColName": "SatI-Statu6", "fieldName": "SAT_I_STATU6", "description": "卫星转动惯量"}, {"accessColName": "旋转角度bate", "fieldName": "ROTATE_ANGLE_BATE", "description": "卫星倾倒角度"}]}}}