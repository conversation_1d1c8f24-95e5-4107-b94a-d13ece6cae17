/**
 * 全景图热点编辑系统 - UI交互模块
 * 
 * <AUTHOR>
 * @date 2025-06-10
 * @description 处理界面交互、拖拽调整、蒙版管理等UI相关功能
 * @requires panorama-core.js
 */

/**
 * 全景图编辑器UI模块
 */
var PanoramaUI = (function() {
    'use strict';
    
    // ==================== 私有变量 ====================
    var isResizing = false;
    var startX = 0;
    var startLeftWidth = 0;
    
    // ==================== 拖拽调整功能 ====================
    
    /**
     * 初始化拖拽调整功能
     */
    function initResizeHandle() {
        var resizeHandle = document.getElementById('resizeHandle');
        var leftPanel = document.getElementById('leftPanel');
        var rightPanel = document.getElementById('rightPanel');
        var container = document.querySelector('.container-content');
        var iframe = document.getElementById('panoramaFrame');

        if (!resizeHandle || !leftPanel || !rightPanel || !container) {
            return; // 如果必要元素不存在，直接返回
        }

        resizeHandle.addEventListener('mousedown', function (e) {
            isResizing = true;
            startX = e.clientX;
            startLeftWidth = leftPanel.offsetWidth;

            // 添加全局样式，防止选择文本
            document.body.style.userSelect = 'none';
            document.body.style.cursor = 'col-resize';

            // 禁用iframe的鼠标事件，防止干扰拖拽
            if (iframe) {
                iframe.style.pointerEvents = 'none';
            }

            // 添加遮罩层防止iframe捕获事件
            createDragOverlay();

            e.preventDefault();
            e.stopPropagation();
        });

        document.addEventListener('mousemove', function (e) {
            if (!isResizing) return;

            var deltaX = e.clientX - startX;
            var newLeftWidth = startLeftWidth + deltaX;
            var containerWidth = container.offsetWidth;
            var minLeftWidth = PanoramaCore.CONFIG.UI.MIN_LEFT_WIDTH;
            var maxLeftWidth = containerWidth * PanoramaCore.CONFIG.UI.MAX_LEFT_WIDTH_RATIO;
            var minRightWidth = PanoramaCore.CONFIG.UI.MIN_RIGHT_WIDTH;

            // 限制最小和最大宽度
            if (newLeftWidth < minLeftWidth) {
                newLeftWidth = minLeftWidth;
            } else if (newLeftWidth > maxLeftWidth) {
                newLeftWidth = maxLeftWidth;
            } else if (containerWidth - newLeftWidth - 6 < minRightWidth) {
                newLeftWidth = containerWidth - minRightWidth - 6;
            }

            // 设置新宽度
            leftPanel.style.width = newLeftWidth + 'px';

            e.preventDefault();
            e.stopPropagation();
        });

        document.addEventListener('mouseup', function (e) {
            if (isResizing) {
                isResizing = false;

                // 恢复样式
                document.body.style.userSelect = '';
                document.body.style.cursor = '';

                // 恢复iframe的鼠标事件
                if (iframe) {
                    iframe.style.pointerEvents = '';
                }

                // 移除遮罩层
                removeDragOverlay();

                // 重新渲染表格以适应新宽度
                var hotspotTable = PanoramaCore.getHotspotTable();
                if (hotspotTable) {
                    setTimeout(function () {
                        hotspotTable.resize();
                    }, 100);
                }

                e.preventDefault();
                e.stopPropagation();
            }
        });

        // 窗口大小改变时重新计算
        window.addEventListener('resize', PanoramaCore.throttle(function () {
            var containerWidth = container.offsetWidth;
            var currentLeftWidth = leftPanel.offsetWidth;
            var minRightWidth = PanoramaCore.CONFIG.UI.MIN_RIGHT_WIDTH;

            if (containerWidth - currentLeftWidth - 6 < minRightWidth) {
                leftPanel.style.width = (containerWidth - minRightWidth - 6) + 'px';

                // 重新渲染表格
                var hotspotTable = PanoramaCore.getHotspotTable();
                if (hotspotTable) {
                    setTimeout(function () {
                        hotspotTable.resize();
                    }, 100);
                }
            }
        }, 250));
    }

    /**
     * 创建拖拽遮罩层
     */
    function createDragOverlay() {
        var overlay = document.createElement('div');
        overlay.id = 'dragOverlay';
        overlay.style.cssText = [
            'position: fixed',
            'top: 0',
            'left: 0',
            'width: 100%',
            'height: 100%',
            'z-index: 9999',
            'cursor: col-resize',
            'background: transparent'
        ].join(';');
        document.body.appendChild(overlay);
    }

    /**
     * 移除拖拽遮罩层
     */
    function removeDragOverlay() {
        var overlay = document.getElementById('dragOverlay');
        if (overlay) {
            document.body.removeChild(overlay);
        }
    }
    
    // ==================== 按钮状态管理 ====================
    
    /**
     * 统一管理导出按钮状态
     */
    function updateExportButtonState() {
        var exportBtn = document.getElementById('exportBtn');
        if (!exportBtn) return;
        
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) {
            // 没有选择任务时，隐藏导出按钮
            exportBtn.style.display = 'none';
        } else {
            // 有任务时，显示按钮（始终可点击）
            exportBtn.style.display = '';
            exportBtn.disabled = false;
        }
    }
    
    /**
     * 启用上传按钮和查看按钮
     */
    function enableUploadButtons() {
        var uploadZipBtn = document.getElementById('uploadZipBtn');
        var viewDeviceBtn = document.getElementById('viewDeviceBtn');
        
        if (uploadZipBtn) uploadZipBtn.disabled = false;
        if (viewDeviceBtn) viewDeviceBtn.disabled = false;

        // 更新设备状态显示
        if (typeof updateDeviceStatus === 'function') {
            updateDeviceStatus();
        }
    }

    /**
     * 禁用上传按钮和查看按钮
     */
    function disableUploadButtons() {
        var uploadZipBtn = document.getElementById('uploadZipBtn');
        var viewDeviceBtn = document.getElementById('viewDeviceBtn');
        var deviceStatus = document.getElementById('deviceStatus');
        
        if (uploadZipBtn) uploadZipBtn.disabled = true;
        if (viewDeviceBtn) viewDeviceBtn.disabled = true;
        if (deviceStatus) deviceStatus.textContent = '-';
    }
    
    // ==================== 蒙版管理 ====================
    
    /**
     * 显示任务选择蒙版
     */
    function showTaskSelectionMask() {
        var mask = document.getElementById('taskSelectionMask');
        if (mask) {
            // 添加show类来显示蒙版
            mask.classList.add('show');
        }

        // 隐藏顶部操作按钮
        hideTopActionButtons();

        // 绑定蒙版上的创建任务按钮事件
        var maskCreateTaskBtn = document.getElementById('maskCreateTaskBtn');
        if (maskCreateTaskBtn) {
            // 移除之前的事件监听器，避免重复绑定
            maskCreateTaskBtn.removeEventListener('click', handleMaskCreateTask);
            maskCreateTaskBtn.addEventListener('click', handleMaskCreateTask);
        }
    }

    /**
     * 隐藏任务选择蒙版
     */
    function hideTaskSelectionMask() {
        var mask = document.getElementById('taskSelectionMask');
        if (mask) {
            mask.classList.add('fade-out');

            // 显示顶部操作按钮
            showTopActionButtons();

            setTimeout(function () {
                mask.classList.remove('show');
                mask.classList.remove('fade-out');
            }, 300);
        }
    }
    
    /**
     * 蒙版创建任务按钮处理函数
     */
    function handleMaskCreateTask() {
        // 这里需要调用任务模块的创建任务函数
        if (typeof PanoramaTask !== 'undefined' && PanoramaTask.showCreateTaskDialog) {
            PanoramaTask.showCreateTaskDialog();
        }
    }
    
    /**
     * 隐藏顶部操作按钮
     */
    function hideTopActionButtons() {
        var createTaskBtn = document.getElementById('createTaskBtn');
        if (createTaskBtn) {
            createTaskBtn.style.display = 'none';
        }
        // 导出按钮的显示/隐藏由updateExportButtonState统一管理
    }

    /**
     * 显示顶部操作按钮
     */
    function showTopActionButtons() {
        var createTaskBtn = document.getElementById('createTaskBtn');
        if (createTaskBtn) {
            createTaskBtn.style.display = '';
        }
        // 导出按钮的显示/隐藏和状态由updateExportButtonState统一管理
        updateExportButtonState();
    }
    
    // ==================== 节点显示管理 ====================
    
    /**
     * 更新节点显示
     * @param {String} nodeId 节点ID
     * @param {String} switchType 切换类型
     */
    function updateNodeDisplay(nodeId, switchType) {
        try {
            var nodeInfo = document.getElementById('currentNodeInfo');
            var nodeIdElement = document.getElementById('currentNodeId');
            var nodeIndicator = document.getElementById('nodeIndicator');

            if (nodeId && nodeId !== '-') {
                // 显示节点信息
                if (nodeInfo) nodeInfo.style.display = 'block';
                if (nodeIdElement) nodeIdElement.textContent = nodeId;

                // 根据切换类型设置不同的视觉效果
                if (nodeIndicator) {
                    var icon = nodeIndicator.querySelector('i');
                    if (icon) {
                        if (switchType === 'switch') {
                            // 节点切换时的闪烁效果
                            icon.style.color = '#FF5722';
                            setTimeout(function () {
                                icon.style.color = '#5FB878';
                            }, 1000);
                        } else {
                            // 初始化时的正常颜色
                            icon.style.color = '#5FB878';
                        }
                    }
                }
            } else {
                // 隐藏节点信息
                if (nodeInfo) nodeInfo.style.display = 'none';
                if (nodeIdElement) nodeIdElement.textContent = '-';
            }
        } catch (error) {
            // 更新节点显示失败，静默处理
            console.warn('更新节点显示失败:', error);
        }
    }
    
    // ==================== 公共API ====================
    return {
        // 拖拽调整
        initResizeHandle: initResizeHandle,
        
        // 按钮状态管理
        updateExportButtonState: updateExportButtonState,
        enableUploadButtons: enableUploadButtons,
        disableUploadButtons: disableUploadButtons,
        
        // 蒙版管理
        showTaskSelectionMask: showTaskSelectionMask,
        hideTaskSelectionMask: hideTaskSelectionMask,
        
        // 节点显示
        updateNodeDisplay: updateNodeDisplay,
        
        // 内部函数（供其他模块使用）
        hideTopActionButtons: hideTopActionButtons,
        showTopActionButtons: showTopActionButtons
    };
})();

// ==================== 向后兼容性支持 ====================
// 保持原有函数的全局访问方式

window.initResizeHandle = PanoramaUI.initResizeHandle;
window.updateExportButtonState = PanoramaUI.updateExportButtonState;
window.enableUploadButtons = PanoramaUI.enableUploadButtons;
window.disableUploadButtons = PanoramaUI.disableUploadButtons;
window.showTaskSelectionMask = PanoramaUI.showTaskSelectionMask;
window.hideTaskSelectionMask = PanoramaUI.hideTaskSelectionMask;
window.updateNodeDisplay = PanoramaUI.updateNodeDisplay;
