package com.cirpoint.model;

import cn.hutool.core.io.FileUtil;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 日志文件信息模型
 * 用于封装日志文件的基本信息和元数据
 */
public class LogFile {
    
    private String fileName;         // 文件名：application.log
    private String displayName;      // 显示名称：当前日志 / 2025-06-01
    private long fileSize;           // 文件大小（字节）
    private Date lastModified;       // 最后修改时间
    private boolean isArchived;      // 是否为归档文件
    private String filePath;         // 完整文件路径（内部使用）
    private LogFileType type;        // 文件类型枚举
    
    /**
     * 日志文件类型枚举
     */
    public enum LogFileType {
        CURRENT("当前日志"),
        ARCHIVED("历史日志");
        
        private final String description;
        
        LogFileType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // 归档文件名正则表达式：application.2025-06-01.0.log
    private static final Pattern ARCHIVED_PATTERN = Pattern.compile(
        "application\\.(\\d{4}-\\d{2}-\\d{2})\\.(\\d+)\\.log"
    );
    
    /**
     * 默认构造函数
     */
    public LogFile() {
    }
    
    /**
     * 构造函数
     */
    public LogFile(String fileName, long fileSize, Date lastModified, 
                   boolean isArchived, String filePath) {
        this.fileName = fileName;
        this.fileSize = fileSize;
        this.lastModified = lastModified;
        this.isArchived = isArchived;
        this.filePath = filePath;
        this.type = isArchived ? LogFileType.ARCHIVED : LogFileType.CURRENT;
        this.displayName = generateDisplayName();
    }
    
    /**
     * 格式化文件大小的工具方法
     * 使用Hutool工具库进行格式化
     */
    public String getFormattedSize() {
        return FileUtil.readableFileSize(fileSize);
    }
    
    /**
     * 生成友好的显示名称
     */
    public String generateDisplayName() {
        if (type == LogFileType.CURRENT) {
            return "当前日志";
        } else {
            // 从归档文件名提取日期：application.2025-06-01.0.log
            Matcher matcher = ARCHIVED_PATTERN.matcher(fileName);
            if (matcher.matches()) {
                String date = matcher.group(1);
                String index = matcher.group(2);
                int fileIndex = Integer.parseInt(index) + 1;  // 从0开始，显示时+1
                return date + " (第" + fileIndex + "个文件)";
            }
            // 如果不匹配标准格式，直接返回文件名
            return fileName;
        }
    }
    
    /**
     * 获取文件的相对路径（用于下载链接）
     */
    public String getRelativePath() {
        if (filePath == null) {
            return fileName;
        }
        // 提取相对于logs目录的路径
        if (filePath.contains("logs")) {
            int logsIndex = filePath.indexOf("logs");
            return filePath.substring(logsIndex);
        }
        return fileName;
    }
    
    /**
     * 判断文件是否存在且可读
     */
    public boolean isAccessible() {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }
        java.io.File file = new java.io.File(filePath);
        return file.exists() && file.canRead();
    }
    
    /**
     * 获取文件扩展名
     */
    public String getFileExtension() {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }
    
    /**
     * 比较方法 - 按最后修改时间倒序排序
     */
    public int compareByLastModified(LogFile other) {
        if (this.lastModified == null && other.lastModified == null) {
            return 0;
        }
        if (this.lastModified == null) {
            return 1;
        }
        if (other.lastModified == null) {
            return -1;
        }
        return other.lastModified.compareTo(this.lastModified);  // 倒序
    }
    
    // Getter和Setter方法
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }
    
    public Date getLastModified() {
        return lastModified;
    }
    
    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }
    
    public boolean isArchived() {
        return isArchived;
    }
    
    public void setArchived(boolean archived) {
        isArchived = archived;
    }
    
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    public LogFileType getType() {
        return type;
    }
    
    public void setType(LogFileType type) {
        this.type = type;
    }
    
    @Override
    public String toString() {
        return "LogFile{" +
                "fileName='" + fileName + '\'' +
                ", displayName='" + displayName + '\'' +
                ", fileSize=" + fileSize +
                ", lastModified=" + lastModified +
                ", isArchived=" + isArchived +
                ", type=" + type +
                '}';
    }
} 