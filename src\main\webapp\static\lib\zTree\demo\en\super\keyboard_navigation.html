<!DOCTYPE html>
<HTML>
<HEAD>
	<TITLE> ZTREE DEMO - Keyboard navigation </TITLE>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<link rel="stylesheet" href="../../../css/demo.css" type="text/css">
	<link rel="stylesheet" href="../../../css/zTreeStyle/zTreeStyle.css" type="text/css">
	<script type="text/javascript" src="../../../js/jquery-1.4.4.min.js"></script>
	<script type="text/javascript" src="../../js/keyboard_navigation.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.core.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.exedit.js"></script>
	<SCRIPT type="text/javascript">

		var setting = {
			data: {
				simpleData: {
					enable: true
				}
			}
		};

		var zNodes =[
			{ id:1, pId:0, name:"Custom Icon 01", open:true, iconSkin:"pIcon01", accesskey: 'c'},
			{ id:11, pId:1, name:"leaf node 01", iconSkin:"icon01", accesskey: 'l'},
			{ id:12, pId:1, name:"leaf node 02", iconSkin:"icon02"},
			{ id:13, pId:1, name:"parent node 03", iconSkin:"pIcon01", accesskey: 'p'},
			{ id:131, pId:13, name:"leaf node 01", iconSkin:"icon01"},
			{ id:132, pId:13, name:"leaf node 02", iconSkin:"icon02"},
			{ id:133, pId:13, name:"leaf node 03", iconSkin:"icon03"},
			{ id:2, pId:0, name:"Custom Icon 02", open:true, iconSkin:"pIcon02"},
			{ id:21, pId:2, name:"leaf node 01", iconSkin:"icon04"},
			{ id:22, pId:2, name:"leaf node 02", iconSkin:"icon05"},
			{ id:23, pId:2, name:"leaf node 03", iconSkin:"icon06"},
			{ id:3, pId:0, name:"no Custom Icon", open:true, accesskey: 'n' },
			{ id:31, pId:3, name:"leaf node 01"},
			{ id:32, pId:3, name:"leaf node 02"},
			{ id:33, pId:3, name:"leaf node 03"}
		];

		var $ = jQuery;
		$(document).ready(function()
		{
			var element = "#treeDemo";
			var zTree = $.fn.zTree.init($(element), setting, zNodes);
			// Initialize keyboard navigation
			$.fn.zTreeKeyboardNavigation(zTree, document.body);
		});

	</SCRIPT>
	<style type="text/css">
		.ztree li > a
		{
			border-left: 1px solid white;
		}

		.ztree li > a.curSelectedNode {
			border-radius: 3px;
		}

		.ztree li span.button.pIcon01_ico_open{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/1_open.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.pIcon01_ico_close{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/1_close.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.pIcon02_ico_open, .ztree li span.button.pIcon02_ico_close{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/2.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.icon01_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/3.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.icon02_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/4.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.icon03_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/5.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.icon04_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/6.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.icon05_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/7.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
		.ztree li span.button.icon06_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/8.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
	</style>
</HEAD>

<BODY>
<h1>Keyboard navigation</h1>
<h6>[ File Path: super/keyboard_navigation.html ]</h6>
<div class="content_wrap">
	<div class="zTreeDemoBackground left">
		<ul id="treeDemo" class="ztree"></ul>
	</div>
	<div class="right">
		<ul class="info">
			<li class="title"><h2>1. Explanation of setting</h2>
				<ul class="list">
				<li>Keyboard navigation does not require special configuration setting.</li>
				<li>It does require that the keyboard_navigation.js file is loaded and the function initialized.</li>
				</ul>
			</li>
			<li class="title"><h2>2. Explanation of navigation</h2>
				<ul class="list">
					<li>Home: home key (keycode 36)		Goes to the first root element is visible</li>
					<li>End: end key (keycode 35)		Goes to the last leaf node and will expand nodes and scroll the element into view</li>
					<li>Down: right cursor key (keycode 39)	Goes to the next visible node in the tree following the hierarchy</li>
					<li>Next: down cursor key (keycode 40)		Goes to the next visible node at the same level</li>
					<li>Up: up cursor key (keycode 37)		Goes to the prior visible node at the same level</li>
					<li>Previous: left cursor key (keycode 38)	Goes to the prior visible node following the hierarchy</li>
					<li>Toggle: space key (keycode 32)	Toggles the expand/collapse state of a parent node</li>
				</ul>
			</li>
		</ul>
	</div>
</div>
</BODY>
</HTML>