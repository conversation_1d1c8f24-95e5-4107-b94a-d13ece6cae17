package com.cirpoint.controller;

import com.cirpoint.model.PathEntry;
import com.cirpoint.service.MultiPathService;
import com.cirpoint.service.TerminalService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.Arrays;

/**
 * 终端命令控制器
 */
@Slf4j
@RestController
@RequestMapping("/terminal")
public class TerminalController {

    private final TerminalService terminalService;
    private final MultiPathService multiPathService;

    @Autowired
    public TerminalController(TerminalService terminalService, MultiPathService multiPathService) {
        this.terminalService = terminalService;
        this.multiPathService = multiPathService;
    }

    /**
     * 执行终端命令
     */
    @PostMapping("/execute")
    public ResponseEntity<List<String>> executeCommand(
            @RequestParam String command,
            @RequestParam String sessionId,
            @RequestParam(required = false) String workingDirectory) {
        try {
            CompletableFuture<List<String>> future = terminalService.executeCommand(command, sessionId, workingDirectory);
            List<String> result = future.get(); // 等待执行完成
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("执行命令失败", e);
            return ResponseEntity.ok(Arrays.asList("执行命令失败: " + e.getMessage()));
        }
    }

    /**
     * 终止正在执行的命令
     */
    @PostMapping("/terminate")
    public ResponseEntity<Boolean> terminateCommand(@RequestParam String sessionId) {
        return ResponseEntity.ok(terminalService.terminateCommand(sessionId));
    }

    /**
     * 获取当前工作目录
     */
    @GetMapping("/working-directory")
    public ResponseEntity<String> getWorkingDirectory(@RequestParam String sessionId) {
        return ResponseEntity.ok(terminalService.getWorkingDirectory(sessionId));
    }

    @PostMapping("/init")
    public ResponseEntity<String> initTerminal(@RequestParam String sessionId) {
        // 获取启用的路径列表
        List<PathEntry> enabledPaths = multiPathService.getEnabledPaths();

        // 如果有启用的路径，使用第一个作为初始工作目录
        if (!enabledPaths.isEmpty()) {
            String workingDir = enabledPaths.get(0).getPath();
            terminalService.setWorkingDirectory(sessionId, workingDir);
            return ResponseEntity.ok(workingDir);
        } else {
            // 如果没有启用的路径，使用用户主目录
            String userHome = System.getProperty("user.home");
            terminalService.setWorkingDirectory(sessionId, userHome);
            return ResponseEntity.ok(userHome);
        }
    }

    /**
     * 命令请求实体
     */
    @Data
    public static class CommandRequest {
        private String command;
        private String sessionId;
        private String workingDirectory;
    }

    /**
     * 终止命令请求实体
     */
    @Data
    public static class TerminateRequest {
        private String sessionId;
    }
}
