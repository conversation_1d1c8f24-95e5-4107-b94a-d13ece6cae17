package com.cirpoint.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONObject;
import com.cirpoint.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 电缆同步定时任务
 */
@Slf4j
@Component
public class DlSyncTask {

    @Value("${dl.sync.log.path}")
    private String logPath;

    @Scheduled(cron = "${dl.sync.cron}")
    public void syncDlReport() {
        log.info("开始执行电缆同步定时任务");
        
        try {
            // 获取应用程序所在目录（jar包所在目录）
            String appPath = getAppPath();
            // 构建完整的日志路径
            String fullLogPath = appPath + File.separator + "logs" + File.separator + logPath;
            log.info("日志路径: {}", fullLogPath);
            
            // 创建日志目录
            FileUtil.mkdir(fullLogPath);
            
            // 生成日志文件名（使用时间戳）
            String logFileName = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss").format(new Date()) + ".log";
            File logFile = new File(fullLogPath + File.separator + logFileName);
            
            try (FileWriter writer = new FileWriter(logFile)) {
                // 写入任务开始时间
                writer.write("=== 电缆同步任务开始 ===\n");
                writer.write("执行时间：" + DateUtil.now() + "\n\n");
                
                try {
                    // 调用Thingworx接口
                    JSONObject result = Util.postTwxForObject("Thing.Fn.Dl", "SyncDLReport");
                    
                    // 写入执行结果
                    writer.write("同步结果：\n");
                    writer.write(result.toStringPretty());
                    writer.write("\n");
                    
                    log.info("电缆同步定时任务执行成功");
                } catch (Exception e) {
                    // 写入错误信息
                    writer.write("同步失败：\n");
                    writer.write("错误信息：" + e.getMessage() + "\n");
                    
                    log.error("电缆同步定时任务执行失败", e);
                }
                
                // 写入任务结束时间
                writer.write("\n=== 电缆同步任务结束 ===\n");
                writer.write("结束时间：" + DateUtil.now() + "\n");
                
            } catch (IOException e) {
                log.error("写入电缆同步日志失败", e);
            }
        } catch (Exception e) {
            log.error("获取项目路径失败", e);
        }
    }
    
    /**
     * 获取应用程序所在目录路径
     * 
     * @return 应用程序所在目录的绝对路径
     */
    private String getAppPath() {
        try {
            // 获取当前类所在的jar包路径
            String path = DlSyncTask.class.getProtectionDomain().getCodeSource().getLocation().toURI().getPath();
            File jarFile = new File(path);
            
            // 如果是jar包，返回jar包所在目录
            if (jarFile.isFile()) {
                return jarFile.getParentFile().getAbsolutePath();
            } else {
                // 如果是开发环境，返回classes目录的上两级目录
                return jarFile.getAbsolutePath();
            }
        } catch (URISyntaxException e) {
            log.error("获取应用程序路径失败", e);
            // 返回当前工作目录
            return System.getProperty("user.dir");
        }
    }
}