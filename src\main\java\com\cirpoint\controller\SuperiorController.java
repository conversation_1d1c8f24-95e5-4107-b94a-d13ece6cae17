package com.cirpoint.controller;

import cn.hutool.json.JSONObject;
import com.cirpoint.service.SuperiorService;
import com.cirpoint.util.FileDownloadUtil;
import java.io.File;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 院数据包文件相关操作的控制器
 */
@Slf4j
@RestController
@RequestMapping("/superior")
public class SuperiorController {

	private final SuperiorService superiorService;

	@Autowired
	public SuperiorController(SuperiorService superiorService) {
		this.superiorService = superiorService;
	}

	/**
	 * 下载文档文件
	 *
	 * @param filePath 文件路径
	 * @param fileName 文件名称
	 * @return ResponseEntity
	 */
	@PostMapping("/download/doc")
	public ResponseEntity<?> downloadDocFile(@RequestParam String filePath,
												  @RequestParam String fileName) {
		File resultFile = superiorService.downloadDocFile(filePath, fileName);
		return FileDownloadUtil.fileResponseAndDelete(resultFile);
	}

	/**
	 * 上传文档文件
	 *
	 * @param file 上传的文件
	 * @return ResponseEntity
	 */
	@PostMapping("/upload/doc")
	public ResponseEntity<?> uploadDocFile(@RequestParam("file") MultipartFile file) {
		try {
			JSONObject result = superiorService.uploadDocFile(file);
			return ResponseEntity.ok(result);
		} catch (Exception e) {
			log.error("上传文件失败", e);
			return ResponseEntity.badRequest().body(new JSONObject()
					.set("success", false)
					.set("msg", "上传文件失败：" + e.getMessage()));
		}
	}

}