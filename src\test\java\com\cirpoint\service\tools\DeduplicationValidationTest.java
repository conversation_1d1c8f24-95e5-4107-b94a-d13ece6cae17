package com.cirpoint.service.tools;

import com.cirpoint.constant.WorkTimeConstants;
import com.cirpoint.model.worktime.AttendanceRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 去重功能验证测试
 */
public class DeduplicationValidationTest {

    private DuplicateRecordProcessor processor;
    private DateTimeFormatter formatter;

    @BeforeEach
    public void setUp() {
        processor = new DuplicateRecordProcessor();
        formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    }

    @Test
    @DisplayName("验证基本去重功能")
    public void testBasicDeduplication() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 张三在910厂房2分钟内多次刷入卡
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:10"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:30"));
        
        List<AttendanceRecord> result = processor.removeDuplicateRecords(records);
        
        assertEquals(1, result.size(), "2分钟内的重复记录应该只保留1条");
        assertEquals("张三", result.get(0).getEmployeeName());
        assertEquals("910厂房", result.get(0).getLocation());
        assertEquals(WorkTimeConstants.DIRECTION_IN, result.get(0).getDirection());
    }

    @Test
    @DisplayName("验证统计信息功能")
    public void testStatistics() {
        List<AttendanceRecord> originalRecords = new ArrayList<>();
        originalRecords.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:10"));
        originalRecords.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:30"));
        originalRecords.add(createRecord("李四", "920厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:15:00"));
        
        List<AttendanceRecord> deduplicatedRecords = processor.removeDuplicateRecords(originalRecords);
        String stats = processor.getDeduplicationStats(originalRecords, deduplicatedRecords);
        
        assertTrue(stats.contains("原始记录: 3"));
        assertTrue(stats.contains("去重后记录: 2"));
        assertTrue(stats.contains("移除重复记录: 1"));
    }

    @Test
    @DisplayName("验证不同分组不互相影响")
    public void testDifferentGroupsIndependent() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 不同员工
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:10"));
        records.add(createRecord("李四", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:20"));
        
        // 不同场所
        records.add(createRecord("张三", "920厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:30"));
        
        // 不同方向
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-26 08:10:40"));
        
        List<AttendanceRecord> result = processor.removeDuplicateRecords(records);
        
        assertEquals(4, result.size(), "不同分组的记录应该都保留");
    }

    @Test
    @DisplayName("验证时间窗口边界")
    public void testTimeWindowBoundary() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 恰好120秒间隔
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:00:00"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:02:00")); // 120秒，应被去重
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:02:01")); // 121秒，应保留
        
        List<AttendanceRecord> result = processor.removeDuplicateRecords(records);
        
        assertEquals(2, result.size(), "120秒内的记录应该被去重，121秒的应该保留");
    }

    /**
     * 创建测试用的打卡记录
     */
    private AttendanceRecord createRecord(String employeeName, String location, String direction, String eventTimeStr) {
        AttendanceRecord record = new AttendanceRecord();
        record.setEmployeeName(employeeName);
        record.setLocation(location);
        record.setDirection(direction);
        record.setEventTime(LocalDateTime.parse(eventTimeStr, formatter));
        
        // 根据location设置accessPoint
        if ("910厂房".equals(location)) {
            record.setAccessPoint("910" + direction);
        } else if ("920厂房".equals(location)) {
            record.setAccessPoint("920" + direction);
        }
        
        return record;
    }
}
