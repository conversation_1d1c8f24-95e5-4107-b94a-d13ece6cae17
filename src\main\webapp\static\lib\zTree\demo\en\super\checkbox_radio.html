<!DOCTYPE html>
<HTML>
<HEAD>
	<TITLE> ZTREE DEMO - checkbox & radio</TITLE>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<link rel="stylesheet" href="../../../css/demo.css" type="text/css">
	<link rel="stylesheet" href="../../../css/zTreeStyle/zTreeStyle.css" type="text/css">
	<script type="text/javascript" src="../../../js/jquery-1.4.4.min.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.core.js"></script>
	<!--  <script type="text/javascript" src="../../../js/jquery.ztree.excheck.js"></script>
	  <script type="text/javascript" src="../../../js/jquery.ztree.exedit.js"></script>-->
	<SCRIPT type="text/javascript">
		<!--
		var IDMark_A = "_a";
		var setting = {
			view: {
				addDiyDom: addDiyDom
			},
			data: {
				simpleData: {
					enable: true
				}
			}
		};

		var zNodes =[
			{ id:1, pId:0, name:"parent node 1", open:true},
			{ id:11, pId:1, name:"leaf node 1-1"},
			{ id:12, pId:1, name:"leaf node 1-2"},
			{ id:13, pId:1, name:"leaf node 1-3"},
			{ id:2, pId:0, name:"parent node 2", open:true},
			{ id:21, pId:2, name:"leaf node 2-1"},
			{ id:22, pId:2, name:"leaf node 2-2"},
			{ id:23, pId:2, name:"leaf node 2-3"},
			{ id:3, pId:0, name:"parent node 3", open:true },
			{ id:31, pId:3, name:"leaf node 3-1"},
			{ id:32, pId:3, name:"leaf node 3-2"},
			{ id:33, pId:3, name:"leaf node 3-3"}
		];

		function addDiyDom(treeId, treeNode) {
			var aObj = $("#" + treeNode.tId + IDMark_A);
			if (treeNode.level == 0) {
				var editStr = "<input type='checkbox' class='checkboxBtn' id='checkbox_" +treeNode.id+ "' onfocus='this.blur();'></input>";
				aObj.before(editStr);
				var btn = $("#checkbox_"+treeNode.id);
			if (btn) btn.bind("change", function() {checkAccessories(treeNode, btn);});
			} else if (treeNode.level == 1) {
				var editStr = "<input type='radio' class='radioBtn' id='radio_" +treeNode.id+ "' name='radio_"+treeNode.getParentNode().id+"' onfocus='this.blur();'></input>";
				aObj.before(editStr);
				var btn = $("#radio_"+treeNode.id);
				if (btn) btn.bind("click", function() {checkBrand(treeNode, btn);});
			}
		}

		function checkAccessories(treeNode, btn) {
			var checkedRadio = getCheckedRadio("radio_"+treeNode.id);
			if (btn.attr("checked")) {
				if (!checkedRadio) {
					$("#radio_" + treeNode.children[0].id).attr("checked", true);
				}
			} else {
				checkedRadio.attr("checked", false);
			}
		}

		function checkBrand(treeNode, btn) {
			if (btn.attr("checked")) {
				var pObj = $("#checkbox_" + treeNode.getParentNode().id);
				if (!pObj.attr("checked")) {
					pObj.attr("checked", true);
				}
			}
		}

		function getCheckedRadio(radioName) {
			var r = document.getElementsByName(radioName);
			for(var i=0; i<r.length; i++)    {
				if(r[i].checked)    {
					return $(r[i]);
				}
			}
			return null;
		}

		$(document).ready(function(){
			$.fn.zTree.init($("#treeDemo"), setting, zNodes);
		});
		//-->
	</SCRIPT>
	<style type="text/css">
	  .radioBtn {height: 16px;vertical-align: middle;}
	  .checkboxBtn {vertical-align: middle;margin-right: 2px;}
	  </style>
 </HEAD>

<BODY>
<h1>Checkbox / Radio Coexistence</h1>
<h6>[ File Path: super/checkbox_radio.html ]</h6>
<div class="content_wrap">
	<div class="zTreeDemoBackground left">
		<ul id="treeDemo" class="ztree"></ul>
	</div>
	<div class="right">
		<ul class="info">
			<li class="title"><h2>Explanation of implementation method</h2>
				<ul class="list">
				<li>zTree default checkbox and radio can not coexist, but can be used the custom DOM features to achieve this requirement, refer to the "Adding Custom DOM".</li>
				<li class="highlight_red">For checkbox / radio association rules of the parent nodes and child nodes, according to the needs to develop its own rules.</li>
				</ul>
			</li>
		</ul>
	</div>
</div>
</BODY>
</HTML>