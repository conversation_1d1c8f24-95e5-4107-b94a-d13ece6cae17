<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><defs><linearGradient id="a" x1="13.859" y1="29.219" x2="18.106" y2="29.219" gradientUnits="userSpaceOnUse"><stop offset="0.034" stop-color="#636361"/><stop offset="0.178" stop-color="#6c6d70"/><stop offset="0.219" stop-color="#6f7175"/><stop offset="0.309" stop-color="#616366"/><stop offset="0.481" stop-color="#3c3d3f"/><stop offset="0.716" stop-color="#010101"/><stop offset="0.719"/><stop offset="1" stop-color="#636a6e"/></linearGradient><linearGradient id="b" x1="15.178" y1="29.734" x2="16.787" y2="29.734" xlink:href="#a"/><linearGradient id="c" x1="15.277" y1="29.881" x2="16.688" y2="29.881" gradientUnits="userSpaceOnUse"><stop offset="0.034" stop-color="#9b9b98"/><stop offset="0.131" stop-color="#a4a5a7"/><stop offset="0.219" stop-color="#aeb1b8"/><stop offset="0.352" stop-color="#9fa4ab"/><stop offset="0.605" stop-color="#788188"/><stop offset="0.719" stop-color="#657076"/><stop offset="1" stop-color="#8b949a"/></linearGradient><radialGradient id="d" cx="-8.456" cy="-16.616" r="19.383" gradientTransform="translate(34.042 37.063) scale(1.054)" gradientUnits="userSpaceOnUse"><stop offset="0.034" stop-color="#70706e"/><stop offset="0.112" stop-color="#616261"/><stop offset="0.219" stop-color="#46474a"/><stop offset="0.408" stop-color="#48494c"/><stop offset="0.487" stop-color="#4e5053"/><stop offset="0.544" stop-color="#5a5c5f"/><stop offset="0.592" stop-color="#6a6e71"/><stop offset="0.633" stop-color="#7f8588"/><stop offset="0.667" stop-color="#979ea1"/><stop offset="0.719" stop-color="#3d4447"/><stop offset="1" stop-color="#656c70"/></radialGradient><linearGradient id="e" x1="16.126" y1="28.108" x2="16.05" y2="26.893" gradientUnits="userSpaceOnUse"><stop offset="0.034" stop-color="#9b9b98"/><stop offset="0.109" stop-color="#a5a6a7"/><stop offset="0.219" stop-color="#babcc4"/><stop offset="0.311" stop-color="#abaeb5"/><stop offset="0.487" stop-color="#84898f"/><stop offset="0.719" stop-color="#485054"/><stop offset="1" stop-color="#8b949a"/></linearGradient><linearGradient id="f" x1="16.013" y1="26.542" x2="15.895" y2="24.635" xlink:href="#e"/><linearGradient id="g" x1="16.023" y1="25.546" x2="15.887" y2="23.355" xlink:href="#e"/><linearGradient id="h" x1="16.013" y1="27.627" x2="15.895" y2="25.72" xlink:href="#e"/><linearGradient id="j" x1="13.68" y1="28.805" x2="18.284" y2="28.805" gradientUnits="userSpaceOnUse"><stop offset="0.034" stop-color="#545453"/><stop offset="0.071" stop-color="#616262"/><stop offset="0.143" stop-color="#848589"/><stop offset="0.219" stop-color="#aeb1b8"/><stop offset="0.352" stop-color="#9fa4ab"/><stop offset="0.605" stop-color="#788188"/><stop offset="0.719" stop-color="#657076"/><stop offset="1" stop-color="#505659"/></linearGradient><linearGradient id="k" x1="12.159" y1="25.813" x2="19.475" y2="25.813" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#e5e1df"/><stop offset="0" stop-color="#dddad6"/><stop offset="0.038" stop-color="#ceccc9"/><stop offset="0.112" stop-color="#a7a9a8"/><stop offset="0.212" stop-color="#697072"/><stop offset="0.264" stop-color="#465054"/><stop offset="0.303" stop-color="#636c6f"/><stop offset="0.398" stop-color="#a5aaac"/><stop offset="0.478" stop-color="#d5d7d9"/><stop offset="0.539" stop-color="#f3f4f5"/><stop offset="0.573" stop-color="#fefeff"/><stop offset="0.62" stop-color="#f4f5f6"/><stop offset="0.699" stop-color="#dadcdf"/><stop offset="0.8" stop-color="#b0b4b9"/><stop offset="0.876" stop-color="#8b9298"/><stop offset="1" stop-color="#d1d3d4"/><stop offset="1" stop-color="#a7a9ac"/></linearGradient><linearGradient id="l" x1="12.159" y1="24.728" x2="19.475" y2="24.728" xlink:href="#k"/><linearGradient id="m" x1="12.159" y1="23.548" x2="19.475" y2="23.548" xlink:href="#k"/><linearGradient id="n" x1="12.159" y1="26.898" x2="19.475" y2="26.898" xlink:href="#k"/><linearGradient id="o" x1="12.159" y1="25.967" x2="19.475" y2="25.967" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#e5e1df"/><stop offset="0" stop-color="#dde8d0"/><stop offset="0" stop-color="#d2f3ba"/><stop offset="0.036" stop-color="#c5e4b1"/><stop offset="0.105" stop-color="#a4bd98"/><stop offset="0.199" stop-color="#6e7f71"/><stop offset="0.264" stop-color="#465054"/><stop offset="0.303" stop-color="#636c6f"/><stop offset="0.398" stop-color="#a5aaac"/><stop offset="0.478" stop-color="#d5d7d9"/><stop offset="0.539" stop-color="#f3f4f5"/><stop offset="0.573" stop-color="#fefeff"/><stop offset="0.62" stop-color="#f4f5f6"/><stop offset="0.699" stop-color="#dadcdf"/><stop offset="0.8" stop-color="#b0b4b9"/><stop offset="0.876" stop-color="#8b9298"/><stop offset="1" stop-color="#d1d3d4"/><stop offset="1" stop-color="#a7a9ac"/></linearGradient><linearGradient id="p" x1="12.159" y1="24.883" x2="19.475" y2="24.883" xlink:href="#o"/><linearGradient id="q" x1="12.159" y1="27.05" x2="19.475" y2="27.05" xlink:href="#o"/><linearGradient id="r" x1="11.902" y1="23.201" x2="19.884" y2="23.201" xlink:href="#e"/><radialGradient id="s" cx="-16.983" cy="-22.948" r="10.718" gradientTransform="translate(34.042 37.063) scale(1.054)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffffd1"/><stop offset="0.505" stop-color="#ff0"/><stop offset="0.568" stop-color="#fdf700"/><stop offset="0.664" stop-color="#f6e200"/><stop offset="0.782" stop-color="#ebbf00"/><stop offset="0.906" stop-color="#de9200"/><stop offset="1" stop-color="#da9819"/></radialGradient></defs><title>file_type_smarty</title><path d="M13.859,28.776a.242.242,0,0,0,.128.191,12,12,0,0,0,1.392.622,4.3,4.3,0,0,0,.578.072h.052a4.3,4.3,0,0,0,.578-.072,12.014,12.014,0,0,0,1.392-.622.243.243,0,0,0,.128-.191l-2.137.192Z" style="fill:url(#a)"/><path d="M16.014,30a1.123,1.123,0,0,0,.688-.247.175.175,0,0,0,.085-.126v-.105a3.235,3.235,0,0,0-.773-.053h-.063a3.237,3.237,0,0,0-.773.053v.105a.175.175,0,0,0,.085.126,1.122,1.122,0,0,0,.688.247Z" style="fill:url(#b)"/><path d="M15.951,29.807a4.732,4.732,0,0,1-.674-.044,1.105,1.105,0,0,0,.674.237h.063a1.106,1.106,0,0,0,.675-.237,4.733,4.733,0,0,1-.675.044Z" style="fill:url(#c)"/><path d="M19.809,26.838a1,1,0,0,0-.234.389c-.036.181-.1.585-1.326,1.406l-.025.017a.435.435,0,0,1-.086.037,9.968,9.968,0,0,1-2.155.209,9.968,9.968,0,0,1-2.156-.209.436.436,0,0,1-.085-.036l-.025-.017a4.579,4.579,0,0,1-1.12-.957c-.164-.219-.373-.257-.39-.345a21.055,21.055,0,0,0,3.855.057,13.049,13.049,0,0,0,3.761-.654A.692.692,0,0,1,19.809,26.838Z" style="fill:url(#d)"/><path d="M19.772,26.88a.985.985,0,0,1-.246.129c-.076.028-.245.078-.245.078a25.15,25.15,0,0,1-6.458.436c-.136-.009-.32-.028-.32-.028a1.062,1.062,0,0,1-.14-.028,1.016,1.016,0,0,1,.14.107c.16.013.319.019.319.019a26.864,26.864,0,0,0,6.743-.323l.009-.042A.885.885,0,0,1,19.772,26.88Z" style="fill:url(#e)"/><path d="M19.439,25.382s.245-.07.245-.162a.114.114,0,0,0-.013-.065.214.214,0,0,1-.061-.144.437.437,0,0,1,.119-.27,1.069,1.069,0,0,1-.2.1c-.076.028-.245.078-.245.078a25.151,25.151,0,0,1-6.458.436c-.136-.009-.32-.028-.32-.028a1.055,1.055,0,0,1-.181-.041.377.377,0,0,1,.078.217.716.716,0,0,1-.254.432.673.673,0,0,1,.368-.079A26.2,26.2,0,0,0,19.439,25.382Z" style="fill:url(#f)"/><path d="M19.439,24.229s.245-.078.245-.182a.143.143,0,0,0-.013-.073.254.254,0,0,1-.061-.162.514.514,0,0,1,.119-.3,1.026,1.026,0,0,1-.2.111c-.076.032-.245.088-.245.088a22.507,22.507,0,0,1-6.458.489c-.136-.01-.32-.032-.32-.032a.953.953,0,0,1-.181-.046.455.455,0,0,1,.078.243.825.825,0,0,1-.254.484.614.614,0,0,1,.368-.089C13.014,24.758,16.935,25.041,19.439,24.229Z" style="fill:url(#g)"/><path d="M19.439,26.467s.245-.07.245-.163a.115.115,0,0,0-.013-.065.214.214,0,0,1-.061-.144.435.435,0,0,1,.119-.27,1.07,1.07,0,0,1-.2.1c-.076.028-.245.078-.245.078a25.162,25.162,0,0,1-6.458.436c-.136-.009-.32-.028-.32-.028a1.042,1.042,0,0,1-.181-.041.377.377,0,0,1,.078.217.717.717,0,0,1-.254.432.67.67,0,0,1,.368-.079A26.207,26.207,0,0,0,19.439,26.467Z" style="fill:url(#h)"/><path d="M19.439,26.467s.245-.07.245-.163a.115.115,0,0,0-.013-.065.214.214,0,0,1-.061-.144.435.435,0,0,1,.119-.27,1.07,1.07,0,0,1-.2.1c-.076.028-.245.078-.245.078a25.168,25.168,0,0,1-6.458.436c-.136-.009-.32-.028-.32-.028a1.042,1.042,0,0,1-.181-.041.376.376,0,0,1,.078.217.717.717,0,0,1-.254.432.67.67,0,0,1,.368-.079A26.207,26.207,0,0,0,19.439,26.467Z" style="fill:url(#h)"/><path d="M18.224,28.65a.435.435,0,0,1-.086.037,9.968,9.968,0,0,1-2.155.209,9.968,9.968,0,0,1-2.156-.209.436.436,0,0,1-.085-.036l-.025-.017-.029-.019a.112.112,0,0,0,0,.073c.024.073.147.1.147.1a9.943,9.943,0,0,0,2.15.209,9.942,9.942,0,0,0,2.15-.209s.126-.029.147-.1a.118.118,0,0,0,0-.073l-.029.019Z" style="fill:url(#j)"/><path d="M19.944,25.5s.041.252-.418.423c-.076.028-.245.078-.245.078a25.162,25.162,0,0,1-6.458.436c-.136-.009-.32-.028-.32-.028-.276-.038-.467-.16-.455-.308,0,0-.033-.249.466-.249a26.2,26.2,0,0,0,6.925-.472s.245-.07.245-.162a.111.111,0,0,0-.013-.066S19.944,25.312,19.944,25.5Z" style="fill:url(#k)"/><path d="M19.944,24.416s.041.252-.418.423c-.076.028-.245.078-.245.078a25.151,25.151,0,0,1-6.458.436c-.136-.009-.32-.028-.32-.028-.276-.038-.467-.16-.455-.308,0,0-.033-.248.466-.248a26.193,26.193,0,0,0,6.925-.472s.245-.07.245-.162a.11.11,0,0,0-.013-.065S19.944,24.227,19.944,24.416Z" style="fill:url(#l)"/><path d="M19.893,24.406v-.021c-.012.076-.078.236-.417.355-.076.026-.245.073-.245.073a22.372,22.372,0,0,1-5.1.5h0a22.155,22.155,0,0,0,5.1-.441s.169-.047.245-.073C19.933,24.643,19.893,24.406,19.893,24.406Z" style="fill:#fff"/><path d="M19.893,25.491V25.47c-.012.076-.078.236-.417.355-.076.027-.245.073-.245.073a22.38,22.38,0,0,1-5.1.5h0a22.171,22.171,0,0,0,5.1-.441s.169-.047.245-.073C19.933,25.728,19.893,25.491,19.893,25.491Z" style="fill:#fff"/><path d="M19.944,23.034s.041.329-.418.552c-.076.037-.245.1-.245.1a19.409,19.409,0,0,1-6.458.569c-.136-.012-.32-.037-.32-.037-.276-.05-.467-.209-.455-.4,0,0-.033-.324.466-.324a18.283,18.283,0,0,0,6.925-.461,1.014,1.014,0,0,0,.444-.238A.53.53,0,0,1,19.944,23.034Z" style="fill:url(#m)"/><path d="M19.944,26.586s.041.252-.418.423c-.076.028-.245.078-.245.078a25.15,25.15,0,0,1-6.458.436c-.136-.009-.32-.028-.32-.028-.276-.038-.467-.16-.455-.308,0,0-.033-.248.466-.248a26.207,26.207,0,0,0,6.925-.472s.245-.07.245-.163a.111.111,0,0,0-.013-.065S19.944,26.4,19.944,26.586Z" style="fill:url(#n)"/><path d="M19.893,26.576v-.021c-.012.076-.078.236-.417.355-.076.026-.245.073-.245.073a22.372,22.372,0,0,1-5.1.5h0a22.163,22.163,0,0,0,5.1-.441s.169-.047.245-.073C19.933,26.813,19.893,26.576,19.893,26.576Z" style="fill:#fff"/><path d="M19.893,23.321V23.3c-.012.076-.078.236-.417.355-.076.027-.245.073-.245.073a22.372,22.372,0,0,1-5.1.5h0a22.156,22.156,0,0,0,5.1-.441s.169-.047.245-.073C19.933,23.558,19.893,23.321,19.893,23.321Z" style="fill:#fff"/><path d="M12.417,25.881s-.236,0-.241.08.246.085.246.085.175,0,.3.005c.619-.013,1.993-.038,3.437-.166C14.384,25.958,12.723,25.883,12.417,25.881Z" style="fill:url(#o)"/><path d="M12.417,24.8s-.236,0-.241.08.246.085.246.085.175,0,.3.005c.619-.012,1.993-.038,3.437-.166C14.384,24.874,12.723,24.8,12.417,24.8Z" style="fill:url(#p)"/><path d="M12.417,26.965s-.236,0-.241.08.246.085.246.085.175,0,.3.005c.619-.012,1.993-.038,3.437-.166C14.384,27.042,12.723,26.966,12.417,26.965Z" style="fill:url(#q)"/><path d="M11.965,22.779s-.274.315.319.711c0,0,5.775.587,7.6-.695C19.884,22.795,13.623,23.32,11.965,22.779Z" style="fill:url(#r)"/><path d="M20.035,22.779a4.449,4.449,0,0,0,.81-1.155,3.386,3.386,0,0,0,.2-1.115,6.733,6.733,0,0,1,.632-2.91c.534-1.014,2.81-4.447,2.81-7.462A8.012,8.012,0,0,0,16,2a8.012,8.012,0,0,0-8.483,8.136c0,3.015,2.276,6.448,2.81,7.462a6.731,6.731,0,0,1,.632,2.91,3.384,3.384,0,0,0,.2,1.115,4.453,4.453,0,0,0,.81,1.155,13.866,13.866,0,0,0,3.987.37A15.151,15.151,0,0,0,20.035,22.779Z" style="fill:url(#s)"/></svg>