package com.cirpoint.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import java.io.BufferedInputStream;

/**
 *
 */
@Slf4j
public class CommonUtil {

	@Setter
	private static String tempPath;

	@Setter
	private static String thingworxDomain;
	
	@Setter
	private static String thingworxKey;

	public static File createExcelFile(String fileName, String sheetName, JSONArray headers, JSONArray data, JSONArray columnWidths, int rowHeight) {
		String filePath = tempPath + File.separator + System.currentTimeMillis() + File.separator + fileName + ".xlsx";
		ExcelWriter writer = ExcelUtil.getWriter(filePath);

		// 写入表头和数据
		JSONArray fullData = new JSONArray();
		fullData.add(headers);
		if (data != null) {
			fullData.addAll(data);
		}
		writer.write(fullData, false);
		writer.renameSheet(sheetName);
		// 设置列宽
		for (int i = 0; i < columnWidths.size(); i++) {
			writer.setColumnWidth(i, columnWidths.getInt(i));
		}

		// 设置行高
		for (int i = 0; i < writer.getRowCount(); i++) {
			writer.setRowHeight(i, rowHeight);
		}

		// 设置表头样式
		Font font = writer.createFont();
		font.setBold(true);
		CellStyle headerStyle = writer.createCellStyle();
		headerStyle.setFont(font);
		headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headerStyle.setAlignment(HorizontalAlignment.CENTER);
		headerStyle.setBorderBottom(BorderStyle.THIN);
		headerStyle.setBorderLeft(BorderStyle.THIN);
		headerStyle.setBorderTop(BorderStyle.THIN);
		headerStyle.setBorderRight(BorderStyle.THIN);
		headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

		// 应用样式
		writer.setRowStyleIfHasData(0, headerStyle);
		writer.setFreezePane(1);
		writer.close();

		return new File(filePath);
	}

	public static File createExcelFile(String fileName, JSONArray headers, JSONArray data, JSONArray columnWidths, int rowHeight) {
		return createExcelFile(fileName, "sheet1", headers, data, columnWidths, rowHeight);
	}
	
	/**
	 * 创建包含多个sheet页的Excel文件
	 *
	 * @param fileName       文件名
	 * @param headersList    多个sheet页的表头列表
	 * @param dataList       多个sheet页的数据列表
	 * @param columnWidthsList 多个sheet页的列宽列表
	 * @param sheetNames     多个sheet页的名称列表
	 * @param rowHeight      行高
	 * @return Excel文件
	 */
	public static File createMultiSheetExcelFile(String fileName, List<JSONArray> headersList, List<JSONArray> dataList,
			List<JSONArray> columnWidthsList, List<String> sheetNames, int rowHeight) {
		
		// 创建输出文件路径
		String filePath = tempPath + File.separator + System.currentTimeMillis() + File.separator + fileName + ".xlsx";
		// 创建一个Excel写入器
		ExcelWriter writer = ExcelUtil.getWriter(filePath);
		
		// 创建并应用表头样式
		Font font = writer.createFont();
		font.setBold(true);
		CellStyle headerStyle = writer.createCellStyle();
		headerStyle.setFont(font);
		headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headerStyle.setAlignment(HorizontalAlignment.CENTER);
		headerStyle.setBorderBottom(BorderStyle.THIN);
		headerStyle.setBorderLeft(BorderStyle.THIN);
		headerStyle.setBorderTop(BorderStyle.THIN);
		headerStyle.setBorderRight(BorderStyle.THIN);
		headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		
		// 处理每个sheet页
		for (int sheetIndex = 0; sheetIndex < headersList.size(); sheetIndex++) {
			// 如果不是第一个sheet，创建新的sheet
			if (sheetIndex > 0) {
				writer.setSheet(sheetNames.get(sheetIndex));
			} else {
				writer.renameSheet(sheetNames.get(sheetIndex));
			}
			
			// 获取当前sheet的表头、数据和列宽
			JSONArray headers = headersList.get(sheetIndex);
			JSONArray data = dataList.get(sheetIndex);
			JSONArray columnWidths = columnWidthsList.get(sheetIndex);
			
			// 合并表头和数据
			JSONArray fullData = new JSONArray();
			fullData.add(headers);
			if (data != null && !data.isEmpty()) {
				fullData.addAll(data);
			}
			
			// 写入数据
			writer.write(fullData, false);
			
			// 设置列宽
			for (int i = 0; i < columnWidths.size(); i++) {
				writer.setColumnWidth(i, columnWidths.getInt(i));
			}
			
			// 设置行高
			for (int i = 0; i < writer.getRowCount(); i++) {
				writer.setRowHeight(i, rowHeight);
			}
			
			// 应用表头样式
			writer.setRowStyleIfHasData(0, headerStyle);
			
			// 设置冻结窗格
			writer.setFreezePane(1);
		}
		
		// 关闭写入器
		writer.close();
		
		return new File(filePath);
	}

	/**
	 * 比对设计值和实测值是否符合要求
	 * <p>
	 * 方法执行流程：
	 * 1. 基础检查：
	 * - 检查实测值是否为空
	 * - 检查设计值是否为null
	 * - 检查是否包含特殊标记（ST或M）
	 * - 检查公差值是否有效
	 * <p>
	 * 2. 预处理阶段：
	 * - 处理设计值：去除特殊符号，提取数值
	 * - 处理实测值：统一格式，转换单位
	 * - 提取目标值：对于多值情况（如4-5），提取实际比较值
	 * <p>
	 * 3. 公差范围计算：
	 * - 解析公差值格式（±、/、~）
	 * - 计算允许的最小值和最大值
	 * <p>
	 * 4. 比对阶段：
	 * - 典型值比对：允许范围内的偏差
	 * - 多值比对：检查数量和范围
	 * - 范围值比对：检查是否有重叠
	 * - 列表值比对：检查所有值是否在范围内
	 * - 单值比对：检查是否在范围内
	 * <p>
	 * 空值处理规则：
	 * 1. 当实测值为空（null或空字符串）时，返回false
	 * 2. 当设计值为null时，返回true
	 * 3. 当公差值为空或为"/"或"--"时，返回true
	 * 4. 当公差值不包含数字时，返回true
	 * <p>
	 * 特殊符号处理规则：
	 * 1. ∅、Φ符号：在比对前会被去除
	 * 2. 波浪线：支持中文（～）和英文（~）波浪线
	 * 3. 分号：支持中文（；）和英文（;）分号
	 * <p>
	 * 角度单位处理规则：
	 * 1. 当公差值中存在′（分）时，默认实测值中的单位是°（度）
	 * 2. 支持度分秒格式转换：
	 * - 45°1′5″ -> 45.018055556
	 * - 45°1′ -> 45.016666667
	 * - 45.1° -> 45.1
	 * <p>
	 * 多值处理规则：
	 * 1. 范围值（使用波浪线分隔）：使用公差值确定允许范围
	 * 2. 多个值（使用分号分隔）：使用公差值确定允许范围
	 * 3. 数量前缀（如4-Φ5.5）：检查数量是否满足要求
	 * <p>
	 * 描述性要求处理规则：
	 * 1. 包含以下关键词时视为描述性要求：
	 * - 符合：表示需要符合某个标准或规范
	 * - 清除：表示需要清除某些内容
	 * - 刻刻：表示需要刻画某些内容
	 * - 标识：表示需要标识某些内容
	 * - 字体：表示对字体有特殊要求
	 * - 字深：表示对字的深度有要求
	 * 2. 描述性要求必须有实测值，否则返回false
	 * <p>
	 * 特殊类型处理规则：
	 * 1. ST类型：如包含"ST"，直接返回true
	 * 2. M类型：如包含"M"，直接返回true
	 * 3. 典型值：使用公差值来确定误差范围
	 *
	 * @param designValue   设计要求指标，如："4-Φ5.5"、"45°"、"2 典型"等
	 * @param tolerance     公差值，如："±0.1"、"±1′"、"0.1~0.3"等
	 * @param measuredValue 实测值，如："5.4;5.5;5.5;5.6"、"45°1′"等
	 * @return 是否符合要求
	 */
	public static boolean compareValues(String designValue, String tolerance, String measuredValue) {
		// 预处理实测值，统一格式，转换单位
		String processedMeasuredValue = preprocessMeasuredValue(measuredValue);

		// 检查实测值是否为空，为空则记录日志并返回false
		if (StrUtil.isBlank(processedMeasuredValue)) {
			log.info("比对失败 - 实测值为空 | 设计值: {}, 公差值: {}, 实测值: {}", designValue, tolerance, measuredValue);
			return false;
		}

		// 如果设计值为null，直接返回true（表示不需要比对）
		if (designValue == null) {
			return true;
		}

		// 如果设计值包含特殊标记（ST或M），直接返回true
		if (designValue.contains("ST") || designValue.contains("M")) {
			return true;
		}

		// 如果公差值为空或特殊字符，直接返回true
		if (StrUtil.isBlank(tolerance) || "--".equals(tolerance) || "/".equals(tolerance)) {
			return true;
		}

		// 如果公差值不包含任何数字，直接返回true
		if (!tolerance.matches(".*\\d+.*")) {
			return true;
		}

		// 检查是否为描述性要求（如"符合"、"清除"等）
		if (isDescriptiveRequirement(designValue)) {
			if (StrUtil.isBlank(measuredValue)) {
				log.info("比对失败 - 描述性要求的实测值为空 | 设计值: {}, 公差值: {}, 实测值: {}", designValue, tolerance, measuredValue);
				return false;
			}
			return true;
		}

		try {
			// 预处理设计值，去除特殊符号，提取数值
			String processedDesignValue = preprocessDesignValue(designValue);
			if (processedDesignValue == null) {
				return true;
			}

			// 处理多值情况，提取实际的目标值（如"4-5"中的"5"）
			String targetValue = processedDesignValue;
			if (designValue.matches("\\d+-.*")) {
				String[] parts = processedDesignValue.split("-");
				if (parts.length > 1) {
					targetValue = parts[1];
				}
			}

			// 计算公差范围（最小值和最大值）
			double[] toleranceRange = parseToleranceRange(targetValue, tolerance);
			if (toleranceRange == null) {
				return true;
			}

			boolean result;
			// 处理典型值的情况
			if (designValue.contains("典型")) {
				result = compareTypicalValues(processedMeasuredValue, toleranceRange);
				if (!result) {
					log.info("比对失败 - 典型值不符 | 设计值: {}, 公差值: {}, 实测值: {} | 允许范围: [{}, {}]",
							designValue, tolerance, measuredValue, toleranceRange[0], toleranceRange[1]);
				}
				return result;
			}

			// 处理多值情况（如"4-Φ5"）
			if (designValue.matches("\\d+-.*")) {
				result = compareMultipleValues(processedDesignValue, processedMeasuredValue, toleranceRange);
				if (!result) {
					log.info("比对失败 - 多值不符 | 设计值: {}, 公差值: {}, 实测值: {} | 允许范围: [{}, {}]",
							designValue, tolerance, measuredValue, toleranceRange[0], toleranceRange[1]);
				}
				return result;
			}

			// 处理其他情况：范围值、列表值或单个值
			if (processedMeasuredValue.contains("~")) {
				// 处理范围值（如"5.1~5.3"）
				result = compareMeasuredRange(toleranceRange, processedMeasuredValue);
				if (!result) {
					log.info("比对失败 - 实测范围不符 | 设计值: {}, 公差值: {}, 实测值: {} | [{}, {}]",
							designValue, tolerance, measuredValue, toleranceRange[0], toleranceRange[1]);
				}
			} else if (processedMeasuredValue.contains(";")) {
				// 处理列表值（如"5.1;5.2;5.3"）
				result = compareMeasuredList(toleranceRange, processedMeasuredValue);
				if (!result) {
					log.info("比对失败 - 实测列表不符 | 设计值: {}, 公差值: {}, 实测值: {} | [{}, {}]",
							designValue, tolerance, measuredValue, toleranceRange[0], toleranceRange[1]);
				}
			} else {
				// 处理单个值
				result = compareSingleValue(toleranceRange, processedMeasuredValue);
				if (!result) {
					log.info("比对失败 - 实测值不符 | 设计值: {}, 公差值: {}, 实测值: {} | [{}, {}]",
							designValue, tolerance, measuredValue, toleranceRange[0], toleranceRange[1]);
				}
			}
			return result;
		} catch (Exception e) {
			// 发生异常时记录日志并返回true
			log.info("比对失败 - 异常 | 设计值: {}, 公差值: {}, 实测值: {} | {}",
					designValue, tolerance, measuredValue, e.getLocalizedMessage());
			return false;
		}
	}

	/**
	 * 判断是否为描述性要求
	 * <p>
	 * 方法执行流程：
	 * 1. 检查设计值是否包含以下任意关键词：
	 * - 符合：表示需要符合某个标准或规范
	 * - 清除：表示需要清除某些内容
	 * - 刻刻：表示需要刻画某些内容
	 * - 标识：表示需要标识某些内容
	 * - 字体：表示对字体有特殊要求
	 * - 字深：表示对字的深度有要求
	 * 2. 如果包含任意关键词，返回true；否则返回false
	 *
	 * @param designValue 设计值字符串
	 * @return 如果包含任一关键词返回true，否则返回false
	 */
	private static boolean isDescriptiveRequirement(String designValue) {
		// 检查是否包含任意描述性要求的关键词
		return designValue.contains("符合") ||
				designValue.contains("清除") ||
				designValue.contains("刻刻") ||
				designValue.contains("标识") ||
				designValue.contains("字体") ||
				designValue.contains("字深");
	}

	/**
	 * 预处理设计值，将设计值转换为可比较的数值格式
	 * <p>
	 * 方法执行流程：
	 * 1. 预处理：
	 * - 去除特殊符号：Φ、R、C等
	 * - 去除"销孔"及其后面的内容
	 * - 去除首尾空白字符
	 * <p>
	 * 2. 特殊情况处理：
	 * - 如果包含"符合"或"清除"，返回null
	 * - 如果包含"典型"，提取数字部分
	 * - 如果包含数字，提取数字部分
	 * <p>
	 * 处理规则示例：
	 * - "Φ5.5" -> "5.5"
	 * - "R0.5" -> "0.5"
	 * - "销孔45°" -> "45"
	 * - "典型值5.5" -> "5.5"
	 * - "符合要求" -> null
	 *
	 * @param designValue 原始设计值
	 * @return 处理后的设计值，如果是描述性要求则返回null
	 */
	private static String preprocessDesignValue(String designValue) {
		// 去除Φ、R、C等符号和销孔相关内容
		String processed = designValue.replaceAll("[ΦRC]", "")
				.replaceAll("销孔.*", "")
				.trim();

		// 如果包含描述性文字，返回null
		if (processed.contains("符合") || processed.contains("清除")) {
			return null;
		}

		// 如果包含"典型"，提取数字部分
		if (processed.contains("典型")) {
			return processed.replaceAll("[^\\d.-]", "").trim();
		}

		// 如果包含数字，提取数字部分
		if (processed.matches(".*\\d+.*")) {
			return processed.replaceAll("[^\\d.-]", "");
		}

		return processed;
	}

	/**
	 * 预处理实测值，处理各种格式并统一转换
	 * <p>
	 * 方法执行流程：
	 * 1. 空值检查：
	 * - 如果输入为null，直接返回null
	 * <p>
	 * 2. 特殊格式处理：
	 * - 如果包含度分秒符号，转换为十进制度数
	 * <p>
	 * 3. 符号统一化：
	 * - 去除∅、Φ符号
	 * - 将中文波浪线（～）转为英文波浪线（~）
	 * - 将中文分号（；）转为英文分号（;）
	 * - 去除字母和度分符号
	 * <p>
	 * 处理规则示例：
	 * - "45°1′5″" -> "45.018055556"
	 * - "∅5.5" -> "5.5"
	 * - "5.5～5.6" -> "5.5~5.6"
	 * - "5.5；5.6" -> "5.5;5.6"
	 *
	 * @param measuredValue 原始实测值
	 * @return 处理后的实测值
	 */
	private static String preprocessMeasuredValue(String measuredValue) {
		// 处理null值
		if (measuredValue == null) {
			return null;
		}

		// 如果包含度分秒符号，转换为十进制度数
		if (measuredValue.contains("°") || measuredValue.contains("′") || measuredValue.contains("″")) {
			return String.valueOf(convertToDecimalDegrees(measuredValue));
		}

		// 统一符号格式并去除特殊字符
		String processed = measuredValue.replaceAll("[∅Φ]", "")
				.replaceAll("～", "~")
				.replaceAll("[a-zA-Z°′]+", "")
				.replaceAll("；", ";")
				.trim();
		
		// 检查处理后的字符串是否包含数字
		if (!processed.matches(".*\\d+.*")) {
			log.info("实测值不包含数字: {}", measuredValue);
			return null;
		}

		return processed;
	}

	/**
	 * 将度分秒格式转换为十进制度数
	 * <p>
	 * 方法执行流程：
	 * 1. 初始化：
	 * - 设置度数初始值为0
	 * <p>
	 * 2. 格式检查：
	 * - 检查是否包含度分秒符号
	 * <p>
	 * 3. 分段处理：
	 * - 分割度分秒字符串
	 * - 处理度部分：直接转换为数值
	 * - 处理分部分：除以60后加到度数
	 * - 处理秒部分：除以3600后加到度数
	 * <p>
	 * 转换规则示例：
	 * - "45°1′5″" -> 45 + 1/60 + 5/3600 = 45.018055556
	 * - "45°1′" -> 45 + 1/60 = 45.016666667
	 * - "45.1°" -> 45.1
	 *
	 * @param angle 角度字符串，支持度(°)分(′)秒(″)格式
	 * @return 转换后的十进制度数
	 */
	private static double convertToDecimalDegrees(String angle) {
		// 初始化度数
		double degrees = 0;

		// 如果包含度分秒符号，进行分段处理
		if (angle.contains("°") || angle.contains("′") || angle.contains("″")) {
			String[] parts = angle.split("[°′″]");

			// 处理度部分
			if (parts.length > 0 && !parts[0].trim().isEmpty()) {
				degrees = Double.parseDouble(parts[0].trim());
			}

			// 处理分部分
			if (parts.length > 1 && !parts[1].trim().isEmpty()) {
				degrees += Double.parseDouble(parts[1].trim()) / 60.0;
			}

			// 处理秒部分
			if (parts.length > 2 && !parts[2].trim().isEmpty()) {
				degrees += Double.parseDouble(parts[2].trim()) / 3600.0;
			}
		} else {
			// 如果不包含度分秒符号，直接转换数值
			degrees = Double.parseDouble(angle.replaceAll("[^\\d.-]", ""));
		}

		return degrees;
	}

	/**
	 * 解析公差范围，计算允许的最小值和最大值
	 * <p>
	 * 方法执行流程：
	 * 1. 基准值处理：
	 * - 将设计值转换为double类型基准值
	 * <p>
	 * 2. 公差格式解析：
	 * - ±格式：计算基准值±公差值
	 * - 分格式：如果包含′，先将分转换为度
	 * - /格式：解析上下公差值
	 * - ~格式：解析范围上下限
	 * <p>
	 * 处理规则示例：
	 * 1. ±格式：
	 * - 设计值=5，公差="±0.1" -> [4.9, 5.1]
	 * 2. 分格式：
	 * - 设计值=45，公差="±1′" -> [44.983333, 45.016667]
	 * 3. /格式：
	 * - 设计值=5，公差="0.1/-0.2" -> [4.8, 5.1]
	 * 4. ~格式：
	 * - 设计值=5，公差="0.1~0.2" -> [5.1, 5.2]
	 *
	 * @param designValue 设计值（基准值）
	 * @param tolerance   公差值（如：±0.1或0.1~0.3）
	 * @return 包含上下限的数组[最小值, 最大值]，如果解析失败返回null
	 */
	private static double[] parseToleranceRange(String designValue, String tolerance) {
		// 转换基准值
		double baseValue = Double.parseDouble(designValue);

		// 处理±格式
		if (tolerance.contains("±")) {
			String tolValue = tolerance.replace("±", "");
			// 如果是分单位，先转换为度
			if (tolValue.contains("′")) {
				double minutesToDegrees = Double.parseDouble(tolValue.replace("′", "")) / 60.0;
				return new double[]{baseValue - minutesToDegrees, baseValue + minutesToDegrees};
			}
			double tol = Double.parseDouble(tolValue);
			return new double[]{baseValue - tol, baseValue + tol};
		}
		// 处理/格式
		else if (tolerance.contains("/")) {
			String[] parts = tolerance.split("/");
			double upperTol = Double.parseDouble(parts[0]);
			double lowerTol = Double.parseDouble(parts[1]);
			return new double[]{baseValue + lowerTol, baseValue + upperTol};
		}
		// 处理~格式
		else if (tolerance.contains("~")) {
			String[] parts = tolerance.split("~");
			double lowerTol = Double.parseDouble(parts[0]);
			double upperTol = Double.parseDouble(parts[1]);
			return new double[]{baseValue + lowerTol, baseValue + upperTol};
		}

		return null;
	}

	/**
	 * 比对典型值
	 * <p>
	 * 方法执行流程：
	 * 1. 对于范围值（包含~）：
	 * - 只要实测范围与允许范围有重叠即可
	 * 2. 对于多个值（用分号分隔）：
	 * - 所有值都必须在允许范围内
	 *
	 * @param measuredValue  实测值（可能是范围或多个值）
	 * @param toleranceRange 允许的范围[最小值, 最大值]
	 * @return 是否符合典型值要求
	 */
	private static boolean compareTypicalValues(String measuredValue, double[] toleranceRange) {
		if (toleranceRange == null) {
			return true;
		}

		double lowerBound = toleranceRange[0];
		double upperBound = toleranceRange[1];

		if (measuredValue.contains("~")) {
			String[] parts = measuredValue.split("~");
			double min = Double.parseDouble(parts[0]);
			double max = Double.parseDouble(parts[1]);
			// 只要范围有重叠就可以
			return !(max < lowerBound || min > upperBound);
		}

		String[] values = measuredValue.split(";");
		for (String value : values) {
			double measured = Double.parseDouble(value);
			if (measured < lowerBound || measured > upperBound) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 比对多个值（例如：4-Φ5.5）
	 * <p>
	 * 方法执行流程：
	 * 1. 分割前缀数量和目标值：
	 * - 4-Φ5.5分割为count=4和targetValue=5.5
	 * 2. 对于范围值（包含~）：
	 * - 使用公差值确定允许范围
	 * - 不检查数量要求
	 * 3. 对于多个值（用分号分隔）：
	 * - 检查数量是否满足要求
	 * - 使用公差值确定允许范围
	 * - 所有值都必须在允许范围内
	 *
	 * @param designValue    设计值（格式：数量-值）
	 * @param measuredValue  实测值（可能是范围或多个值）
	 * @param toleranceRange 允许的范围[最小值, 最大值]
	 * @return 是否符合多值要求
	 */
	private static boolean compareMultipleValues(String designValue, String measuredValue, double[] toleranceRange) {
		if (toleranceRange == null) {
			return true;
		}

		String[] parts = designValue.split("-");
		int count = Integer.parseInt(parts[0]);

		// 处理范围值（包含波浪线的情况）
		if (measuredValue.contains("~") || measuredValue.contains("～")) {
			String processedValue = preprocessMeasuredValue(measuredValue);
			String[] rangeParts = processedValue.split("~");
			double min = Double.parseDouble(rangeParts[0]);
			double max = Double.parseDouble(rangeParts[1]);
			return !(max < toleranceRange[0] || min > toleranceRange[1]);
		}

		// 处理分号分隔的多个值（支持中文分号和英文分号）
		String[] values = measuredValue.contains("；") ?
				measuredValue.split("；") :
				measuredValue.split(";");

		if (values.length < count) {
			return false; // 数量不足
		}

		// 检查每个值是否在允许范围内
		for (String val : values) {
			double measured = Double.parseDouble(preprocessMeasuredValue(val));
			if (measured < toleranceRange[0] || measured > toleranceRange[1]) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 比对实测值范围
	 * <p>
	 * 方法执行流程：
	 * - 分割范围值（用~分隔）
	 * - 检查范围的最小值是否大于等于允许的最小值
	 * - 检查范围的最大值是否小于等于允许的最大值
	 *
	 * @param toleranceRange 允许的范围[最小值, 最大值]
	 * @param measuredValue  实测值范围（格式：最小值~最大值）
	 * @return 是否在允许范围内
	 */
	private static boolean compareMeasuredRange(double[] toleranceRange, String measuredValue) {
		String processedValue = preprocessMeasuredValue(measuredValue);
		if (processedValue.contains("~")) {
			String[] parts = processedValue.split("~");
			double min = Double.parseDouble(parts[0]);
			double max = Double.parseDouble(parts[1]);
			return min >= toleranceRange[0] && max <= toleranceRange[1];
		}
		return false;
	}

	/**
	 * 比对实测值列表
	 * <p>
	 * 方法执行流程：
	 * - 分割多个值（用分号分隔）
	 * - 检查每个值是否都在允许范围内
	 *
	 * @param toleranceRange 允许的范围[最小值, 最大值]
	 * @param measuredValue  实测值列表（用分号分隔）
	 * @return 是否所有值都在允许范围内
	 */
	private static boolean compareMeasuredList(double[] toleranceRange, String measuredValue) {
		String[] values = measuredValue.split(";");
		for (String value : values) {
			double measured = Double.parseDouble(value);
			if (measured < toleranceRange[0] || measured > toleranceRange[1]) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 比对单个值
	 * <p>
	 * 方法执行流程：
	 * - 将实测值转换为double类型
	 * - 检查是否在允许范围内
	 *
	 * @param toleranceRange 允许的范围[最小值, 最大值]
	 * @param measuredValue  单个实测值
	 * @return 是否在允许范围内
	 */
	private static boolean compareSingleValue(double[] toleranceRange, String measuredValue) {
		double measured = Double.parseDouble(measuredValue);
		return measured >= toleranceRange[0] && measured <= toleranceRange[1];
	}

    /**
     * 导入XML文件到Thingworx平台
     * 
     * @param xmlFilePath XML文件的路径
     * @return 导入是否成功
     */
    public static boolean importXmlToThingworx(String xmlFilePath) {
        try {
            // 检查配置是否已设置
            if (thingworxDomain == null || thingworxKey == null) {
                log.error("Thingworx配置未设置，请检查application.properties中的thingworx.domain和thingworx.key配置");
                return false;
            }

            // 构建请求URL和参数
            String serverUrl = String.format("%s/Thingworx/Importer?IgnoreBadValueStreamData=false&WithSubsystems=false&purpose=import&usedefaultdataprovider=false&appkey=%s",
                    thingworxDomain, thingworxKey);
			log.info("请求URL：{}", serverUrl);
            // 构建请求头
            HashMap<String, String> headerMap = new HashMap<>();
            headerMap.put("Accept", "*/*");
            headerMap.put("Accept-Language", "zh-CN,zh;q=0.9");
            headerMap.put("Connection", "keep-alive");
            headerMap.put("X-Requested-With", "XMLHttpRequest");
            headerMap.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36");

            // 添加文件
            File xmlFile = new File(xmlFilePath);
            if (!xmlFile.exists()) {
                log.error("XML文件不存在：{}", xmlFilePath);
                return false;
            }
            
            // 构建文件参数
            HashMap<String, Object> formMap = new HashMap<>();
            formMap.put("file", xmlFile);

            try {
                log.info("开始导入XML文件，请求URL：{}", serverUrl);
                // 发送请求
                String result = HttpUtil.createPost(serverUrl)
                        .addHeaders(headerMap)
                        .form(formMap)
                        .execute()
                        .body();

                log.info("导入XML文件成功，服务器响应：{}", result);
                return true;
            } catch (Exception e) {
                log.error("导入XML文件失败", e);
                return false;
            }
        } catch (Exception e) {
            log.error("准备导入XML文件参数时发生错误", e);
            return false;
        }
    }

	/**
	 * 自定义解压方法，不进行zip炸弹检测
	 * @param zipFile zip文件路径
	 * @param destDir 目标目录
	 * @throws IOException IO异常
	 */
	public static void customUnzip(String zipFile, String destDir) throws IOException {
		try (java.util.zip.ZipFile zip = new java.util.zip.ZipFile(zipFile, StandardCharsets.UTF_8)) {
			java.util.Enumeration<? extends java.util.zip.ZipEntry> entries = zip.entries();
			while (entries.hasMoreElements()) {
				java.util.zip.ZipEntry entry = entries.nextElement();
				File destFile = new File(destDir, entry.getName());

				// 创建目标文件的父目录
				if (entry.isDirectory()) {
					FileUtil.mkdir(destFile);
					continue;
				}

				// 确保目标文件的父目录存在
				File parent = destFile.getParentFile();
				if (parent != null && !parent.exists()) {
					FileUtil.mkdir(parent);
				}

				// 复制文件内容
				try (java.io.InputStream in = zip.getInputStream(entry);
					 java.io.OutputStream out = Files.newOutputStream(destFile.toPath())) {
					byte[] buffer = new byte[8192];
					int len;
					while ((len = in.read(buffer)) != -1) {
						out.write(buffer, 0, len);
					}
				}
			}
		}
	}

	/**
	 * 去除JSONObject的getStr获取的数字字符串中的".0"后缀，并处理科学计数法
	 * <p>
	 * 如果字符串是数字且以".0"结尾，则去除".0"后缀
	 * 如果字符串是科学计数法格式，则转换为普通数字字符串
	 * 否则返回原字符串
	 * 
	 * 例如：
	 * "123.0" -> "123"
	 * "123.10" -> "123.10"
	 * "123" -> "123"
	 * "abc" -> "abc"
	 * "1.27395898E+9" -> "1273958980"
	 * 
	 * @param str 待处理的字符串
	 * @return 处理后的字符串
	 */
	public static String removeDecimalZero(String str) {
		if (str == null) {
			return null;
		}
		
		try {
			// 尝试解析为数字，如果是科学计数法会自动转换
			if (str.toUpperCase().contains("E")) {
				double d = Double.parseDouble(str);
				// 使用String.format去除科学计数法
				str = String.format("%.0f", d);
			}
			
			// 判断是否为数字且以.0结尾
			if (str.matches("^\\d+\\.0$")) {
				return str.substring(0, str.length() - 2);
			}
			
			return str;
		} catch (NumberFormatException e) {
			// 如果解析失败，返回原字符串
			return str;
		}
	}

	/**
	 * 处理从MES同步的三级表Excel
	 * <p>
	 * 该方法接收Excel文件路径和数据起始行，处理Excel中的合并单元格：
	 * 1. 遍历excel中除去表头的所有单元格，查找合并单元格并进行拆分
	 * 2. 拆分后的单元格数据与拆分前的单元格数据保持一致，且不为空
	 * 3. 将处理后的excel文件保存到临时路径，文件名为原文件名加上后缀`_deal`
	 * 4. 返回处理后excel文件的绝对路径
	 *
	 * @param excelFilePath Excel文件的绝对路径
	 * @param startRow      数据开始行(从1开始计数)
	 * @return 处理后Excel文件的绝对路径
	 */
	public static String processMesExcel(String excelFilePath, int startRow) {
		log.info("开始处理MES三级表Excel，文件路径: {}, 数据起始行: {}", excelFilePath, startRow);
		
		File srcFile = new File(excelFilePath);
		if (!srcFile.exists()) {
			log.error("Excel文件不存在: {}", excelFilePath);
			throw new IllegalArgumentException("Excel文件不存在: " + excelFilePath);
		}
		
		String fileName = srcFile.getName();
		String mainName = fileName;
		String extName = "";
		int lastDotIndex = fileName.lastIndexOf(".");
		if (lastDotIndex != -1) {
			mainName = fileName.substring(0, lastDotIndex);
			extName = fileName.substring(lastDotIndex + 1);
		}
		
		Workbook workbook = null;
		
		try (InputStream is = Files.newInputStream(srcFile.toPath())) {
			// 使用输入流检测Excel格式并创建对应的Workbook对象
			workbook = createWorkbookFromStream(is, srcFile.getPath());
			
			// 处理每个Sheet中的合并单元格
			int sheetCount = workbook.getNumberOfSheets();
			for (int i = 0; i < sheetCount; i++) {
				Sheet sheet = workbook.getSheetAt(i);
				processSheetMergedCells(sheet, startRow);
			}
			
			// 创建输出文件路径
			String tempDirPath = tempPath + File.separator + System.currentTimeMillis();
			File tempDir = new File(tempDirPath);
			if (!tempDir.exists()) {
				tempDir.mkdirs();
			}
			
			// 生成输出文件名，保持与输入文件相同的扩展名处理方式
			String outputFileName;
			if (lastDotIndex != -1) {
				// 如果原文件有扩展名，添加_deal并保留原扩展名
				outputFileName = mainName + "_deal." + extName;
			} else {
				// 如果原文件没有扩展名，输出文件也不添加扩展名
				outputFileName = mainName + "_deal";
			}
			
			String outputFilePath = tempDirPath + File.separator + outputFileName;
			
			// 写入文件
			try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
				workbook.write(fos);
				log.info("MES三级表Excel处理完成，输出文件: {}", outputFilePath);
				return outputFilePath;
			}
		} catch (IOException e) {
			log.error("处理MES三级表Excel时发生异常", e);
			throw new RuntimeException("处理Excel文件失败: " + e.getMessage(), e);
		} finally {
			if (workbook != null) {
				try {
					workbook.close();
				} catch (IOException e) {
					log.error("关闭Workbook时发生异常", e);
				}
			}
		}
	}
	
	/**
	 * 从输入流创建对应的Workbook对象，自动检测Excel格式
	 *
	 * @param is           Excel文件输入流
	 * @param filePath     文件路径(仅用于日志记录)
	 * @return 对应的Workbook对象
	 * @throws IOException 如果读取文件时发生IO异常
	 */
	private static Workbook createWorkbookFromStream(InputStream is, String filePath) throws IOException {
		// 需要支持mark/reset的输入流
		if (!is.markSupported()) {
			is = new BufferedInputStream(is);
		}
		
		is.mark(8);
		
		try {
			// 尝试检测文件格式
			byte[] header = new byte[8];
			int read = is.read(header);
			is.reset();
			
			// 检查是否为OLE2格式(xls)
			if (read >= 8 && (header[0] == (byte)0xD0 && header[1] == (byte)0xCF && 
				header[2] == (byte)0x11 && header[3] == (byte)0xE0)) {
				log.info("检测到XLS格式文件: {}", filePath);
				return new HSSFWorkbook(is);
			}
			// 检查是否为OOXML格式(xlsx)
			else if (read >= 4 && (header[0] == (byte)0x50 && header[1] == (byte)0x4B && 
				header[2] == (byte)0x03 && header[3] == (byte)0x04)) {
				log.info("检测到XLSX格式文件: {}", filePath);
				return new XSSFWorkbook(is);
			}
			// 如果无法确定，尝试使用WorkbookFactory自动检测
			else {
				log.info("无法确定Excel格式，使用WorkbookFactory尝试检测: {}", filePath);
				is.reset();
				return WorkbookFactory.create(is);
			}
		} catch (Exception e) {
			log.error("创建Workbook对象时发生异常: {}", e.getMessage());
			// 如果上述方法都失败，尝试使用WorkbookFactory作为最后手段
			is.reset();
			return WorkbookFactory.create(is);
		}
	}

	/**
	 * 处理Sheet中的合并单元格
	 *
	 * @param sheet    要处理的Sheet对象
	 * @param startRow 数据开始行(从1开始计数)
	 */
	private static void processSheetMergedCells(Sheet sheet, int startRow) {
		// 获取所有合并区域的副本，因为处理过程中会修改合并区域
		List<CellRangeAddress> mergedRegions = new ArrayList<>(sheet.getMergedRegions());
		
		// 从后向前移除合并区域(避免索引变化导致的问题)
		for (int i = mergedRegions.size() - 1; i >= 0; i--) {
			CellRangeAddress region = mergedRegions.get(i);
			
			// 只处理数据区域(startRow之后)的合并单元格
			if (region.getFirstRow() >= startRow - 1) {
				// 获取合并单元格的值
				Row firstRow = sheet.getRow(region.getFirstRow());
				if (firstRow == null) {
					// 如果行不存在，跳过此合并区域
					continue;
				}
				
				Cell firstCell = firstRow.getCell(region.getFirstColumn());
				String cellValue = getCellValueAsString(firstCell);
				
				// 移除合并区域
				sheet.removeMergedRegion(i);
				
				// 填充值到每个拆分后的单元格
				fillUnmergedRegion(sheet, region, cellValue);
			}
		}
	}

	/**
	 * 获取单元格的值并转换为字符串
	 *
	 * @param cell 单元格对象
	 * @return 单元格值的字符串表示
	 */
	private static String getCellValueAsString(Cell cell) {
		if (cell == null) {
			return "";
		}
		
		CellType cellType = cell.getCellType();
		if (cellType == CellType.FORMULA) {
			cellType = cell.getCachedFormulaResultType();
		}
		
		switch (cellType) {
			case STRING:
				return cell.getStringCellValue();
			case NUMERIC:
				if (DateUtil.isCellDateFormatted(cell)) {
					return new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
				}
				// 避免数值型显示为科学计数法或小数点后多余的0
				double numValue = cell.getNumericCellValue();
				if (numValue == (long) numValue) {
					return String.valueOf((long) numValue);
				}
				return String.valueOf(numValue);
			case BOOLEAN:
				return String.valueOf(cell.getBooleanCellValue());
			case BLANK:
				return "";
			default:
				return "";
		}
	}

	/**
	 * 填充拆分后的单元格区域
	 *
	 * @param sheet  工作表对象
	 * @param region 原合并单元格区域
	 * @param value  要填充的值
	 */
	private static void fillUnmergedRegion(Sheet sheet, CellRangeAddress region, String value) {
		if (value == null || value.isEmpty()) {
			log.warn("拆分的合并单元格值为空，将使用空字符串填充");
			value = "";
		}
		
		for (int r = region.getFirstRow(); r <= region.getLastRow(); r++) {
			Row row = sheet.getRow(r);
			if (row == null) {
				row = sheet.createRow(r);
			}
			
			for (int c = region.getFirstColumn(); c <= region.getLastColumn(); c++) {
				Cell cell = row.getCell(c);
				if (cell == null) {
					cell = row.createCell(c);
				}
				
				// 保持原有的单元格样式
				if (r == region.getFirstRow() && c == region.getFirstColumn() && cell.getCellStyle() != null) {
					CellStyle originalStyle = cell.getCellStyle();
					
					for (int nr = region.getFirstRow(); nr <= region.getLastRow(); nr++) {
						Row nrow = sheet.getRow(nr);
						if (nrow == null) {
							nrow = sheet.createRow(nr);
						}
						
						for (int nc = region.getFirstColumn(); nc <= region.getLastColumn(); nc++) {
							if (nr == region.getFirstRow() && nc == region.getFirstColumn()) {
								continue; // 跳过第一个单元格，它已经有样式了
							}
							
							Cell ncell = nrow.getCell(nc);
							if (ncell == null) {
								ncell = nrow.createCell(nc);
							}
							ncell.setCellStyle(originalStyle);
						}
					}
				}
				
				// 设置单元格值
				cell.setCellValue(value);
			}
		}
	}

}
