<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <path d="M29.6,5.5L18.2,5.5L16.1,9.7L4.4,9.7L4.4,26.5L29.6,26.5L29.6,5.5ZM27.5,9.7L19.3,9.7L20.4,7.6L27.5,7.6L27.5,9.7Z" style="fill:rgb(192,149,83);fill-rule:nonzero;"/>
    <g transform="matrix(0.751624,0,0,0.751624,12.3555,11.2047)">
        <path d="M7.068,4.359C7.086,5.36 7.492,6.316 8.2,7.024C8.739,7.519 9.441,7.799 10.173,7.811C10.411,7.802 10.636,7.701 10.801,7.53C11.523,6.762 11.789,5.668 11.501,4.654C10.97,2.815 9.5,1.8 7.7,0.915C7.238,2.002 7.023,3.179 7.068,4.359Z" style="fill:url(#_Linear1);fill-rule:nonzero;"/>
        <path d="M4.4,7.031C4.799,7.949 5.542,8.676 6.468,9.056C7.155,9.306 7.911,9.295 8.59,9.025C8.806,8.926 8.976,8.746 9.063,8.525C9.438,7.541 9.269,6.428 8.617,5.6C7.42,4.1 5.678,3.728 3.676,3.607C3.662,4.788 3.91,5.957 4.4,7.031Z" style="fill:url(#_Linear2);fill-rule:nonzero;"/>
        <path d="M2.959,10.524C3.677,11.218 4.639,11.604 5.638,11.6C6.369,11.567 7.063,11.266 7.587,10.756C7.749,10.581 7.837,10.35 7.833,10.111C7.804,9.059 7.223,8.096 6.306,7.58C4.631,6.661 2.878,6.98 0.982,7.64C1.42,8.735 2.095,9.72 2.959,10.524Z" style="fill:url(#_Linear3);fill-rule:nonzero;"/>
        <path d="M2.956,14.307C3.885,14.672 4.921,14.66 5.841,14.272C6.504,13.96 7.03,13.416 7.32,12.743C7.402,12.519 7.395,12.271 7.3,12.052C6.874,11.091 5.97,10.424 4.926,10.3C3.026,10.1 1.532,11.066 0.026,12.4C0.849,13.245 1.85,13.897 2.956,14.307Z" style="fill:url(#_Linear4);fill-rule:nonzero;"/>
        <path d="M4.394,17.8C5.393,17.78 6.346,17.371 7.047,16.659C7.54,16.116 7.819,15.412 7.831,14.678C7.822,14.44 7.722,14.213 7.551,14.047C6.79,13.322 5.699,13.053 4.688,13.341C2.857,13.881 1.844,15.354 0.966,17.165C2.047,17.628 3.218,17.845 4.394,17.8Z" style="fill:url(#_Linear5);fill-rule:nonzero;"/>
        <path d="M7.054,20.479C7.971,20.075 8.694,19.329 9.07,18.4C9.319,17.709 9.308,16.951 9.039,16.268C8.94,16.051 8.761,15.88 8.539,15.792C7.558,15.415 6.447,15.587 5.625,16.241C4.139,17.441 3.765,19.194 3.643,21.205C4.82,21.219 5.985,20.971 7.054,20.479Z" style="fill:url(#_Linear6);fill-rule:nonzero;"/>
        <path d="M10.531,21.93C11.222,21.205 11.606,20.24 11.6,19.238C11.567,18.505 11.268,17.808 10.759,17.28C10.585,17.117 10.355,17.028 10.117,17.032C9.067,17.064 8.109,17.648 7.6,18.567C6.686,20.25 7.006,22.01 7.66,23.915C8.752,23.474 9.733,22.796 10.531,21.93Z" style="fill:url(#_Linear7);fill-rule:nonzero;"/>
        <path d="M14.3,21.933C14.664,20.999 14.652,19.958 14.265,19.033C13.956,18.369 13.415,17.84 12.743,17.547C12.52,17.464 12.274,17.47 12.056,17.565C11.096,17.997 10.431,18.906 10.311,19.952C10.111,21.858 11.074,23.361 12.404,24.87C13.245,24.043 13.892,23.04 14.3,21.933Z" style="fill:url(#_Linear8);fill-rule:nonzero;"/>
        <path d="M17.776,20.489C17.757,19.487 17.35,18.531 16.64,17.824C16.101,17.329 15.399,17.048 14.667,17.036C14.429,17.045 14.204,17.146 14.039,17.317C13.317,18.085 13.051,19.179 13.339,20.193C13.877,22.032 15.339,23.05 17.146,23.932C17.607,22.845 17.822,21.669 17.776,20.489Z" style="fill:url(#_Linear9);fill-rule:nonzero;"/>
        <path d="M20.441,17.817C20.042,16.899 19.301,16.173 18.376,15.792C17.689,15.542 16.933,15.553 16.254,15.823C16.038,15.922 15.868,16.102 15.781,16.323C15.405,17.308 15.575,18.422 16.228,19.25C17.428,20.743 19.168,21.119 21.17,21.241C21.182,20.06 20.933,18.891 20.441,17.817Z" style="fill:url(#_Linear10);fill-rule:nonzero;"/>
        <path d="M21.885,14.324C21.167,13.63 20.205,13.243 19.206,13.247C18.475,13.28 17.781,13.581 17.257,14.091C17.095,14.266 17.007,14.497 17.011,14.736C17.04,15.789 17.621,16.753 18.539,17.269C20.214,18.188 21.967,17.869 23.863,17.209C23.425,16.113 22.749,15.128 21.885,14.324Z" style="fill:url(#_Linear11);fill-rule:nonzero;"/>
        <path d="M21.888,10.541C20.958,10.175 19.921,10.187 19,10.576C18.337,10.888 17.811,11.432 17.521,12.105C17.438,12.329 17.444,12.577 17.539,12.796C17.965,13.758 18.87,14.426 19.915,14.549C21.815,14.749 23.309,13.782 24.815,12.449C23.993,11.604 22.993,10.952 21.888,10.541Z" style="fill:url(#_Linear12);fill-rule:nonzero;"/>
        <path d="M20.451,7.046C19.453,7.066 18.501,7.476 17.8,8.187C17.306,8.73 17.027,9.435 17.016,10.169C17.025,10.407 17.125,10.634 17.296,10.8C18.057,11.525 19.148,11.794 20.159,11.506C21.99,10.966 23.003,9.493 23.881,7.682C22.799,7.218 21.627,7.001 20.451,7.046Z" style="fill:url(#_Linear13);fill-rule:nonzero;"/>
        <path d="M17.791,4.369C16.875,4.772 16.152,5.516 15.775,6.443C15.526,7.134 15.537,7.892 15.806,8.575C15.905,8.792 16.084,8.963 16.306,9.051C17.287,9.427 18.397,9.255 19.218,8.6C20.7,7.4 21.079,5.649 21.2,3.638C20.024,3.626 18.859,3.875 17.791,4.369Z" style="fill:url(#_Linear14);fill-rule:nonzero;"/>
        <path d="M14.313,2.918C13.621,3.642 13.236,4.608 13.241,5.61C13.273,6.343 13.572,7.039 14.081,7.568C14.255,7.731 14.485,7.82 14.723,7.816C15.775,7.786 16.736,7.201 17.245,6.28C18.159,4.597 17.839,2.837 17.185,0.932C16.092,1.373 15.111,2.052 14.313,2.918Z" style="fill:url(#_Linear15);fill-rule:nonzero;"/>
        <path d="M10.548,2.915C10.184,3.849 10.196,4.89 10.583,5.815C10.892,6.479 11.434,7.008 12.105,7.3C12.328,7.383 12.574,7.377 12.792,7.282C13.752,6.85 14.417,5.941 14.537,4.895C14.737,2.989 13.774,1.486 12.444,-0.023C11.603,0.805 10.956,1.808 10.548,2.915Z" style="fill:url(#_Linear16);fill-rule:nonzero;"/>
    </g>
    <defs>
        <linearGradient id="_Linear1" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear2" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear3" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear4" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear5" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear6" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear7" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear8" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear9" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear10" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear11" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear12" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear13" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear14" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear15" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
        <linearGradient id="_Linear16" x1="0" y1="0" x2="1" y2="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(25,-25,25,25,0,25)"><stop offset="0" style="stop-color:rgb(10,0,178);stop-opacity:0.65"/><stop offset="0.5" style="stop-color:rgb(255,0,0);stop-opacity:0.65"/><stop offset="1" style="stop-color:rgb(255,252,0);stop-opacity:0.65"/></linearGradient>
    </defs>
</svg>
