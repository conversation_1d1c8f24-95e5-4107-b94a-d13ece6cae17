package com.cirpoint.controller;

import com.cirpoint.service.ListService;
import com.cirpoint.util.FileDownloadUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * List相关操作的控制器
 */
@Slf4j
@RestController
@RequestMapping("/list")
public class ListController {

	private final ListService listService;

	@Autowired
	public ListController(ListService listService) {
		this.listService = listService;
	}

	/**
	 * 导出数据包
	 *
	 * @param ids      数据包ID列表
	 * @param secLevel 密级
	 * @param username 用户名
	 * @return ResponseEntity
	 */
	@PostMapping("/package/export")
	public ResponseEntity<?> exportDataPackage(@RequestParam String ids,
													@RequestParam String secLevel,
													@RequestParam String username) {
		return FileDownloadUtil.fileResponseAndDelete(listService.exportDataPackage(ids, secLevel, username));
	}

	/**
	 * 导出清单列表Excel
	 *
	 * @param query 查询参数
	 * @return ResponseEntity
	 */
	@PostMapping("/export/excel")
	public ResponseEntity<?> exportListExcel(@RequestParam String query) {
		return FileDownloadUtil.fileResponseAndDelete(listService.exportListExcel(query));
	}

	/**
	 * 获取360全景图片查看路径
	 *
	 * @param path 文件路径
	 * @return ResponseEntity
	 */
	@PostMapping("/get/360/path")
	public ResponseEntity<?> get360ImageViewUrl(@RequestParam String path) {
		return ResponseEntity.ok(listService.get360ImageViewUrl(path));
	}

	/**
	 * 获取PDM文件路径
	 *
	 * @param zipUrl zip文件URL
	 * @return ResponseEntity
	 */
	@PostMapping("/get/pdm/path")
	public ResponseEntity<?> getPDMFilePath(@RequestParam String zipUrl) {
		return ResponseEntity.ok(listService.getPDMFilePath(zipUrl));
	}

	/**
	 * 推送文件到档案系统
	 *
	 * @param id       树节点ID
	 * @param username 用户名
	 * @param pushKey  推送标识
	 * @return ResponseEntity
	 */
	@PostMapping("/push/files")
	public ResponseEntity<?> pushFilesToSystem(@RequestParam String id,
											   @RequestParam String username,
											   @RequestParam String pushKey) {
		return ResponseEntity.ok(listService.pushFilesToSystem(id, username, pushKey));
	}
}