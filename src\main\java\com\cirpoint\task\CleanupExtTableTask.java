package com.cirpoint.task;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 文件清理定时任务 清理509 接口调用产生的文件
 * 每两天的凌晨3点执行一次，删除指定目录下两天前的文件
 */
@Slf4j
@Component
public class CleanupExtTableTask {

    @Value("${file.upload.path}")
    private String fileUploadPath;

    /**
     * 定时任务：每两天的凌晨3点执行一次
     * cron表达式：秒 分 时 日 月 星期
     */
    @Scheduled(cron = "0 0 3 */2 * *")
    public void cleanupOldFiles() {
        log.info("开始执行文件清理定时任务");

        try {
            // 构建ExtTable目录的完整路径
            String extTablePath = fileUploadPath + "ExtTable";
            Path dirPath = Paths.get(extTablePath);

            // 检查目录是否存在
            if (!Files.exists(dirPath)) {
                log.warn("目标目录不存在: {}", extTablePath);
                return;
            }

            // 计算两天前的时间
            LocalDateTime twoDaysAgo = LocalDateTime.now().minusDays(2);

            // 用于统计删除的文件数量
            AtomicInteger deletedCount = new AtomicInteger(0);

            // 遍历目录下的所有文件
            try (Stream<Path> pathStream = Files.walk(dirPath)) {
                pathStream.filter(Files::isRegularFile)
                        .forEach(filePath -> {
                            try {
                                BasicFileAttributes attrs = Files.readAttributes(filePath, BasicFileAttributes.class);
                                LocalDateTime fileCreationTime = LocalDateTime.ofInstant(
                                        attrs.creationTime().toInstant(), ZoneId.systemDefault());

                                // 如果文件创建时间早于两天前
                                if (fileCreationTime.isBefore(twoDaysAgo)) {
                                    // 删除文件
                                    Files.delete(filePath);
                                    deletedCount.incrementAndGet();
                                }
                            } catch (Exception e) {
                                log.error("处理文件时出错: {}", filePath, e);
                            }
                        });
            }

            log.info("文件清理任务执行成功，共删除 {} 个文件", deletedCount.get());

        } catch (Exception e) {
            log.error("文件清理任务执行失败", e);
        }
    }
}
