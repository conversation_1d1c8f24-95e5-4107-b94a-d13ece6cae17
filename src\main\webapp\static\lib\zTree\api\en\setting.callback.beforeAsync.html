<div class="apiDetail">
<div>
	<h2><span>Function(treeId, treeNode)</span><span class="path">setting.callback.</span>beforeAsync</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>This callback is made before z<PERSON><PERSON> makes an ajax request, giving you an opportunity to decide if it should proceed or not. 
                            Return false to prevent zT<PERSON> from sending the ajax request.</p>
			<p>Default: null</p>
		</div>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>treeId</b><span>String</span></h4>
	<p>zTree unique identifier: <b class="highlight_red">treeId</b>.</p>
	<h4 class="topLine"><b>treeNode</b><span>JSON</span></h4>
	<p>JSON data object of the parent node</p>
	<p class="highlight_red">When asynchronously loading the root, treeNode = null</p>
	<h4 class="topLine"><b>Return </b><span>Boolean</span></h4>
	<p>return true or false</p>
	<p class="highlight_red">If the function returns false, zTree will not send the ajax request, and will not trigger the 'onAsyncSuccess / onAsyncError' callback.</p>
	</div>
	<h3>Examples of setting & function</h3>
	<h4>1. If the parent node's attribute 'id' is 1, don't send the ajax request.</h4>
	<pre xmlns=""><code>function myBeforeCallBack(treeId, treeNode) {
    return (treeNode.id !== 1);
};
var setting = {
	callback: {
		beforeAsync: myBeforeCallBack
	}
};
......</code></pre>
</div>
</div>