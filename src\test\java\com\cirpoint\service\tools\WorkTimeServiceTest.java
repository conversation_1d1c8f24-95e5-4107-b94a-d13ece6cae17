package com.cirpoint.service.tools;

import com.cirpoint.constant.WorkTimeConstants;
import com.cirpoint.model.worktime.AttendanceRecord;
import com.cirpoint.model.worktime.WorkTimeResult;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工时统计服务测试类
 * 用于验证核心业务逻辑的正确性
 */
@SpringBootTest
@TestPropertySource(properties = {
    "file.temp.path=target/test-temp"
})
public class WorkTimeServiceTest {
    
    /**
     * 测试正常工作日场景
     */
    @Test
    public void testNormalWorkDay() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 创建正常工作日数据：8:00入场，17:00出场
        LocalDateTime baseDate = LocalDateTime.of(2025, 6, 24, 8, 0, 0);
        
        AttendanceRecord inRecord = new AttendanceRecord();
        inRecord.setEmployeeName("张三");
        inRecord.setAccessPoint("910入");
        inRecord.setDirection(WorkTimeConstants.DIRECTION_IN);
        inRecord.setEventTime(baseDate);
        
        AttendanceRecord outRecord = new AttendanceRecord();
        outRecord.setEmployeeName("张三");
        outRecord.setAccessPoint("910出");
        outRecord.setDirection(WorkTimeConstants.DIRECTION_OUT);
        outRecord.setEventTime(baseDate.plusHours(9)); // 9小时后
        
        records.add(inRecord);
        records.add(outRecord);
        
        // 验证记录有效性
        assertTrue(inRecord.isValid());
        assertTrue(outRecord.isValid());
        assertEquals("910厂房", inRecord.getLocation());
        assertEquals("910厂房", outRecord.getLocation());
    }
    
    /**
     * 测试跨天工作场景
     */
    @Test
    public void testCrossDayWork() {
        AttendanceRecord inRecord = new AttendanceRecord();
        inRecord.setEmployeeName("李四");
        inRecord.setAccessPoint("920入");
        inRecord.setDirection(WorkTimeConstants.DIRECTION_IN);
        inRecord.setEventTime(LocalDateTime.of(2025, 6, 24, 22, 0, 0));
        
        AttendanceRecord outRecord = new AttendanceRecord();
        outRecord.setEmployeeName("李四");
        outRecord.setAccessPoint("920出");
        outRecord.setDirection(WorkTimeConstants.DIRECTION_OUT);
        outRecord.setEventTime(LocalDateTime.of(2025, 6, 25, 6, 0, 0)); // 第二天
        
        assertTrue(inRecord.isValid());
        assertTrue(outRecord.isValid());
        assertEquals("920厂房", inRecord.getLocation());
        assertEquals("920厂房", outRecord.getLocation());
    }
    
    /**
     * 测试多地点工作场景
     */
    @Test
    public void testMultiLocationWork() {
        List<AttendanceRecord> records = new ArrayList<>();
        LocalDateTime baseDate = LocalDateTime.of(2025, 6, 24, 8, 0, 0);
        
        // 910厂房工作
        records.add(createRecord("王五", "910入", WorkTimeConstants.DIRECTION_IN, baseDate));
        records.add(createRecord("王五", "910出", WorkTimeConstants.DIRECTION_OUT, baseDate.plusHours(4)));
        
        // 920厂房工作
        records.add(createRecord("王五", "920入", WorkTimeConstants.DIRECTION_IN, baseDate.plusHours(5)));
        records.add(createRecord("王五", "920出", WorkTimeConstants.DIRECTION_OUT, baseDate.plusHours(9)));
        
        // 验证场所判定
        assertEquals("910厂房", records.get(0).getLocation());
        assertEquals("910厂房", records.get(1).getLocation());
        assertEquals("920厂房", records.get(2).getLocation());
        assertEquals("920厂房", records.get(3).getLocation());
    }
    
    /**
     * 测试异常数据处理
     */
    @Test
    public void testAbnormalData() {
        // 测试悬挂的出场记录
        AttendanceRecord hangingOut = new AttendanceRecord();
        hangingOut.setEmployeeName("赵六");
        hangingOut.setAccessPoint("910出");
        hangingOut.setDirection(WorkTimeConstants.DIRECTION_OUT);
        hangingOut.setEventTime(LocalDateTime.of(2025, 6, 24, 17, 0, 0));
        
        assertTrue(hangingOut.isValid());
        
        // 测试悬挂的入场记录
        AttendanceRecord hangingIn = new AttendanceRecord();
        hangingIn.setEmployeeName("钱七");
        hangingIn.setAccessPoint("920入");
        hangingIn.setDirection(WorkTimeConstants.DIRECTION_IN);
        hangingIn.setEventTime(LocalDateTime.of(2025, 6, 24, 8, 0, 0));
        
        assertTrue(hangingIn.isValid());
    }
    
    /**
     * 测试场所判定逻辑
     */
    @Test
    public void testLocationDetermination() {
        assertEquals("910厂房", AttendanceRecord.determineLocation("910入"));
        assertEquals("910厂房", AttendanceRecord.determineLocation("910出"));
        assertEquals("920厂房", AttendanceRecord.determineLocation("920入"));
        assertEquals("920厂房", AttendanceRecord.determineLocation("920出"));
        assertEquals("二号楼", AttendanceRecord.determineLocation("二号楼北入"));
        assertEquals("二号楼", AttendanceRecord.determineLocation("二号楼南出"));
        assertEquals("其他场所", AttendanceRecord.determineLocation("未知门禁点"));
        assertEquals("未知场所", AttendanceRecord.determineLocation(null));
    }
    
    /**
     * 测试工时结果模型
     */
    @Test
    public void testWorkTimeResult() {
        WorkTimeResult result = new WorkTimeResult();
        result.setSequence(1);
        result.setEmployeeName("测试员工");
        result.setWorkDate(LocalDate.of(2025, 6, 24));
        result.setDailyHours(8.5);
        result.setAverageHours(8.0);
        
        assertTrue(result.isValid());
        assertEquals("2025-06-24", result.getWorkDateString());
        assertEquals("8.50", result.getFormattedDailyHours());
        assertEquals("8.00", result.getFormattedAverageHours());
        assertEquals("测试员工|2025-06-24", result.getSortKey());
    }
    
    /**
     * 测试数据验证
     */
    @Test
    public void testDataValidation() {
        // 测试无效记录
        AttendanceRecord invalidRecord = new AttendanceRecord();
        assertFalse(invalidRecord.isValid());
        
        // 测试缺少姓名
        invalidRecord.setAccessPoint("910入");
        invalidRecord.setDirection(WorkTimeConstants.DIRECTION_IN);
        invalidRecord.setEventTime(LocalDateTime.now());
        assertFalse(invalidRecord.isValid());
        
        // 测试完整有效记录
        invalidRecord.setEmployeeName("测试员工");
        assertTrue(invalidRecord.isValid());
    }
    
    /**
     * 测试分组键生成
     */
    @Test
    public void testGroupKeyGeneration() {
        AttendanceRecord record = new AttendanceRecord();
        record.setEmployeeName("张三");
        record.setEmployeeId("123456789");
        record.setAccessPoint("910入");
        record.setEventTime(LocalDateTime.of(2025, 6, 24, 8, 0, 0));

        String expectedKey = "张三+123456789|910厂房|2025-06-24";
        assertEquals(expectedKey, record.getGroupKey());
    }

    /**
     * 测试分组键生成（无工号）
     */
    @Test
    public void testGroupKeyGenerationWithoutEmployeeId() {
        AttendanceRecord record = new AttendanceRecord();
        record.setEmployeeName("张三");
        record.setAccessPoint("910入");
        record.setEventTime(LocalDateTime.of(2025, 6, 24, 8, 0, 0));

        String expectedKey = "张三|910厂房|2025-06-24";
        assertEquals(expectedKey, record.getGroupKey());
    }
    
    /**
     * 创建测试记录的辅助方法
     */
    private AttendanceRecord createRecord(String employeeName, String accessPoint, 
                                        String direction, LocalDateTime eventTime) {
        AttendanceRecord record = new AttendanceRecord();
        record.setEmployeeName(employeeName);
        record.setAccessPoint(accessPoint);
        record.setDirection(direction);
        record.setEventTime(eventTime);
        return record;
    }
}
