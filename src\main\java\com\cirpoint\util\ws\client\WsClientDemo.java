package com.cirpoint.util.ws.client;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.cirpoint.util.ws.interceptor.ClientLoginInterceptor;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.apache.cxf.transport.http.HTTPConduit;
import org.apache.cxf.transports.http.configuration.HTTPClientPolicy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WsClientDemo {
	
	private static Logger logger = LoggerFactory.getLogger(WsClientDemo.class);
	
	public static void main(String[] args) {
		WsClientDemo client =new WsClientDemo();
		String baseUrl = "http://10.0.37.202:8085";
		//String baseUrl = "http://127.0.0.1:2030";
		//瀚海无认证服务
		client.testHanhaiWs(baseUrl);
		
		//标准认证服务
		client.testAvfmsWs(baseUrl);
		
		//REST服务
		client.testRest(baseUrl);
		
	}
	public void testRest(String baseUrl) {
		String url = baseUrl+"/avfms/collect/rest/test";		
		String title = "测试信息";
		String auth = "avfms";
		logger.info("调用服务【{}】",url);
		long begin =System.currentTimeMillis();
		String result = this.callRest(url,auth,title);
		long end =System.currentTimeMillis();
		logger.info("瀚海无认证服务,耗时【{}】毫秒",end-begin);
		logger.info("调用返回【{}】",result);
	}
	
	public void testHanhaiWs(String baseUrl) {
		String url = baseUrl+"/avfms/ws/hanhai?wsdl";
		String method = "test";
		String title = "测试信息";
		logger.info("调用服务【{}】",url);
		long begin =System.currentTimeMillis();
		String result = this.callHanhaiWS(url,method,title);
		long end =System.currentTimeMillis();
		logger.info("瀚海无认证服务,耗时【{}】毫秒",end-begin);
		logger.info("调用返回【{}】",result);
	}
	
	public void testAvfmsWs(String baseUrl) {
		String url = baseUrl+"/avfms/ws/collect?wsdl";
		String method = "test";
		String username = "avfms";
		String password = "";
		String title = "测试信息";
		logger.info("调用服务【{}】",url);
		long begin =System.currentTimeMillis();
		String result = this.callWS(url,method,username,password,title);
		long end =System.currentTimeMillis();
		logger.info("标准认证服务,耗时【{}】毫秒",end-begin);
		logger.info("调用返回【{}】",result);	
	}
	
	private String callWS(String url, String method, String username,String password,String title) {		
		 try{			 
			//创建动态客户端
	        JaxWsDynamicClientFactory factory = JaxWsDynamicClientFactory.newInstance();
	        Client client = factory.createClient(url);
	        
	        // 设置拦截器.需要密码的情况需要加上用户名和密码
	        client.getOutInterceptors().add(new ClientLoginInterceptor(username,password));
	        HTTPConduit conduit = (HTTPConduit) client.getConduit();
	        HTTPClientPolicy httpClientPolicy = new HTTPClientPolicy();
	        httpClientPolicy.setConnectionTimeout(2000); //连接超时
	        httpClientPolicy.setAllowChunking(false); //取消块编码
	        httpClientPolicy.setReceiveTimeout(120000); //响应超时
	        conduit.setClient(httpClientPolicy);
	               
            Object[] objects = new Object[0];
            // invoke("方法名",参数1,参数2,参数3....);
            objects = client.invoke(method,title);
            //objects = client.invoke(method,title, "", null, 0, true,"");
            logger.info("WS返回数据:" + objects[0]);
            return (String) objects[0];
        }catch (Exception e){
            //e.printStackTrace();
            return e.getMessage();
        }
			
	}
	
	private String callHanhaiWS(String url, String method, String title) {		
		 try{			 
			//创建动态客户端
	        JaxWsDynamicClientFactory factory = JaxWsDynamicClientFactory.newInstance();
	        Client client = factory.createClient(url);
	        
	        // 设置拦截器.需要密码的情况需要加上用户名和密码
	        //client.getOutInterceptors().add(new ClientLoginInterceptor(username,password));
	        HTTPConduit conduit = (HTTPConduit) client.getConduit();
	        HTTPClientPolicy httpClientPolicy = new HTTPClientPolicy();
	        httpClientPolicy.setConnectionTimeout(2000); //连接超时
	        httpClientPolicy.setAllowChunking(false); //取消块编码
	        httpClientPolicy.setReceiveTimeout(120000); //响应超时
	        conduit.setClient(httpClientPolicy);
	               
           Object[] objects = new Object[0];
           // invoke("方法名",参数1,参数2,参数3....);
           objects = client.invoke(method,title);
           //objects = client.invoke(method,title, "", null, 0, true,"");
           logger.info("WS返回数据:" + objects[0]);
           return (String) objects[0];
       }catch (Exception e){
           //e.printStackTrace();
           return e.getMessage();
       }
			
	}
	
	private String callRest(String url,String auth,String title) {
		JSONObject paramMap = new JSONObject();
		paramMap.set("auth", auth);
		paramMap.set("title", title);		
		String result = HttpUtil.post(url, paramMap.toString());
		return result;
	}

}
