<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><defs><linearGradient id="a" x1="-66.852" y1="-240.154" x2="-66.841" y2="-240.191" gradientTransform="matrix(192.29, 0, 0, -207.729, 12866.44, -49886.653)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#f6b884"/><stop offset="0.618" stop-color="#c28555"/><stop offset="1" stop-color="#64441f"/></linearGradient><linearGradient id="b" x1="-66.921" y1="-240.187" x2="-66.808" y2="-240.259" gradientTransform="matrix(188.606, 0, 0, -203.703, 12621.303, -48923.376)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#f4e06f"/><stop offset="0.618" stop-color="#e5bd87"/><stop offset="1" stop-color="#c48e30"/></linearGradient><linearGradient id="c" x1="-66.923" y1="-240.339" x2="-66.807" y2="-240.399" gradientTransform="matrix(165.034, 0, 0, -177.71, 11053.518, -42700.349)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fff"/><stop offset="0.143" stop-color="#f1f7fa"/><stop offset="0.415" stop-color="#cce1ee"/><stop offset="0.783" stop-color="#90bedb"/><stop offset="1" stop-color="#6aa7ce"/></linearGradient><radialGradient id="d" cx="-72.351" cy="-249.713" r="1.586" gradientTransform="matrix(1.412, 0, 0, -1.414, 109.302, -347.66)" gradientUnits="userSpaceOnUse"><stop offset="0"/><stop offset="1" stop-color="#e8e9eb"/></radialGradient><radialGradient id="e" cx="-72.342" cy="-249.718" r="1.584" gradientTransform="matrix(1.414, 0, 0, -1.414, 112.293, -347.965)" xlink:href="#d"/><radialGradient id="f" cx="-72.341" cy="-249.714" r="1.584" gradientTransform="matrix(1.414, 0, 0, -1.414, 115.217, -348.579)" xlink:href="#d"/><radialGradient id="g" cx="-72.337" cy="-249.719" r="1.582" gradientTransform="matrix(1.415, 0, 0, -1.414, 118.195, -348.93)" xlink:href="#d"/><radialGradient id="h" cx="-72.343" cy="-249.715" r="1.584" gradientTransform="matrix(1.414, 0, 0, -1.414, 120.897, -349.51)" xlink:href="#d"/><radialGradient id="i" cx="-72.195" cy="-243.375" r="3.612" gradientTransform="matrix(1.35, 0, 0, -3.2, 110.7, -777.789)" gradientUnits="userSpaceOnUse"><stop offset="0"/><stop offset="0.624" stop-color="#e8e9eb"/><stop offset="1"/></radialGradient><radialGradient id="j" cx="-72.196" cy="-243.373" r="3.613" gradientTransform="matrix(1.35, 0, 0, -3.201, 113.57, -778.501)" xlink:href="#i"/><radialGradient id="k" cx="-72.193" cy="-243.374" r="3.611" gradientTransform="matrix(1.35, 0, 0, -3.2, 116.451, -778.732)" xlink:href="#i"/><radialGradient id="l" cx="-71.786" cy="-243.41" r="3.333" gradientTransform="matrix(1.454, 0, 0, -3.177, 114.631, -771.838)" xlink:href="#i"/><radialGradient id="m" cx="-71.795" cy="-243.135" r="3.53" gradientTransform="matrix(1.452, 0, 0, -3.363, 111.635, -815.689)" xlink:href="#i"/><linearGradient id="n" x1="-68.086" y1="-240.348" x2="-68.101" y2="-240.344" gradientTransform="matrix(70.589, 0, 0, -134.346, 4828.573, -32270.351)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#cc772f"/><stop offset="0.309" stop-color="#e98e38"/><stop offset="1" stop-color="#edce27"/></linearGradient><linearGradient id="o" x1="-72.813" y1="-245.749" x2="-72.769" y2="-245.764" gradientTransform="matrix(22.592, 0, 0, -28.597, 1670.35, -7017.545)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fff"/><stop offset="1" stop-color="#9fa4ab"/></linearGradient><linearGradient id="p" x1="-76.814" y1="-259.826" x2="-76.82" y2="-259.696" gradientTransform="matrix(13.542, 0, 0, -8.575, 1065.861, -2216.325)" xlink:href="#o"/><linearGradient id="q" x1="-77.023" y1="-260.918" x2="-77.03" y2="-260.788" gradientTransform="matrix(13.411, 0, 0, -8.289, 1058.839, -2151.486)" xlink:href="#o"/><linearGradient id="r" x1="-77.039" y1="-260.587" x2="-77.046" y2="-260.458" gradientTransform="matrix(13.542, 0, 0, -8.586, 1069.322, -2226.647)" xlink:href="#o"/></defs><title>file_type_poedit</title><path d="M5.494,4.735a1.447,1.447,0,0,0-1.124,1.7l3.1,18.827a1.449,1.449,0,0,0,1.7,1.125l16.469-2.856a1.445,1.445,0,0,0,1.123-1.7L23.007,3.309a1.446,1.446,0,0,0-1.7-1.122ZM25.684,21.007a1.449,1.449,0,0,1-1.123,1.7L9.851,25.2c-.779.16-1.546.439-1.313-.338L5.848,7.26a1.056,1.056,0,0,1,.732-1.7L20.63,3.37a1.447,1.447,0,0,1,1.7,1.123Z"/><path d="M5.494,4.735a1.447,1.447,0,0,0-1.124,1.7l3.1,18.827a1.449,1.449,0,0,0,1.7,1.125l16.469-2.856a1.445,1.445,0,0,0,1.123-1.7L23.007,3.309a1.446,1.446,0,0,0-1.7-1.122ZM25.684,21.007a1.449,1.449,0,0,1-1.123,1.7L9.851,25.2c-.779.16-1.546.439-1.313-.338L5.848,7.26a1.056,1.056,0,0,1,.732-1.7L20.63,3.37a1.447,1.447,0,0,1,1.7,1.123Z" style="fill:url(#a)"/><path d="M5.664,4.789a1.421,1.421,0,0,0-1.1,1.672L7.59,24.933a1.419,1.419,0,0,0,1.671,1.1l16.166-2.793a1.42,1.42,0,0,0,1.1-1.671L22.841,3.406a1.417,1.417,0,0,0-1.67-1.1Z" style="fill:url(#b)"/><path d="M8.976,26.123a1.478,1.478,0,0,1-1.443-1.179L4.5,6.47A1.479,1.479,0,0,1,5.653,4.731h0L21.161,2.247A1.475,1.475,0,0,1,22.9,3.394l3.69,18.165a1.476,1.476,0,0,1-1.148,1.74L9.271,26.093A1.469,1.469,0,0,1,8.976,26.123ZM5.675,4.846a1.362,1.362,0,0,0-1.056,1.6L7.648,24.923a1.359,1.359,0,0,0,1.6,1.055l16.168-2.794a1.359,1.359,0,0,0,1.056-1.6L22.783,3.418a1.358,1.358,0,0,0-1.6-1.056Z" style="fill:#6b5735"/><path d="M6.873,5.74A1.242,1.242,0,0,0,5.906,7.2L8.544,23.3a1.241,1.241,0,0,0,1.463.961l14.157-2.448a1.241,1.241,0,0,0,.967-1.458L21.914,4.52a1.239,1.239,0,0,0-1.462-.96Z" style="fill:url(#c)"/><path d="M9.757,24.348a1.3,1.3,0,0,1-1.27-1.033L5.849,7.207A1.3,1.3,0,0,1,6.861,5.683h0L20.443,3.5a1.3,1.3,0,0,1,1.529,1.006l3.216,15.837a1.3,1.3,0,0,1-1.012,1.527L10.016,24.321A1.306,1.306,0,0,1,9.757,24.348ZM6.883,5.8a1.18,1.18,0,0,0-.92,1.389L8.6,23.294a1.181,1.181,0,0,0,1.393.913l14.159-2.449a1.18,1.18,0,0,0,.919-1.389L21.857,4.532a1.181,1.181,0,0,0-1.393-.914Z" style="fill:#fff"/><path d="M21.721,4.829a1.19,1.19,0,0,0-1.407-.957L7.219,6.081a1.247,1.247,0,0,0-.935,1.461L6.47,8.731l15.5-2.62Z" style="fill:#6aa7ce"/><path d="M8.389,6.694a.706.706,0,1,1-.92-.391A.707.707,0,0,1,8.389,6.694Z" style="fill:url(#d)"/><path d="M7.736,7.681A.722.722,0,1,1,8.4,6.689h0a.723.723,0,0,1-.4.94A.714.714,0,0,1,7.736,7.681Zm0-1.415a.691.691,0,0,0-.64.951.691.691,0,0,0,.9.384.694.694,0,0,0,.383-.9h0a.693.693,0,0,0-.641-.433Z" style="fill:#fff"/><path d="M11.243,6.223a.707.707,0,1,1-.923-.391A.71.71,0,0,1,11.243,6.223Z" style="fill:url(#e)"/><path d="M10.589,7.21a.722.722,0,1,1,.668-.992h0a.725.725,0,0,1-.4.94A.714.714,0,0,1,10.589,7.21Zm0-1.413a.692.692,0,1,0,.644.433h0A.7.7,0,0,0,10.585,5.8Z" style="fill:#fff"/><path d="M14.144,5.744a.707.707,0,1,1-.922-.39A.709.709,0,0,1,14.144,5.744Z" style="fill:url(#f)"/><path d="M13.49,6.731a.722.722,0,0,1-.665-1,.713.713,0,0,1,.392-.386.724.724,0,0,1,.941.4h0a.721.721,0,0,1-.668.992Zm0-1.414a.7.7,0,0,0-.26.05.693.693,0,0,0-.381.9A.692.692,0,1,0,14.13,5.75h0A.693.693,0,0,0,13.487,5.317Z" style="fill:#fff"/><path d="M17.068,5.262a.707.707,0,1,1-.922-.391A.706.706,0,0,1,17.068,5.262Z" style="fill:url(#g)"/><path d="M16.414,6.248a.721.721,0,1,1,.668-.991.722.722,0,0,1-.668.991Zm0-1.414a.692.692,0,1,0,.258,1.334.691.691,0,0,0,.383-.9h0a.693.693,0,0,0-.642-.434Z" style="fill:#fff"/><path d="M19.868,4.8a.707.707,0,1,1-.921-.39A.71.71,0,0,1,19.868,4.8Z" style="fill:url(#h)"/><path d="M19.214,5.787a.723.723,0,0,1-.67-.452.721.721,0,0,1,.4-.938.724.724,0,0,1,.94.4h0a.727.727,0,0,1-.4.94A.718.718,0,0,1,19.214,5.787Zm0-1.414a.692.692,0,1,0,.26,1.335.7.7,0,0,0,.383-.9h0A.7.7,0,0,0,19.211,4.373Z" style="fill:#fff"/><path d="M12.587,3.064l.574,3a.11.11,0,0,0,.135.067l.545-.122a.108.108,0,0,0,.093-.118l-.567-2.957Z" style="fill:url(#i)"/><path d="M13.266,6.153a.118.118,0,0,1-.119-.082l-.577-3.02.809-.13,0,.014.567,2.957a.1.1,0,0,1-.014.074.143.143,0,0,1-.091.062L13.3,6.15A.154.154,0,0,1,13.266,6.153ZM12.6,3.076l.571,2.99a.1.1,0,0,0,.118.056L13.838,6a.115.115,0,0,0,.072-.049.068.068,0,0,0,.01-.052l-.565-2.944Z" style="fill:#444b4c"/><path d="M15.462,2.585l.575,3a.109.109,0,0,0,.135.067l.546-.122a.108.108,0,0,0,.093-.118l-.568-2.958Z" style="fill:url(#j)"/><path d="M16.141,5.675a.116.116,0,0,1-.119-.082l-.577-3.02.809-.13,0,.014.568,2.958a.1.1,0,0,1-.014.074.142.142,0,0,1-.09.061l-.546.122A.156.156,0,0,1,16.141,5.675ZM15.48,2.6l.572,2.99a.1.1,0,0,0,.117.056l.546-.122a.114.114,0,0,0,.072-.048.068.068,0,0,0,.01-.052l-.565-2.944Z" style="fill:#444b4c"/><path d="M18.3,2.142l.574,3a.108.108,0,0,0,.134.067l.546-.122a.109.109,0,0,0,.094-.118l-.568-2.957Z" style="fill:url(#k)"/><path d="M18.98,5.232a.116.116,0,0,1-.119-.082l-.577-3.02L19.093,2l0,.014.568,2.957a.124.124,0,0,1-.106.135l-.546.122A.154.154,0,0,1,18.98,5.232Zm-.661-3.077.571,2.99a.094.094,0,0,0,.117.056l.546-.122a.1.1,0,0,0,.083-.1L19.07,2.033Z" style="fill:#444b4c"/><path d="M9.519,3.56,10.2,6.54a.111.111,0,0,0,.137.069l.543-.121a.111.111,0,0,0,.094-.122l-.68-2.931Z" style="fill:url(#l)"/><path d="M10.3,6.628a.12.12,0,0,1-.121-.084l-.681-3,.8-.129.683,2.944a.126.126,0,0,1-.105.14l-.543.121A.152.152,0,0,1,10.3,6.628ZM9.537,3.572l.674,2.965a.1.1,0,0,0,.12.058l.543-.121a.1.1,0,0,0,.083-.105l-.676-2.917Z" style="fill:#444b4c"/><path d="M6.686,4.019l.675,3.164a.111.111,0,0,0,.137.063l.544-.123a.105.105,0,0,0,.093-.114L7.5,3.886Z" style="fill:url(#m)"/><path d="M7.466,7.264a.116.116,0,0,1-.119-.077L6.668,4.007l.015,0,.829-.135L8.15,7.006a.091.091,0,0,1-.013.07.145.145,0,0,1-.092.061L7.5,7.26A.177.177,0,0,1,7.466,7.264ZM6.7,4.031,7.376,7.18a.1.1,0,0,0,.12.052l.544-.123a.117.117,0,0,0,.073-.048.063.063,0,0,0,.009-.048L7.49,3.9Z" style="fill:#444b4c"/><path d="M15.791,16.409a2.1,2.1,0,0,0-1.7-.215,2.308,2.308,0,0,0-1.571.882,2.178,2.178,0,0,0-.226,1.452L10.97,18.8l-.229-1.13a.57.57,0,0,1,0-.333.271.271,0,0,1,.2-.148l.2-.055c.075-.021.106-.067.091-.136a.087.087,0,0,0-.136-.089c-.013,0-.047.011-.105.027-.227.056-.448.107-.661.15s-.454.087-.692.125c-.057.006-.091.013-.1.015a.173.173,0,0,0-.091.037.15.15,0,0,0,0,.1c.014.07.062.1.145.088l.2-.027a.279.279,0,0,1,.246.06.576.576,0,0,1,.132.305l.615,3.033a.583.583,0,0,1,0,.331.272.272,0,0,1-.2.15l-.2.053a.053.053,0,0,1-.018.006.1.1,0,0,0-.082.126.092.092,0,0,0,.139.1l.1-.02c.235-.062.466-.115.689-.16s.435-.084.667-.119l.106-.017c.05-.011.079-.022.091-.039a.159.159,0,0,0,0-.1.092.092,0,0,0-.116-.084h-.018l-.2.028a.278.278,0,0,1-.246-.057.567.567,0,0,1-.133-.306L11.1,19.448l1.316-.267a2.407,2.407,0,0,0,.769,1.377,2.047,2.047,0,0,0,1.667.223,2.37,2.37,0,0,0,1.6-.89,2.441,2.441,0,0,0,.185-1.841A2.6,2.6,0,0,0,15.791,16.409Zm.169,3.409a1.364,1.364,0,0,1-1.056.715,1.37,1.37,0,0,1-1.254-.247,2.761,2.761,0,0,1-.676-1.507,2.727,2.727,0,0,1,.043-1.636,1.362,1.362,0,0,1,1.052-.72,1.365,1.365,0,0,1,1.254.246A2.769,2.769,0,0,1,16,18.177,2.736,2.736,0,0,1,15.96,19.819Z" style="fill:#1b6a9e"/><path d="M10.365,21.615a.106.106,0,0,1-.052-.011.169.169,0,0,1-.056-.115.127.127,0,0,1,.1-.159l.011,0,.007,0,.2-.053a.244.244,0,0,0,.179-.133.562.562,0,0,0,0-.314l-.615-3.033a.556.556,0,0,0-.123-.29.251.251,0,0,0-.222-.052l-.2.027a.138.138,0,0,1-.178-.111.163.163,0,0,1,.006-.122c.009-.013.03-.031.109-.048.011,0,.046-.009.1-.015.235-.038.467-.08.689-.125.2-.04.417-.089.66-.149l.006,0a.932.932,0,0,1,.1-.026c.076-.016.1-.007.118,0a.173.173,0,0,1,.05.11.136.136,0,0,1-.112.17l-.2.055a.244.244,0,0,0-.176.132.55.55,0,0,0,0,.316l.223,1.1,1.271-.258a2.193,2.193,0,0,1,.234-1.444,2.345,2.345,0,0,1,1.59-.9,2.138,2.138,0,0,1,1.721.22h0a2.64,2.64,0,0,1,.856,1.66,2.479,2.479,0,0,1-.189,1.862,2.408,2.408,0,0,1-1.615.9,2.082,2.082,0,0,1-1.691-.228,2.415,2.415,0,0,1-.773-1.367l-1.261.256.249,1.224a.549.549,0,0,0,.124.29.252.252,0,0,0,.221.05l.225-.028a.119.119,0,0,1,.145.107.175.175,0,0,1,0,.125.171.171,0,0,1-.109.052l-.092.015-.016,0c-.216.033-.44.073-.666.119s-.459.1-.688.159l-.088.019-.009,0A.323.323,0,0,1,10.365,21.615Zm.031-.237a.083.083,0,0,1-.024.008.068.068,0,0,0-.058.091c.012.062.026.075.029.077a.147.147,0,0,0,.075,0l.01,0,.086-.018c.229-.06.461-.114.69-.16s.452-.086.669-.119l.016,0,.089-.014c.058-.012.07-.023.073-.027h0s.009-.016-.006-.081-.034-.069-.084-.061h-.022l-.2.028a.306.306,0,0,1-.27-.065.59.59,0,0,1-.141-.321l-.261-1.281,1.372-.279.007.027a2.385,2.385,0,0,0,.759,1.361,2.029,2.029,0,0,0,1.644.218,2.354,2.354,0,0,0,1.577-.878,2.426,2.426,0,0,0,.18-1.819,2.587,2.587,0,0,0-.833-1.624h0a2.082,2.082,0,0,0-1.675-.21,2.291,2.291,0,0,0-1.552.869,2.159,2.159,0,0,0-.222,1.433l0,.027-1.381.28-.235-1.159a.594.594,0,0,1,0-.351.3.3,0,0,1,.215-.165l.2-.054c.077-.022.078-.066.07-.1-.011-.059-.024-.072-.025-.074h-.015a.308.308,0,0,0-.061.008c-.013,0-.05.012-.1.025l-.007,0c-.245.061-.461.11-.663.15-.223.045-.456.087-.693.125-.057.006-.09.012-.1.014a.173.173,0,0,0-.073.025s-.008.014,0,.077c.008.038.027.076.112.064l.2-.027a.309.309,0,0,1,.27.068.6.6,0,0,1,.14.32l.615,3.033a.6.6,0,0,1,0,.348.3.3,0,0,1-.218.166Zm4.083-.77a1.218,1.218,0,0,1-.848-.3,2.8,2.8,0,0,1-.685-1.523,2.762,2.762,0,0,1,.045-1.654,1.394,1.394,0,0,1,1.073-.737,1.4,1.4,0,0,1,1.279.253,2.806,2.806,0,0,1,.683,1.523,2.774,2.774,0,0,1-.038,1.66h0a1.4,1.4,0,0,1-1.076.732A2.188,2.188,0,0,1,14.479,20.609Zm.017-4.2a2.117,2.117,0,0,0-.421.046,1.339,1.339,0,0,0-1.031.7A2.709,2.709,0,0,0,13,18.775a2.748,2.748,0,0,0,.667,1.491,1.349,1.349,0,0,0,1.229.24,1.342,1.342,0,0,0,1.035-.7h0a2.722,2.722,0,0,0,.034-1.624,2.755,2.755,0,0,0-.665-1.491A1.158,1.158,0,0,0,14.5,16.407Z" style="fill:#1b6a9e"/><path d="M20.258,13.528l2.769,4.142.418-.085.058.29-1.671.339-.06-.289.907-.184-.816-1.224-2.3.467L19.3,18.426l.85-.171.058.289-1.64.333-.059-.29.425-.086.889-4.584-1.3.265-.06-.29Zm-.1.4L19.6,16.674l2.08-.422Zm-1.429-1.967.741-.151,1.5,1.237-.34.068Z" style="fill:#e24c49"/><path d="M18.544,18.912l-.071-.347.434-.088.877-4.521-1.29.263-.072-.347,1.848-.375.011.016,2.758,4.125.427-.087.07.348-1.728.351-.072-.346.893-.181-.781-1.171-2.263.459-.251,1.38.837-.168.07.346Zm0-.3.047.232,1.583-.322-.047-.232-.864.174.274-1.5.019,0,2.321-.471.851,1.276-.92.187.048.232,1.614-.327-.047-.233-.408.083L23,17.686,20.245,13.56l-1.752.356.048.232,1.318-.268-.9,4.646Zm1.025-1.9.572-2.854,1.594,2.414Zm.6-2.7-.527,2.628,1.995-.4Zm.455-.857-.01-.006-1.968-1.193.828-.168.011.009,1.551,1.276ZM18.8,11.979l1.831,1.11.269-.054-1.445-1.189Z" style="fill:#db3c72"/><path d="M12.763,10.841l.273-.519.769.32c-.034.087-.118.135-.306.174l-.978.2.133.659c.942.019,1.3.3,1.356.554a.341.341,0,0,1-.282.4.449.449,0,0,1-.3-.056,1.866,1.866,0,0,0-.751-.8l.51,2.507a.833.833,0,0,1-.558.3L12.5,14.6l-.452-2.223a5.171,5.171,0,0,1-1,1.558l-.1-.061a6.358,6.358,0,0,0,.65-2.674l-1.069.217-.059-.112,1.307-.265-.218-1.07c-.4.156-.84.307-1.25.433l-.043-.073a12.169,12.169,0,0,0,2.267-1.543l.953.4a.3.3,0,0,1-.185.092.86.86,0,0,1-.244.013c-.223.12-.5.251-.8.386l.246,1.212Zm3.152-1.877.913.34a.56.56,0,0,1-.274.192l.754,3.708c0,.011-.161.212-.713.324l-.116-.571-1.319.268.088.433c0,.012-.034.266-.69.4L13.631,9.5l.833.128,1.215-.248Zm-.779,4.142,1.319-.268-.682-3.357-1.319.268Z" style="fill:#6f577d"/><path d="M17.942,25.507l6.683-14.265,1.519.864L19.461,26.371c-.791,1.02-.627.465-.4-.229-.147.262-.517.615-.356-.2-.959,1.657-.416.065-.309-.177-.663,1.006-.473-.008-.451-.256Z" style="fill:url(#n)"/><path d="M18.917,26.991a.1.1,0,0,1-.045-.012c-.086-.046-.075-.183.036-.552a.213.213,0,0,1-.2.072c-.075-.027-.108-.116-.1-.279-.322.529-.4.549-.469.515-.1-.049-.021-.354.064-.614-.106.116-.186.155-.254.13-.135-.05-.113-.322-.073-.647,0-.04.009-.075.012-.1l.005-.02L24.6,11.16l1.621.922-.023.049L19.511,26.4l0,.005C19.132,26.891,18.994,26.991,18.917,26.991Zm.09-.878.107.046a3.424,3.424,0,0,0-.182.707,2.222,2.222,0,0,0,.48-.527L26.068,12.13l-1.417-.806L18,25.523c0,.026-.007.058-.011.1-.039.321-.04.5,0,.524,0,0,.084,0,.358-.411l.1.056a4.1,4.1,0,0,0-.245.81,4.115,4.115,0,0,0,.45-.686l.108.04c-.077.389-.015.437-.014.437C18.771,26.4,18.907,26.29,19.006,26.113Z" style="fill:#cc772f"/><line x1="18.302" y1="26.217" x2="25.08" y2="11.399" style="fill:#f3cb83"/><rect x="13.543" y="18.75" width="16.295" height="0.117" transform="translate(-4.432 30.717) rotate(-65.436)" style="fill:#f3b262"/><rect x="13.689" y="18.934" width="16.296" height="0.029" transform="translate(-4.473 30.936) rotate(-65.445)" style="fill:#f3cb83"/><rect x="13.644" y="18.938" width="16.295" height="0.029" transform="translate(-4.502 30.897) rotate(-65.445)" style="fill:#f3cb83"/><rect x="13.599" y="18.942" width="16.296" height="0.029" transform="translate(-4.533 30.858) rotate(-65.445)" style="fill:#f3cb83"/><rect x="13.553" y="18.946" width="16.295" height="0.029" transform="translate(-4.563 30.819) rotate(-65.445)" style="fill:#f3cb83"/><path d="M25.744,8.766l.015-.035h0l-.015.035Zm1.535.831.023.014-.014.034-.025-.014ZM26.166,12.12h0l-.023-.014h0Z" style="fill:#343833"/><path d="M25.758,8.732l.217-.482a.875.875,0,0,1,1.519.866l-.216.481Z" style="fill:#e24c49"/><path d="M27.305,9.68l-1.622-.923.239-.53a.771.771,0,0,1,.653-.439,1.048,1.048,0,0,1,.934.4.93.93,0,0,1,.038.955Zm-1.472-.972,1.418.807.19-.422a.8.8,0,0,0-.027-.839.926.926,0,0,0-.828-.35.657.657,0,0,0-.558.371Z" style="fill:#aa342e"/><path d="M27.3,9.611l-.014.034-.025-.014.016-.034Zm-1.557-.845h0l.015-.035h0Z" style="fill:#343833"/><path d="M27.013,10.052c.061-.139.047-.1.2-.448l.024.014c-.062.138-.047.1-.2.448l-.022-.014Z" style="fill:#f6ca2a"/><path d="M26.989,10.038l.2-.447.023.014-.2.448-.024-.014Z" style="fill:#f4c92a"/><path d="M26.965,10.025c.063-.138.047-.1.2-.447l.025.013c-.062.138-.047.1-.2.447l-.025-.013Z" style="fill:#f4c92a"/><path d="M26.942,10.012c.061-.139.047-.105.2-.449l.023.014-.2.447-.023-.013Z" style="fill:#f2c82b"/><path d="M26.918,10c.063-.137.047-.1.2-.445l.025.012c-.063.137-.047.1-.2.449L26.918,10Z" style="fill:#f0c52d"/><path d="M26.894,9.985c.062-.139.047-.105.2-.448l.023.014c-.062.137-.047.1-.2.445l-.023-.012Z" style="fill:#ecc23a"/><path d="M26.87,9.97l.2-.447.024.014c-.063.136-.048.1-.2.448L26.87,9.97Z" style="fill:#ecc344"/><path d="M26.846,9.957c.063-.138.048-.1.2-.447l.024.014c-.063.137-.047.1-.2.447l-.024-.014Z" style="fill:#ebc24f"/><path d="M26.823,9.944l.2-.448.023.014c-.062.138-.046.1-.2.447l-.023-.013Z" style="fill:#e9c158"/><path d="M26.8,9.929l.2-.447.024.014c-.063.137-.046.1-.2.448L26.8,9.929Z" style="fill:#e4be5c"/><path d="M26.775,9.915a2.844,2.844,0,0,1,.2-.447L27,9.482c-.063.139-.047.1-.2.447l-.023-.014Z" style="fill:#e4bf61"/><path d="M26.753,9.9c.061-.138.046-.1.2-.448l.023.014c-.062.139-.046.1-.2.447L26.753,9.9Z" style="fill:#e1bb64"/><path d="M26.727,9.889l.2-.447.023.012c-.063.139-.047.1-.2.448l-.025-.014Z" style="fill:#ddb967"/><path d="M26.7,9.876c.062-.137.046-.1.2-.447l.047.026a2.655,2.655,0,0,0-.2.448L26.7,9.876Z" style="fill:#dab869"/><path d="M26.68,9.862l.2-.447.048.027a2.637,2.637,0,0,0-.2.447l-.047-.027Z" style="fill:#d7b66b"/><path d="M26.657,9.848a2.575,2.575,0,0,1,.2-.447l.047.027a2.651,2.651,0,0,0-.2.447l-.048-.027Z" style="fill:#d7b66e"/><path d="M26.634,9.835a2.481,2.481,0,0,1,.2-.447l.047.027a2.572,2.572,0,0,0-.2.447l-.047-.027Z" style="fill:#d4b470"/><path d="M26.609,9.822a2.57,2.57,0,0,1,.2-.447l.049.026a2.641,2.641,0,0,0-.2.447l-.047-.027Z" style="fill:#d2b270"/><path d="M26.585,9.808c.063-.139.048-.1.2-.448l.048.027a2.455,2.455,0,0,0-.2.447l-.048-.027Z" style="fill:#d0b373"/><path d="M26.562,9.795a2.692,2.692,0,0,1,.2-.447l.046.028a2.756,2.756,0,0,0-.2.447l-.048-.027Z" style="fill:#d1b374"/><path d="M26.538,9.781a2.735,2.735,0,0,1,.2-.447l.047.026c-.062.138-.046.1-.2.448l-.047-.027Z" style="fill:#cdb175"/><path d="M26.514,9.768a2.707,2.707,0,0,1,.2-.447l.049.027c-.063.138-.047.1-.2.447l-.048-.027Z" style="fill:#ccae77"/><path d="M26.489,9.753a2.857,2.857,0,0,1,.2-.446l.047.027c-.062.137-.047.1-.2.447l-.049-.028Z" style="fill:#c8ad78"/><path d="M26.467,9.741a1.309,1.309,0,0,1,.2-.447l.047.027c-.061.137-.046.1-.2.447l-.046-.027Z" style="fill:#c8ac7a"/><path d="M26.444,9.725a1.291,1.291,0,0,1,.2-.446l.047.027c-.063.138-.046.1-.2.446a.491.491,0,0,1-.045-.028Z" style="fill:#c7ab7b"/><path d="M25.542,9.213c.061-.138.046-.1.2-.447l.024.013c-.063.138-.047.1-.2.448l-.023-.014Z" style="fill:#fccd29"/><path d="M25.565,9.227c.062-.139.047-.1.2-.448l.024.013-.2.448-.024-.014Z" style="fill:#f6ca2a"/><path d="M25.589,9.241l.2-.448.022.015c-.062.138-.045.1-.2.446l-.024-.013Z" style="fill:#f6ca2a"/><path d="M25.613,9.254c.061-.138.046-.1.2-.446l.024.013c-.062.139-.046.1-.2.447l-.023-.014Z" style="fill:#f4c92a"/><path d="M25.637,9.267c.061-.137.046-.1.2-.447l.024.014c-.063.139-.047.1-.2.447l-.023-.014Z" style="fill:#f4c92a"/><path d="M25.66,9.281l.2-.447.025.014c-.063.138-.048.1-.2.446l-.023-.013Z" style="fill:#f2c82b"/><path d="M25.683,9.294c.062-.137.047-.1.2-.446l.023.013c-.063.138-.046.1-.2.446l-.024-.013Z" style="fill:#f2c62c"/><path d="M25.707,9.307l.2-.446.023.014-.2.447-.024-.015Z" style="fill:#f0c52d"/><path d="M25.731,9.322a2.484,2.484,0,0,1,.2-.447l.023.014c-.062.138-.047.1-.2.446l-.023-.012Z" style="fill:#f0c631"/><path d="M25.731,9.322c.062-.139.047-.1.2-.447l.047.027-.2.447-.048-.027Z" style="fill:#ecc23a"/><path d="M25.755,9.334l.2-.446L26,8.915c-.063.137-.047.1-.2.446l-.047-.027Z" style="fill:#ecc344"/><path d="M25.779,9.348c.062-.138.047-.1.2-.447l.047.027c-.062.138-.046.1-.2.448l-.047-.027Z" style="fill:#ebc24a"/><path d="M25.8,9.361c.063-.136.047-.1.2-.446l.048.027c-.088.124-.072.09-.2.447L25.8,9.361Z" style="fill:#ebc24f"/><path d="M25.826,9.376l.2-.448.025.014c-.063.137-.048.1-.2.447l-.024-.013Z" style="fill:#e9c158"/><path d="M25.85,9.389l.2-.447.022.014c-.061.137-.046.1-.2.445l-.023-.012Z" style="fill:#e9c15b"/><path d="M25.873,9.4c.063-.137.047-.1.2-.445l.024.013-.2.446L25.873,9.4Z" style="fill:#e4be5c"/><path d="M25.9,9.415c.063-.137.046-.1.2-.446l.024.015c-.063.137-.046.1-.2.447L25.9,9.415Z" style="fill:#e4bf61"/><path d="M25.92,9.431l.2-.447L26.147,9c-.062.139-.046.1-.2.446l-.023-.012Z" style="fill:#e1bb63"/><path d="M25.944,9.442c.062-.137.047-.1.2-.446l.024.014-.2.447-.024-.014Z" style="fill:#e1bb64"/><path d="M25.968,9.457a1.315,1.315,0,0,1,.2-.447l.024.012-.2.448-.025-.014Z" style="fill:#dcba67"/><path d="M25.968,9.457c.062-.137.046-.1.2-.447a.387.387,0,0,1,.047.027c-.063.137-.046.1-.2.447l-.048-.027Z" style="fill:#ddb967"/><path d="M25.993,9.47c.061-.137.047-.1.2-.448a.542.542,0,0,0,.047.028c-.063.137-.047.1-.2.447l-.047-.027Z" style="fill:#dab869"/><path d="M26.016,9.484c.062-.138.046-.1.2-.447l.047.027-.2.447-.047-.027Z" style="fill:#dab86a"/><path d="M26.04,9.5c.063-.138.046-.1.2-.447l.047.027c-.061.139-.046.1-.2.447L26.04,9.5Z" style="fill:#d7b66b"/><path d="M26.063,9.51l.2-.447.047.027-.2.447-.048-.027Z" style="fill:#d7b66e"/><path d="M26.087,9.524c.063-.138.047-.1.2-.447l.049.028c-.063.137-.048.1-.2.446l-.047-.027Z" style="fill:#d4b46f"/><path d="M26.111,9.538c.062-.139.046-.1.2-.447l.048.027c-.063.137-.048.1-.2.445a.463.463,0,0,1-.047-.026Z" style="fill:#d4b470"/><path d="M26.134,9.551c.062-.138.047-.1.2-.446l.046.026a2.543,2.543,0,0,0-.2.447.487.487,0,0,0-.049-.027Z" style="fill:#d2b270"/><path d="M26.158,9.563c.062-.137.047-.1.2-.445l.047.027a1.3,1.3,0,0,0-.2.448l-.048-.029Z" style="fill:#d2b372"/><path d="M26.183,9.578a2.549,2.549,0,0,1,.2-.447l.024.014c-.062.139-.046.1-.2.448l-.023-.015Z" style="fill:#d0b373"/><path d="M26.206,9.592a1.307,1.307,0,0,1,.2-.448l.025.013-.2.448-.024-.013Z" style="fill:#d1b374"/><path d="M26.206,9.592c.061-.139.047-.1.2-.448a.365.365,0,0,1,.047.028c-.063.137-.046.1-.2.446l-.046-.026Z" style="fill:#cdb175"/><path d="M26.23,9.605c.062-.137.046-.1.2-.448a.378.378,0,0,0,.046.027c-.061.138-.047.1-.2.448l-.048-.027Z" style="fill:#cdb175"/><path d="M26.253,9.618l.2-.446L26.5,9.2l-.2.448-.048-.028Z" style="fill:#ccb078"/><path d="M26.278,9.633c.062-.138.045-.1.2-.448l.048.027-.2.447c-.025-.012,0,0-.047-.026Z" style="fill:#ccae77"/><path d="M26.3,9.646c.063-.137.047-.1.2-.448l.047.027c-.063.139-.047.1-.2.448-.024-.014,0,0-.049-.027Z" style="fill:#c8ad77"/><path d="M26.325,9.659c.061-.137.046-.1.2-.447l.048.028c-.064.137-.048.1-.2.447l-.045-.028Z" style="fill:#c8ad78"/><path d="M26.35,9.673c.061-.138.045-.1.2-.448l.047.028c-.061.137-.047.1-.2.446a.46.46,0,0,1-.046-.027Z" style="fill:#c7ab78"/><path d="M26.371,9.687c.062-.137.048-.1.2-.447l.046.027c-.061.138-.046.1-.2.448l-.048-.027Z" style="fill:#c7ab7a"/><path d="M26.4,9.7c.063-.138.047-.1.2-.446l.048.027c-.063.137-.047.1-.2.446A.391.391,0,0,1,26.4,9.7Z" style="fill:#c4aa7b"/><polygon points="25.734 9.389 25.538 9.278 25.787 8.725 25.985 8.836 25.734 9.389" style="fill:#343833"/><polygon points="26.965 10.025 27.168 9.577 27.263 9.631 27.06 10.078 26.965 10.025" style="fill:#fccd29"/><polygon points="27.087 10.16 26.889 10.049 27.141 9.495 27.338 9.606 27.087 10.16" style="fill:#343833"/><polygon points="26.897 10.053 26.7 9.939 26.951 9.385 27.148 9.499 26.897 10.053" style="fill:#343833"/><polygon points="26.731 9.958 26.534 9.846 26.784 9.293 26.982 9.404 26.731 9.958" style="fill:#343833"/><polygon points="26.395 9.699 26.598 9.253 26.716 9.321 26.489 9.753 26.395 9.699" style="fill:#c4aa7b"/><path d="M26.512,9.834l-.192-.111.251-.553.222.127Zm.112-.5-.112.248.126-.24Z" style="fill:#343833"/><polygon points="26.327 9.729 26.131 9.617 26.381 9.062 26.579 9.174 26.327 9.729" style="fill:#343833"/><polygon points="26.138 9.62 25.941 9.508 26.192 8.954 26.388 9.066 26.138 9.62" style="fill:#343833"/><polygon points="25.947 9.513 25.751 9.4 26.001 8.845 26.198 8.96 25.947 9.513" style="fill:#343833"/><polygon points="24.624 11.242 25.744 8.766 27.263 9.631 26.143 12.106 24.624 11.242" style="fill:url(#o)"/><path d="M26.17,12.188l-1.621-.922,1.168-2.582,1.621.923ZM24.7,11.217l1.417.806,1.071-2.368-1.417-.806Z" style="fill:#8f989b"/><polygon points="24.842 10.761 24.903 10.624 26.423 11.488 26.359 11.625 24.842 10.761" style="fill:url(#p)"/><path d="M26.385,11.708l-1.619-.923.11-.243,1.622.922Zm-1.469-.971,1.416.807.015-.031-1.417-.806Z" style="fill:#343833"/><polygon points="25.043 10.314 25.091 10.21 26.609 11.075 26.563 11.178 25.043 10.314" style="fill:url(#q)"/><polygon points="26.59 11.261 24.968 10.339 25.064 10.128 26.684 11.051 26.59 11.261" style="fill:#737a7f"/><polygon points="25.246 9.867 25.308 9.729 26.827 10.594 26.765 10.731 25.246 9.867" style="fill:url(#r)"/><path d="M26.791,10.814l-1.621-.922.11-.245,1.621.924Zm-1.47-.971,1.417.807.014-.031-1.417-.807Z" style="fill:#737a7f"/><path d="M17.136,28.81l-.293,1.173.656-.924c.038-.282-.215-.382-.364-.249Z" style="fill:#8a9196"/><path d="M17.136,28.81l-.293,1.173.656-.924c.038-.282-.215-.382-.364-.249Z"/><path d="M16.867,30l-.052-.024.295-1.181.007-.006a.245.245,0,0,1,.28-.038.29.29,0,0,1,.132.312l-.005.013Zm.295-1.173-.253,1.014.562-.792a.232.232,0,0,0-.1-.246A.186.186,0,0,0,17.162,28.827Z" style="fill:#343833"/><path d="M19.461,26.371l-1.947,2.654a.229.229,0,0,0-.378-.214l.806-3.3c-.022.248-.212,1.262.451.256-.108.242-.65,1.835.309.177-.161.816.209.464.356.2-.224.694-.387,1.249.4.229Z" style="fill:#c6bdaf"/><path d="M19.461,26.371l-1.947,2.654a.229.229,0,0,0-.378-.214l.806-3.3c-.022.248-.212,1.262.451.256-.108.242-.65,1.835.309.177-.161.816.209.464.356.2-.224.694-.387,1.249.4.229Z" style="fill:#e5bd87"/><path d="M17.475,29.127l.01-.105a.218.218,0,0,0-.129-.231.168.168,0,0,0-.2.039l-.051-.026.806-3.3.057.01c0,.027-.007.064-.012.106-.019.157-.064.524.017.553.022.007.114,0,.393-.422l.051.028a2.9,2.9,0,0,0-.242.884c.014,0,.111-.062.5-.733l.054.02c-.08.406-.017.463,0,.47.059.022.2-.119.3-.289l.053.023c-.075.234-.232.72-.173.751.012.007.092.011.525-.548l.047.035Zm-.2-.411a.253.253,0,0,1,.1.021.271.271,0,0,1,.161.2l1.5-2.047c-.085.071-.127.074-.156.059-.077-.041-.042-.225.085-.633-.082.1-.181.179-.257.151s-.1-.15-.068-.378c-.4.668-.459.635-.5.615-.094-.048.039-.47.135-.737-.191.255-.276.272-.328.253-.073-.027-.09-.147-.083-.308l-.689,2.822A.22.22,0,0,1,17.279,28.717Z" style="fill:#c48e30"/><path d="M17.31,29.212l-.274.407.227-.435s.062-.006.047.028Z" style="fill:#dadde0"/><path d="M17.408,28.794l-.081.074,1.011-3.177a4.3,4.3,0,0,0,.126.613l.341-.092-1.386,2.719Z" style="fill:#e6d7c2;opacity:0.589999973773956;isolation:isolate"/><path d="M17.474,28.646l-.063.061,1-2.916a5.515,5.515,0,0,0,.028.6l.245-.047-1.218,2.434Z" style="fill:#e6d7c2"/></svg>