// ui-utils.js - UI工具函数

// 切换侧边栏页签
function switchSidebarTab(target) {
    // 更新页签状态
    $('.sidebar-tab').removeClass('active');
    $(`.sidebar-tab[data-target="${target}"]`).addClass('active');
    
    // 先隐藏所有面板
    $('.sidebar-panel').removeClass('active');
    
    // 激活并显示目标面板
    const $targetPanel = $(`.sidebar-panel[data-panel="${target}"]`);
    $targetPanel.addClass('active').css('display', 'flex');
    
    // 特殊处理：如果切换到资源管理器，确保文件树可见
    if (target === 'explorer') {
        $('#fileTree').show();
    }
    
    // 特殊处理：如果切换到搜索页签，自动聚焦搜索框
    if (target === 'search') {
        setTimeout(function() {
            $('#searchInput').focus();
        }, 100);
        
        // 如果有搜索范围，显示清除按钮
        if (window.currentSearchFolder) {
            $('#clearSearchScope').show();
        } else {
            $('#clearSearchScope').hide();
        }
    }
}

// 清除所有标签页和编辑器内容
function clearAll() {
    if (window.tabManager) {
        window.tabManager.closeAll();
    }
    
    if (window.editor) {
        window.editor.setValue('');
    }
    
    // 移除图片预览
    $('#imagePreview').remove();
    $('#editor').show();
}

// 导出函数
window.uiUtils = {
    switchSidebarTab: switchSidebarTab,
    clearAll: clearAll
}; 