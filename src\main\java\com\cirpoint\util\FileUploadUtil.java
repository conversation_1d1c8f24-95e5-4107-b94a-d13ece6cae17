package com.cirpoint.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传工具类
 */
@Slf4j
public class FileUploadUtil {

	@Setter
	private static String tempPath;
	@Setter
	private static String fileUploadPath;

	/**
	 * 上传文件结果
	 */
	@Getter
	public static class UploadResult {
		private final String originalFilename;
		private final String fileFormat;
		private final String storagePath;
		private final String absolutePath;

		public UploadResult(String originalFilename, String fileFormat, String storagePath, String absolutePath) {
			this.originalFilename = originalFilename;
			this.fileFormat = fileFormat;
			this.storagePath = storagePath;
			this.absolutePath = absolutePath;
		}

	}

	/**
	 * 处理文件分片上传
	 *
	 * @param file      上传的文件
	 * @param chunk     当前分片序号（null表示不分片上传）
	 * @param chunks    总分片数（null表示不分片上传）
	 * @param fileName  文件名
	 * @param reqIdent  请求标识
	 * @param extraData 额外参数（JSON字符串）
	 * @return 上传结果
	 */
	public static JSONObject handleFileUpload(MultipartFile file, Integer chunk, Integer chunks,
											  String fileName, String reqIdent, String extraData) {

		// 参数校验
		if (file == null || file.isEmpty() || StrUtil.isEmpty(fileName) || StrUtil.isEmpty(reqIdent)) {
			return createErrorResult("上传参数不完整");
		}

		// 创建临时目录
		String uploadDir = tempPath + File.separator + "upload" + File.separator + reqIdent;
		try {
			FileUtil.mkdir(uploadDir);

			// 处理文件上传
			if (chunk == null || chunks == null) {
				return handleSingleFileUpload(file, fileName, uploadDir, extraData);
			} else {
				return handleChunkFileUpload(file, chunk, chunks, fileName, uploadDir, extraData);
			}
		} catch (Exception e) {
			log.error("文件上传失败: {}", e.getMessage(), e);
			return createErrorResult("上传失败：" + e.getMessage());
		}
	}

	/**
	 * 处理单文件上传
	 */
	private static JSONObject handleSingleFileUpload(MultipartFile file, String fileName,
													 String uploadDir, String extraData) throws IOException {
		String finalPath = uploadDir + File.separator + fileName;
		file.transferTo(new File(finalPath));

		return createSuccessResult(true, "上传成功！", finalPath, extraData);
	}

	/**
	 * 处理分片文件上传
	 */
	private static JSONObject handleChunkFileUpload(MultipartFile file, int chunk, int chunks,
													String fileName, String uploadDir, String extraData)
			throws IOException, InterruptedException {
		// 保存分片
		String chunkPath = uploadDir + File.separator + chunk + "_" + fileName;
		file.transferTo(new File(chunkPath));

		// 如果不是最后一片，直接返回成功
		if (chunk != chunks - 1) {
			return createSuccessResult(false, "分片上传成功！", null, null);
		}

		// 合并所有分片
		String finalPath = uploadDir + File.separator + fileName;
		try (BufferedOutputStream os = new BufferedOutputStream(
				Files.newOutputStream(new File(finalPath).toPath()))) {

			// 合并分片文件
			log.info("开始合并分片文件，总分片数: {}, 文件名: {}", chunks, fileName);
			for (int i = 0; i < chunks; i++) {
				File partFile = new File(uploadDir + File.separator + i + "_" + fileName);

				// 等待分片文件上传完成
				if (!waitForChunkFile(partFile)) {
					// 记录所有分片文件状态用于调试
					StringBuilder debugInfo = new StringBuilder();
					debugInfo.append("分片文件状态检查:\n");
					for (int j = 0; j < chunks; j++) {
						File debugFile = new File(uploadDir + File.separator + j + "_" + fileName);
						debugInfo.append(String.format("分片%d: 存在=%s, 大小=%s\n",
							j, debugFile.exists(),
							debugFile.exists() ? debugFile.length() + "字节" : "N/A"));
					}
					log.error(debugInfo.toString());

					throw new FileNotFoundException("分片文件丢失：" + partFile.getAbsolutePath() +
						"。请检查网络连接和磁盘空间，或稍后重试。");
				}

				// 写入分片数据
				byte[] fileData = FileUtil.readBytes(partFile);
				os.write(fileData);
				os.flush();

				// 删除分片文件
				FileUtil.del(partFile);
			}
			log.info("分片文件合并完成: {}", fileName);

			return createSuccessResult(true, "上传成功！", finalPath, extraData);
		}
	}

	/**
	 * 等待分片文件上传完成
	 * 优化版本：增加等待时间，检查文件大小稳定性，确保文件写入完成
	 */
	private static boolean waitForChunkFile(File partFile) throws InterruptedException {
		final int WAIT_INTERVAL_MS = 100;
		final int MAX_WAIT_SECONDS = 30;
		final int MAX_RETRIES = MAX_WAIT_SECONDS * 1000 / WAIT_INTERVAL_MS;
		final int STABLE_CHECK_COUNT = 3;
		final int LOG_INTERVAL_RETRIES = 50; // 每5秒记录一次

		int retryCount = 0;
		long lastSize = -1;
		int stableCount = 0;

		while (retryCount < MAX_RETRIES) {
			if (partFile.exists()) {
				long currentSize = partFile.length();
				if (currentSize == lastSize && currentSize > 0) {
					stableCount++;
					// 文件大小连续检查保持不变，认为写入完成
					if (stableCount >= STABLE_CHECK_COUNT) {
						return true;
					}
				} else {
					stableCount = 0;
					lastSize = currentSize;
				}
			}

			TimeUnit.MILLISECONDS.sleep(WAIT_INTERVAL_MS);
			retryCount++;

			// 定期记录等待日志
			if (retryCount % LOG_INTERVAL_RETRIES == 0) {
				log.warn("等待分片文件: {}, 已等待{}秒", partFile.getName(),
					retryCount * WAIT_INTERVAL_MS / 1000.0);
			}
		}

		log.error("分片文件等待超时: {}, 文件存在: {}, 文件大小: {}",
				  partFile.getName(), partFile.exists(),
				  partFile.exists() ? partFile.length() : "N/A");
		return false;
	}

	/**
	 * 创建成功响应
	 */
	private static JSONObject createSuccessResult(boolean isAll, String msg, String filePath, String extraData) {
		JSONObject result = new JSONObject();
		result.set("success", true);
		result.set("msg", msg);
		result.set("isAll", isAll);

		if (filePath != null) {
			result.set("file", filePath);
		}

		if (isAll) {
			result.set("extraData", StrUtil.isNotBlank(extraData) ?
					JSONUtil.parseObj(extraData) : JSONUtil.createObj());
		}

		return result;
	}

	/**
	 * 创建错误响应
	 */
	private static JSONObject createErrorResult(String msg) {
		JSONObject result = new JSONObject();
		result.set("success", false);
		result.set("msg", msg);
		return result;
	}




	/**
	 * 上传文件到系统临时目录
	 *
	 * @param file 上传的文件
	 * @return 上传结果，包含文件信息
	 */
	public static UploadResult uploadToTemp(MultipartFile file) {
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
		FileUtil.mkdir(thisTempPath);
		String currTime = System.currentTimeMillis() + "";
		String fullPath = thisTempPath + currTime;
		// 保存文件
		File tempFile = new File(fullPath);
		try {
			file.transferTo(tempFile);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}

		// 获取文件信息
		String originalFilename = file.getOriginalFilename();
		String fileFormat = null;
		if (originalFilename != null) {
			fileFormat = FileNameUtil.extName(originalFilename);
		}

		return new UploadResult(originalFilename, fileFormat, currTime, fullPath);
	}

	/**
	 * 上传文件到指定目录，按年月分类存储
	 *
	 * @param file 上传的文件
	 * @return 上传结果，包含文件信息
	 */
	public static UploadResult uploadToStorage(MultipartFile file) {
		// 获取原始文件名和格式
		String originalFilename = file.getOriginalFilename();
		String fileFormat = extractFileFormat(originalFilename);

		// 创建存储目录和文件路径
		StoragePathInfo pathInfo = createStoragePathInfo();

		// 保存MultipartFile到目标位置
		try {
			file.transferTo(new File(pathInfo.getAbsolutePath()));
		} catch (IOException e) {
			throw new RuntimeException("文件保存失败: " + e.getMessage(), e);
		}

		return new UploadResult(originalFilename, fileFormat, pathInfo.getStoragePath(), pathInfo.getAbsolutePath());
	}

	/**
	 * 上传File对象到指定目录，按年月分类存储
	 *
	 * @param file File对象
	 * @return 上传结果，包含文件信息
	 */
	public static UploadResult uploadFileToStorage(File file) {
		// 参数校验
		if (file == null || !file.exists()) {
			throw new IllegalArgumentException("文件不存在或为空");
		}

		// 获取原始文件名和格式
		String originalFilename = file.getName();
		String fileFormat = extractFileFormat(originalFilename);

		// 创建存储目录和文件路径
		StoragePathInfo pathInfo = createStoragePathInfo();

		// 复制File到目标位置
		try {
			FileUtil.copy(file, new File(pathInfo.getAbsolutePath()), true);
		} catch (Exception e) {
			throw new RuntimeException("文件复制失败: " + e.getMessage(), e);
		}

		return new UploadResult(originalFilename, fileFormat, pathInfo.getStoragePath(), pathInfo.getAbsolutePath());
	}

	/**
	 * 提取文件格式（扩展名）
	 *
	 * @param filename 文件名
	 * @return 文件格式，如果无法提取则返回null
	 */
	private static String extractFileFormat(String filename) {
		if (filename != null && filename.contains(".")) {
			return filename.substring(filename.lastIndexOf(".") + 1);
		}
		return null;
	}

	/**
	 * 创建存储路径信息
	 *
	 * @return 存储路径信息对象
	 */
	private static StoragePathInfo createStoragePathInfo() {
		// 创建年月子目录
		String month = new SimpleDateFormat("yyyy-MM").format(new Date());
		String monthPath = fileUploadPath + File.separator + month;
		FileUtil.mkdir(monthPath);

		// 生成唯一文件名
		String uuid = UUID.randomUUID().toString();
		String absolutePath = monthPath + File.separator + uuid;
		String storagePath = "//" + month + "//" + uuid;

		return new StoragePathInfo(storagePath, absolutePath);
	}

	/**
	 * 存储路径信息内部类
	 */
	@Getter
	private static class StoragePathInfo {
		private final String storagePath;
		private final String absolutePath;

		public StoragePathInfo(String storagePath, String absolutePath) {
			this.storagePath = storagePath;
			this.absolutePath = absolutePath;
		}

	}

	/**
	 * 清理临时文件
	 *
	 * @param path 临时文件路径
	 */
	public static void cleanupTemp(String path) {
		if (path != null) {
			FileUtil.del(path);
		}
	}
}
