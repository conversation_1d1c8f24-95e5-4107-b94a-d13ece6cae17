package com.cirpoint.service.panorama;

import lombok.extern.slf4j.Slf4j;

import java.io.File;

/**
 * 全景图文件处理工具类
 * 提供文件查找等静态工具方法
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@Slf4j
public class PanoramaFileUtils {

    /**
     * 在指定目录中递归查找pano.xml文件
     * 
     * @param dir 搜索目录
     * @return 找到的pano.xml文件，如果未找到则返回null
     */
    public static File findPanoXmlFile(File dir) {
        if (!dir.exists() || !dir.isDirectory()) {
            return null;
        }

        File[] files = dir.listFiles();
        if (files == null) {
            return null;
        }

        for (File file : files) {
            if (file.isFile() && "pano.xml".equals(file.getName())) {
                return file;
            } else if (file.isDirectory()) {
                File found = findPanoXmlFile(file);
                if (found != null) {
                    return found;
                }
            }
        }
        return null;
    }

    /**
     * 在指定目录中递归查找指定名称的文件
     *
     * @param dir 搜索目录
     * @param fileName 文件名
     * @return 找到的文件，如果未找到则返回null
     */
    public static File findFileByName(File dir, String fileName) {
        if (!dir.exists() || !dir.isDirectory()) {
            return null;
        }

        File[] files = dir.listFiles();
        if (files == null) {
            return null;
        }

        for (File file : files) {
            if (file.isFile() && fileName.equals(file.getName())) {
                return file;
            } else if (file.isDirectory()) {
                File found = findFileByName(file, fileName);
                if (found != null) {
                    return found;
                }
            }
        }
        return null;
    }
}
