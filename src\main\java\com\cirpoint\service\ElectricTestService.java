package com.cirpoint.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.cell.CellUtil;
import com.cirpoint.util.Util;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.XSSFChart;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * 电测试服务
 */
@Slf4j
@Service
public class ElectricTestService extends ApplicationConfig {

	/**
	 * 导出测试事件到Excel
	 *
	 * @param query 查询条件
	 * @return 生成的Excel文件
	 */
	public File exportTestEvent(String query) {
		// 查询导出数据
		JSONArray sheets = Util.postTwxForObject("Thing.Fn.ElectricTest", "QueryExportData",
				JSONUtil.createObj().set("query", query)).getJSONArray("data");

		// 创建Excel文件
		String path = tempPath + File.separator + System.currentTimeMillis() + ".xlsx";

		try (ExcelWriter writer = ExcelUtil.getWriter(path)) {
			// 处理每个sheet页
			for (int s = 0; s < sheets.size(); s++) {
				JSONObject sheet = sheets.getJSONObject(s);
				processSheet(writer, sheet, s);
			}

			return new File(path);
		}
	}

	/**
	 * 处理单个sheet页
	 */
	private void processSheet(ExcelWriter writer, JSONObject sheet, int sheetIndex) {
		JSONArray data = sheet.getJSONArray("data");
		int dataSize = sheet.getInt("dataSize");
		JSONArray columns = sheet.getJSONArray("columns");
		String sheetName = sheet.getStr("sheetName");
		JSONArray charts = sheet.getJSONArray("charts");

		// 设置sheet
		writer.setSheet(sheetIndex);
		writer.renameSheet(sheetName);
		writer.clearHeaderAlias();

		// 设置列信息
		for (int i = 0; i < columns.size(); i++) {
			JSONObject column = columns.getJSONObject(i);
			writer.addHeaderAlias(column.getStr("colName"), column.getStr("colDisplayName"));
			writer.setColumnWidth(i, column.getInt("colWidth"));
		}

		if (dataSize > 0) {
			writeSheetData(writer, data, charts);
		}
	}

	/**
	 * 写入sheet数据和图表
	 */
	private void writeSheetData(ExcelWriter writer, JSONArray data, JSONArray charts) {
		// 写入数据
		writer.setOnlyAlias(true);
		Font font = writer.getWorkbook().createFont();
		font.setBold(true);
		writer.getHeadCellStyle().setFont(font);
		writer.write(data, true);
		writer.setFreezePane(1);

		// 处理图表
		if (charts != null) {
			for (int i = 0; i < charts.size(); i++) {
				JSONObject chartObj = charts.getJSONObject(i);
				createChart(writer, chartObj);
			}
		}
	}

	/**
	 * 创建图表
	 */
	private void createChart(ExcelWriter writer, JSONObject chartObj) {
		String chartType = chartObj.getStr("type");
		JSONArray anchorArr = chartObj.getJSONArray("anchor");
		JSONArray labelsAddress = chartObj.getJSONArray("labelsAddress");
		JSONArray seriesArr = chartObj.getJSONArray("series");

		XSSFSheet xssfSheet = (XSSFSheet) writer.getSheet();
		XSSFDrawing drawing = xssfSheet.createDrawingPatriarch();

		// 创建图表锚点
		ClientAnchor anchor = drawing.createAnchor(
				anchorArr.getInt(0), anchorArr.getInt(1),
				anchorArr.getInt(2), anchorArr.getInt(3),
				anchorArr.getInt(4), anchorArr.getInt(5),
				anchorArr.getInt(6), anchorArr.getInt(7));

		XSSFChart chart = drawing.createChart(anchor);

		// 设置图例
		XDDFChartLegend legend = chart.getOrAddLegend();
		legend.setPosition(LegendPosition.TOP_RIGHT);

		// 设置数据源
		XDDFDataSource<String> labelsData = XDDFDataSourcesFactory.fromStringCellRange(
				xssfSheet, new CellRangeAddress(
						labelsAddress.getInt(0), labelsAddress.getInt(1),
						labelsAddress.getInt(2), labelsAddress.getInt(3)));

		// 根据图表类型创建不同的图表
		if ("pie".equals(chartType)) {
			createPieChart(chart, xssfSheet, labelsData, seriesArr, writer);
		} else if ("line".equals(chartType)) {
			createLineChart(chart, xssfSheet, labelsData, seriesArr, writer);
		}
	}

	/**
	 * 创建饼图
	 */
	private void createPieChart(XSSFChart chart, XSSFSheet sheet,
								XDDFDataSource<String> labelsData, JSONArray seriesArr, ExcelWriter writer) {
		XDDFPieChartData pieChartData = (XDDFPieChartData) chart.createData(ChartTypes.PIE, null, null);

		for (int i = 0; i < seriesArr.size(); i++) {
			JSONObject seriesObj = seriesArr.getJSONObject(i);
			JSONArray valuesAddress = seriesObj.getJSONArray("valuesAddress");
			String seriesName = seriesObj.getStr("name");

			setCellNumberType(valuesAddress, writer);

			XDDFNumericalDataSource<Double> valuesData = XDDFDataSourcesFactory.fromNumericCellRange(
					sheet, new CellRangeAddress(
							valuesAddress.getInt(0), valuesAddress.getInt(1),
							valuesAddress.getInt(2), valuesAddress.getInt(3)));

			XDDFPieChartData.Series series = (XDDFPieChartData.Series) pieChartData.addSeries(labelsData, valuesData);
			series.setTitle(seriesName, null);
		}

		chart.plot(pieChartData);
	}

	/**
	 * 创建折线图
	 */
	private void createLineChart(XSSFChart chart, XSSFSheet sheet,
								 XDDFDataSource<String> labelsData, JSONArray seriesArr, ExcelWriter writer) {
		XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
		XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
		leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);

		XDDFLineChartData lineChartData = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);

		for (int i = 0; i < seriesArr.size(); i++) {
			JSONObject seriesObj = seriesArr.getJSONObject(i);
			JSONArray valuesAddress = seriesObj.getJSONArray("valuesAddress");
			String seriesName = seriesObj.getStr("name");

			setCellNumberType(valuesAddress, writer);

			XDDFNumericalDataSource<Double> valuesData = XDDFDataSourcesFactory.fromNumericCellRange(
					sheet, new CellRangeAddress(
							valuesAddress.getInt(0), valuesAddress.getInt(1),
							valuesAddress.getInt(2), valuesAddress.getInt(3)));

			XDDFLineChartData.Series series = (XDDFLineChartData.Series) lineChartData.addSeries(labelsData, valuesData);
			series.setTitle(seriesName, null);
		}

		chart.plot(lineChartData);
	}

	/**
	 * 设置单元格数字类型
	 */
	private void setCellNumberType(JSONArray valuesAddress, ExcelWriter writer) {
		int firstRow = valuesAddress.getInt(0);
		int lastRow = valuesAddress.getInt(1);
		int firstCol = valuesAddress.getInt(2);
		int lastCol = valuesAddress.getInt(3);

		for (int i = firstRow; i <= lastRow; i++) {
			for (int j = firstCol; j <= lastCol; j++) {
				Cell cell = writer.getCell(j, i);
				if (cell != null) {
					cell.setCellValue(Convert.toInt(CellUtil.getCellValue(cell)));
				}
			}
		}
	}

	/**
	 * 同步创建测试表格
	 *
	 * @param model    模型名称
	 * @param nodeCode 节点代码
	 * @return 成功创建的数量
	 */
	public int syncCreateTable(String model, String nodeCode) {
		JSONArray files = getTestFiles(model, nodeCode);
		String relativePath = File.separator + "electricTest" + File.separator +
				DateUtil.format(new Date(), "yyyy-MM") + File.separator;
		String uploadPath = fileUploadPath + relativePath;
		FileUtil.mkdir(uploadPath);

		int successCount = 0;
		for (int i = 0; i < files.size(); i++) {
			try {
				JSONObject file = files.getJSONObject(i);
				String fileName = file.getStr("fileName");

				// 保存测试表格
				JSONObject result = Util.postTwxForObject("Thing.Fn.ElectricTest", "SaveTestTable",
						JSONUtil.createObj().set("fileStr", file.toString()));

				if (result.getBool("success")) {
					// 处理文件移动
					Integer logId = result.getInt("logId");
					String uuid = UUID.randomUUID().toString();
					FileUtil.move(FileUtil.file(file.getStr("filePath")),
							FileUtil.file(uploadPath + uuid), true);

					// 更新文件路径
					Util.postCommandSql("update ELECTRIC_TEST_LOG set FILE_PATH='" +
							relativePath + uuid + "' where ID=" + logId);

					// 清理附件
					JSONArray attachments = file.getJSONArray("attachments");
					for (int j = 0; j < attachments.size(); j++) {
						FileUtil.del(attachments.getJSONObject(j).getStr("absolutePath"));
					}
					successCount++;
				} else {
					log.error("同步创建表格失败 - 文件: {}, 原因: {}", fileName, result.getStr("msg"));
				}
			} catch (Exception e) {
				log.error("同步创建表格失败", e);
			}
		}
		return successCount;
	}

	/**
	 * 获取测试文件列表
	 */
	private JSONArray getTestFiles(String queryModel, String queryNodeCode) {
		JSONArray files = new JSONArray();

		// 获取所有测试模型
		JSONArray allModels = Util.postTwxForObject("Thing.Fn.ElectricTest", "GetAllTestModel",
				JSONUtil.createObj()).getJSONArray("data");

		if (allModels.isEmpty()) {
			return files;
		}

		// 获取同步目录
		String syncDirectory = Convert.toStr(Util.getThingProperty("Thing.Fn.ElectricTest", "syncDirectory"));
		JSONArray fileList = Util.listFilesOrderByTime(syncDirectory);
		String month = DateUtil.format(new Date(), "yyyy-MM");

		// 处理附件列表
		JSONArray attachments = new JSONArray();

		// 处理文件
		for (int i = 0; i < fileList.size(); i++) {
			JSONObject fileObj = fileList.getJSONObject(i);
			String fileName = fileObj.getStr("fileName");
			String filePath = fileObj.getStr("filePath");
			String fileSize = fileObj.getStr("fileSize");
			String prefixFileName = fileObj.getStr("prefixFileName");
			String fileFormat = fileObj.getStr("fileFormat");

			// 解析文件名
			List<String> parts = StrUtil.split(prefixFileName, "#$");
			if (parts.size() < 6) {
				continue;
			}

			// 提取文件信息
			String status = parts.get(0);
			String type = parts.get(1);
			String model = parts.get(2);

			// 验证模型
			if (!allModels.contains(model)) {
				continue;
			}

			// 检查模型匹配
			if (!"null".equals(queryModel) && !StrUtil.equals(model, queryModel)) {
				continue;
			}

			String node = parts.get(3);
			String nodeCode = parts.get(4);

			// 获取映射节点代码
			String mappedNodeCode = Util.postTwxForObject("Thing.Fn.ElectricTest", "GetMappingNodeCode",
							JSONUtil.createObj().set("nodeCode", node))
					.getJSONArray("rows").getJSONObject(0).getStr("result");

			// 检查节点代码匹配
			if (!"null".equals(queryNodeCode) && !StrUtil.equals(mappedNodeCode, queryNodeCode)) {
				continue;
			}

			// 构建文件对象
			JSONObject file = buildFileObject(parts, status, model, node, nodeCode,
					fileName, fileFormat, filePath, fileSize);

			// 处理不同类型的文件
			if (StrUtil.equals(type, "00")) {
				if (StrUtil.equals(fileFormat, "xlsx")) {
					processExcelFile(file, filePath);
					files.add(file);
				}
			} else if (StrUtil.startWith(type, "11")) {
				processAttachment(file, type, month, attachments);
			}
		}

		// 关联附件
		if (!attachments.isEmpty()) {
			linkAttachments(files, attachments);
		}

		return files;
	}

	/**
	 * 构建文件对象
	 */
	private JSONObject buildFileObject(List<String> parts, String status, String model,
									   String node, String nodeCode, String fileName, String fileFormat,
									   String filePath, String fileSize) {
		JSONObject file = new JSONObject();
		file.set("status", status)
				.set("model", model)
				.set("node", node)
				.set("nodeCode", nodeCode)
				.set("fileName", fileName)
				.set("fileFormat", fileFormat)
				.set("filePath", filePath)
				.set("fileSize", fileSize);

		// 设置表格信息
		if (parts.size() > 5) file.set("table1", parts.get(5));
		if (parts.size() > 6) file.set("table2", parts.get(6));
		if (parts.size() > 7) file.set("table3", parts.get(7));

		return file;
	}

	/**
	 * 处理Excel文件
	 */
	private void processExcelFile(JSONObject file, String filePath) {
		file.set("attachments", new JSONArray());
		JSONObject parseResult = Util.excel2HandsonTable(filePath);
		if (parseResult.getBool("success")) {
			file.set("isParseSuccess", true)
					.set("tableData", parseResult.getJSONObject("data").toString());
		} else {
			file.set("isParseSuccess", false)
					.set("parseFailedMsg", parseResult.getStr("msg"));
		}
	}

	/**
	 * 处理附件
	 */
	private void processAttachment(JSONObject file, String type, String month, JSONArray attachments) {
		String attachment = StrUtil.split(type, "_").get(1);
		String uuid = UUID.randomUUID().toString();
		String relativePath = "//" + month + "//" + uuid;
		String absolutePath = fileUploadPath + relativePath;

		FileUtil.copy(file.getStr("filePath"), absolutePath, true);

		JSONObject attachmentData = new JSONObject()
				.set("fileName", attachment)
				.set("absolutePath", file.getStr("filePath"))
				.set("filePath", relativePath)
				.set("fileFormat", file.getStr("fileFormat"))
				.set("fileSize", file.getStr("fileSize"))
				.set("createTime", DateUtil.now());

		file.set("attachmentData", attachmentData);
		attachments.add(file);
	}

	/**
	 * 关联附件
	 */
	private void linkAttachments(JSONArray files, JSONArray attachments) {
		for (int i = 0; i < files.size(); i++) {
			JSONObject file = files.getJSONObject(i);
			JSONArray fileAttachments = file.getJSONArray("attachments");

			for (int j = 0; j < attachments.size(); j++) {
				JSONObject attachment = attachments.getJSONObject(j);
				if (isAttachmentMatch(file, attachment)) {
					fileAttachments.add(attachment.getJSONObject("attachmentData"));
				}
			}
		}
	}

	/**
	 * 检查附件是否匹配
	 */
	private boolean isAttachmentMatch(JSONObject file, JSONObject attachment) {
		return StrUtil.equals(file.getStr("status"), attachment.getStr("status")) &&
				StrUtil.equals(file.getStr("model"), attachment.getStr("model")) &&
				StrUtil.equals(file.getStr("node"), attachment.getStr("node")) &&
				StrUtil.equals(file.getStr("nodeCode"), attachment.getStr("nodeCode")) &&
				StrUtil.equals(file.getStr("table1"), attachment.getStr("table1")) &&
				StrUtil.equals(file.getStr("table2"), attachment.getStr("table2")) &&
				StrUtil.equals(file.getStr("table3"), attachment.getStr("table3"));
	}

	/**
	 * 同步测试表格数据
	 *
	 * @return 成功同步的记录数
	 */
	public int syncTestFile() {
		// 获取配置信息
		String syncDirectory = Convert.toStr(Util.getThingProperty("Thing.Fn.ElectricTest", "syncDirectory"));
		String testFileName = Convert.toStr(Util.getThingProperty("Thing.Fn.ElectricTest", "testFileName"));
		String filePath = syncDirectory + File.separator + testFileName + ".xlsx";

		// 设置表头映射
		Map<String, String> headerAlias = new HashMap<>();
		initHeaderAlias(headerAlias);

		// 读取Excel数据
		JSONArray array = Util.readExcelToObject(filePath, headerAlias);

		// 过滤有效数据
		JSONArray filteredArray = filterValidData(array);

		// 分批处理数据
		return processBatchData(filteredArray);
	}

	/**
	 * 初始化表头映射
	 */
	private void initHeaderAlias(Map<String, String> headerAlias) {
		headerAlias.put("卫星名称", "model_name");
		headerAlias.put("阶段名称", "stage_name");
		headerAlias.put("测试项目", "test_item");
		headerAlias.put("创建时间", "creation_time");
		headerAlias.put("备注", "remarks");
		headerAlias.put("编号", "code");
		headerAlias.put("发生时刻", "occurrence_time");
		headerAlias.put("异常现象", "exception_phenomenon");
		headerAlias.put("异常发生地点", "exception_location");
		headerAlias.put("异常产品名称", "exception_product_name");
		headerAlias.put("产品代号", "product_code");
		headerAlias.put("批次号/版本号", "batch_or_version");
		headerAlias.put("责任单位", "responsible_unit");
		headerAlias.put("现象描述", "phenomenon_description");
		headerAlias.put("问题分类", "problem_category");
		headerAlias.put("初步定位", "initial_position");
		headerAlias.put("处理措施", "handling_measures");
		headerAlias.put("处置确认-509所十八室", "confirmation_509_18");
		headerAlias.put("处置确认-812所测试中心", "confirmation_812_test");
		headerAlias.put("闭环情况", "closure_status");
		headerAlias.put("闭环情况描述", "closure_description");
		headerAlias.put("闭环时间", "closure_time");
		headerAlias.put("闭环情况-509所质量师", "closure_509_quality");
		headerAlias.put("闭环情况-812所质量师", "closure_812_quality");
	}

	/**
	 * 过滤有效数据
	 */
	private JSONArray filterValidData(JSONArray array) {
		return JSONUtil.parseArray(array.stream()
				.filter(obj -> {
					JSONObject jsonObject = (JSONObject) obj;
					String occurrenceTime = jsonObject.getStr("occurrence_time");
					return StrUtil.isNotBlank(occurrenceTime);
				})
				.toArray());
	}

	/**
	 * 分批处理数据
	 */
	private int processBatchData(JSONArray array) {
		int size = array.size();
		int groupSize = 100;
		int successCount = 0;

		for (int i = 0; i < size; i += groupSize) {
			int end = Math.min(i + groupSize, size);
			JSONArray batch = JSONUtil.parseArray(array.subList(i, end));

			try {
				JSONObject result = Util.postTwxForObject("Thing.Fn.ElectricTest", "UpdateTestLog",
						JSONUtil.createObj()
								.set("dataStr", batch.toString())
								.set("arrFirstIndex", i));

				if (result.getBool("success")) {
					successCount += result.getInt("data");
				} else {
					log.error("同步测试数据失败: {}", result);
				}
			} catch (Exception e) {
				log.error("处理批次数据失败 [{}~{}]", i, end, e);
			}
		}

		return successCount;
	}

	/**
	 * 创建测试文件
	 */
	public void createTestFile() {
		// 获取同步目录
		String syncDirectory = Convert.toStr(Util.getThingProperty("Thing.Fn.ElectricTest", "syncDirectory"));
		String tplFile = "C:\\TestOut\\test.xlsx";

		// 获取需要创建的文件数据
		JSONArray datas = Util.postTwxForObject("Thing.Fn.ElectricTest", "CreateTestFile",
				new JSONObject()).getJSONArray("data");

		// 处理每个文件
		for (int i = 0; i < datas.size(); i++) {
			try {
				JSONObject data = datas.getJSONObject(i);
				String fileName = data.getStr("name");

				// 复制模板文件
				FileUtil.copy(tplFile, syncDirectory + File.separator + fileName, true);

				// 下载附件 (注释掉的功能保留)
                /*
                String tplAttachment = "https://picsum.photos/1920/1080";
                String attachment = data.getStr("attachment", "");
                if (StrUtil.isNotBlank(attachment)) {
                    HttpUtil.downloadFile(tplAttachment, syncDirectory + File.separator + attachment);
                }
                */
			} catch (Exception e) {
				log.error("创建测试文件失败: {}", e.getMessage(), e);
			}
		}
	}

	/**
	 * 创建临时测试文件
	 */
	public void createTemporaryFile() {
		// 获取同步目录
		String syncDirectory = Convert.toStr(Util.getThingProperty("Thing.Fn.ElectricTest", "syncDirectory"));

		// 获取需要创建的文件数据
		JSONArray datas = Util.postTwxForObject("Thing.Fn.ElectricTest", "CreateTestTemporaryFile",
				new JSONObject()).getJSONArray("data");

		// 处理每个文件
		for (int i = 0; i < datas.size(); i++) {
			try {
				JSONObject data = datas.getJSONObject(i);
				String fileName = data.getStr("name");
				String filePath = syncDirectory + File.separator + fileName;

				// 创建Excel文件
				try (ExcelWriter writer = ExcelUtil.getWriter(FileUtil.file(filePath))) {
					// 创建10x10的数据矩阵
					Object[][] objects = new Object[10][10];
					for (int j = 0; j < 10; j++) {
						for (int k = 0; k < 10; k++) {
							objects[j][k] = 0;
						}
					}
					writer.write(JSONUtil.parseArray(objects));

					// 设置特定单元格的值
					if (i < 5) {
						writer.getOrCreateCell("B3").setCellValue("58422");
					} else {
						writer.getOrCreateCell("B3").setCellValue(getRandomDate());
					}
					writer.getOrCreateCell("B4").setCellValue(getRandomCategory());
				}
			} catch (Exception e) {
				log.error("创建临时测试文件失败: {}", e.getMessage(), e);
			}
		}
	}

	/**
	 * 获取随机日期 (2024年)
	 */
	private String getRandomDate() {
		int year = 2024;
		int month = (int) (Math.random() * 2 + 1);
		String monthStr = StrUtil.padPre(String.valueOf(month), 2, '0');
		int day = (int) (Math.random() * 28 + 1);
		String dayStr = StrUtil.padPre(String.valueOf(day), 2, '0');
		return year + "-" + monthStr + "-" + dayStr;
	}

	/**
	 * 获取随机分类
	 */
	private String getRandomCategory() {
		String[] categories = {
				"报警遥测波道屏蔽",
				"遥测遥控指令变更",
				"报警遥测波道屏蔽、遥测遥控指令变更",
				"加载软件修改",
				"变更程序、增减项目"
		};
		return categories[(int) (Math.random() * categories.length)];
	}
} 