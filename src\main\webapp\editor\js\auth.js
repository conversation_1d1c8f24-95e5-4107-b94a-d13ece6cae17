// auth.js - 密码验证相关功能

// 密码验证函数（全局）
window.verifyPassword = function () {
    const password = document.getElementById('password').value;
    // 使用CryptoJS进行MD5加密
    const encryptedPassword = CryptoJS.MD5(password || '').toString();

    // 调用后端验证接口
    $.ajax({
        url: '/editor/verify',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            password: encryptedPassword
        }),
        success: function (response) {
            if (response.verified) {
                document.getElementById('passwordLayer').style.display = 'none';
                document.getElementById('mainContent').style.display = 'block';
                editorApp.init(); // 初始化编辑器和文件树
            } else {
                layer.msg('密码错误，请重试', { icon: 2 });
            }
        },
        error: function () {
            layer.msg('验证失败，请稍后重试', { icon: 2 });
        }
    });
};

// 绑定回车事件到密码输入框
function bindPasswordEvents() {
    document.getElementById('password').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            verifyPassword();
        }
    });
}

// 导出函数
window.authManager = {
    bindPasswordEvents: bindPasswordEvents
}; 