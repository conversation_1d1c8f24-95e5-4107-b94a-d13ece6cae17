package com.cirpoint.service.tools;

import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.cirpoint.constant.WorkTimeConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 工时统计测试数据生成器
 * 用于生成包含各种测试场景的模拟门禁打卡数据
 */
@Slf4j
@Component
public class WorkTimeTestDataGenerator {
    
    @Value("${file.temp.path}")
    private String tempDir;
    
    // 测试员工姓名列表
    private static final String[] EMPLOYEE_NAMES = {
        "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十",
        "陈一", "刘二", "杨三", "黄四", "朱五", "林六", "郭七", "何八"
    };
    
    // 门禁点列表
    private static final String[] ACCESS_POINTS = {
        "910入", "910出", "920入", "920出", 
        "二号楼北入", "二号楼北出", "二号楼南入", "二号楼南出"
    };
    
    // 部门列表
    private static final String[] DEPARTMENTS = {
        "生产一部", "生产二部", "技术部", "质量部", "管理部"
    };
    
    /**
     * 生成测试数据Excel文件
     * @param employeeCount 员工数量
     * @param days 天数
     * @return Excel文件字节数组
     */
    public byte[] generateTestExcel(int employeeCount, int days) {
        try {
            log.info("开始生成测试数据，员工数量: {}, 天数: {}", employeeCount, days);

            // 验证和限制参数范围
            if (employeeCount <= 0 || days <= 0) {
                throw new IllegalArgumentException("员工数量和天数必须大于0");
            }

            employeeCount = Math.min(employeeCount, EMPLOYEE_NAMES.length);
            days = Math.min(days, 30);

            if (employeeCount != Math.min(Math.max(employeeCount, 1), EMPLOYEE_NAMES.length) ||
                days != Math.min(Math.max(days, 1), 30)) {
                log.warn("参数已调整为合理范围：员工数量={}, 天数={}", employeeCount, days);
            }
            
            // 创建临时文件
            String tempFileName = System.currentTimeMillis() + "_test_data.xlsx";
            String tempFilePath = tempDir + File.separator + tempFileName;
            FileUtil.mkdir(tempDir);
            
            // 生成测试数据
            List<List<Object>> testData = generateTestData(employeeCount, days);

            try (ExcelWriter writer = ExcelUtil.getWriter(tempFilePath)) {
                // 写入表头
                List<String> headers = Arrays.asList(
                    WorkTimeConstants.COLUMN_EMPLOYEE_NAME,
                    WorkTimeConstants.COLUMN_EMPLOYEE_ID,
                    WorkTimeConstants.COLUMN_DEPARTMENT,
                    WorkTimeConstants.COLUMN_ACCESS_POINT,
                    WorkTimeConstants.COLUMN_CONTROLLER,
                    WorkTimeConstants.COLUMN_DIRECTION,
                    WorkTimeConstants.COLUMN_EVENT_TIME
                );
                writer.writeRow(headers);

                // 写入数据
                for (List<Object> row : testData) {
                    writer.writeRow(row);
                }

                // 设置列宽
                writer.setColumnWidth(0, 12); // 人员姓名
                writer.setColumnWidth(1, 12); // 工号
                writer.setColumnWidth(2, 15); // 所属组织
                writer.setColumnWidth(3, 15); // 门禁点
                writer.setColumnWidth(4, 15); // 控制器
                writer.setColumnWidth(5, 8);  // 出/入
                writer.setColumnWidth(6, 20); // 事件时间

                // 设置工作表名称
                writer.renameSheet("门禁打卡数据");
            }

            // 读取文件内容
            File tempFile = new File(tempFilePath);
            byte[] fileBytes = FileUtil.readBytes(tempFile);

            // 清理临时文件
            FileUtil.del(tempFile);

            log.info("测试数据生成完成，数据行数: {}", testData.size());
            return fileBytes;
            
        } catch (Exception e) {
            log.error("生成测试数据失败", e);
            throw new RuntimeException("生成测试数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成测试数据
     * @param employeeCount 员工数量
     * @param days 天数
     * @return 测试数据列表
     */
    private List<List<Object>> generateTestData(int employeeCount, int days) {
        List<List<Object>> data = new ArrayList<>();
        Random random = new Random();
        
        // 获取员工列表
        List<String> employees = Arrays.asList(EMPLOYEE_NAMES).subList(0, employeeCount);
        
        // 为每个员工生成每天的数据
        for (int day = 0; day < days; day++) {
            LocalDateTime baseDate = LocalDateTime.now().minusDays(days - 1 - day);
            
            for (String employee : employees) {
                // 随机选择今天的工作场景
                int scenario = random.nextInt(10);
                
                if (scenario < 6) {
                    // 60% 概率：正常单次进出
                    generateNormalWorkDay(data, employee, baseDate, random);
                } else if (scenario < 8) {
                    // 20% 概率：多次进出
                    generateMultipleInOutDay(data, employee, baseDate, random);
                } else if (scenario < 9) {
                    // 10% 概率：跨天工作
                    generateCrossDayWork(data, employee, baseDate, random);
                } else {
                    // 10% 概率：异常数据（悬挂记录）
                    generateAbnormalData(data, employee, baseDate, random);
                }
            }
        }
        
        // 添加一些特殊测试场景
        addSpecialTestScenarios(data, employees.get(0));

        // 添加重复记录测试场景
        addDuplicateRecordTestScenarios(data, employees, random);

        // 按时间排序
        data.sort((r1, r2) -> {
            String time1 = (String) r1.get(6);
            String time2 = (String) r2.get(6);
            return time1.compareTo(time2);
        });
        
        return data;
    }
    
    /**
     * 生成正常工作日数据（单次进出）
     */
    private void generateNormalWorkDay(List<List<Object>> data, String employee, 
                                     LocalDateTime baseDate, Random random) {
        String location = getRandomLocation(random);
        String department = getRandomDepartment(random);
        String employeeId = generateEmployeeId(employee);
        
        // 上班时间 8:00-9:30
        LocalDateTime inTime = baseDate.withHour(8 + random.nextInt(2)).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));

        // 下班时间 17:00-19:00
        LocalDateTime outTime = baseDate.withHour(17 + random.nextInt(2)).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));
        
        // 入场记录
        data.add(createRecord(employee, employeeId, department, location + "入", 
                             WorkTimeConstants.DIRECTION_IN, inTime));
        
        // 出场记录
        data.add(createRecord(employee, employeeId, department, location + "出", 
                             WorkTimeConstants.DIRECTION_OUT, outTime));
    }
    
    /**
     * 生成多次进出数据
     */
    private void generateMultipleInOutDay(List<List<Object>> data, String employee, 
                                        LocalDateTime baseDate, Random random) {
        String location = getRandomLocation(random);
        String department = getRandomDepartment(random);
        String employeeId = generateEmployeeId(employee);
        
        // 第一次进出（上午）
        LocalDateTime inTime1 = baseDate.withHour(8).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));
        LocalDateTime outTime1 = baseDate.withHour(11).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));
        
        data.add(createRecord(employee, employeeId, department, location + "入", 
                             WorkTimeConstants.DIRECTION_IN, inTime1));
        data.add(createRecord(employee, employeeId, department, location + "出", 
                             WorkTimeConstants.DIRECTION_OUT, outTime1));
        
        // 第二次进出（下午）
        LocalDateTime inTime2 = baseDate.withHour(13).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));
        LocalDateTime outTime2 = baseDate.withHour(17 + random.nextInt(2)).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));
        
        data.add(createRecord(employee, employeeId, department, location + "入", 
                             WorkTimeConstants.DIRECTION_IN, inTime2));
        data.add(createRecord(employee, employeeId, department, location + "出", 
                             WorkTimeConstants.DIRECTION_OUT, outTime2));
    }
    
    /**
     * 生成跨天工作数据
     */
    private void generateCrossDayWork(List<List<Object>> data, String employee, 
                                    LocalDateTime baseDate, Random random) {
        String location = getRandomLocation(random);
        String department = getRandomDepartment(random);
        String employeeId = generateEmployeeId(employee);
        
        // 晚上入场
        LocalDateTime inTime = baseDate.withHour(22).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));
        
        // 第二天早上出场
        LocalDateTime outTime = baseDate.plusDays(1).withHour(6).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));
        
        data.add(createRecord(employee, employeeId, department, location + "入", 
                             WorkTimeConstants.DIRECTION_IN, inTime));
        data.add(createRecord(employee, employeeId, department, location + "出", 
                             WorkTimeConstants.DIRECTION_OUT, outTime));
    }
    
    /**
     * 生成异常数据（悬挂记录）
     */
    private void generateAbnormalData(List<List<Object>> data, String employee,
                                    LocalDateTime baseDate, Random random) {
        String location = getRandomLocation(random);
        String department = getRandomDepartment(random);
        String employeeId = generateEmployeeId(employee);

        int abnormalType = random.nextInt(5); // 增加到5种异常类型

        if (abnormalType == 0) {
            // 只有入场，没有出场（后置入场记录）
            LocalDateTime inTime = baseDate.withHour(8).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));
            data.add(createRecord(employee, employeeId, department, location + "入",
                                 WorkTimeConstants.DIRECTION_IN, inTime));
        } else if (abnormalType == 1) {
            // 只有出场，没有入场（前置出场记录）
            LocalDateTime outTime = baseDate.withHour(17).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));
            data.add(createRecord(employee, employeeId, department, location + "出",
                                 WorkTimeConstants.DIRECTION_OUT, outTime));
        } else if (abnormalType == 2) {
            // 连续两次入场
            LocalDateTime inTime1 = baseDate.withHour(8).withMinute(random.nextInt(30)).withSecond(random.nextInt(60));
            LocalDateTime inTime2 = baseDate.withHour(8).withMinute(30 + random.nextInt(30)).withSecond(random.nextInt(60));

            data.add(createRecord(employee, employeeId, department, location + "入",
                                 WorkTimeConstants.DIRECTION_IN, inTime1));
            data.add(createRecord(employee, employeeId, department, location + "入",
                                 WorkTimeConstants.DIRECTION_IN, inTime2));
        } else if (abnormalType == 3) {
            // 跨日悬挂出场记录（规则1测试）：当日第一条记录为"出"，但前一日无对应"入"
            generateCrossDayHangingOutRecord(data, employee, baseDate, random);
        } else {
            // 跨日悬挂入场记录（规则2测试）：当日最后一条记录为"入"，但后一日无对应"出"
            generateCrossDayHangingInRecord(data, employee, baseDate, random);
        }
    }
    
    /**
     * 生成跨日悬挂出场记录（规则1测试）
     * 当日第一条记录为"出"，但前一日该场所没有对应的"入"记录
     */
    private void generateCrossDayHangingOutRecord(List<List<Object>> data, String employee,
                                                LocalDateTime baseDate, Random random) {
        String location = getRandomLocation(random);
        String department = getRandomDepartment(random);
        String employeeId = generateEmployeeId(employee);

        // 当日第一条记录为出场，但前一日该场所没有入场记录
        LocalDateTime outTime = baseDate.withHour(6).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));
        data.add(createRecord(employee, employeeId, department, location + "出",
                             WorkTimeConstants.DIRECTION_OUT, outTime));

        // 为了增加测试复杂度，可以在当日稍后添加正常的入出记录
        if (random.nextBoolean()) {
            LocalDateTime normalInTime = baseDate.withHour(8).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));
            LocalDateTime normalOutTime = baseDate.withHour(17).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));

            data.add(createRecord(employee, employeeId, department, location + "入",
                                 WorkTimeConstants.DIRECTION_IN, normalInTime));
            data.add(createRecord(employee, employeeId, department, location + "出",
                                 WorkTimeConstants.DIRECTION_OUT, normalOutTime));
        }
    }

    /**
     * 生成跨日悬挂入场记录（规则2测试）
     * 当日最后一条记录为"入"，但后一日该场所没有对应的"出"记录
     */
    private void generateCrossDayHangingInRecord(List<List<Object>> data, String employee,
                                               LocalDateTime baseDate, Random random) {
        String location = getRandomLocation(random);
        String department = getRandomDepartment(random);
        String employeeId = generateEmployeeId(employee);

        // 为了增加测试复杂度，可以先添加正常的入出记录
        if (random.nextBoolean()) {
            LocalDateTime normalInTime = baseDate.withHour(8).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));
            LocalDateTime normalOutTime = baseDate.withHour(12).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));

            data.add(createRecord(employee, employeeId, department, location + "入",
                                 WorkTimeConstants.DIRECTION_IN, normalInTime));
            data.add(createRecord(employee, employeeId, department, location + "出",
                                 WorkTimeConstants.DIRECTION_OUT, normalOutTime));
        }

        // 当日最后一条记录为入场，但后一日该场所没有出场记录
        LocalDateTime hangingInTime = baseDate.withHour(22).withMinute(random.nextInt(60)).withSecond(random.nextInt(60));
        data.add(createRecord(employee, employeeId, department, location + "入",
                             WorkTimeConstants.DIRECTION_IN, hangingInTime));
    }

    /**
     * 添加特殊测试场景
     */
    private void addSpecialTestScenarios(List<List<Object>> data, String employee) {
        LocalDateTime baseDate = LocalDateTime.now().minusDays(1);
        String department = DEPARTMENTS[0];
        String employeeId = generateEmployeeId(employee);
        
        // 多地点工作场景
        LocalDateTime time1 = baseDate.withHour(8).withMinute(0).withSecond(0);
        LocalDateTime time2 = baseDate.withHour(10).withMinute(0).withSecond(0);
        LocalDateTime time3 = baseDate.withHour(10).withMinute(30).withSecond(0);
        LocalDateTime time4 = baseDate.withHour(12).withMinute(0).withSecond(0);
        LocalDateTime time5 = baseDate.withHour(13).withMinute(0).withSecond(0);
        LocalDateTime time6 = baseDate.withHour(17).withMinute(0).withSecond(0);
        
        // 910厂房工作
        data.add(createRecord(employee, employeeId, department, "910入", WorkTimeConstants.DIRECTION_IN, time1));
        data.add(createRecord(employee, employeeId, department, "910出", WorkTimeConstants.DIRECTION_OUT, time2));
        
        // 920厂房工作
        data.add(createRecord(employee, employeeId, department, "920入", WorkTimeConstants.DIRECTION_IN, time3));
        data.add(createRecord(employee, employeeId, department, "920出", WorkTimeConstants.DIRECTION_OUT, time4));
        
        // 二号楼工作
        data.add(createRecord(employee, employeeId, department, "二号楼北入", WorkTimeConstants.DIRECTION_IN, time5));
        data.add(createRecord(employee, employeeId, department, "二号楼北出", WorkTimeConstants.DIRECTION_OUT, time6));

        // 添加跨日验证规则测试场景
        addCrossDayValidationTestScenarios(data, employee);
    }

    /**
     * 添加跨日验证规则测试场景
     */
    private void addCrossDayValidationTestScenarios(List<List<Object>> data, String employee) {
        String department = DEPARTMENTS[0];
        String employeeId = generateEmployeeId(employee);
        LocalDateTime testDate = LocalDateTime.now().minusDays(3);

        // 测试场景1：规则1验证 - 前置出场记录
        // 第1天：920厂房只有出场记录，没有前一日的入场记录
        LocalDateTime hangingOutTime = testDate.withHour(6).withMinute(0).withSecond(0);
        data.add(createRecord(employee + "_测试1", employeeId, department, "920出",
                             WorkTimeConstants.DIRECTION_OUT, hangingOutTime));

        // 测试场景2：规则2验证 - 后置入场记录
        // 第2天：910厂房只有入场记录，没有后一日的出场记录
        LocalDateTime hangingInTime = testDate.plusDays(1).withHour(22).withMinute(0).withSecond(0);
        data.add(createRecord(employee + "_测试2", employeeId, department, "910入",
                             WorkTimeConstants.DIRECTION_IN, hangingInTime));

        // 测试场景3：正确的跨日配对（对比场景）
        // 第3天晚上入场，第4天早上出场
        LocalDateTime validCrossInTime = testDate.plusDays(2).withHour(22).withMinute(30).withSecond(0);
        LocalDateTime validCrossOutTime = testDate.plusDays(3).withHour(6).withMinute(30).withSecond(0);
        data.add(createRecord(employee + "_测试3", employeeId, department, "二号楼北入",
                             WorkTimeConstants.DIRECTION_IN, validCrossInTime));
        data.add(createRecord(employee + "_测试3", employeeId, department, "二号楼北出",
                             WorkTimeConstants.DIRECTION_OUT, validCrossOutTime));

        // 测试场景4：混合场景 - 同一员工不同场所的跨日验证
        LocalDateTime mixedDate = testDate.plusDays(4);

        // 910厂房：正常工作
        data.add(createRecord(employee + "_测试4", employeeId, department, "910入",
                             WorkTimeConstants.DIRECTION_IN, mixedDate.withHour(8).withMinute(0).withSecond(0)));
        data.add(createRecord(employee + "_测试4", employeeId, department, "910出",
                             WorkTimeConstants.DIRECTION_OUT, mixedDate.withHour(17).withMinute(0).withSecond(0)));

        // 920厂房：悬挂入场记录（规则2）
        data.add(createRecord(employee + "_测试4", employeeId, department, "920入",
                             WorkTimeConstants.DIRECTION_IN, mixedDate.withHour(20).withMinute(0).withSecond(0)));
    }
    
    /**
     * 创建单条记录
     */
    private List<Object> createRecord(String employee, String employeeId, String department, 
                                    String accessPoint, String direction, LocalDateTime eventTime) {
        return Arrays.asList(
            employee,
            employeeId,
            department,
            accessPoint,
            "Controller_" + accessPoint.charAt(0),
            direction,
            eventTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        );
    }
    
    /**
     * 获取随机场所
     */
    private String getRandomLocation(Random random) {
        String[] locations = {"910", "920", "二号楼北"};
        return locations[random.nextInt(locations.length)];
    }
    
    /**
     * 获取随机部门
     */
    private String getRandomDepartment(Random random) {
        return DEPARTMENTS[random.nextInt(DEPARTMENTS.length)];
    }
    
    /**
     * 生成员工工号
     */
    private String generateEmployeeId(String employee) {
        return String.format("%09d", Math.abs(employee.hashCode()) % 1000000000);
    }

    /**
     * 添加重复记录测试场景
     * 用于测试门禁数据去重功能
     * @param data 测试数据列表
     * @param employees 员工列表
     * @param random 随机数生成器
     */
    private void addDuplicateRecordTestScenarios(List<List<Object>> data, List<String> employees, Random random) {
        if (employees == null || employees.size() < 6) {
            return; // 员工数量不足，跳过重复记录测试场景
        }

        LocalDateTime testDate = LocalDateTime.now().minusDays(2);
        String department = DEPARTMENTS[0];

        // 场景1：同一员工在2分钟内多次刷同一门禁卡（典型的重复刷卡）
        addTypicalDuplicateScenario(data, employees.get(0), testDate, department);

        // 场景2：不同时间间隔的重复记录测试
        addTimeIntervalDuplicateScenario(data, employees.get(1), testDate, department);

        // 场景3：边界时间测试（恰好120秒）
        addBoundaryTimeDuplicateScenario(data, employees.get(2), testDate, department);

        // 场景4：大量重复记录测试
        addMassiveDuplicateScenario(data, employees.get(3), testDate, department, random);

        // 场景5：混合场景：重复记录 + 跨日验证
        addMixedDuplicateAndCrossDayScenario(data, employees.get(4), testDate, department);

        // 场景6：不同分组的重复记录（不应互相影响）
        addDifferentGroupDuplicateScenario(data, employees.get(5), testDate, department);
    }

    /**
     * 场景1：典型的重复刷卡场景
     * 员工因为门禁系统响应慢或门未开而多次刷卡
     */
    private void addTypicalDuplicateScenario(List<List<Object>> data, String employee,
                                           LocalDateTime testDate, String department) {
        String employeeId = generateEmployeeId(employee);
        LocalDateTime baseTime = testDate.withHour(8).withMinute(10).withSecond(0);

        // 张三在910厂房入口多次刷卡（门未开，连续刷卡）
        data.add(createRecord(employee + "_重复测试1", employeeId, department, "910入",
                             WorkTimeConstants.DIRECTION_IN, baseTime)); // 8:10:00
        data.add(createRecord(employee + "_重复测试1", employeeId, department, "910入",
                             WorkTimeConstants.DIRECTION_IN, baseTime.plusSeconds(10))); // 8:10:10 - 应被去重
        data.add(createRecord(employee + "_重复测试1", employeeId, department, "910入",
                             WorkTimeConstants.DIRECTION_IN, baseTime.plusSeconds(30))); // 8:10:30 - 应被去重
        data.add(createRecord(employee + "_重复测试1", employeeId, department, "910入",
                             WorkTimeConstants.DIRECTION_IN, baseTime.plusSeconds(45))); // 8:10:45 - 应被去重

        // 正常的出场记录（不重复）
        data.add(createRecord(employee + "_重复测试1", employeeId, department, "910出",
                             WorkTimeConstants.DIRECTION_OUT, baseTime.plusHours(8))); // 16:10:00
    }

    /**
     * 场景2：不同时间间隔的重复记录测试
     */
    private void addTimeIntervalDuplicateScenario(List<List<Object>> data, String employee,
                                                LocalDateTime testDate, String department) {
        String employeeId = generateEmployeeId(employee);
        LocalDateTime baseTime = testDate.withHour(9).withMinute(0).withSecond(0);

        // 测试不同时间间隔的重复记录
        data.add(createRecord(employee + "_时间间隔测试", employeeId, department, "920入",
                             WorkTimeConstants.DIRECTION_IN, baseTime)); // 9:00:00
        data.add(createRecord(employee + "_时间间隔测试", employeeId, department, "920入",
                             WorkTimeConstants.DIRECTION_IN, baseTime.plusSeconds(60))); // 9:01:00 - 应被去重
        data.add(createRecord(employee + "_时间间隔测试", employeeId, department, "920入",
                             WorkTimeConstants.DIRECTION_IN, baseTime.plusSeconds(119))); // 9:01:59 - 应被去重
        data.add(createRecord(employee + "_时间间隔测试", employeeId, department, "920入",
                             WorkTimeConstants.DIRECTION_IN, baseTime.plusSeconds(121))); // 9:02:01 - 应保留（超过120秒）

        // 出场记录
        data.add(createRecord(employee + "_时间间隔测试", employeeId, department, "920出",
                             WorkTimeConstants.DIRECTION_OUT, baseTime.plusHours(8)));
    }

    /**
     * 场景3：边界时间测试（恰好120秒）
     */
    private void addBoundaryTimeDuplicateScenario(List<List<Object>> data, String employee,
                                                LocalDateTime testDate, String department) {
        String employeeId = generateEmployeeId(employee);
        LocalDateTime baseTime = testDate.withHour(10).withMinute(0).withSecond(0);

        // 测试恰好120秒的边界情况
        data.add(createRecord(employee + "_边界测试", employeeId, department, "二号楼北入",
                             WorkTimeConstants.DIRECTION_IN, baseTime)); // 10:00:00
        data.add(createRecord(employee + "_边界测试", employeeId, department, "二号楼北入",
                             WorkTimeConstants.DIRECTION_IN, baseTime.plusSeconds(120))); // 10:02:00 - 恰好120秒，应被去重
        data.add(createRecord(employee + "_边界测试", employeeId, department, "二号楼北入",
                             WorkTimeConstants.DIRECTION_IN, baseTime.plusSeconds(121))); // 10:02:01 - 121秒，应保留

        // 出场记录
        data.add(createRecord(employee + "_边界测试", employeeId, department, "二号楼北出",
                             WorkTimeConstants.DIRECTION_OUT, baseTime.plusHours(7)));
    }

    /**
     * 场景4：大量重复记录测试（性能测试）
     */
    private void addMassiveDuplicateScenario(List<List<Object>> data, String employee,
                                           LocalDateTime testDate, String department, Random random) {
        String employeeId = generateEmployeeId(employee);
        LocalDateTime baseTime = testDate.withHour(11).withMinute(0).withSecond(0);

        // 模拟门禁系统故障，员工疯狂刷卡的场景
        for (int i = 0; i < 10; i++) {
            data.add(createRecord(employee + "_大量重复测试", employeeId, department, "910入",
                                 WorkTimeConstants.DIRECTION_IN, baseTime.plusSeconds(i * 5))); // 每5秒一次
        }

        // 正常出场
        data.add(createRecord(employee + "_大量重复测试", employeeId, department, "910出",
                             WorkTimeConstants.DIRECTION_OUT, baseTime.plusHours(8)));
    }

    /**
     * 场景5：混合场景：重复记录 + 跨日验证
     */
    private void addMixedDuplicateAndCrossDayScenario(List<List<Object>> data, String employee,
                                                    LocalDateTime testDate, String department) {
        String employeeId = generateEmployeeId(employee);
        LocalDateTime baseTime = testDate.withHour(22).withMinute(0).withSecond(0);

        // 晚上入场时有重复刷卡
        data.add(createRecord(employee + "_混合测试", employeeId, department, "920入",
                             WorkTimeConstants.DIRECTION_IN, baseTime)); // 22:00:00
        data.add(createRecord(employee + "_混合测试", employeeId, department, "920入",
                             WorkTimeConstants.DIRECTION_IN, baseTime.plusSeconds(15))); // 22:00:15 - 应被去重
        data.add(createRecord(employee + "_混合测试", employeeId, department, "920入",
                             WorkTimeConstants.DIRECTION_IN, baseTime.plusSeconds(30))); // 22:00:30 - 应被去重

        // 第二天早上出场时也有重复刷卡
        LocalDateTime nextDayTime = testDate.plusDays(1).withHour(6).withMinute(0).withSecond(0);
        data.add(createRecord(employee + "_混合测试", employeeId, department, "920出",
                             WorkTimeConstants.DIRECTION_OUT, nextDayTime)); // 第二天 6:00:00
        data.add(createRecord(employee + "_混合测试", employeeId, department, "920出",
                             WorkTimeConstants.DIRECTION_OUT, nextDayTime.plusSeconds(20))); // 6:00:20 - 应被去重
    }

    /**
     * 场景6：不同分组的重复记录（验证分组逻辑）
     */
    private void addDifferentGroupDuplicateScenario(List<List<Object>> data, String employee,
                                                  LocalDateTime testDate, String department) {
        String employeeId = generateEmployeeId(employee);
        LocalDateTime baseTime = testDate.withHour(13).withMinute(0).withSecond(0);

        // 同一时间，不同员工在同一场所刷卡（不应互相影响）
        data.add(createRecord(employee + "_分组测试A", employeeId, department, "910入",
                             WorkTimeConstants.DIRECTION_IN, baseTime));
        data.add(createRecord(employee + "_分组测试B", employeeId, department, "910入",
                             WorkTimeConstants.DIRECTION_IN, baseTime.plusSeconds(10))); // 不同员工，不应被去重

        // 同一员工，不同场所刷卡（不应互相影响）
        data.add(createRecord(employee + "_分组测试A", employeeId, department, "920入",
                             WorkTimeConstants.DIRECTION_IN, baseTime.plusSeconds(20)));
        data.add(createRecord(employee + "_分组测试A", employeeId, department, "920入",
                             WorkTimeConstants.DIRECTION_IN, baseTime.plusSeconds(30))); // 同员工同场所，应被去重

        // 同一员工同一场所，不同方向刷卡（不应互相影响）
        data.add(createRecord(employee + "_分组测试A", employeeId, department, "910出",
                             WorkTimeConstants.DIRECTION_OUT, baseTime.plusSeconds(40)));
        data.add(createRecord(employee + "_分组测试A", employeeId, department, "910出",
                             WorkTimeConstants.DIRECTION_OUT, baseTime.plusSeconds(50))); // 同员工同场所同方向，应被去重

        // 添加其他员工的出场记录
        data.add(createRecord(employee + "_分组测试B", employeeId, department, "910出",
                             WorkTimeConstants.DIRECTION_OUT, baseTime.plusHours(4)));
        data.add(createRecord(employee + "_分组测试A", employeeId, department, "920出",
                             WorkTimeConstants.DIRECTION_OUT, baseTime.plusHours(5)));
    }
}
