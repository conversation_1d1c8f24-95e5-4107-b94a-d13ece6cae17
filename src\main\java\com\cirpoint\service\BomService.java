package com.cirpoint.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.util.CommonUtil;
import com.cirpoint.util.FileUploadUtil;
import com.cirpoint.util.Util;
import java.io.File;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * BOM服务
 */
@Slf4j
@Service
public class BomService {

	/**
	 * 导出BOM模板
	 *
	 * @return 生成的Excel文件
	 */
	public File exportBomTemplate() {
		// 准备空数据
		JSONArray data = new JSONArray();
		for (int i = 0; i < 400; i++) {
			JSONArray row = new JSONArray();
			for (int j = 0; j < 3; j++) {
				row.add("");
			}
			data.add(row);
		}

		// 设置表头和列宽
		JSONArray headers = JSONUtil.parseArray(Arrays.asList("产品名称", "产品编号", "产品批次号"));
		JSONArray columnWidths = JSONUtil.parseArray(Arrays.asList(30, 30, 30));

		return CommonUtil.createExcelFile("批量导入产品模板", headers, data, columnWidths, 28);
	}

	/**
	 * 导入BOM数据
	 *
	 * @param file Excel文件
	 * @param pid  父节点ID
	 * @param user 用户名
	 * @return 导入结果
	 */
	public JSONObject importBom(MultipartFile file, String pid, String user) {
		// 创建临时文件
		FileUploadUtil.UploadResult uploadResult = FileUploadUtil.uploadToTemp(file);
		try {
			// 设置Excel表头别名映射
			Map<String, String> headerAlias = new HashMap<>();
			headerAlias.put("产品名称", "name");
			headerAlias.put("产品编号", "code");
			headerAlias.put("产品批次号", "batchNumber");

			// 读取Excel数据
			JSONArray data = Util.readExcelToObject(uploadResult.getAbsolutePath(), headerAlias);

			// 调用TWX接口保存数据
			return Util.postTwxForObject("Thing.Fn.BOM", "BatchImportBom",
					JSONUtil.createObj()
							.set("datas", data)
							.set("user", user)
							.set("pid", pid));
		} finally {
			// 清理临时文件
			FileUploadUtil.cleanupTemp(uploadResult.getAbsolutePath());
		}
	}
} 