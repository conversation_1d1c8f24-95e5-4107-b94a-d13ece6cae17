package com.cirpoint.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.cirpoint.util.Util;
import java.io.File;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;

/**
 * 实验鉴定服务类
 */
@Slf4j
@Service
public class TestEvaluationService extends ApplicationConfig {

	/**
	 * 导出数据包
	 *
	 * @param treeId 树节点ID
	 * @return 文件名
	 */
	public File exportDataPackage(String treeId) {
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator + System.currentTimeMillis();
		FileUtil.mkdir(thisTempPath);
		String name = "";
		// 调用接口获取测试文件信息
		JSONObject res = Util.postTwxForObject(
				"Thing.Fn.TestEvaluation",
				"QueryTreeTestFile",
				JSONUtil.createObj().set("treeId", treeId));

		if (res.getBool("success")) {
			JSONArray data = res.getJSONArray("data");
			name = data.getJSONObject(0).getStr("NAME_");
			createFolder(data, thisTempPath);
		}
		File zipFile = ZipUtil.zip(thisTempPath);
		return FileUtil.rename(zipFile, name + "（内部）.zip", true);
	}

	/**
	 * 创建文件夹
	 *
	 * @param arr        文件夹数组
	 * @param parentPath 父路路径
	 */
	private void createFolder(JSONArray arr, String parentPath) {
		for (int i = 0; i < arr.size(); i++) {
			JSONObject obj = arr.getJSONObject(i);
			String name_ = obj.getStr("NAME_");
			String type_ = obj.getStr("TYPE_");

			if (!type_.equals("file")) {
				String folderPath = parentPath + File.separator + name_;
				FileUtil.mkdir(folderPath);

				JSONArray children = obj.getJSONArray("children");
				if (ObjectUtil.isNotNull(children) && !children.isEmpty()) {
					createFolder(children, folderPath);
					if (type_.equals("product")) {
						createExcelFile(parentPath, name_, children);
					}
				}
			}
		}
	}

	/**
	 * 创建Excel文件
	 *
	 * @param parentPath 父路径
	 * @param name       文件名
	 * @param data       数据数组
	 */
	private void createExcelFile(String parentPath, String name, JSONArray data) {
		// 创建表头
		Object[][] headers = {{name}, {"序号", "类别", "文件名称及范围", "审核人", "见证材料"}};
		JSONArray tableData = JSONUtil.parseArray(headers);

		// 处理数据
		for (int i = 0; i < data.size(); i++) {
			JSONObject row = data.getJSONObject(i);
			JSONArray arr = new JSONArray();
			arr.add(i + 1);
			arr.add(row.getStr("CATEGORY_", ""));
			arr.add(row.getStr("NAME_", ""));
			arr.add(row.getStr("REVIEWER_", ""));
			arr.add(row.getStr("FILE_NAME_", ""));
			tableData.add(arr);

			// 复制文件
			try {
				String filePath = row.getStr("FILE_PATH_", "");
				File destFile = new File(parentPath + File.separator + name +
						File.separator + row.getStr("FILE_NAME_", ""));
				FileUtil.copy(FileUtil.file(fileUploadPath + filePath), destFile, true);
			} catch (Exception e) {
				log.error("复制文件失败", e);
			}
		}

		// 写入Excel
		String filePath = parentPath + File.separator + name + "文件清单列表(内部).xlsx";
		ExcelWriter writer = ExcelUtil.getWriter(filePath);

		// 写入数据
		writer.write(tableData, false);

		// 设置样式
		writer.merge(0, 0, 0, 4, name, false);
		writer.setColumnWidth(0, 7);
		writer.setColumnWidth(1, 30);
		writer.setColumnWidth(2, 60);
		writer.setColumnWidth(3, 10);
		writer.setColumnWidth(4, 60);

		// 设置行高
		for (int i = 0; i < writer.getRowCount(); i++) {
			writer.setRowHeight(i, 28);
		}

		// 设置标题样式
		Font font = writer.createFont();
		font.setBold(true);
		CellStyle headerStyle = writer.createCellStyle();
		headerStyle.setFont(font);
		headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headerStyle.setAlignment(HorizontalAlignment.CENTER);
		headerStyle.setBorderBottom(BorderStyle.THIN);
		headerStyle.setBorderLeft(BorderStyle.THIN);
		headerStyle.setBorderTop(BorderStyle.THIN);
		headerStyle.setBorderRight(BorderStyle.THIN);
		headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

		writer.setRowStyleIfHasData(0, headerStyle);
		writer.setRowStyleIfHasData(1, headerStyle);
		writer.setFreezePane(2);
		writer.close();
	}
}