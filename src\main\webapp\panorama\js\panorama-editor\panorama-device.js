/**
 * 全景图热点编辑系统 - 设备管理模块
 * 
 * <AUTHOR>
 * @date 2025-06-10
 * @description 处理设备列表管理、设备数据导入导出等功能
 * @requires panorama-core.js, panorama-ui.js
 */

/**
 * 全景图编辑器设备管理模块
 */
var PanoramaDevice = (function () {
    'use strict';

    // ==================== 设备列表管理 ====================

    /**
     * 加载设备列表
     */
    function loadDeviceList() {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) return;

        var $ = layui.$;
        $.get('/panorama/device/list', { taskId: currentTaskId }, function (res) {
            if (res.success) {
                PanoramaCore.setDeviceList(res.data);
            }
        });
    }

    /**
     * 更新设备状态显示
     */
    function updateDeviceStatus() {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) return;

        var $ = layui.$;
        $.get('/panorama/device/list', { taskId: currentTaskId, page: 1, limit: 1 }, function (res) {
            var deviceStatus = document.getElementById('deviceStatus');
            if (deviceStatus) {
                if (res.code === 0 && res.count > 0) {
                    deviceStatus.textContent = '共' + res.count + '条';
                } else {
                    deviceStatus.textContent = '-';
                }
            }
        });
    }

    // ==================== 设备列表对话框 ====================

    /**
     * 显示设备列表弹窗
     */
    function showDeviceListDialog() {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) {
            layui.layer.msg('请先选择任务', { icon: 2 });
            return;
        }

        var dialogHtml =
            '<div id="deviceListContainer" style="padding: 0; width: 100%;">' +
                '<div style="padding: 15px; border-bottom: 1px solid #e6e8eb; background: #fafbfc;">' +
                    '<div style="display: flex; justify-content: space-between; align-items: center;">' +
                        '<h3 style="margin: 0; font-size: 16px; color: #333;">' +
                            '<i class="layui-icon layui-icon-table" style="color: #667eea;"></i> 单机数据管理' +
                        '</h3>' +
                        '<div>' +
                            '<button type="button" class="layui-btn layui-btn-sm" id="reimportDeviceDataBtn" style="margin-right: 8px;">' +
                                '<i class="layui-icon layui-icon-refresh-1"></i> 重新导入型号单机数据' +
                            '</button>' +
                            '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="uploadExcelInDialog" style="margin-right: 8px;">' +
                                '<i class="layui-icon layui-icon-upload"></i> 上传Excel' +
                            '</button>' +
                            '<button type="button" class="layui-btn layui-btn-sm layui-btn-warm" id="downloadExcelInDialog">' +
                                '<i class="layui-icon layui-icon-download-circle"></i> 下载Excel' +
                            '</button>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
                '<div style="height: 639px; overflow: hidden; width: 100%;">' +
                    '<table style="width: 100%;" class="layui-hide" id="deviceTable" lay-filter="deviceTable"></table>' +
                '</div>' +
            '</div>';

        layui.layer.open({
            type: 1,
            title: false,
            content: dialogHtml,
            area: ['900px', '700px'],
            success: function () {
                // 初始化设备表格
                initDeviceTable();

                // 初始化弹窗内的上传功能
                initDialogUpload();

                // 初始化弹窗内的下载功能
                initDialogDownload();

                // 重新导入单机数据
                var reimportBtn = document.getElementById('reimportDeviceDataBtn');
                if (reimportBtn) {
                    reimportBtn.addEventListener('click', function () {
                        handleReimportDeviceData(this);
                    });
                }
            }
        });
    }

    /**
     * 初始化设备表格
     */
    function initDeviceTable() {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) return;

        layui.table.render({
            elem: '#deviceTable',
            url: '/panorama/device/list',
            where: {
                taskId: currentTaskId
            },
            page: true,
            limit: 15,
            limits: [15, 30, 50],
            height: 639,
            cols: [[
                { type: 'numbers', title: '序号', width: 50 },
                { field: 'DEVICE_NAME', title: '单机名称', width: 297 },
                { field: 'DEVICE_CODE', title: '单机代号', width: 250 },
                { field: 'BATCH_NO', title: '批次号', width: 250 }
            ]]
        });
    }

    /**
     * 处理重新导入设备数据
     * @param {HTMLElement} btnElement 按钮元素
     */
    function handleReimportDeviceData(btnElement) {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) return;

        layui.layer.confirm('确定要重新导入最新的型号单机数据吗？<br>这将覆盖当前已有的单机数据。', {
            icon: 3,
            title: '确认导入'
        }, function (index) {
            layui.layer.close(index);

            var layerIndex = layui.layer.load(1, { shade: [0.3, '#000'] });
            btnElement.disabled = true;
            btnElement.classList.add('layui-btn-disabled');

            var $ = layui.$;
            $.ajax({
                url: '/panorama/task/' + currentTaskId + '/refresh-devices',
                type: 'POST',
                success: function (res) {
                    layui.layer.close(layerIndex);
                    btnElement.disabled = false;
                    btnElement.classList.remove('layui-btn-disabled');

                    if (res.success) {
                        layui.layer.msg('单机数据刷新成功！', { icon: 1 });
                        layui.table.reload('deviceTable'); // 刷新设备表格
                        updateDeviceStatus(); // 更新主页面上的状态
                    } else {
                        layui.layer.alert('刷新失败: ' + res.msg, { icon: 2 });
                    }
                },
                error: function (xhr) {
                    layui.layer.close(layerIndex);
                    btnElement.disabled = false;
                    btnElement.classList.remove('layui-btn-disabled');

                    var errorMsg = xhr.responseJSON && xhr.responseJSON.msg ? xhr.responseJSON.msg : '网络请求失败';
                    layui.layer.alert('刷新失败: ' + errorMsg, { icon: 2 });
                }
            });
        });
    }

    // ==================== 文件上传下载 ====================

    /**
     * 初始化弹窗内的上传功能
     */
    function initDialogUpload() {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) return;

        layui.upload.render({
            elem: '#uploadExcelInDialog',
            url: '/panorama/upload/excel',
            accept: 'file',
            exts: 'xlsx|xls',
            data: {
                taskId: function () {
                    return currentTaskId;
                }
            },
            before: function () {
                layui.layer.load();
            },
            done: function (res) {
                layui.layer.closeAll('loading');
                if (res.success) {
                    layui.layer.msg('Excel文件上传成功', { icon: 1 });
                    // 刷新设备表格
                    layui.table.reload('deviceTable');
                    // 更新外部状态
                    updateDeviceStatus();
                } else {
                    layui.layer.msg('上传失败: ' + res.msg, { icon: 2 });
                }
            },
            error: function () {
                layui.layer.closeAll('loading');
                layui.layer.msg('上传失败', { icon: 2 });
            }
        });
    }

    /**
     * 初始化弹窗内的下载功能
     */
    function initDialogDownload() {
        var downloadBtn = document.getElementById('downloadExcelInDialog');
        if (!downloadBtn) return;

        downloadBtn.addEventListener('click', function () {
            var currentTaskId = PanoramaCore.getCurrentTaskId();
            if (!currentTaskId) {
                layui.layer.msg('请先选择任务', { icon: 2 });
                return;
            }

            // 显示下载提示
            layui.layer.msg('正在生成Excel文件...', { icon: 16, time: 2000 });

            // 创建隐藏的iframe进行下载
            var downloadUrl = '/panorama/device/export?taskId=' + currentTaskId;
            var iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = downloadUrl;
            document.body.appendChild(iframe);

            // 下载完成后移除iframe
            setTimeout(function () {
                document.body.removeChild(iframe);
            }, 5000);
        });
    }

    // ==================== 为热点编辑对话框加载设备列表 ====================

    /**
     * 为热点编辑对话框加载单机列表
     * @param {Object} layero 对话框jQuery对象
     * @param {Object} hotspotData 热点数据
     */
    function loadDeviceListForHotspot(layero, hotspotData) {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) {
            return;
        }

        // 强制重新加载设备数据，确保数据最新
        var $ = layui.$;
        $.get('/panorama/device/list', { taskId: currentTaskId }, function (res) {
            if (res.code == 0) {
                PanoramaCore.setDeviceList(res.data);
                populateHotspotSelectOptions(layero, hotspotData);
            }
        });
    }

    /**
     * 填充热点选择框选项
     * @param {Object} layero 对话框jQuery对象
     * @param {Object} hotspotData 热点数据
     */
    function populateHotspotSelectOptions(layero, hotspotData) {
        var deviceList = PanoramaCore.getDeviceList();
        var select = layero.find('select[name="selectModeTitle"]');

        // 清空并填充选择框选项
        var options = '<option value="">请选择设备名称</option>';

        if (deviceList && deviceList.length > 0) {
            for (var i = 0; i < deviceList.length; i++) {
                var device = deviceList[i];
                options += '<option value="' + device.DEVICE_ID + '">' + device.DEVICE_NAME + '</option>';
            }
        }

        select.html(options);

        // 设置当前值
        var currentTitle = hotspotData.TITLE || '';
        var currentDescription = hotspotData.DESCRIPTION || '';

        // 在选择模式中设置值
        if (hotspotData.DEVICE_ID) {
            select.val(hotspotData.DEVICE_ID);
        } else if (currentTitle) {
            // 尝试根据标题匹配设备
            var matchedDevice = deviceList.find(function (device) {
                return device.DEVICE_NAME === currentTitle;
            });
            if (matchedDevice) {
                select.val(matchedDevice.DEVICE_ID);
            }
        }

        layero.find('input[name="selectModeDescription"]').val(currentDescription);

        // 在填写模式中设置值
        layero.find('input[name="inputModeTitle"]').val(currentTitle);
        layero.find('input[name="inputModeDescription"]').val(currentDescription);

        // 重新渲染表单组件
        layui.form.render('select');
    }

    /**
     * 自动填充描述信息
     * @param {Object} layero 对话框jQuery对象
     * @param {string} inputTitle 输入的标题
     */
    function autoFillDescription(layero, inputTitle) {
        var deviceList = PanoramaCore.getDeviceList();
        if (!deviceList || !inputTitle) {
            return;
        }

        // 查找匹配的设备
        var matchedDevice = deviceList.find(function (device) {
            return device.DEVICE_NAME === inputTitle;
        });

        if (matchedDevice) {
            // 如果匹配到设备，自动填充描述
            var description = matchedDevice.DEVICE_NAME + ' ' +
                (matchedDevice.DEVICE_CODE || '') + ' ' +
                (matchedDevice.BATCH_NO || '');
            layero.find('input[name="editedDescription"]').val(description.trim());
        }
        // 如果没有匹配到设备，保持描述框为用户输入的内容，不自动清空
    }

    // ==================== 公共API ====================
    return {
        // 设备列表管理
        loadDeviceList: loadDeviceList,
        updateDeviceStatus: updateDeviceStatus,

        // 设备列表对话框
        showDeviceListDialog: showDeviceListDialog,
        initDeviceTable: initDeviceTable,

        // 文件上传下载
        initDialogUpload: initDialogUpload,
        initDialogDownload: initDialogDownload,

        // 热点编辑相关
        loadDeviceListForHotspot: loadDeviceListForHotspot,
        populateHotspotSelectOptions: populateHotspotSelectOptions,
        autoFillDescription: autoFillDescription
    };
})();

// ==================== 向后兼容性支持 ====================
// 保持原有函数的全局访问方式

window.loadDeviceList = PanoramaDevice.loadDeviceList;
window.updateDeviceStatus = PanoramaDevice.updateDeviceStatus;
window.showDeviceListDialog = PanoramaDevice.showDeviceListDialog;
window.initDeviceTable = PanoramaDevice.initDeviceTable;
window.initDialogUpload = PanoramaDevice.initDialogUpload;
window.initDialogDownload = PanoramaDevice.initDialogDownload;
window.loadDeviceListForHotspot = PanoramaDevice.loadDeviceListForHotspot;
window.populateHotspotSelectOptions = PanoramaDevice.populateHotspotSelectOptions;
window.autoFillDescription = PanoramaDevice.autoFillDescription;
