package com.cirpoint.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TreeNode {
	private String id;
	private String pid;
	private String name;
	private String saveData;
	private String tableNum;
	private String htmlData;
	private String signHtml;
	private int levelNum;
	private int sort;
	private String type;
	private String security;
	private String tableStatus;
	private String tableHeader;
	private String fileName;
	private String fileFormat;
	private String filePath;
	private List<TreeNode> children = new ArrayList<TreeNode>();

	public void add(TreeNode node) {
		children.add(node);
	}

	@Override
	public String toString() {
		return "TreeNode [id=" + id + ", pid=" + pid + ", name=" + name + ", children=" + children + "]";
	}

    /**
     * 从JSONObject创建TreeNode实例
     *
     * @param node JSON数据
     * @return TreeNode实例
     */
    public static TreeNode fromJson(JSONObject node) {
        TreeNode treeNode = new TreeNode();
        treeNode.setId(node.getStr("ID"));
        treeNode.setPid(node.getStr("PID"));
        treeNode.setName(node.getStr("NAME"));
        treeNode.setSecurity(node.getStr("SECURITY_NAME"));
        treeNode.setSaveData(node.getStr("SAVE_DATA", ""));
        treeNode.setTableNum(node.getStr("TABLE_NUM", ""));
        treeNode.setSignHtml(node.getStr("HTML_DATA", ""));
        treeNode.setHtmlData(node.getStr("HTML_DATA", ""));
        treeNode.setLevelNum(node.getInt("LEVEL_NUM"));
        treeNode.setSort(node.getInt("SORT", 1));
        treeNode.setTableHeader(node.getStr("TABLE_HEADER", "0"));
        treeNode.setType(node.getStr("TYPE"));
        treeNode.setTableStatus(node.getStr("TABLE_STATUS", "edit"));
        return treeNode;
    }

    /**
     * 将JSONArray转换为TreeNode列表
     *
     * @param array JSONArray数据
     * @return TreeNode列表
     */
    public static List<TreeNode> fromJsonArray(JSONArray array) {
        List<TreeNode> nodes = CollUtil.newArrayList();
        for (int i = 0; i < array.size(); i++) {
            nodes.add(fromJson(array.getJSONObject(i)));
        }
        return nodes;
    }
}
