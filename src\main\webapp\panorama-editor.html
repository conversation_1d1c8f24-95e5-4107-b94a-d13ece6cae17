<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全景图热点编辑系统</title>
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="static/lib/layui/css/layui.css">
    <link rel="stylesheet" href="panorama/css/panorama-editor.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="panorama-header">
        <div class="header-content">
            <div class="header-left">
                <h1 class="system-title">
                    <i class="layui-icon layui-icon-camera"></i>
                    全景图热点编辑系统
                </h1>
            </div>
            <div class="header-center">
                <div class="task-selector layui-form">
                    <label>当前任务：</label>
                    <select id="taskSelect" lay-filter="taskSelect" lay-search>
                        <option value="">请选择任务</option>
                    </select>
                </div>
            </div>
            <div class="header-right">
                <button type="button" class="layui-btn layui-btn-primary" id="taskManagementBtn">
                    <i class="layui-icon layui-icon-table"></i> 任务管理
                </button>
                <button type="button" class="layui-btn layui-btn-normal" id="createTaskBtn">
                    <i class="layui-icon layui-icon-add-1"></i> 创建任务
                </button>
                <button type="button" class="layui-btn layui-btn-warm" id="exportBtn" disabled>
                    <i class="layui-icon layui-icon-export"></i> 导出
                </button>
            </div>
        </div>
    </div>

    <!-- 主体内容区域 -->
    <div class="panorama-container">
        <div class="container-content" style="position: relative;">
            <div id="taskSelectionMask" class="task-selection-mask">
                <div class="mask-content">
                    <div class="mask-icon">
                        <i class="layui-icon layui-icon-notice" style="font-size: 48px; color: #667eea;"></i>
                    </div>
                    <div class="mask-title">欢迎使用全景图热点编辑系统</div>
                    <div class="mask-message">请先选择一个任务或创建新任务开始编辑</div>
                    <div class="mask-actions">
                        <button type="button" class="layui-btn layui-btn-normal" id="maskCreateTaskBtn">
                            <i class="layui-icon layui-icon-add-1"></i> 创建新任务
                        </button>
                    </div>
                </div>
            </div>

            <!-- 左侧功能区域 -->
            <div class="left-panel" id="leftPanel">
                <!-- 任务信息和文件上传合并区域 -->
                <div class="task-upload-combined">
                    <div class="card-header">
                        <h3><i class="layui-icon layui-icon-form"></i> 任务信息 & 文件上传</h3>
                    </div>
                    <div class="card-body">
                        <!-- 任务信息区域 -->
                        <div class="task-info-section">
                            <div class="task-info" id="taskInfo">
                                <div class="info-item">
                                    <span class="info-label">任务名称：</span>
                                    <span class="info-value" id="taskName">未选择任务</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">型号信息：</span>
                                    <span class="info-value" id="modelInfo">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">创建时间：</span>
                                    <span class="info-value" id="createTime">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">任务状态：</span>
                                    <span class="info-value" id="taskStatus">-</span>
                                </div>
                            </div>
                        </div>

                        <!-- 文件上传区域 -->
                        <div class="upload-section">
                            <div class="upload-item">
                                <span class="upload-label">全景图ZIP：</span>
                                <div class="upload-controls">
                                    <button type="button" class="layui-btn layui-btn-normal" id="uploadZipBtn" disabled>
                                        <i class="layui-icon layui-icon-upload"></i> 选择
                                    </button>
                                    <span class="upload-status" id="zipStatus">未上传</span>
                                </div>
                            </div>
                            <div class="upload-item">
                                <span class="upload-label">单机数据：</span>
                                <div class="upload-controls">
                                    <button type="button" class="layui-btn layui-btn-normal" id="viewDeviceBtn" disabled>
                                        <i class="layui-icon layui-icon-table"></i> 查看数据
                                    </button>
                                    <span class="upload-status" id="deviceStatus">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 热点编辑表格 -->
                <div class="table-card">
                    <div class="card-header">
                        <h3><i class="layui-icon layui-icon-edit"></i> 热点编辑</h3>
                        <div class="card-tools">
                            <div class="status-legend" style="margin-right: 10px; font-size: 12px; color: #666;">
                                <span class="status-dot status-unedited" style="margin: 0 3px 0 0;"></span>未编辑
                                <span class="status-dot status-edited" style="margin: 0 3px 0 8px;"></span>已编辑
                            </div>
                            <div class="node-info" id="currentNodeInfo" style="display: none;">
                                <span class="node-label">当前节点：</span>
                                <span class="node-value" id="currentNodeId">-</span>
                                <span class="node-indicator" id="nodeIndicator">
                                    <i class="layui-icon layui-icon-radio" style="color: #5FB878;"></i>
                                </span>
                            </div>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" id="refreshTableBtn">
                                <i class="layui-icon layui-icon-refresh"></i> 刷新
                            </button>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-warm" id="batchDeleteBtn">
                                <i class="layui-icon layui-icon-delete"></i> 批量删除
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="layui-hide" id="hotspotTable" lay-filter="hotspotTable"></table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 可拖拽分隔条 -->
            <div class="resize-handle" id="resizeHandle"></div>

            <!-- 右侧预览区域 -->
            <div class="right-panel" id="rightPanel">
                <div class="preview-card">
                    <div class="card-header">
                        <h3><i class="layui-icon layui-icon-camera-fill"></i> 全景图预览</h3>
                        <div class="card-tools">
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" id="refreshPreviewBtn">
                                <i class="layui-icon layui-icon-refresh"></i> 刷新预览
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="preview-container" id="previewContainer">
                            <div class="preview-placeholder">
                                <i class="layui-icon layui-icon-picture"></i>
                                <p>请先选择任务并上传全景图文件</p>
                            </div>
                            <iframe id="panoramaFrame" class="panorama-iframe" style="display: none;"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- 创建任务对话框 -->
    <div id="createTaskDialog" class="layui-hide">
        <form class="layui-form" lay-filter="createTaskForm" style="padding: 20px 20px 0 20px">
            <div class="layui-form-item">
                <label class="layui-form-label">任务名称</label>
                <div class="layui-input-block">
                    <input type="text" name="taskName" required lay-verify="required" 
                           placeholder="请输入任务名称" autocomplete="off" class="layui-input">
                </div>
            </div>
            <!-- 隐藏型号ID字段 -->
            <input type="hidden" name="modelId" id="modelId">
            <div class="layui-form-item">
                <label class="layui-form-label">型号名称</label>
                <div class="layui-input-block">
                    <select name="modelName" id="modelName" lay-filter="modelName" lay-verify="required" lay-search="">
                        <option value="">请选择型号</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-form-text" style="margin-bottom: 0;">
                <label class="layui-form-label">任务描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入任务描述" 
                              class="layui-textarea"></textarea>
                </div>
            </div>
        </form>
    </div>

    <!-- 热点编辑对话框 - 现在使用layer.tab，这个div保留用于兼容性 -->
    <div id="editHotspotDialog" class="layui-hide">
        <!-- 内容由layer.tab动态生成 -->
    </div>

    <!-- 编辑任务对话框 -->
    <div id="editTaskDialog" class="layui-hide">
        <form class="layui-form" lay-filter="editTaskForm" style="padding: 20px 20px 0 20px">
            <input type="hidden" name="taskId" id="editTaskId">
            <div class="layui-form-item">
                <label class="layui-form-label">任务名称</label>
                <div class="layui-input-block">
                    <input type="text" name="taskName" id="editTaskName" required lay-verify="required"
                           placeholder="请输入任务名称" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-form-text" style="margin-bottom: 0;">
                <label class="layui-form-label">任务描述</label>
                <div class="layui-input-block">
                    <textarea name="description" id="editTaskDescription" placeholder="请输入任务描述"
                              class="layui-textarea"></textarea>
                </div>
            </div>
        </form>
    </div>



    <!-- layer.tab样式修复 -->
    <style>
        /* 修复layer.tab激活标签的文字颜色问题 */
        .layui-layer-tab .layui-tab-title li.layui-this {
            color: #333 !important;
            background-color: #fff !important;
            border-bottom-color: #fff !important;
        }

        /* 未激活标签的样式 */
        .layui-layer-tab .layui-tab-title li {
            color: #666 !important;
            background-color: #f8f8f8 !important;
        }

        /* 悬停效果 */
        .layui-layer-tab .layui-tab-title li:hover {
            color: #409eff !important;
            background-color: #f0f8ff !important;
        }

        /* Tab内容区域样式 */
        .layui-layer-tab .layui-tab-content {
            background-color: #fff !important;
            padding: 0 !important;
        }

        /* 表单样式优化 */
        .layui-layer-tab .layui-form-label {
            width: 100px !important;
        }

        .layui-layer-tab .layui-input-block {
            margin-left: 130px !important;
        }
    </style>


    <!-- 引入JS文件 -->
    <script src="static/lib/jquery/jquery.min.js"></script>
    <script src="static/lib/layui/layui.js"></script>
    <!-- 添加fileDownload插件 -->
    <script src="static/lib/jquery/jquery.fileDownload.js"></script>
    <script src="panorama/js/panorama-editor/panorama-core.js"></script>
    <script src="panorama/js/panorama-editor/panorama-ui.js"></script>
    <script src="panorama/js/panorama-editor/panorama-task.js"></script>
    <script src="panorama/js/panorama-editor/panorama-device.js"></script>
    <script src="panorama/js/panorama-editor/panorama-hotspot.js"></script>
    <script src="panorama/js/panorama-editor/panorama-upload.js"></script>
    <script src="panorama/js/panorama-editor/panorama-preview.js"></script>
    <script src="panorama/js/panorama-editor/panorama-export.js"></script>
    <script src="panorama/js/panorama-editor/panorama-main.js"></script>

    <!-- 表格操作列模板 -->
    <script type="text/html" id="hotspotTableBar">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="locate">定位</a>
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
    </script>

    <!-- 状态圆点模板 -->
    <script type="text/html" id="statusDotTpl">
        <div style="text-align: center;">
            {{# if(d.IS_EDITED == 1) { }}
            <span class="status-dot status-edited" title="已编辑"></span>
            {{# } else { }}
            <span class="status-dot status-unedited" title="未编辑"></span>
            {{# } }}
        </div>
    </script>

    <!-- 编辑状态模板（保留备用） -->
    <script type="text/html" id="editStatusTpl">
        {{# if(d.IS_EDITED == 1){ }}
            <span class="layui-badge layui-bg-green">已编辑</span>
        {{# } else { }}
            <span class="layui-badge">未编辑</span>
        {{# } }}
    </script>

    <!-- 热点标题显示模板 -->
    <script type="text/html" id="titleDisplayTpl">
        {{ d.TITLE || '-' }}
    </script>

    <!-- 热点描述显示模板 -->
    <script type="text/html" id="descriptionDisplayTpl">
        {{ d.DESCRIPTION || '-' }}
    </script>

    <!-- 任务管理表格操作列模板 -->
    <script type="text/html" id="taskManagementTableBar">
        <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="export" title="导出">
            <i class="layui-icon layui-icon-export"></i> 导出
        </a>
        <a class="layui-btn layui-btn-xs" lay-event="edit" title="编辑">
            <i class="layui-icon layui-icon-edit"></i> 编辑
        </a>
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete" title="删除">
            <i class="layui-icon layui-icon-delete"></i> 删除
        </a>
    </script>

    <!-- 任务状态显示模板 -->
    <script type="text/html" id="taskStatusTpl">
        {{# if(d.STATUS == 0) { }}
        <span class="layui-badge">创建中</span>
        {{# } else if(d.STATUS == 1) { }}
        <span class="layui-badge layui-bg-green">已完成</span>
        {{# } else if(d.STATUS == 2) { }}
        <span class="layui-badge layui-bg-blue">已导出</span>
        {{# } else { }}
        <span class="layui-badge layui-bg-gray">未知</span>
        {{# } }}
    </script>

    <!-- 任务描述显示模板（支持长文本截断） -->
    <script type="text/html" id="taskDescriptionTpl">
        {{# if(d.DESCRIPTION && d.DESCRIPTION.trim() !== '') { }}
        <span title="{{ d.DESCRIPTION }}">
            {{# if(d.DESCRIPTION.length > 30) { }}
            {{ d.DESCRIPTION.substring(0, 30) }}...
            {{# } else { }}
            {{ d.DESCRIPTION }}
            {{# } }}
        </span>
        {{# } else { }}
        <span style="color: #999;">-</span>
        {{# } }}
    </script>
</body>
</html>
