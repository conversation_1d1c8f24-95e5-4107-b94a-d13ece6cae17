package com.cirpoint.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.util.Util;
import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 清理服务
 * 负责清理过期的文件和数据库记录
 */
@Slf4j
@Service
public class CleanupService extends ApplicationConfig {

	/**
	 * 每天凌晨3点执行清理任务
	 */
	@Scheduled(cron = "0 0 3 * * ?")
	public void cleanupExpiredData() {
		JSONObject result = cleanupExpiredDataWithResult(7);
		log.info("定时清理任务执行结果: {}", result);
	}

	/**
	 * 执行清理并返回详细结果
	 *
	 * @param days 清理多少天前的数据
	 * @return 清理结果
	 */
	public JSONObject cleanupExpiredDataWithResult(int days) {
		JSONObject result = new JSONObject();
		List<String> logs = new ArrayList<>();
		result.set("success", true);

		try {
			logs.add("开始清理过期的文件和数据库记录...");

			// 获取指定天数前的时间
			LocalDateTime cutoffDate = LocalDateTime.now().minusDays(days);
			String cutoffDateStr = cutoffDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			logs.add("清理" + days + "天前的数据（" + cutoffDateStr + "之前的数据）");

			// 清理过期的文件
			JSONObject fileCleanupResult = cleanupExpiredFiles(cutoffDate.toEpochSecond(java.time.ZoneOffset.of("+8")));
			logs.addAll(fileCleanupResult.getJSONArray("logs").toList(String.class));
			result.set("fileCleanup", fileCleanupResult);

			// 清理数据库记录
			JSONObject dbCleanupResult = cleanupDatabaseRecords(cutoffDateStr);
			logs.addAll(dbCleanupResult.getJSONArray("logs").toList(String.class));
			result.set("dbCleanup", dbCleanupResult);

			logs.add("清理任务完成");
		} catch (Exception e) {
			result.set("success", false);
			logs.add("执行清理任务时发生错误: " + e.getMessage());
			log.error("执行清理任务时发生错误", e);
		}

		result.set("logs", logs);
		return result;
	}

	/**
	 * 清理过期的文件
	 */
	private JSONObject cleanupExpiredFiles(long cutoffTime) {
		JSONObject result = new JSONObject();
		JSONArray details = new JSONArray();

		// 清理质量下载目录
		JSONObject qualityResult = cleanupDirectory(fileUploadPath + File.separator + "qualityDownload", cutoffTime);
		details.add(qualityResult);
		List<String> logs = new ArrayList<>(qualityResult.getJSONArray("logs").toList(String.class));

		// 清理确认下载目录
		JSONObject confirmResult = cleanupDirectory(fileUploadPath + File.separator + "confirmDownload", cutoffTime);
		details.add(confirmResult);
		logs.addAll(confirmResult.getJSONArray("logs").toList(String.class));

		result.set("details", details);
		result.set("logs", logs);
		return result;
	}

	/**
	 * 清理指定目录下的过期文件
	 */
	private JSONObject cleanupDirectory(String directoryPath, long cutoffTime) {
		JSONObject result = new JSONObject();
		List<String> logs = new ArrayList<>();
		result.set("path", directoryPath);

		File directory = new File(directoryPath);
		if (!directory.exists() || !directory.isDirectory()) {
			logs.add("目录不存在: " + directoryPath);
			result.set("logs", logs);
			return result;
		}

		// 遍历目录
		File[] userDirs = directory.listFiles();
		if (userDirs == null) {
			logs.add("无法列出目录内容: " + directoryPath);
			result.set("logs", logs);
			return result;
		}

		int deletedFiles = 0;
		int deletedDirs = 0;
		List<String> deletedFilesList = new ArrayList<>();
		List<String> deletedDirsList = new ArrayList<>();

		for (File userDir : userDirs) {
			if (!userDir.isDirectory()) {
				continue;
			}

			// 遍历用户目录
			File[] typeDirs = userDir.listFiles();
			if (typeDirs == null) {
				continue;
			}

			for (File typeDir : typeDirs) {
				if (!typeDir.isDirectory()) {
					continue;
				}

				// 遍历类型目录下的文件和目录
				File[] files = typeDir.listFiles();
				if (files == null) {
					continue;
				}

				for (File file : files) {
					if (file.lastModified() < cutoffTime * 1000) {
						if (FileUtil.del(file)) {
							deletedFiles++;
							deletedFilesList.add(file.getAbsolutePath());
							logs.add("已删除过期文件: " + file.getAbsolutePath());
						}
					}
				}

				// 如果类型目录为空，删除它
				if (typeDir.list() != null && Objects.requireNonNull(typeDir.list()).length == 0) {
					if (FileUtil.del(typeDir)) {
						deletedDirs++;
						deletedDirsList.add(typeDir.getAbsolutePath());
						logs.add("已删除空目录: " + typeDir.getAbsolutePath());
					}
				}
			}

			// 如果用户目录为空，删除它
			if (userDir.list() != null && Objects.requireNonNull(userDir.list()).length == 0) {
				if (FileUtil.del(userDir)) {
					deletedDirs++;
					deletedDirsList.add(userDir.getAbsolutePath());
					logs.add("已删除空目录: " + userDir.getAbsolutePath());
				}
			}
		}

		logs.add(String.format("目录[%s]清理完成: 删除了%d个文件和%d个空目录", directoryPath, deletedFiles, deletedDirs));

		result.set("deletedFiles", deletedFiles);
		result.set("deletedDirs", deletedDirs);
		result.set("deletedFilesList", deletedFilesList);
		result.set("deletedDirsList", deletedDirsList);
		result.set("logs", logs);
		return result;
	}

	/**
	 * 清理数据库记录
	 */
	private JSONObject cleanupDatabaseRecords(String cutoffTime) {
		JSONObject result = new JSONObject();
		List<String> logs = new ArrayList<>();
		JSONArray details = new JSONArray();

		try {
			// 删除质量下载记录
			String qualityDownloadSql = "DELETE FROM QUALITY_DOWNLOAD WHERE START_TIME < '" + cutoffTime + "'";
			int qualityDeletedCount = Util.postCommandSql(qualityDownloadSql);
			logs.add("删除了" + qualityDeletedCount + "条质量下载记录");
			details.add(new JSONObject()
					.set("table", "QUALITY_DOWNLOAD")
					.set("deletedCount", qualityDeletedCount));

			// 删除确认下载记录
			String confirmDownloadSql = "DELETE FROM CONFIRM_DOWNLOAD WHERE START_TIME < '" + cutoffTime + "'";
			int confirmDeletedCount = Util.postCommandSql(confirmDownloadSql);
			logs.add("删除了" + confirmDeletedCount + "条确认下载记录");
			details.add(new JSONObject()
					.set("table", "CONFIRM_DOWNLOAD")
					.set("deletedCount", confirmDeletedCount));

			result.set("success", true);
			result.set("totalDeletedCount", qualityDeletedCount + confirmDeletedCount);
		} catch (Exception e) {
			result.set("success", false);
			logs.add("清理数据库记录时发生错误: " + e.getMessage());
			log.error("清理数据库记录时发生错误", e);
		}

		result.set("details", details);
		result.set("logs", logs);
		return result;
	}

	/**
	 * 测试方法：清理指定天数前的数据
	 * 注意：此方法仅用于测试，请谨慎使用
	 */
	public void cleanupExpiredDataForTest() {
		JSONObject result = cleanupExpiredDataWithResult(7);
		log.info("测试清理任务执行结果: {}", result);
	}
} 