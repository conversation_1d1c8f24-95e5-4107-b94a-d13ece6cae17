<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>在线代码编辑器</title>
        <link rel="shortcut icon" href="favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="static/lib/layui/css/layui.css" />
        <link rel="stylesheet" href="static/lib/zTree/css/zTreeStyle/zTreeStyle.css" />
        <link rel="stylesheet" href="editor/css/vscode-tree.css" />
        <link rel="stylesheet" href="editor/css/main.css" />
        <link rel="stylesheet" href="editor/css/terminal.css" />
        <link rel="stylesheet" href="static/lib/viewerjs/viewer.min.css" />
    </head>
    <body class="layui-layout-body">
        <!-- 密码验证层 -->
        <div id="passwordLayer" class="password-layer">
            <div class="password-container">
                <h2>请输入访问密码</h2>
                <div class="layui-form">
                    <input type="password" id="password" class="layui-input" placeholder="请输入密码" />
                    <button type="button" class="layui-btn layui-btn-normal" onclick="verifyPassword()">确认</button>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div id="mainContent" class="layui-layout layui-layout-admin" style="display: none">
            <!-- 左侧资源管理器 -->
            <div class="layui-side">
                <div class="layui-side-scroll">
                    <!-- 左侧页签导航 -->
                    <div class="sidebar-tabs">
                        <div class="sidebar-tab" data-target="explorer" title="资源管理器">
                            <div class="sidebar-tab-content">
                                <i class="layui-icon layui-icon-file"></i>
                                <span class="sidebar-tab-text">资源管理器</span>
                            </div>
                        </div>
                        <div class="sidebar-tab" data-target="search" title="搜索">
                            <div class="sidebar-tab-content">
                                <i class="layui-icon layui-icon-search"></i>
                                <span class="sidebar-tab-text">搜索</span>
                            </div>
                        </div>

                        <div class="sidebar-tab" data-target="multiPathManager" title="多路径管理">
                            <div class="sidebar-tab-content">
                                <i class="layui-icon layui-icon-template"></i>
                                <span class="sidebar-tab-text">多路径管理</span>
                            </div>
                        </div>
                        <div class="sidebar-actions">
                            <button type="button" id="saveBtnContainer" class="layui-btn layui-btn-xs layui-btn-normal">
                                <i class="layui-icon layui-icon-success"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 侧边栏面板 -->
                    <div class="sidebar-panels">
                        <!-- 资源管理器面板 -->
                        <div class="sidebar-panel active" data-panel="explorer">
                            <div class="file-explorer-content">
                                <ul id="fileTree" class="ztree"></ul>
                            </div>
                        </div>

                        <!-- 搜索面板 -->
                        <div class="sidebar-panel" data-panel="search">
                            <div class="search-panel-header">
                                <span>搜索</span>
                                <button type="button" id="clearSearchScope" class="layui-btn layui-btn-xs layui-btn-primary" title="清除搜索范围" style="display: none">
                                    <i class="layui-icon layui-icon-close"></i>
                                </button>
                            </div>

                            <!-- 搜索范围提示 -->
                            <div id="searchScopeInfo" class="search-scope-info" style="display: none"></div>

                            <!-- 搜索框 -->
                            <div class="search-container">
                                <div class="search-input-group">
                                    <input type="text" id="searchInput" class="layui-input" autocomplete="off" placeholder="搜索文件..." />
                                    <button type="button" id="searchBtn" class="layui-btn layui-btn-normal">
                                        <i class="layui-icon layui-icon-search"></i>
                                    </button>
                                </div>
                                <div class="search-options">
                                    <input type="checkbox" id="searchContent" lay-skin="primary" title="搜索文件内容" />
                                    <label for="searchContent">搜索文件内容</label>
                                </div>
                                <div class="search-options file-type-container">
                                    <div class="file-type-label">文件类型:</div>
                                    <div class="file-type-checkboxes">
                                        <div class="file-type-checkbox">
                                            <input type="checkbox" id="fileTypeJava" class="file-type-cb" data-ext=".java" />
                                            <label for="fileTypeJava">Java</label>
                                        </div>
                                        <div class="file-type-checkbox">
                                            <input type="checkbox" id="fileTypeJs" class="file-type-cb" data-ext=".js" />
                                            <label for="fileTypeJs">JavaScript</label>
                                        </div>
                                        <div class="file-type-checkbox">
                                            <input type="checkbox" id="fileTypeHtml" class="file-type-cb" data-ext=".html" />
                                            <label for="fileTypeHtml">HTML</label>
                                        </div>
                                        <div class="file-type-checkbox">
                                            <input type="checkbox" id="fileTypeCss" class="file-type-cb" data-ext=".css" />
                                            <label for="fileTypeCss">CSS</label>
                                        </div>
                                        <div class="file-type-checkbox">
                                            <input type="checkbox" id="fileTypeXml" class="file-type-cb" data-ext=".xml" />
                                            <label for="fileTypeXml">XML</label>
                                        </div>
                                        <div class="file-type-checkbox">
                                            <input type="checkbox" id="fileTypeMd" class="file-type-cb" data-ext=".md" />
                                            <label for="fileTypeMd">Markdown</label>
                                        </div>
                                    </div>
                                    <div class="file-type-input-container">
                                        <input type="text" id="fileTypeFilter" class="layui-input" placeholder="自定义类型 (例如: .java,.js)" title="输入文件扩展名，多个类型用逗号分隔" />
                                        <div id="fileTypeError" class="file-type-error"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- 搜索结果区域 -->
                            <div class="search-results-container">
                                <div class="search-results-content">
                                    <ul id="searchResultsList" class="search-results-list"></ul>
                                </div>
                            </div>
                        </div>



                        <!-- 多路径管理面板 -->
                        <div class="sidebar-panel" data-panel="multiPathManager">
                            <div class="multi-path-panel-header">
                                <span>多路径管理</span>
                                <button type="button" id="addPathBtn" class="layui-btn layui-btn-xs layui-btn-normal">
                                    <i class="layui-icon layui-icon-add-1"></i>
                                </button>
                            </div>

                            <!-- 添加路径区域 -->
                            <div class="add-path-container">
                                <div class="add-path-input-group">
                                    <textarea id="newPathInput" class="layui-textarea" placeholder="输入新路径..."></textarea>
                                </div>
                            </div>

                            <!-- 路径列表区域 -->
                            <div class="path-list-container">
                                <div class="path-list-header">
                                    <span>路径列表</span>
                                </div>
                                <div class="path-list-content">
                                    <ul id="pathList" class="path-list"></ul>
                                </div>
                            </div>

                            <!-- 已启用路径列表区域 -->
                            <div class="enabled-path-list-container">
                                <div class="enabled-path-list-header">
                                    <span>已启用路径列表</span>
                                </div>
                                <div class="enabled-path-list-content">
                                    <ul id="enabledPathList" class="enabled-path-list"></ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 添加可拖动分隔线 -->
            <div id="resizer" class="resizer"></div>

            <!-- 右侧编辑区域 -->
            <div class="layui-body">
                <div class="editor-container">
                    <div class="editor-toolbar">
                        <div class="tabs-container">
                            <ul id="tabList" class="tabs-list"></ul>
                        </div>
                    </div>
                    <div id="editor"></div>
                </div>
            </div>
        </div>

        <!-- 右键菜单 -->
        <div id="rMenu" class="right-menu layui-hide">
            <ul>
                <li id="m_add_file"><i class="layui-icon layui-icon-file"></i> 新建文件</li>
                <li id="m_add_folder"><i class="layui-icon layui-icon-folder"></i> 新建文件夹</li>
                <li id="m_delete"><i class="layui-icon layui-icon-delete"></i> 删除</li>
                <li id="m_rename"><i class="layui-icon layui-icon-edit"></i> 重命名</li>
                <li id="m_copy_path"><i class="layui-icon layui-icon-link"></i> 复制路径</li>
                <li id="m_copy_abs_path"><i class="layui-icon layui-icon-link"></i> 复制绝对路径</li>
                <li id="m_download"><i class="layui-icon layui-icon-download-circle"></i> 下载</li>
                <li id="m_export"><i class="layui-icon layui-icon-export"></i> 导出</li>
                <li id="m_import"><i class="layui-icon layui-icon-upload-drag"></i> 导入</li>
                <li id="m_search_in_folder"><i class="layui-icon layui-icon-search"></i> 在文件夹中查找</li>
                <li id="m_open_in_terminal"><i class="layui-icon layui-icon-console"></i> 在终端中打开</li>
                <li id="m_properties"><i class="layui-icon layui-icon-about"></i> 属性</li>
            </ul>
        </div>

        <!-- 导入文件对话框 -->
        <div id="importDialog" class="layui-hide">
            <div class="layui-form" style="padding: 15px; box-sizing: border-box; width: 100%;">
                <div class="layui-form-item" style="margin: 0;">
                    <div class="layui-upload-drag" id="importUpload" style="width: 100%; height: 200px; display: flex; flex-direction: column; justify-content: center; align-items: center; background-color: #f9f9f9; border: 2px dashed #1E9FFF; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.05); transition: all 0.3s ease; margin-bottom: 15px; box-sizing: border-box;">
                        <i class="layui-icon layui-icon-upload" style="font-size: 56px; color: #1E9FFF; margin-bottom: 8px; text-shadow: 0 2px 4px rgba(30,159,255,0.2);"></i>
                        <p style="margin-top: 10px; font-size: 16px; color: #333; font-weight: 500;">点击上传或拖拽文件</p>
                        <p style="margin-top: 8px; font-size: 14px; color: #666; line-height: 1.4;">支持ZIP文件批量导入或多选单个文件上传</p>
                        <div class="layui-hide" id="uploadPreview"></div>
                    </div>
                </div>
                <div class="layui-form-item" style="margin-top: 10px; text-align: center;">
                    <button type="button" class="layui-btn layui-btn-normal" id="importSubmit" style="width: 120px; height: 38px; border-radius: 4px; font-size: 14px;"><i class="layui-icon layui-icon-upload"></i> 导入</button>
                    <button type="button" class="layui-btn layui-btn-primary" id="importCancel" style="width: 120px; height: 38px; border-radius: 4px; font-size: 14px; margin-left: 15px;"><i class="layui-icon layui-icon-close"></i> 取消</button>
                </div>
            </div>
        </div>

        <!-- 标签页右键菜单 -->
        <div id="tabRightMenu" class="right-menu layui-hide">
            <ul>
                <li id="tab_close"><i class="layui-icon layui-icon-close"></i> 关闭</li>
                <li id="tab_close_others"><i class="layui-icon layui-icon-close"></i> 关闭其他</li>
                <li id="tab_close_right"><i class="layui-icon layui-icon-right"></i> 关闭右侧</li>
                <li id="tab_close_left"><i class="layui-icon layui-icon-left"></i> 关闭左侧</li>
                <li id="tab_close_all"><i class="layui-icon layui-icon-close-fill"></i> 全部关闭</li>
            </ul>
        </div>

        <!-- 终端容器 -->
        <div id="terminalContainer" class="terminal-container">
            <div class="terminal-header">
                <div class="terminal-title">终端</div>
                <div class="terminal-actions">
                    <button type="button" id="terminateCommand" class="terminal-action-btn" title="终止当前命令">
                        <i class="layui-icon layui-icon-close"></i> 终止
                    </button>
                    <button type="button" id="clearTerminal" class="terminal-action-btn" title="清空终端">
                        <i class="layui-icon layui-icon-refresh"></i> 清空
                    </button>
                    <button type="button" id="closeTerminal" class="terminal-action-btn" title="关闭终端">
                        <i class="layui-icon layui-icon-down"></i> 关闭
                    </button>
                </div>
            </div>
            <div id="terminalOutput" class="terminal-content"></div>
            <div class="terminal-input-area">
                <span class="terminal-prompt">~</span>
                <input type="text" id="terminalInput" class="terminal-input" placeholder="输入命令..." />
            </div>
        </div>

        <!-- 终端切换按钮 -->
        <div id="terminalToggleBtn" class="terminal-toggle-btn" title="打开终端">
            <i class="layui-icon layui-icon-console"></i>
        </div>

        <!-- 引入必要的JS文件 -->
        <script src="static/lib/jquery/jquery.min.js"></script>
        <script src="static/lib/jquery/jquery-ui.min.js"></script>
        <script src="static/lib/layui/layui.js"></script>
        <script src="static/lib/zTree/js/jquery.ztree.all.min.js"></script>
        <script src="static/lib/sql-formatter/sql-formatter.min.js"></script>
        <script src="static/lib/viewerjs/viewer.min.js"></script>
        <script src="static/lib/monaco-editor-0.34/min/vs/loader.js"></script>
        <script src="static/lib/crypto-js/crypto-js.min.js"></script>

        <!-- 引入VSCode图标支持 -->
        <script src="editor/js/vscode-icons.js"></script>

        <!-- 引入拆分后的JS文件 -->
        <script src="editor/js/auth.js"></script>
        <script src="editor/js/multi-path-manager.js"></script>
        <script src="editor/js/ui-utils.js"></script>
        <script src="editor/js/editor-manager.js"></script>
        <script src="editor/js/tab-manager.js"></script>
        <script src="editor/js/tree-manager.js"></script>
        <script src="editor/js/search-manager.js"></script>
        <script src="editor/js/terminal-manager.js"></script>
        <script src="editor/js/main-app.js"></script>
    </body>
</html>
