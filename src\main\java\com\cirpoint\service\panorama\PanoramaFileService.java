package com.cirpoint.service.panorama;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.service.ApplicationConfig;
import com.cirpoint.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * 全景图文件处理服务
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@Slf4j
@Service
public class PanoramaFileService extends ApplicationConfig {

    @Autowired
    private PanoramaHotspotService panoramaHotspotService;

    /**
     * 上传全景图ZIP包
     */
    public JSONObject uploadPanoramaZip(Long taskId, MultipartFile file) throws IOException {
        try {
            // 创建任务专用目录
            String taskDir = fileUploadPath + File.separator + "panorama" + File.separator + taskId;
            FileUtil.mkdir(taskDir);

            // 保存ZIP文件
            String zipFileName = "panorama_" + System.currentTimeMillis() + ".zip";
            String zipFilePath = taskDir + File.separator + zipFileName;
            file.transferTo(new File(zipFilePath));

            // 解压ZIP文件
            String extractPath = taskDir + File.separator + "extracted";
            ZipUtil.unzip(zipFilePath, extractPath);

            // 执行预置文件替换操作
            replacePresetFiles(extractPath);

            // 查找pano.xml文件
            File panoXmlFile = PanoramaFileUtils.findPanoXmlFile(new File(extractPath));
            if (panoXmlFile == null) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "未找到pano.xml文件");
            }

            // 解析XML并保存热点信息
            parseAndSaveHotspots(taskId, panoXmlFile);

            // 更新任务信息
            String updateSql = "UPDATE PANORAMA_TASK SET ZIP_FILE_PATH = '" + zipFilePath +
                    "', EXTRACT_PATH = '" + extractPath + "', UPDATE_TIME = SYSDATE WHERE TASK_ID = " + taskId;
            Util.postCommandSql(updateSql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "ZIP文件上传成功")
                    .set("data", JSONUtil.createObj()
                            .set("zipPath", zipFilePath)
                            .set("extractPath", extractPath));
        } catch (Exception e) {
            log.error("上传ZIP文件失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "上传ZIP文件失败: " + e.getMessage());
        }
    }

    /**
     * 解析XML并保存热点信息
     */
    private void parseAndSaveHotspots(Long taskId, File panoXmlFile) throws Exception {
        SAXReader reader = new SAXReader();
        Document document = reader.read(panoXmlFile);
        Element root = document.getRootElement();

        // 查找所有panorama节点
        List<Element> panoramas = root.elements("panorama");

        panoramaHotspotService.parseAndSaveHotspots(taskId, panoramas);
    }

    /**
     * 获取预置文件的输入流
     * 优先从classpath读取（JAR环境），降级到文件系统读取（开发环境）
     *
     * @param fileName 预置文件名
     * @return 文件输入流
     * @throws IOException 如果文件不存在或读取失败
     * @throws IllegalArgumentException 如果文件名为空或无效
     */
    private InputStream getPresetFileInputStream(String fileName) throws IOException {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("预置文件名不能为空");
        }
        // 1. 优先尝试从classpath读取（适用于JAR环境）
        try {
            String classpathPath = "panorama/replace/" + fileName;
            ClassPathResource resource = new ClassPathResource(classpathPath);
            if (resource.exists()) {
                log.debug("从classpath读取预置文件: {}", classpathPath);
                return resource.getInputStream();
            }
        } catch (Exception e) {
            log.debug("从classpath读取预置文件失败: {}, 错误: {}", fileName, e.getMessage());
        }

        // 2. 尝试从META-INF/resources读取（Spring Boot打包后的路径）
        try {
            String metaInfPath = "META-INF/resources/panorama/replace/" + fileName;
            ClassPathResource resource = new ClassPathResource(metaInfPath);
            if (resource.exists()) {
                log.debug("从META-INF读取预置文件: {}", metaInfPath);
                return resource.getInputStream();
            }
        } catch (Exception e) {
            log.debug("从META-INF读取预置文件失败: {}, 错误: {}", fileName, e.getMessage());
        }

        // 3. 降级到文件系统读取（适用于开发环境）
        try {
            String fileSystemPath = System.getProperty("user.dir") + File.separator +
                    "src" + File.separator + "main" + File.separator + "webapp" + File.separator +
                    "panorama" + File.separator + "replace" + File.separator + fileName;

            File file = new File(fileSystemPath);
            if (file.exists()) {
                log.debug("从文件系统读取预置文件: {}", fileSystemPath);
                return new FileInputStream(file);
            }
        } catch (Exception e) {
            log.debug("从文件系统读取预置文件失败: {}, 错误: {}", fileName, e.getMessage());
        }

        // 4. 尝试从当前webapp目录读取（Tomcat环境）
        try {
            String webappPath = System.getProperty("user.dir") + File.separator +
                    "panorama" + File.separator + "replace" + File.separator + fileName;
            File file = new File(webappPath);
            if (file.exists()) {
                log.debug("从webapp目录读取预置文件: {}", webappPath);
                return new FileInputStream(file);
            }
        } catch (Exception e) {
            log.debug("从webapp目录读取预置文件失败: {}, 错误: {}", fileName, e.getMessage());
        }

        // 5. 如果都失败，抛出异常
        throw new IOException("无法找到预置文件: " + fileName +
                " (已尝试classpath、META-INF、文件系统和webapp路径)");
    }

    /**
     * 替换预置文件
     * 用项目中的预置文件覆盖解压目录中的对应文件
     *
     * @param extractPath 解压目录路径
     */
    private void replacePresetFiles(String extractPath) {
        if (extractPath == null || extractPath.trim().isEmpty()) {
            log.error("解压目录路径不能为空");
            return;
        }

        File extractDir = new File(extractPath);
        if (!extractDir.exists() || !extractDir.isDirectory()) {
            log.error("解压目录不存在或不是有效目录: {}", extractPath);
            return;
        }

        try {
            // 替换pano2vr_player.js文件
            replaceFile("pano2vr_player.js", extractPath, this::findPano2VRPlayerFile);

            // 替换index.html文件
            replaceFile("index.html", extractPath, this::findIndexHtmlFile);

            // 替换skin.js文件
            replaceFile("skin.js", extractPath, this::findSkinJsFile);

        } catch (Exception e) {
            log.error("替换预置文件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 替换单个文件的通用方法（支持InputStream源）
     */
    private void replaceFile(String fileName, String extractPath,
                           java.util.function.Function<File, File> fileFinder) {
        InputStream sourceInputStream = null;
        File tempFile = null;

        try {
            // 获取预置文件的输入流
            sourceInputStream = getPresetFileInputStream(fileName);

            // 在解压目录中查找目标文件
            File targetFile = fileFinder.apply(new File(extractPath));
            if (targetFile == null) {
                log.warn("解压目录中未找到{}文件，将在根目录创建: {}", fileName, extractPath);
                // 即使未找到对应文件，也要在解压根目录创建该文件以保证程序正常运行
                targetFile = new File(extractPath, fileName);
            }

            // 创建临时文件来处理InputStream
            tempFile = File.createTempFile("preset_" + fileName, ".tmp");

            // 将InputStream内容写入临时文件
            try (FileOutputStream tempOut = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = sourceInputStream.read(buffer)) != -1) {
                    tempOut.write(buffer, 0, bytesRead);
                }
            }

            // 确保目标文件的父目录存在
            File parentDir = targetFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            // 执行文件替换
            FileUtil.copy(tempFile, targetFile, true);

            // 验证替换结果
            if (!targetFile.exists() || targetFile.length() == 0) {
                log.error("{}文件替换失败", fileName);
            } else {
                log.info("成功替换预置文件: {} -> {}", fileName, targetFile.getAbsolutePath());
            }

        } catch (IOException e) {
            log.error("替换{}文件失败: {}", fileName, e.getMessage(), e);
        } catch (Exception e) {
            log.error("替换{}文件时发生未知错误: {}", fileName, e.getMessage(), e);
        } finally {
            // 确保资源清理
            if (sourceInputStream != null) {
                try {
                    sourceInputStream.close();
                } catch (IOException e) {
                    log.warn("关闭输入流失败: {}", e.getMessage());
                }
            }
            if (tempFile != null && tempFile.exists()) {
                if (!tempFile.delete()) {
                    log.warn("删除临时文件失败: {}", tempFile.getAbsolutePath());
                }
            }
        }
    }

    /**
     * 在指定目录中递归查找pano2vr_player.js文件
     *
     * @param dir 搜索目录
     * @return 找到的文件，如果未找到则返回null
     */
    private File findPano2VRPlayerFile(File dir) {
        return findFileByName(dir, "pano2vr_player.js");
    }

    /**
     * 在指定目录中递归查找index.html文件
     *
     * @param dir 搜索目录
     * @return 找到的文件，如果未找到则返回null
     */
    private File findIndexHtmlFile(File dir) {
        return findFileByName(dir, "index.html");
    }

    /**
     * 在指定目录中递归查找skin.js文件
     *
     * @param dir 搜索目录
     * @return 找到的文件，如果未找到则返回null
     */
    private File findSkinJsFile(File dir) {
        return findFileByName(dir, "skin.js");
    }

    /**
     * 在指定目录中递归查找指定名称的文件
     *
     * @param dir 搜索目录
     * @param fileName 文件名
     * @return 找到的文件，如果未找到则返回null
     */
    private File findFileByName(File dir, String fileName) {
        return PanoramaFileUtils.findFileByName(dir, fileName);
    }
}
