package com.cirpoint.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.util.Util;
import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 第一期服务类
 */
@Slf4j
@Service
public class FirstPhaseService extends ApplicationConfig {

	/**
	 * 导出Excel文件
	 *
	 * @param type 统计类型
	 * @param rsId 记录ID
	 * @return 生成的Excel文件
	 */
	public File exportExcel(String type, String rsId) {
		File excelFile = null;
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
		try {
			// 获取文件类型
			JSONArray list = Util.postTwx("Thing.Fn.SystemDic",
					"getFileTypeByName", JSONUtil.createObj().set("type", "质量统计清单").set("name", type));
			String entype = list.getJSONObject(0).getStr("result");
			if (!entype.isEmpty()) {
				String fileName = entype + ".xlsx";
				File file = new File(excelTplPath + "\\" + fileName);
				excelFile = new File(thisTempPath, type + ".xlsx");
				// 复制模板到新文件
				FileUtil.copy(file, excelFile, true);

				// 获取数据
				JSONArray dataList = Util.postTwx("Thing.Statictics.Util",
						"QueryData", JSONUtil.createObj().set("type", entype).set("rsId", rsId));

				// 写入数据
				try (XSSFWorkbook workbook = new XSSFWorkbook(Files.newInputStream(excelFile.toPath()))) {
					for (int i = 0; i < dataList.size(); i++) {
						JSONObject data = dataList.getJSONObject(i);
						generateExcel(workbook, data, entype);
					}

					// 保存文件
					try (FileOutputStream fos = new FileOutputStream(excelFile)) {
						workbook.write(fos);
					}
				}
			}
		} catch (Exception e) {
			log.error("导出Excel失败", e);
			throw new RuntimeException("导出Excel失败：" + e.getMessage());
		}
		return excelFile;
	}

	// 其他辅助方法保持不变，只是移动到Service类中并设置为private
	public static String getMapValue(JSONObject map, String key) {
		String valueStr;
		if ("ROWNUM".equals(key) || "ROWNO".equals(key)) {
			valueStr = map.getInt(key).toString();
		} else {
			valueStr = map.getStr(key);
		}
		return valueStr;
	}

	public static void generateExcel(XSSFWorkbook workbook, JSONObject map, String type) {
		XSSFSheet sheet;
		CellStyle cellStyle;
		try {
			cellStyle = workbook.createCellStyle();
			//设置居中
			cellStyle.setAlignment(HorizontalAlignment.CENTER);
			cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
			//设置边框样式
			cellStyle.setBorderBottom(BorderStyle.THIN);
			cellStyle.setBorderLeft(BorderStyle.THIN);
			cellStyle.setBorderTop(BorderStyle.THIN);
			cellStyle.setBorderRight(BorderStyle.THIN);

			sheet = workbook.getSheetAt(0);
			int LastRowNum = sheet.getLastRowNum();
			XSSFRow row = sheet.createRow(LastRowNum + 1);
			String rownum = getMapValue(map, "ROWNUM");// 序号
			XSSFCell cell0 = row.createCell(0);
			cell0.setCellType(CellType.STRING);
			cell0.setCellStyle(cellStyle);
			cell0.setCellValue(rownum);
			if ("1".equals(type)) {
				String val4 = getMapValue(map, "VAL4");//
				XSSFCell cell1 = row.createCell(1);
				cell1.setCellType(CellType.STRING);
				cell1.setCellStyle(cellStyle);
				cell1.setCellValue(val4);

				String val5 = getMapValue(map, "VAL5");//
				XSSFCell cell2 = row.createCell(2);
				cell2.setCellType(CellType.STRING);
				cell2.setCellStyle(cellStyle);
				cell2.setCellValue(val5);

				String val6 = getMapValue(map, "VAL6");//
				XSSFCell cell3 = row.createCell(3);
				cell3.setCellType(CellType.STRING);
				cell3.setCellStyle(cellStyle);
				cell3.setCellValue(val6);

				String val7 = getMapValue(map, "VAL7");//
				XSSFCell cell4 = row.createCell(4);
				cell4.setCellType(CellType.STRING);
				cell4.setCellStyle(cellStyle);
				cell4.setCellValue(val7);

				String val8 = getMapValue(map, "VAL8");//
				XSSFCell cell5 = row.createCell(5);
				cell5.setCellType(CellType.STRING);
				cell5.setCellStyle(cellStyle);
				cell5.setCellValue(val8);

				String val9 = getMapValue(map, "VAL9");//
				XSSFCell cell6 = row.createCell(6);
				cell6.setCellType(CellType.STRING);
				cell6.setCellStyle(cellStyle);
				cell6.setCellValue(val9);

				String val10 = getMapValue(map, "VAL10");//
				XSSFCell cell7 = row.createCell(7);
				cell7.setCellType(CellType.STRING);
				cell7.setCellStyle(cellStyle);
				cell7.setCellValue(val10);

				String val11 = getMapValue(map, "VAL11");//
				XSSFCell cell8 = row.createCell(8);
				cell8.setCellType(CellType.STRING);
				cell8.setCellStyle(cellStyle);
				cell8.setCellValue(val11);

				String val12 = getMapValue(map, "VAL12");//
				XSSFCell cell9 = row.createCell(9);
				cell9.setCellType(CellType.STRING);
				cell9.setCellStyle(cellStyle);
				cell9.setCellValue(val12);

				String val13 = getMapValue(map, "VAL13");//
				XSSFCell cell10 = row.createCell(10);
				cell10.setCellType(CellType.STRING);
				cell10.setCellStyle(cellStyle);
				cell10.setCellValue(val13);

				String val14 = getMapValue(map, "VAL14");//
				XSSFCell cell11 = row.createCell(11);
				cell11.setCellType(CellType.STRING);
				cell11.setCellStyle(cellStyle);
				cell11.setCellValue(val14);

				String val15 = getMapValue(map, "VAL15");//
				XSSFCell cell12 = row.createCell(12);
				cell12.setCellType(CellType.STRING);
				cell12.setCellStyle(cellStyle);
				cell12.setCellValue(val15);

				String val16 = getMapValue(map, "VAL16");//
				XSSFCell cell13 = row.createCell(13);
				cell13.setCellType(CellType.STRING);
				cell13.setCellStyle(cellStyle);
				cell13.setCellValue(val16);

				String val17 = getMapValue(map, "VAL17");//
				XSSFCell cell14 = row.createCell(14);
				cell14.setCellType(CellType.STRING);
				cell14.setCellStyle(cellStyle);
				cell14.setCellValue(val17);

				String val18 = getMapValue(map, "VAL18");//
				XSSFCell cell15 = row.createCell(15);
				cell15.setCellType(CellType.STRING);
				cell15.setCellStyle(cellStyle);
				cell15.setCellValue(val18);

				String val19 = getMapValue(map, "VAL19");//
				XSSFCell cell16 = row.createCell(16);
				cell16.setCellType(CellType.STRING);
				cell16.setCellStyle(cellStyle);
				cell16.setCellValue(val19);

				String val20 = getMapValue(map, "VAL20");//
				XSSFCell cell17 = row.createCell(17);
				cell17.setCellType(CellType.STRING);
				cell17.setCellStyle(cellStyle);
				cell17.setCellValue(val20);

				String val21 = getMapValue(map, "VAL21");//
				XSSFCell cell18 = row.createCell(18);
				cell18.setCellType(CellType.STRING);
				cell18.setCellStyle(cellStyle);
				cell18.setCellValue(val21);
			} else if ("2".equals(type)) {
				String val4 = getMapValue(map, "VAL4");//
				XSSFCell cell1 = row.createCell(1);
				cell1.setCellType(CellType.STRING);
				cell1.setCellStyle(cellStyle);
				cell1.setCellValue(val4);

				String val5 = getMapValue(map, "VAL5");//
				XSSFCell cell2 = row.createCell(2);
				cell2.setCellType(CellType.STRING);
				cell2.setCellStyle(cellStyle);
				cell2.setCellValue(val5);

				String val6 = getMapValue(map, "VAL6");//
				XSSFCell cell3 = row.createCell(3);
				cell3.setCellType(CellType.STRING);
				cell3.setCellStyle(cellStyle);
				cell3.setCellValue(val6);

				String val7 = getMapValue(map, "VAL7");//
				XSSFCell cell4 = row.createCell(4);
				cell4.setCellType(CellType.STRING);
				cell4.setCellStyle(cellStyle);
				cell4.setCellValue(val7);

				String val8 = getMapValue(map, "VAL8");//
				XSSFCell cell5 = row.createCell(5);
				cell5.setCellType(CellType.STRING);
				cell5.setCellStyle(cellStyle);
				cell5.setCellValue(val8);

				String val9 = getMapValue(map, "VAL9");//
				XSSFCell cell6 = row.createCell(6);
				cell6.setCellType(CellType.STRING);
				cell6.setCellStyle(cellStyle);
				cell6.setCellValue(val9);

				String val10 = getMapValue(map, "VAL10");//
				XSSFCell cell7 = row.createCell(7);
				cell7.setCellType(CellType.STRING);
				cell7.setCellStyle(cellStyle);
				cell7.setCellValue(val10);

				String val11 = getMapValue(map, "VAL11");//
				XSSFCell cell8 = row.createCell(8);
				cell8.setCellType(CellType.STRING);
				cell8.setCellStyle(cellStyle);
				cell8.setCellValue(val11);

				String val12 = getMapValue(map, "VAL12");//
				XSSFCell cell9 = row.createCell(9);
				cell9.setCellType(CellType.STRING);
				cell9.setCellStyle(cellStyle);
				cell9.setCellValue(val12);

				String val13 = getMapValue(map, "VAL13");//
				XSSFCell cell10 = row.createCell(10);
				cell10.setCellType(CellType.STRING);
				cell10.setCellStyle(cellStyle);
				cell10.setCellValue(val13);

				String val14 = getMapValue(map, "VAL14");//
				XSSFCell cell11 = row.createCell(11);
				cell11.setCellType(CellType.STRING);
				cell11.setCellStyle(cellStyle);
				cell11.setCellValue(val14);

				String val15 = getMapValue(map, "VAL15");//
				XSSFCell cell12 = row.createCell(12);
				cell12.setCellType(CellType.STRING);
				cell12.setCellStyle(cellStyle);
				cell12.setCellValue(val15);

				String val16 = getMapValue(map, "VAL16");//
				XSSFCell cell13 = row.createCell(13);
				cell13.setCellType(CellType.STRING);
				cell13.setCellStyle(cellStyle);
				cell13.setCellValue(val16);

				String val17 = getMapValue(map, "VAL17");//
				XSSFCell cell14 = row.createCell(14);
				cell14.setCellType(CellType.STRING);
				cell14.setCellStyle(cellStyle);
				cell14.setCellValue(val17);

				String val18 = getMapValue(map, "VAL18");//
				XSSFCell cell15 = row.createCell(15);
				cell15.setCellType(CellType.STRING);
				cell15.setCellStyle(cellStyle);
				cell15.setCellValue(val18);

				String val19 = getMapValue(map, "VAL19");//
				XSSFCell cell16 = row.createCell(16);
				cell16.setCellType(CellType.STRING);
				cell16.setCellStyle(cellStyle);
				cell16.setCellValue(val19);

				String val20 = getMapValue(map, "VAL20");//
				XSSFCell cell17 = row.createCell(17);
				cell17.setCellType(CellType.STRING);
				cell17.setCellStyle(cellStyle);
				cell17.setCellValue(val20);

				String val21 = getMapValue(map, "VAL21");//
				XSSFCell cell18 = row.createCell(18);
				cell18.setCellType(CellType.STRING);
				cell18.setCellStyle(cellStyle);
				cell18.setCellValue(val21);

				String val22 = getMapValue(map, "VAL22");//
				XSSFCell cell19 = row.createCell(19);
				cell19.setCellType(CellType.STRING);
				cell19.setCellStyle(cellStyle);
				cell19.setCellValue(val22);

				String val23 = getMapValue(map, "VAL23");//
				XSSFCell cell20 = row.createCell(20);
				cell20.setCellType(CellType.STRING);
				cell20.setCellStyle(cellStyle);
				cell20.setCellValue(val23);

				String val24 = getMapValue(map, "VAL24");//
				XSSFCell cell21 = row.createCell(21);
				cell21.setCellType(CellType.STRING);
				cell21.setCellStyle(cellStyle);
				cell21.setCellValue(val24);

				String val25 = getMapValue(map, "VAL25");//
				XSSFCell cell22 = row.createCell(22);
				cell22.setCellType(CellType.STRING);
				cell22.setCellStyle(cellStyle);
				cell22.setCellValue(val25);

				String val30 = getMapValue(map, "VAL30");//
				XSSFCell cell23 = row.createCell(23);
				cell23.setCellType(CellType.STRING);
				cell23.setCellStyle(cellStyle);
				cell23.setCellValue(val30);

				String val31 = getMapValue(map, "VAL31");//
				XSSFCell cell24 = row.createCell(24);
				cell24.setCellType(CellType.STRING);
				cell24.setCellStyle(cellStyle);
				cell24.setCellValue(val31);

				String val26 = getMapValue(map, "VAL26");//
				XSSFCell cell25 = row.createCell(25);
				cell25.setCellType(CellType.STRING);
				cell25.setCellStyle(cellStyle);
				cell25.setCellValue(val26);

				String val27 = getMapValue(map, "VAL27");//
				XSSFCell cell26 = row.createCell(26);
				cell26.setCellType(CellType.STRING);
				cell26.setCellStyle(cellStyle);
				cell26.setCellValue(val27);

				String val28 = getMapValue(map, "VAL28");//
				XSSFCell cell27 = row.createCell(27);
				cell27.setCellType(CellType.STRING);
				cell27.setCellStyle(cellStyle);
				cell27.setCellValue(val28);

				String val29 = getMapValue(map, "VAL29");//
				XSSFCell cell28 = row.createCell(28);
				cell28.setCellType(CellType.STRING);
				cell28.setCellStyle(cellStyle);
				cell28.setCellValue(val29);
			} else if ("3".equals(type)) {
				String val5 = getMapValue(map, "VAL5");//
				XSSFCell cell1 = row.createCell(1);
				cell1.setCellType(CellType.STRING);
				cell1.setCellStyle(cellStyle);
				cell1.setCellValue(val5);

				String val6 = getMapValue(map, "VAL6");//
				XSSFCell cell2 = row.createCell(2);
				cell2.setCellType(CellType.STRING);
				cell2.setCellStyle(cellStyle);
				cell2.setCellValue(val6);

				String val7 = getMapValue(map, "VAL7");//
				XSSFCell cell3 = row.createCell(3);
				cell3.setCellType(CellType.STRING);
				cell3.setCellStyle(cellStyle);
				cell3.setCellValue(val7);

				String val8 = getMapValue(map, "VAL8");//
				XSSFCell cell4 = row.createCell(4);
				cell4.setCellType(CellType.STRING);
				cell4.setCellStyle(cellStyle);
				cell4.setCellValue(val8);

				String val9 = getMapValue(map, "VAL9");//
				XSSFCell cell5 = row.createCell(5);
				cell5.setCellType(CellType.STRING);
				cell5.setCellStyle(cellStyle);
				cell5.setCellValue(val9);

				String val10 = getMapValue(map, "VAL10");//
				XSSFCell cell6 = row.createCell(6);
				cell6.setCellType(CellType.STRING);
				cell6.setCellStyle(cellStyle);
				cell6.setCellValue(val10);

				String val11 = getMapValue(map, "VAL11");//
				XSSFCell cell7 = row.createCell(7);
				cell7.setCellType(CellType.STRING);
				cell7.setCellStyle(cellStyle);
				cell7.setCellValue(val11);

				String val12 = getMapValue(map, "VAL12");//
				XSSFCell cell8 = row.createCell(8);
				cell8.setCellType(CellType.STRING);
				cell8.setCellStyle(cellStyle);
				cell8.setCellValue(val12);

				String val13 = getMapValue(map, "VAL13");//
				XSSFCell cell9 = row.createCell(9);
				cell9.setCellType(CellType.STRING);
				cell9.setCellStyle(cellStyle);
				cell9.setCellValue(val13);

				String val14 = getMapValue(map, "VAL14");//
				XSSFCell cell10 = row.createCell(10);
				cell10.setCellType(CellType.STRING);
				cell10.setCellStyle(cellStyle);
				cell10.setCellValue(val14);

				String val15 = getMapValue(map, "VAL15");//
				XSSFCell cell11 = row.createCell(11);
				cell11.setCellType(CellType.STRING);
				cell11.setCellStyle(cellStyle);
				cell11.setCellValue(val15);

				String val16 = getMapValue(map, "VAL16");//
				XSSFCell cell12 = row.createCell(12);
				cell12.setCellType(CellType.STRING);
				cell12.setCellStyle(cellStyle);
				cell12.setCellValue(val16);

				String val17 = getMapValue(map, "VAL17");//
				XSSFCell cell13 = row.createCell(13);
				cell13.setCellType(CellType.STRING);
				cell13.setCellStyle(cellStyle);
				cell13.setCellValue(val17);

				String val18 = getMapValue(map, "VAL18");//
				XSSFCell cell14 = row.createCell(14);
				cell14.setCellType(CellType.STRING);
				cell14.setCellStyle(cellStyle);
				cell14.setCellValue(val18);

				String val19 = getMapValue(map, "VAL19");//
				XSSFCell cell15 = row.createCell(15);
				cell15.setCellType(CellType.STRING);
				cell15.setCellStyle(cellStyle);
				cell15.setCellValue(val19);

				String val20 = getMapValue(map, "VAL20");//
				XSSFCell cell16 = row.createCell(16);
				cell16.setCellType(CellType.STRING);
				cell16.setCellStyle(cellStyle);
				cell16.setCellValue(val20);

			} else if ("4".equals(type)) {
				String val5 = getMapValue(map, "VAL5");//
				XSSFCell cell2 = row.createCell(1);
				cell2.setCellType(CellType.STRING);
				cell2.setCellStyle(cellStyle);
				cell2.setCellValue(val5);

				String val6 = getMapValue(map, "VAL6");//
				XSSFCell cell3 = row.createCell(2);
				cell3.setCellType(CellType.STRING);
				cell3.setCellStyle(cellStyle);
				cell3.setCellValue(val6);

				String val7 = getMapValue(map, "VAL7");//
				XSSFCell cell4 = row.createCell(3);
				cell4.setCellType(CellType.STRING);
				cell4.setCellStyle(cellStyle);
				cell4.setCellValue(val7);

				String val8 = getMapValue(map, "VAL8");//
				XSSFCell cell5 = row.createCell(4);
				cell5.setCellType(CellType.STRING);
				cell5.setCellStyle(cellStyle);
				cell5.setCellValue(val8);

				String val9 = getMapValue(map, "VAL9");//
				XSSFCell cell6 = row.createCell(5);
				cell6.setCellType(CellType.STRING);
				cell6.setCellStyle(cellStyle);
				cell6.setCellValue(val9);

				String val10 = getMapValue(map, "VAL10");//
				XSSFCell cell7 = row.createCell(6);
				cell7.setCellType(CellType.STRING);
				cell7.setCellStyle(cellStyle);
				cell7.setCellValue(val10);

				String val11 = getMapValue(map, "VAL11");//
				XSSFCell cell8 = row.createCell(7);
				cell8.setCellType(CellType.STRING);
				cell8.setCellStyle(cellStyle);
				cell8.setCellValue(val11);

				String val12 = getMapValue(map, "VAL12");//
				XSSFCell cell9 = row.createCell(8);
				cell9.setCellType(CellType.STRING);
				cell9.setCellStyle(cellStyle);
				cell9.setCellValue(val12);

				String val13 = getMapValue(map, "VAL13");//
				XSSFCell cell10 = row.createCell(9);
				cell10.setCellType(CellType.STRING);
				cell10.setCellStyle(cellStyle);
				cell10.setCellValue(val13);

				String val14 = getMapValue(map, "VAL14");//
				XSSFCell cell11 = row.createCell(10);
				cell11.setCellType(CellType.STRING);
				cell11.setCellStyle(cellStyle);
				cell11.setCellValue(val14);

				String val15 = getMapValue(map, "VAL15");//
				XSSFCell cell12 = row.createCell(11);
				cell12.setCellType(CellType.STRING);
				cell12.setCellStyle(cellStyle);
				cell12.setCellValue(val15);

				String val16 = getMapValue(map, "VAL16");//
				XSSFCell cell13 = row.createCell(12);
				cell13.setCellType(CellType.STRING);
				cell13.setCellStyle(cellStyle);
				cell13.setCellValue(val16);

				String val17 = getMapValue(map, "VAL17");//
				XSSFCell cell14 = row.createCell(13);
				cell14.setCellType(CellType.STRING);
				cell14.setCellStyle(cellStyle);
				cell14.setCellValue(val17);

				String val18 = getMapValue(map, "VAL18");//
				XSSFCell cell15 = row.createCell(14);
				cell15.setCellType(CellType.STRING);
				cell15.setCellStyle(cellStyle);
				cell15.setCellValue(val18);

				String val19 = getMapValue(map, "VAL19");//
				XSSFCell cell16 = row.createCell(15);
				cell16.setCellType(CellType.STRING);
				cell16.setCellStyle(cellStyle);
				cell16.setCellValue(val19);

				String val20 = getMapValue(map, "VAL20");//
				XSSFCell cell17 = row.createCell(16);
				cell17.setCellType(CellType.STRING);
				cell17.setCellStyle(cellStyle);
				cell17.setCellValue(val20);

				String val21 = getMapValue(map, "VAL21");//
				XSSFCell cell18 = row.createCell(17);
				cell18.setCellType(CellType.STRING);
				cell18.setCellStyle(cellStyle);
				cell18.setCellValue(val21);

				String val22 = getMapValue(map, "VAL22");//
				XSSFCell cell19 = row.createCell(18);
				cell19.setCellType(CellType.STRING);
				cell19.setCellStyle(cellStyle);
				cell19.setCellValue(val22);

				String val23 = getMapValue(map, "VAL23");//
				XSSFCell cell20 = row.createCell(19);
				cell20.setCellType(CellType.STRING);
				cell20.setCellStyle(cellStyle);
				cell20.setCellValue(val23);
			} else if ("5".equals(type)) {
				String val4 = getMapValue(map, "VAL4");//
				XSSFCell cell1 = row.createCell(1);
				cell1.setCellType(CellType.STRING);
				cell1.setCellStyle(cellStyle);
				cell1.setCellValue(val4);

				String val5 = getMapValue(map, "VAL5");//
				XSSFCell cell2 = row.createCell(2);
				cell2.setCellType(CellType.STRING);
				cell2.setCellStyle(cellStyle);
				cell2.setCellValue(val5);

				String val6 = getMapValue(map, "VAL6");//
				XSSFCell cell3 = row.createCell(3);
				cell3.setCellType(CellType.STRING);
				cell3.setCellStyle(cellStyle);
				cell3.setCellValue(val6);

				String val7 = getMapValue(map, "VAL7");//
				XSSFCell cell4 = row.createCell(4);
				cell4.setCellType(CellType.STRING);
				cell4.setCellStyle(cellStyle);
				cell4.setCellValue(val7);

				String val8 = getMapValue(map, "VAL8");//
				XSSFCell cell5 = row.createCell(5);
				cell5.setCellType(CellType.STRING);
				cell5.setCellStyle(cellStyle);
				cell5.setCellValue(val8);

				String val9 = getMapValue(map, "VAL9");//
				XSSFCell cell6 = row.createCell(6);
				cell6.setCellType(CellType.STRING);
				cell6.setCellStyle(cellStyle);
				cell6.setCellValue(val9);

				String val10 = getMapValue(map, "VAL10");//
				XSSFCell cell7 = row.createCell(7);
				cell7.setCellType(CellType.STRING);
				cell7.setCellStyle(cellStyle);
				cell7.setCellValue(val10);

				String val11 = getMapValue(map, "VAL11");//
				XSSFCell cell8 = row.createCell(8);
				cell8.setCellType(CellType.STRING);
				cell8.setCellStyle(cellStyle);
				cell8.setCellValue(val11);

				String val12 = getMapValue(map, "VAL12");//
				XSSFCell cell9 = row.createCell(9);
				cell9.setCellType(CellType.STRING);
				cell9.setCellStyle(cellStyle);
				cell9.setCellValue(val12);

				String val13 = getMapValue(map, "VAL13");//
				XSSFCell cell10 = row.createCell(10);
				cell10.setCellType(CellType.STRING);
				cell10.setCellStyle(cellStyle);
				cell10.setCellValue(val13);

				String val14 = getMapValue(map, "VAL14");//
				XSSFCell cell11 = row.createCell(11);
				cell11.setCellType(CellType.STRING);
				cell11.setCellStyle(cellStyle);
				cell11.setCellValue(val14);

				String val15 = getMapValue(map, "VAL15");//
				XSSFCell cell12 = row.createCell(12);
				cell12.setCellType(CellType.STRING);
				cell12.setCellStyle(cellStyle);
				cell12.setCellValue(val15);

				String val16 = getMapValue(map, "VAL16");//
				XSSFCell cell13 = row.createCell(13);
				cell13.setCellType(CellType.STRING);
				cell13.setCellStyle(cellStyle);
				cell13.setCellValue(val16);

				String val17 = getMapValue(map, "VAL17");//
				XSSFCell cell14 = row.createCell(14);
				cell14.setCellType(CellType.STRING);
				cell14.setCellStyle(cellStyle);
				cell14.setCellValue(val17);

				String val18 = getMapValue(map, "VAL18");//
				XSSFCell cell15 = row.createCell(15);
				cell15.setCellType(CellType.STRING);
				cell15.setCellStyle(cellStyle);
				cell15.setCellValue(val18);

				String val19 = getMapValue(map, "VAL19");//
				XSSFCell cell16 = row.createCell(16);
				cell16.setCellType(CellType.STRING);
				cell16.setCellStyle(cellStyle);
				cell16.setCellValue(val19);

				String val20 = getMapValue(map, "VAL20");//
				XSSFCell cell17 = row.createCell(17);
				cell17.setCellType(CellType.STRING);
				cell17.setCellStyle(cellStyle);
				cell17.setCellValue(val20);
			} else if ("6".equals(type)) {
				String val4 = getMapValue(map, "VAL4");//
				XSSFCell cell1 = row.createCell(1);
				cell1.setCellType(CellType.STRING);
				cell1.setCellStyle(cellStyle);
				cell1.setCellValue(val4);

				String val5 = getMapValue(map, "VAL5");//
				XSSFCell cell2 = row.createCell(2);
				cell2.setCellType(CellType.STRING);
				cell2.setCellStyle(cellStyle);
				cell2.setCellValue(val5);

				String val6 = getMapValue(map, "VAL6");//
				XSSFCell cell3 = row.createCell(3);
				cell3.setCellType(CellType.STRING);
				cell3.setCellStyle(cellStyle);
				cell3.setCellValue(val6);

				String val7 = getMapValue(map, "VAL7");//
				XSSFCell cell4 = row.createCell(4);
				cell4.setCellType(CellType.STRING);
				cell4.setCellStyle(cellStyle);
				cell4.setCellValue(val7);

				String val8 = getMapValue(map, "VAL8");//
				XSSFCell cell5 = row.createCell(5);
				cell5.setCellType(CellType.STRING);
				cell5.setCellStyle(cellStyle);
				cell5.setCellValue(val8);

				String val9 = getMapValue(map, "VAL9");//
				XSSFCell cell6 = row.createCell(6);
				cell6.setCellType(CellType.STRING);
				cell6.setCellStyle(cellStyle);
				cell6.setCellValue(val9);

				String val10 = getMapValue(map, "VAL10");//
				XSSFCell cell7 = row.createCell(7);
				cell7.setCellType(CellType.STRING);
				cell7.setCellStyle(cellStyle);
				cell7.setCellValue(val10);

				String val11 = getMapValue(map, "VAL11");//
				XSSFCell cell8 = row.createCell(8);
				cell8.setCellType(CellType.STRING);
				cell8.setCellStyle(cellStyle);
				cell8.setCellValue(val11);

				String val12 = getMapValue(map, "VAL12");//
				XSSFCell cell9 = row.createCell(9);
				cell9.setCellType(CellType.STRING);
				cell9.setCellStyle(cellStyle);
				cell9.setCellValue(val12);

				String val13 = getMapValue(map, "VAL13");//
				XSSFCell cell10 = row.createCell(10);
				cell10.setCellType(CellType.STRING);
				cell10.setCellStyle(cellStyle);
				cell10.setCellValue(val13);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	/**
	 * 根据文件标识获取实际文件名
	 *
	 * @param fileName 文件标识
	 * @return 实际文件名
	 */
	public String getProcessedFileName(String fileName) {
		switch (fileName) {
			case "electronic_components":
				return "卫星电子元器件汇总表.xlsx";
			case "cable_insulation_test":
				return "电缆导通绝缘测试汇总表.xlsx";
			case "heating_element_reinspection":
				return "加热片入所复验.xlsx";
			case "heating_circuit_test":
				return "加热回路测试.xlsx";
			default:
				return fileName;
		}
	}

	/**
	 * 导出二级表Excel
	 *
	 * @param type   类型
	 * @param treeId 树节点ID
	 * @param fi     第一个索引
	 * @param si     第二个索引
	 * @return 生成的Excel文件
	 */
	public File exportExcel2(String type, String treeId, String fi, String si) {
		File excelFile;
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis();

		try {
			// 获取文件名
			String fileName = getExcel2FileName(type);
			excelFile = new File(thisTempPath, fileName + ".xlsx");
			// 复制模板到新文件
			FileUtil.copy(new File(excelTplPath + "\\" + fileName + ".xlsx"), excelFile, true);

			// 获取数据
			JSONArray dataList = Util.postTwx("Thing.Fn.ProductQuality",
					"QueryData",
					JSONUtil.createObj()
							.set("type", type)
							.set("treeId", treeId));

			// 写入数据
			try (XSSFWorkbook workbook = new XSSFWorkbook(Files.newInputStream(excelFile.toPath()))) {
				if (!dataList.isEmpty()) {
					// 写入表头
					generateExcelHead(workbook, dataList.getJSONObject(0), type);

					// 写入数据行
					for (int i = 0; i < dataList.size(); i++) {
						JSONObject row = dataList.getJSONObject(i);
						generateExcel(workbook, row, type);
					}

					// 处理合并单元格
					if ("6".equals(type)) {
						mergeExcel(workbook, fi, si);
					}
				}

				// 保存文件
				try (FileOutputStream fos = new FileOutputStream(excelFile)) {
					workbook.write(fos);
				}
			}
		} catch (Exception e) {
			log.error("导出二级表Excel失败", e);
			throw new RuntimeException("导出二级表Excel失败：" + e.getMessage());
		}

		return excelFile;
	}

	/**
	 * 生成Excel表头
	 *
	 * @param workbook 工作簿
	 * @param map      数据
	 * @param type     类型
	 */
	private void generateExcelHead(XSSFWorkbook workbook, JSONObject map, String type) {
		try {
			// 创建单元格样式
			CellStyle cellStyle = workbook.createCellStyle();
			cellStyle.setAlignment(HorizontalAlignment.CENTER);
			cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

			// 获取第一行
			XSSFSheet sheet = workbook.getSheetAt(0);
			XSSFRow row = sheet.getRow(0);

			// 根据不同类型设置表头
			switch (type) {
				case "1": // 多层加工二级表
					setCellValue(row, 5, map, "VAL1", cellStyle);  // 型号
					setCellValue(row, 14, map, "VAL2", cellStyle); // 产品类别
					break;

				case "2": // 热管二级表
					setCellValue(row, 4, map, "VAL1", cellStyle);  // 型号
					setCellValue(row, 19, map, "VAL2", cellStyle); // 产品类别
					break;

				case "3": // 热控喷涂二级表
					setCellValue(row, 4, map, "VAL1", cellStyle);  // 型号
					setCellValue(row, 10, map, "VAL2", cellStyle); // 产品类别
					break;

				case "4": // OSR粘贴二级表
					setCellValue(row, 5, map, "VAL1", cellStyle);  // 型号
					setCellValue(row, 13, map, "VAL2", cellStyle); // 产品类别
					break;

				case "5": // 热敏电阻加工二级表
					setCellValue(row, 3, map, "VAL1", cellStyle);  // 型号
					setCellValue(row, 10, map, "VAL2", cellStyle); // 产品类别
					break;

				case "6": // 一般结构件汇总表
					setCellValue(row, 1, map, "VAL1", cellStyle);  // 型号
					setCellValue(row, 3, map, "VAL2", cellStyle);  // 产品类别
					break;
			}
		} catch (Exception e) {
			log.error("生成Excel表头失败", e);
			throw new RuntimeException("生成Excel表头失败：" + e.getMessage());
		}
	}

	/**
	 * 设置单元格值
	 *
	 * @param row       行
	 * @param cellIndex 单元格索引
	 * @param map       数据
	 * @param key       键
	 * @param style     样式
	 */
	private void setCellValue(XSSFRow row, int cellIndex, JSONObject map, String key, CellStyle style) {
		XSSFCell cell = row.getCell(cellIndex);
		if (cell == null) {
			cell = row.createCell(cellIndex);
		}
		cell.setCellType(CellType.STRING);
		cell.setCellStyle(style);
		cell.setCellValue(getMapValue(map, key));
	}

	/**
	 * 获取二级表文件名
	 */
	private String getExcel2FileName(String type) {
		switch (type) {
			case "1":
				return "多层加工二级表";
			case "2":
				return "热管二级表";
			case "3":
				return "热控喷涂二级表";
			case "4":
				return "OSR粘贴二级表";
			case "5":
				return "热敏电阻加工二级表";
			case "6":
				return "一般结构件汇总表";
			default:
				return "导出表";
		}
	}

	/**
	 * 合并Excel单元格
	 */
	private void mergeExcel(XSSFWorkbook workbook, String fi, String si) {
		XSSFSheet sheet = workbook.getSheetAt(0);

		// 处理第一个索引
		if (!"-1".equals(fi)) {
			int firstIndex = Integer.parseInt(fi) + 2;
			if (sheet.getRow(firstIndex) != null) {
				int lastRowNo = sheet.getLastRowNum();
				sheet.shiftRows(firstIndex, lastRowNo, 1, true, false);
			}

			// 创建合并行
			XSSFRow row = sheet.createRow(firstIndex);
			XSSFCell cell = row.createCell(0);

			// 设置样式
			CellStyle cellStyle = workbook.createCellStyle();
			cellStyle.setAlignment(HorizontalAlignment.LEFT);
			cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
			XSSFFont font = workbook.createFont();
			font.setBold(true);
			cellStyle.setFont(font);

			// 设置值和样式
			cell.setCellType(CellType.STRING);
			cell.setCellStyle(cellStyle);
			cell.setCellValue("一、扩热板、支架");

			// 合并单元格
			CellRangeAddress region = new CellRangeAddress(firstIndex, firstIndex, 0, 10);
			sheet.addMergedRegion(region);
			setBorderStyle(BorderStyle.THIN, region, sheet);
		}

		// 处理第二个索引
		if (!"-1".equals(si)) {
			int secIndex = Integer.parseInt(si) + 2;
			if (sheet.getRow(secIndex) != null) {
				int lastRowNo = sheet.getLastRowNum();
				sheet.shiftRows(secIndex, lastRowNo, 1, true, false);

				// 创建合并行
				XSSFRow row = sheet.createRow(secIndex);
				XSSFCell cell = row.createCell(0);

				// 设置样式
				CellStyle cellStyle = workbook.createCellStyle();
				cellStyle.setAlignment(HorizontalAlignment.LEFT);
				cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
				XSSFFont font = workbook.createFont();
				font.setBold(true);
				cellStyle.setFont(font);

				// 设置值和样式
				cell.setCellType(CellType.STRING);
				cell.setCellStyle(cellStyle);
				cell.setCellValue("二、定位块、铲刮片等");

				// 合并单元格
				CellRangeAddress region = new CellRangeAddress(secIndex, secIndex, 0, 10);
				sheet.addMergedRegion(region);
				setBorderStyle(BorderStyle.THIN, region, sheet);
			}
		}
	}

	/**
	 * 设置边框样式
	 */
	private void setBorderStyle(BorderStyle borderStyle, CellRangeAddress region, XSSFSheet sheet) {
		RegionUtil.setBorderBottom(borderStyle, region, sheet);
		RegionUtil.setBorderLeft(borderStyle, region, sheet);
		RegionUtil.setBorderRight(borderStyle, region, sheet);
		RegionUtil.setBorderTop(borderStyle, region, sheet);
	}

	/**
	 * 读取Excel文件并解析数据
	 *
	 * @param params 包含filePath、result_id等参数的Map
	 * @return 处理结果
	 */
	public int readExcelFile(Map<String, String> params) {
		try {
			return parsingExcel(params);
		} catch (Exception e) {
			log.error("解析Excel文件失败", e);
			throw new RuntimeException("解析Excel文件失败：" + e.getMessage());
		}
	}


	/**
	 * 根据文件路径获取sheet对象
	 *
	 * @param filePath
	 * @return
	 */
	@SuppressWarnings("null")
	public static Sheet getSheet(String filePath) {
		Sheet sheet = null;
		File file = new File(filePath);
		if (file.exists()) {
			InputStream is = null;
			Workbook workbook = null;
			try {
				boolean isExcel2003 = isExcel2003(new FileInputStream(file));
				boolean isExcel2007 = isExcel2007(new FileInputStream(file));
				is = new FileInputStream(file);

				if (isExcel2003) {
					workbook = new HSSFWorkbook(is);
				} else if (isExcel2007) {
					workbook = new XSSFWorkbook(is);
				}
				sheet = workbook.getSheetAt(0);// 获取第一个sheet
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return sheet;
	}

	/**
	 * 解析结构装配记录检测表
	 */
	public static int parsingExcel(Map<String, String> map) {
		String filePath = map.get("filePath");
		String result_id = map.get("result_id");
		String data_id = map.get("data_id");
		String product_id = map.get("product_id");
		String ref_dpid = map.get("ref_dpid");
		String model = map.get("model");
		int result = 0;
		Sheet sheet = getSheet(filePath);
		if (sheet != null) {
			int lastRowNum = sheet.getLastRowNum();
			JSONArray jsonArray = new JSONArray();
			for (int i = 1; i <= lastRowNum; i++) {
				Row row = sheet.getRow(i);
				// int cellLast = row.getLastCellNum();
				int cellLast = 9;
				JSONObject jsonObject = new JSONObject();
				jsonObject.set("RESULT_ID", result_id);
				jsonObject.set("PRODUCT_ID", product_id);
				jsonObject.set("DATA_ID", data_id);
				jsonObject.set("MODELCODE", model);
				jsonObject.set("REF_DPID", ref_dpid);
				String criteria = "";// 判据
				String measureddata = "";// 实际数据
				for (int j = 0; j < cellLast; j++) {
					Cell cell = row.getCell(j);
					String value = Util.getCellValue(cell);
					if (j == 0) {
						jsonObject.set("WORKINGPROCEDURE", value);// 工序
					} else if (j == 1) {
						jsonObject.set("TESTINGITEMS", value);// 检测项目
					} else if (j == 2) {
						criteria = value;
						jsonObject.set("DESIGNREQUIREMENT", value);// 设计要求
					} else if (j == 3) {
						measureddata = value;
						jsonObject.set("MEASUREDDATA", value);// 实测数据
					} else if (j == 4) {

					} else if (j == 5) {

					} else if (j == 6) {

					} else if (j == 7) {

					} else if (j == 8) {
						jsonObject.set("REMARK", value);// 备注
					}
				}
				if (criteria(criteria, measureddata).equals("-1")) {
					jsonObject.set("CONCLUSION", "未判定");//
				} else if (criteria(criteria, measureddata).equals("true")) {
					jsonObject.set("CONCLUSION", "合格");//
				} else if (criteria(criteria, measureddata).equals("false")) {
					jsonObject.set("CONCLUSION", "不合格");//
				}
				jsonArray.add(jsonObject);
			}
			String jsons = jsonArray.toString();
			JSONObject json = new JSONObject();
			json.set("jsons", jsons);
			JSONArray list = Util.postTwx("Thing.Integration.DataCollect",
					"insertStructuralAssembly", json);
			result = list.getJSONObject(0).getInt("result");
		}
		return result;
	}

	/**
	 * 根据判据与输入的值 输出结果
	 *
	 * @param criteria
	 * @return
	 */
	public static String criteria(String criteria, String measureddata) {
		criteria = criteria.trim();
		measureddata = measureddata.trim();
		String res = "-1";
		try {
			if (StringUtils.isNotEmpty(criteria)) {
				if (criteria.indexOf("＜") == 0) {
					if (criteria.indexOf("（") > -1 || criteria.indexOf("(") > -1) {

					} else {
						res = criteria0(criteria, measureddata) + "";
					}
				} else if (criteria.indexOf("≤") == 0) {
					if (criteria.indexOf("/") > 0) {
						res = criteria2(criteria, measureddata) + "";
					} else {
						res = criteria1(criteria, measureddata) + "";
					}
				} else if (criteria.indexOf("±") > 0) {
					if (criteria.indexOf("°") > -1 || criteria.indexOf("′") > -1 || criteria.indexOf("″") > -1) {// 带度数的
						res = criteria3(criteria, measureddata) + "";
					} else {
						res = criteria4(criteria, measureddata) + "";
					}
				} else if (criteria.indexOf("~") > 0) {
					res = criteria5(criteria, measureddata) + "";
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			res = "-1";
		}
		return res;
	}

	/**
	 * 判断measureddata的值是否小于等于标准值 <
	 *
	 * @param criteria     例如"＜0.05"
	 * @param measureddata
	 * @return
	 */
	public static boolean criteria0(String criteria, String measureddata) {
		boolean res = true;
		String[] temps = criteria.split("＜");
		double val = Double.valueOf(temps[1]);
		if (Double.valueOf(measureddata) >= val) {
			res = false;
		}
		return res;
	}

	/**
	 * 判断measureddata的值是否小于等于标准值 <
	 *
	 * @param criteria     例如"≤0.05"
	 * @param measureddata
	 * @return
	 */
	public static boolean criteria1(String criteria, String measureddata) {
		boolean res = true;
		String[] temps = criteria.split("≤");
		double val = Double.valueOf(temps[1]);
		if (Double.valueOf(measureddata) > val) {
			res = false;
		}
		return res;
	}

	/**
	 * 判断measureddata的值是否小于等于标准值 <
	 *
	 * @param criteria     例如"≤0.02/1000"
	 * @param measureddata
	 * @return
	 */
	public static boolean criteria2(String criteria, String measureddata) {
		boolean res = true;
		String[] temps = criteria.split("≤");
		String str1 = temps[1];
		String[] temps1 = str1.split("/");
		String v1 = temps1[0];
		String v2 = temps1[1];
		double val = div(Double.valueOf(v1), Double.valueOf(v2));

		String[] temps2 = measureddata.split("/");
		String v3 = temps2[0];
		String v4 = temps2[1];
		double measureddataVal = div(Double.valueOf(v3), Double.valueOf(v4));
		if (measureddataVal > val) {
			res = false;
		}
		return res;
	}


	/**
	 * 精确加法运算
	 *
	 * @param v1 被加数
	 * @param v2 加数
	 * @return
	 */
	public static double add(double v1, double v2) {
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return b1.add(b2).doubleValue();
	}

	/**
	 * 精确减法运算
	 *
	 * @param v1 减数
	 * @param v2 被减数
	 * @return
	 */
	public static double sub(double v1, double v2) {
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return b1.subtract(b2).doubleValue();
	}

	/**
	 * 精确乘法运算
	 *
	 * @param v1 被乘数
	 * @param v2 乘数
	 * @return
	 */
	public static double mul(double v1, double v2) {
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return b1.multiply(b2).doubleValue();
	}

	/**
	 * 精确除法运算
	 *
	 * @param v1 被除数
	 * @param v2 除数
	 * @return
	 */
	public static double div(double v1, double v2) {
		return div(v1, v2, 10);
	}

	/**
	 * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指定精度，以后的数字四舍五入
	 *
	 * @param v1    被除数
	 * @param v2    除数
	 * @param scale 表示表示需要精确到小数点以后几位
	 * @return
	 */
	public static double div(double v1, double v2, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException("刻度必须是正整数或零");
		}
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
	}

	/**
	 * 将度数转换为秒数 方便计算
	 *
	 * @param str
	 * @return
	 */
	public static double du2Miao(String str) {
		double res = 0;
		double du = 0;
		double fen = 0;
		double miao = 0;
		if (str.indexOf("°") > -1) {
			du = Double.parseDouble(str.split("°")[0]);
		}
		if (str.indexOf("′") > -1) {
			if (str.indexOf("°") > -1) {
				fen = Double.parseDouble(str.split("°")[1].split("′")[0]);
			} else {
				fen = Double.parseDouble(str.split("′")[0]);
			}
		}
		if (str.indexOf("″") > -1) {
			if (str.indexOf("°") > -1) {
				if (str.indexOf("′") > -1) {
					miao = Double.parseDouble(str.split("′")[1].split("″")[0]);
				} else {
					miao = Double.parseDouble(str.split("°")[1].split("″")[0]);
				}
			} else {
				if (str.indexOf("′") > -1) {
					miao = Double.parseDouble(str.split("′")[1].split("″")[0]);
				} else {
					miao = Double.parseDouble(str.split("″")[0]);
				}
			}
		}
		res = du * 3600 + fen * 60 + miao;
		return res;
	}

	/**
	 * 判断measureddata的值
	 *
	 * @param criteria     例如"90°±10″"
	 * @param measureddata 89°59′52″
	 * @return
	 */
	public static boolean criteria3(String criteria, String measureddata) {
		boolean res = true;
		String[] temps = criteria.split("±");
		String str0 = temps[0];// 90°
		String str1 = temps[1];// 10″
		double v0 = du2Miao(str0);// 标准值
		double v1 = du2Miao(str1);// 浮动范围值

		double v3 = du2Miao(measureddata);// 实测值

		if (Math.abs(v3 - v0) > v1) {
			res = false;
		}
		return res;
	}

	/**
	 * 判断measureddata的值
	 *
	 * @param criteria     例如 942.5±0.1
	 * @param measureddata 942.6
	 * @return
	 */
	public static boolean criteria4(String criteria, String measureddata) {
		boolean res = true;
		String[] temps = criteria.split("±");
		String str0 = temps[0];// 942.5
		String str1 = temps[1];// 0.1
		double v0 = Double.parseDouble(str0);// 标准值
		double v1 = Double.parseDouble(str1);// 浮动范围值

		double v3 = Double.parseDouble(measureddata);// 实测值
		if (Math.abs(sub(v3, v0)) > v1) {
			res = false;
		}
		return res;
	}

	/**
	 * 判断measureddata的值
	 *
	 * @param criteria     例如 1157.3~1157.5
	 * @param measureddata 1157.35
	 * @return
	 */
	public static boolean criteria5(String criteria, String measureddata) {
		boolean res = true;
		String[] temps = criteria.split("~");
		String str0 = temps[0];// 1157.3
		String str1 = temps[1];// 1157.5
		double v0 = Double.parseDouble(str0);// 标准值
		double v1 = Double.parseDouble(str1);// 浮动范围值

		double v3 = Double.parseDouble(measureddata);// 1157.35
		if (v3 < v0 || v3 > v1) {
			res = false;
		}
		return res;
	}


	/**
	 * 根据横纵坐标获取单元格的值
	 *
	 * @param filePath
	 *            excel文件路径
	 * @param x
	 *            单元格横坐标
	 * @param y
	 *            单元格纵坐标
	 * @param sheetAt表示第几个sheet页
	 *            从0开始
	 * @return String 单元格的值
	 */

	/**
	 * 判断excel文件是否为03版
	 *
	 * @param is 文件流
	 * @return
	 */
	@SuppressWarnings("resource")
	public static boolean isExcel2003(InputStream is) {
		try {
			new HSSFWorkbook(is);
		} catch (Exception e) {
			return false;
		}
		return true;
	}

	/**
	 * 判断excel文件是否为07版
	 *
	 * @param is 文件流
	 * @return
	 */
	@SuppressWarnings("resource")
	public static boolean isExcel2007(InputStream is) {
		try {
			new XSSFWorkbook(is);
		} catch (Exception e) {
			return false;
		}
		return true;
	}

	/**
	 * 检查图片是否存在
	 *
	 * @param imageUrl 图片URL地址
	 * @return 返回检查结果
	 */
	public JSONObject checkPhotoExistence(String imageUrl) {
		JSONObject result = new JSONObject();
		try {
			URL url = new URL(imageUrl);
			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			conn.setRequestProperty("User-Agent", "Mozilla/4.0(compatible MSIE 5.0; Windows NT; DigExt)");
			conn.setRequestMethod("GET");
			conn.setDoOutput(true);

			// 转换编码
			String contentDisposition = URLDecoder.decode(conn.getHeaderField("content-Disposition"), "UTF-8");
			result.set("success", !contentDisposition.contains("服务器中找不到该文件"));
		} catch (Exception e) {
			log.error("检查图片是否存在时发生错误", e);
			result.set("success", false);
		}
		return result;
	}

	/**
	 * 导入Excel文件并解析
	 *
	 * @param file          上传的文件
	 * @param type          文件类型
	 * @param nodeCode      树节点id
	 * @param fileType      文件类型
	 * @param fileTypeValue 文件类型值
	 * @param fileName      文件名称
	 * @param fileFormat    文件形式
	 * @param securityLevel 密级
	 * @param creator       创建者
	 * @return 处理结果
	 */
	public JSONObject importExcel(MultipartFile file, String type, String nodeCode,
								  String fileType, String fileTypeValue, String fileName,
								  String fileFormat, String securityLevel, String creator) {
		JSONObject jsonObject = new JSONObject();
		try {
			// 获取文件上传路径

			// 创建月份目录
			String month = new SimpleDateFormat("yyyy-MM").format(new Date());
			String paths = fileUploadPath + File.separator + month;
			FileUtil.mkdir(paths);
			// 生成唯一文件名并保存文件
			String uuid = UUID.randomUUID().toString();
			String filePath = paths + File.separator + uuid;
			File destFile = new File(filePath);

			// 将上传的文件保存到目标位置
			file.transferTo(destFile);

			// 构建结果对象
			JSONObject rsObj = new JSONObject();
			rsObj.set("type", type);
			rsObj.set("NODECODE", nodeCode);
			rsObj.set("FILE_NUMBER", "");
			rsObj.set("FILE_NAME", fileName);
			rsObj.set("FILE_TYPE", fileType);
			rsObj.set("FILEPATH", "//" + month + "//" + uuid);
			rsObj.set("SECURITY_LEVEL", securityLevel);
			rsObj.set("FILE_FORMAT", fileFormat);
			rsObj.set("CREATOR", creator);

			// 构建请求参数
			JSONObject jsons = new JSONObject();
			jsons.set("json", rsObj.toString());

			// 调用服务添加数据
			JSONArray rslist = Util.postTwx("Thing.Fn.ExcelImport",
					"addDataToResultTable", jsons);
			int resultId = rslist.getJSONObject(0).getInt("result");

			// 根据不同类型解析Excel
			int excelRs = 0;
			switch (fileTypeValue) {
				case "electronic_components":
					excelRs = ParsingExcel.parseExcel(filePath, type + "_" + resultId, ParsingExcel.ExcelType.ELECTRONIC_COMPONENTS);
					break;
				case "cable_insulation_test":
					excelRs = ParsingExcel.parseExcel(filePath, type + "_" + resultId, ParsingExcel.ExcelType.CABLE_INSULATION);
					break;
				case "heating_element_reinspection":
					excelRs = ParsingExcel.parseExcel(filePath, type + "_" + resultId, ParsingExcel.ExcelType.HEATING_ELEMENT);
					break;
				case "heating_circuit_test":
					excelRs = ParsingExcel.parseExcel(filePath, type + "_" + resultId, ParsingExcel.ExcelType.HEATING_CIRCUIT);
					break;
			}

			jsonObject.set("success", true);
			jsonObject.set("result", excelRs);

		} catch (Exception e) {
			log.error("导入Excel失败", e);
			jsonObject.set("success", false);
			jsonObject.set("result", e.toString());
		}
		return jsonObject;
	}

	public JSONObject importExcel2(MultipartFile file, String type, String ctype,
								   String treeId, String creator) {
		JSONObject jsonObject = new JSONObject();
		try {
			// 获取文件上传路径

			// 创建月份目录
			String month = new SimpleDateFormat("yyyy-MM").format(new Date());
			String paths = fileUploadPath + File.separator + month;
			FileUtil.mkdir(paths);
			// 生成唯一文件名并保存文件
			String uuid = UUID.randomUUID().toString();
			String filePath = paths + File.separator + uuid;
			File destFile = new File(filePath);
			String relativePath = "//" + month + "//" + uuid;
			// 将上传的文件保存到目标位置
			file.transferTo(destFile);
			String filename = file.getOriginalFilename();

			int excelRs = 0;
			if ("1".equals(type)) {
				excelRs = ParsingExcel2.parsingExcel1(filePath, relativePath, treeId, creator, filename);
			} else if ("2".equals(type)) {
				excelRs = ParsingExcel2.parsingExcel2(filePath, relativePath, treeId, creator, filename);
			} else if ("3".equals(type)) {
				excelRs = ParsingExcel2.parsingExcel3(filePath, relativePath, treeId, creator, filename);
			} else if ("4".equals(type)) {
				excelRs = ParsingExcel2.parsingExcel4(filePath, relativePath, treeId, creator, filename);
			} else if ("5".equals(type)) {
				excelRs = ParsingExcel2.parsingExcel5(filePath, relativePath, treeId, creator, filename);
			} else if ("6".equals(type)) {
				if ("1".equals(ctype)) {
					excelRs = ParsingExcel2.parsingExcel6_1(filePath, relativePath, treeId, creator, filename);
				} else if ("2".equals(ctype)) {
					excelRs = ParsingExcel2.parsingExcel6_2(filePath, relativePath, treeId, creator, filename);
				}
			} else if ("7".equals(type)) {
				excelRs = ParsingExcel2.parsingExcel7(filePath, relativePath, treeId, creator, filename);
			} else if ("8".equals(type)) {
				excelRs = ParsingExcel2.parsingExcel8(filePath, relativePath, treeId, creator, filename);
			}

			jsonObject.set("success", true);
			jsonObject.set("result", excelRs);

		} catch (Exception e) {
			log.error("导入Excel失败", e);
			jsonObject.set("success", false);
			jsonObject.set("result", e.toString());
		}
		return jsonObject;
	}

	/**
	 * 导出二级表Excel
	 *
	 * @param treeId  树节点ID
	 * @param tableId 表格ID
	 * @return 生成的Excel文件
	 */
	public File exportSecondExcel(String treeId, String tableId) {
		File excelFile = null;
		try (XSSFWorkbook workbook = new XSSFWorkbook()) {
			JSONArray tableInfoList = Util.postTwx("Thing.Fn.TableConfig",
					"QueryTableById", JSONUtil.createObj().set("tableId", tableId));

			if (tableInfoList.isEmpty()) {
				throw new RuntimeException("未找到对应的表格配置信息");
			}

			JSONObject tableInfo = tableInfoList.getJSONObject(0);
			String filepath = tableInfo.getStr("SECOND_FILEPATH");
			String treeName = tableInfo.getStr("TREE_NAME");
			String type = tableInfo.getStr("TYPE");

			// 如果是三级表，使用三级表路径
			if ("4".equals(type)) {
				filepath = tableInfo.getStr("THREE_FILEPATH");
			}

			String filename = treeName + "二级表";
			int secondDataRownum = Integer.parseInt(tableInfo.getStr("SECOND_DATA_ROWNUM"));

			// 创建临时文件
			excelFile = createExcelFromTemplate(filepath, filename);

			// 获取二级表数据
			JSONArray tableDataList = Util.postTwxForArray("Thing.Fn.TableConfig",
					"QueryTableData",
					JSONUtil.createObj()
							.set("tree_id", treeId)
							.set("type", type)
							.set("table_config_id", tableId));

			// 使用模板文件创建工作簿
			try (FileInputStream fis = new FileInputStream(excelFile);
				 XSSFWorkbook templateWorkbook = new XSSFWorkbook(fis)) {

				XSSFSheet sheet = templateWorkbook.getSheetAt(0);
				CellStyle cellStyle = createCellStyle(templateWorkbook);

				// 填充表头数据
				if (!tableDataList.isEmpty()) {
					fillHeaderData(sheet, tableDataList.getJSONObject(0));
				}

				// 填充表格数据
				fillTableData(sheet, tableDataList, secondDataRownum, cellStyle);

				// 保存到文件
				try (FileOutputStream fos = new FileOutputStream(excelFile)) {
					templateWorkbook.write(fos);
				}
			}

			return excelFile;
		} catch (Exception e) {
			log.error("导出二级表Excel失败", e);
			throw new RuntimeException("导出二级表Excel失败：" + e.getMessage());
		}
	}

	/**
	 * 创建单元格样式
	 */
	private CellStyle createCellStyle(XSSFWorkbook workbook) {
		CellStyle cellStyle = workbook.createCellStyle();
		cellStyle.setAlignment(HorizontalAlignment.CENTER);
		cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setBorderTop(BorderStyle.THIN);
		cellStyle.setBorderRight(BorderStyle.THIN);
		return cellStyle;
	}

	/**
	 * 填充表头数据
	 */
	private void fillHeaderData(XSSFSheet sheet, JSONObject headerData) {
		for (Map.Entry<String, Object> entry : headerData.entrySet()) {
			String key = entry.getKey();
			if (key.startsWith("col")) {

				String value = headerData.getStr(key);
				String cellAddress = key.substring(3); // 移除"col"前缀
				int x = Util.getX(cellAddress);
				int y = Util.getY(cellAddress);
				XSSFRow row = sheet.getRow(y - 1);
				if (row != null) {
					XSSFCell cell = row.getCell(x - 1);
					if (cell != null) {
						cell.setCellValue(value);
					}
				}
			}
		}
	}

	/**
	 * 填充表格数据
	 */
	private void fillTableData(XSSFSheet sheet, JSONArray dataList, int secondDataRownum, CellStyle cellStyle) {
		for (int i = 0; i < dataList.size(); i++) {
			JSONObject rowData = dataList.getJSONObject(i);
			int rowIndex = i + secondDataRownum - 1;
			XSSFRow row = sheet.createRow(rowIndex);

			// 填充单元格数据
			for (Map.Entry<String, Object> entry : rowData.entrySet()) {
				String key = entry.getKey();
				String value = rowData.getStr(key);

				if (key.startsWith("val")) {
					int cellIndex = Integer.parseInt(key.substring(3)) - 1;
					XSSFCell cell = row.createCell(cellIndex);
					cell.setCellType(CellType.STRING);
					cell.setCellStyle(cellStyle);
					cell.setCellValue(value);
				}
			}

			// 处理合并单元格
			String mergedInfo = rowData.getStr("mergedInfo");
			if (StringUtils.isNotEmpty(mergedInfo)) {
				processMergedCells(sheet, mergedInfo, rowIndex);
			}
		}
	}

	/**
	 * 处理合并单元格
	 */
	private void processMergedCells(XSSFSheet sheet, String mergedInfo, int rowIndex) {
		String[] mergeds = mergedInfo.split(",");
		for (String merged : mergeds) {
			String[] parts = merged.split(":");
			String columnName = parts[0];
			int rowspan = Integer.parseInt(parts[1]);
			int cellIndex = Integer.parseInt(columnName.substring(3)) - 1;

			CellRangeAddress region = new CellRangeAddress(
					rowIndex,
					rowIndex + rowspan - 1,
					cellIndex,
					cellIndex
			);
			sheet.addMergedRegion(region);
			setBorderStyle(BorderStyle.THIN, region, sheet);
		}
	}


	/**
	 * 从模板创建新的Excel文件
	 */
	private File createExcelFromTemplate(String filepath, String filename) throws IOException {
		File templateFile = new File(filepath);
		if (!templateFile.exists()) {
			throw new RuntimeException("模板文件不存在：" + filepath);
		}

		// 创建新文件
		File newFile = new File(excelTplPath, filename + ".xlsx");
		newFile.createNewFile();
		FileUtil.copy(templateFile, newFile, true);

		return newFile;
	}
}