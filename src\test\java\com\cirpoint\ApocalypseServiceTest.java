package com.cirpoint;

import com.cirpoint.service.ApocalypseService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 启示录生成服务测试类
 */
@SpringBootTest
public class ApocalypseServiceTest {

    @Autowired
    private ApocalypseService apocalypseService;

    /**
     * 测试生成启示录文档
     */
    @Test
    public void testGenerateApocalypse() throws Exception {
        // 调用生成方法
        String filePath = apocalypseService.generateApocalypse("896");
        
        // 验证文件路径不为空
        assertNotNull(filePath, "生成的文件路径不应为空");
        
        // 验证文件确实存在
        File file = new File(filePath);
        assertTrue(file.exists(), "生成的文件应该存在");
        assertTrue(file.length() > 0, "生成的文件不应为空");
        
        System.out.println("启示录文档生成成功，路径：" + filePath);
    }
} 