/**
 * 日志查看器前端控制器
 * 实现SSE实时日志、历史日志分页、搜索过滤、文件下载等功能
 */
class LogViewer {
    constructor() {
        this.eventSource = null;
        this.isRealTimeActive = false;
        this.currentPage = 1;
        this.pageSize = 100;
        this.currentFile = '';
        this.maxDisplayLines = 500;
        this.displayedLines = 0;
        this.searchParams = {};
        
        this.init();
    }

    /**
     * 初始化应用
     */
    init() {
        this.bindEvents();
        this.loadFileList();
        this.updateSystemStatus();
        
        // 定期更新系统状态
        setInterval(() => this.updateSystemStatus(), 5000);
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 实时日志控制
        document.getElementById('toggleRealtime').addEventListener('click', () => {
            this.toggleRealTime();
        });

        document.getElementById('clearLogs').addEventListener('click', () => {
            this.clearRealtimeLogs();
        });

        document.getElementById('autoScroll').addEventListener('change', (e) => {
            this.autoScroll = e.target.checked;
        });

        document.getElementById('maxLines').addEventListener('change', (e) => {
            this.maxDisplayLines = parseInt(e.target.value);
            this.trimDisplayedLogs();
        });

        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 文件列表
        document.getElementById('refreshFiles').addEventListener('click', () => {
            this.loadFileList();
        });

        // 历史日志控制
        document.getElementById('historyFileSelect').addEventListener('change', (e) => {
            this.currentFile = e.target.value;
            this.currentPage = 1;
            this.loadHistoryLogs();
            document.getElementById('downloadFile').disabled = !e.target.value;
        });

        document.getElementById('pageSize').addEventListener('change', (e) => {
            this.pageSize = parseInt(e.target.value);
            this.currentPage = 1;
            this.loadHistoryLogs();
        });

        document.getElementById('downloadFile').addEventListener('click', () => {
            this.downloadFile();
        });

        // 搜索功能
        document.getElementById('searchBtn').addEventListener('click', () => {
            this.performSearch();
        });

        document.getElementById('clearSearch').addEventListener('click', () => {
            this.clearSearch();
        });

        // 搜索框回车事件
        document.getElementById('searchKeyword').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        // 提示框关闭
        document.querySelectorAll('.toast-close').forEach(button => {
            button.addEventListener('click', (e) => {
                e.target.closest('.toast').classList.add('hidden');
            });
        });

        // 窗口关闭时清理资源
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        // 更新标签按钮状态
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新标签内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}Tab`).classList.add('active');

        // 如果切换到历史日志标签，加载文件列表到下拉框
        if (tabName === 'history') {
            this.populateHistoryFileSelect();
        }
    }

    /**
     * 切换实时日志状态
     */
    toggleRealTime() {
        if (this.isRealTimeActive) {
            this.stopRealTime();
        } else {
            this.startRealTime();
        }
    }

    /**
     * 启动实时日志
     */
    startRealTime() {
        if (this.eventSource) {
            this.eventSource.close();
        }

        this.showLoading();
        
        this.eventSource = new EventSource(`${window.location.origin}/api/logs/stream`);
        
        this.eventSource.onopen = () => {
            this.isRealTimeActive = true;
            this.updateRealtimeStatus();
            this.updateConnectionStatus('online', '已连接');
            this.hideLoading();
            this.showSuccess('实时日志连接成功');
        };

        this.eventSource.onmessage = (event) => {
            try {
                const logEntry = JSON.parse(event.data);
                this.appendRealtimeLog(logEntry);
            } catch (error) {
                console.error('解析日志数据失败:', error);
            }
        };

        this.eventSource.onerror = (error) => {
            console.error('SSE连接错误:', error);
            this.updateConnectionStatus('offline', '连接断开');
            
            if (this.isRealTimeActive) {
                this.showError('实时日志连接断开，正在尝试重连...');
                // 自动重连
                setTimeout(() => {
                    if (this.isRealTimeActive) {
                        this.startRealTime();
                    }
                }, 3000);
            }
        };
    }

    /**
     * 停止实时日志
     */
    stopRealTime() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        
        this.isRealTimeActive = false;
        this.updateRealtimeStatus();
        this.updateConnectionStatus('offline', '未连接');
        this.showSuccess('实时日志已停止');
    }

    /**
     * 更新实时日志状态显示
     */
    updateRealtimeStatus() {
        const button = document.getElementById('toggleRealtime');
        const status = document.getElementById('realtimeStatus');
        
        if (this.isRealTimeActive) {
            button.classList.remove('btn-primary');
            button.classList.add('btn-secondary');
            status.textContent = '停止实时';
        } else {
            button.classList.remove('btn-secondary');
            button.classList.add('btn-primary');
            status.textContent = '启动实时';
        }
    }

    /**
     * 更新连接状态显示
     */
    updateConnectionStatus(status, text) {
        const indicator = document.getElementById('connectionIndicator');
        const connectionText = document.getElementById('connectionText');
        
        indicator.className = `status-indicator ${status}`;
        connectionText.textContent = text;
    }

    /**
     * 添加实时日志条目
     */
    appendRealtimeLog(logEntry) {
        const container = document.getElementById('realtimeLogContent');
        
        // 移除占位符
        const placeholder = container.querySelector('.log-placeholder');
        if (placeholder) {
            placeholder.remove();
        }

        // 创建日志条目元素
        const logElement = this.createLogElement(logEntry, true);
        container.appendChild(logElement);

        this.displayedLines++;
        this.updateDisplayedLinesCount();

        // 限制显示行数
        this.trimDisplayedLogs();

        // 自动滚动
        if (document.getElementById('autoScroll').checked) {
            container.scrollTop = container.scrollHeight;
        }

        // 添加新日志动画
        setTimeout(() => {
            logElement.classList.remove('new');
        }, 1000);
    }

    /**
     * 创建日志条目元素
     */
    createLogElement(logEntry, isNew = false) {
        const div = document.createElement('div');
        div.className = `log-entry ${logEntry.level || ''}${isNew ? ' new' : ''}`;
        
        // 格式化日志内容
        let content = '';
        if (logEntry.timestamp) {
            content += `[${logEntry.timestamp}] `;
        }
        if (logEntry.level) {
            content += `${logEntry.level} `;
        }
        if (logEntry.logger) {
            content += `${logEntry.logger} - `;
        }
        content += logEntry.message || logEntry.content || '';

        div.textContent = content;
        return div;
    }

    /**
     * 限制显示的日志行数
     */
    trimDisplayedLogs() {
        const container = document.getElementById('realtimeLogContent');
        const logEntries = container.querySelectorAll('.log-entry');
        
        if (logEntries.length > this.maxDisplayLines) {
            const removeCount = logEntries.length - this.maxDisplayLines;
            for (let i = 0; i < removeCount; i++) {
                logEntries[i].remove();
            }
            this.displayedLines = this.maxDisplayLines;
            this.updateDisplayedLinesCount();
        }
    }

    /**
     * 更新显示行数计数
     */
    updateDisplayedLinesCount() {
        document.getElementById('displayedLines').textContent = this.displayedLines;
    }

    /**
     * 清空实时日志显示
     */
    clearRealtimeLogs() {
        const container = document.getElementById('realtimeLogContent');
        container.innerHTML = '<div class="log-placeholder">实时日志已清空，等待新日志...</div>';
        this.displayedLines = 0;
        this.updateDisplayedLinesCount();
    }

    /**
     * 加载文件列表
     */
    async loadFileList() {
        try {
            this.showLoading();
            const response = await fetch(`${window.location.origin}/api/logs/files`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const files = await response.json();
            this.renderFileList(files);
            this.populateHistoryFileSelect(files);
            
        } catch (error) {
            console.error('加载文件列表失败:', error);
            this.showError('加载文件列表失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 渲染文件列表
     */
    renderFileList(files) {
        const container = document.getElementById('fileList');
        
        if (!files || files.length === 0) {
            container.innerHTML = '<div class="loading">暂无日志文件</div>';
            return;
        }

        container.innerHTML = '';
        files.forEach(file => {
            const fileElement = document.createElement('div');
            fileElement.className = 'file-item';
            fileElement.innerHTML = `
                <div class="file-name">${file.name}</div>
                <div class="file-info">
                    大小: ${this.formatFileSize(file.size)} | 
                    修改时间: ${this.formatDate(file.lastModified)}
                </div>
            `;
            
            fileElement.addEventListener('click', () => {
                // 移除其他文件的选中状态
                container.querySelectorAll('.file-item').forEach(item => {
                    item.classList.remove('active');
                });
                
                // 选中当前文件
                fileElement.classList.add('active');
                
                // 切换到历史日志标签并加载该文件
                this.switchTab('history');
                document.getElementById('historyFileSelect').value = file.name;
                this.currentFile = file.name;
                this.currentPage = 1;
                this.loadHistoryLogs();
                document.getElementById('downloadFile').disabled = false;
            });
            
            container.appendChild(fileElement);
        });
    }

    /**
     * 填充历史日志文件下拉框
     */
    populateHistoryFileSelect(files = null) {
        const select = document.getElementById('historyFileSelect');
        
        if (!files) {
            // 如果没有传入文件列表，从API获取
            fetch(`${window.location.origin}/api/logs/files`)
                .then(response => response.json())
                .then(files => this.populateHistoryFileSelect(files))
                .catch(error => console.error('获取文件列表失败:', error));
            return;
        }

        // 保存当前选中的值
        const currentValue = select.value;
        
        // 清空并重新填充选项
        select.innerHTML = '<option value="">请选择日志文件</option>';
        
        files.forEach(file => {
            const option = document.createElement('option');
            option.value = file.name;
            option.textContent = `${file.name} (${this.formatFileSize(file.size)})`;
            select.appendChild(option);
        });

        // 恢复之前的选中值
        if (currentValue) {
            select.value = currentValue;
        }
    }

    /**
     * 加载历史日志
     */
    async loadHistoryLogs() {
        if (!this.currentFile) {
            return;
        }

        try {
            this.showLoading();
            
            const params = new URLSearchParams({
                file: this.currentFile,
                page: this.currentPage,
                size: this.pageSize,
                ...this.searchParams
            });

            const response = await fetch(`${window.location.origin}/api/logs/history?${params}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            this.renderHistoryLogs(result);
            this.renderPagination(result);
            
        } catch (error) {
            console.error('加载历史日志失败:', error);
            this.showError('加载历史日志失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 渲染历史日志
     */
    renderHistoryLogs(result) {
        const container = document.getElementById('historyLogContent');
        
        if (!result.data || result.data.length === 0) {
            container.innerHTML = '<div class="log-placeholder">没有找到匹配的日志记录</div>';
            return;
        }

        container.innerHTML = '';
        result.data.forEach(logEntry => {
            const logElement = this.createLogElement(logEntry);
            container.appendChild(logElement);
        });

        // 滚动到顶部
        container.scrollTop = 0;
    }

    /**
     * 渲染分页控件
     */
    renderPagination(result) {
        const container = document.getElementById('pagination');
        
        if (result.totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let html = '';
        
        // 上一页按钮
        html += `<button class="pagination-button" ${result.currentPage <= 1 ? 'disabled' : ''} 
                 onclick="logViewer.goToPage(${result.currentPage - 1})">上一页</button>`;

        // 页码按钮
        const startPage = Math.max(1, result.currentPage - 2);
        const endPage = Math.min(result.totalPages, result.currentPage + 2);

        if (startPage > 1) {
            html += `<button class="pagination-button" onclick="logViewer.goToPage(1)">1</button>`;
            if (startPage > 2) {
                html += `<span class="pagination-info">...</span>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            html += `<button class="pagination-button ${i === result.currentPage ? 'active' : ''}" 
                     onclick="logViewer.goToPage(${i})">${i}</button>`;
        }

        if (endPage < result.totalPages) {
            if (endPage < result.totalPages - 1) {
                html += `<span class="pagination-info">...</span>`;
            }
            html += `<button class="pagination-button" onclick="logViewer.goToPage(${result.totalPages})">${result.totalPages}</button>`;
        }

        // 下一页按钮
        html += `<button class="pagination-button" ${result.currentPage >= result.totalPages ? 'disabled' : ''} 
                 onclick="logViewer.goToPage(${result.currentPage + 1})">下一页</button>`;

        // 分页信息
        html += `<div class="pagination-info">
                    第 ${result.currentPage} 页，共 ${result.totalPages} 页，
                    总计 ${result.totalElements} 条记录
                 </div>`;

        container.innerHTML = html;
    }

    /**
     * 跳转到指定页面
     */
    goToPage(page) {
        this.currentPage = page;
        this.loadHistoryLogs();
    }

    /**
     * 执行搜索
     */
    performSearch() {
        const keyword = document.getElementById('searchKeyword').value.trim();
        const level = document.getElementById('logLevel').value;
        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;

        this.searchParams = {};
        
        if (keyword) {
            this.searchParams.keyword = keyword;
        }
        if (level) {
            this.searchParams.level = level;
        }
        if (startTime) {
            this.searchParams.startTime = startTime;
        }
        if (endTime) {
            this.searchParams.endTime = endTime;
        }

        this.currentPage = 1;
        this.loadHistoryLogs();
    }

    /**
     * 清空搜索条件
     */
    clearSearch() {
        document.getElementById('searchKeyword').value = '';
        document.getElementById('logLevel').value = '';
        document.getElementById('startTime').value = '';
        document.getElementById('endTime').value = '';
        
        this.searchParams = {};
        this.currentPage = 1;
        this.loadHistoryLogs();
    }

    /**
     * 下载文件
     */
    downloadFile() {
        if (!this.currentFile) {
            this.showError('请先选择要下载的文件');
            return;
        }

        const url = `${window.location.origin}/api/logs/download?file=${encodeURIComponent(this.currentFile)}`;
        const link = document.createElement('a');
        link.href = url;
        link.download = this.currentFile;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        this.showSuccess('文件下载已开始');
    }

    /**
     * 更新系统状态
     */
    async updateSystemStatus() {
        try {
            const response = await fetch(`${window.location.origin}/api/logs/status`);
            if (response.ok) {
                const status = await response.json();
                
                document.getElementById('connectionCount').textContent = status.connectionCount || 0;
                document.getElementById('currentFile').textContent = status.currentFile || '-';
                document.getElementById('fileSize').textContent = status.fileSize ? this.formatFileSize(status.fileSize) : '-';
                document.getElementById('lastUpdate').textContent = status.lastUpdate ? this.formatDate(status.lastUpdate) : '-';
            }
        } catch (error) {
            console.error('更新系统状态失败:', error);
        }
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 格式化日期
     */
    formatDate(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * 显示加载遮罩
     */
    showLoading() {
        document.getElementById('loadingOverlay').classList.remove('hidden');
    }

    /**
     * 隐藏加载遮罩
     */
    hideLoading() {
        document.getElementById('loadingOverlay').classList.add('hidden');
    }

    /**
     * 显示错误提示
     */
    showError(message) {
        const toast = document.getElementById('errorToast');
        toast.querySelector('.toast-message').textContent = message;
        toast.classList.remove('hidden');
        
        // 自动隐藏
        setTimeout(() => {
            toast.classList.add('hidden');
        }, 5000);
    }

    /**
     * 显示成功提示
     */
    showSuccess(message) {
        const toast = document.getElementById('successToast');
        toast.querySelector('.toast-message').textContent = message;
        toast.classList.remove('hidden');
        
        // 自动隐藏
        setTimeout(() => {
            toast.classList.add('hidden');
        }, 3000);
    }

    /**
     * 清理资源
     */
    cleanup() {
        if (this.eventSource) {
            this.eventSource.close();
        }
    }
}

// 全局变量，供HTML中的onclick事件使用
let logViewer;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    logViewer = new LogViewer();
}); 