<!DOCTYPE html>
<HTML>
<HEAD>
	<TITLE> ZTREE DEMO - updateNode</TITLE>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<link rel="stylesheet" href="../../../css/demo.css" type="text/css">
	<link rel="stylesheet" href="../../../css/zTreeStyle/zTreeStyle.css" type="text/css">
	<script type="text/javascript" src="../../../js/jquery-1.4.4.min.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.core.js"></script>
	<!--  <script type="text/javascript" src="../../../js/jquery.ztree.excheck.js"></script>
	  <script type="text/javascript" src="../../../js/jquery.ztree.exedit.js"></script>-->
	<SCRIPT type="text/javascript">
		<!--
		var setting = {
			view: {
				selectedMulti: false
			},
			data: {
				simpleData: {
					enable: true
				}
			}
		};

		var zNodes =[
			{ id:1, pId:0, name:"pNode 01", open:true},
			{ id:11, pId:1, name:"leaf node 01"},
			{ id:12, pId:1, name:"leaf node 02"},
			{ id:13, pId:1, name:"leaf node 03"},
			{ id:2, pId:0, name:"pNode 02", open:true},
			{ id:21, pId:2, name:"leaf node 01"},
			{ id:22, pId:2, name:"leaf node 02"},
			{ id:23, pId:2, name:"leaf node 03"},
			{ id:3, pId:0, name:"pNode 03", open:true },
			{ id:31, pId:3, name:"leaf node 01"},
			{ id:32, pId:3, name:"leaf node 02"},
			{ id:33, pId:3, name:"leaf node 03"}
		];

		var nameCount = 0, iconCount = 1, color = [0, 0, 0];
		function updateNode(e) {
			var zTree = $.fn.zTree.getZTreeObj("treeDemo"),
			type = e.data.type,
			nodes = zTree.getSelectedNodes();
			if (nodes.length == 0) {
				alert("Please select one node...");
			}
			for (var i=0, l=nodes.length; i<l; i++) {
				zTree.setting.view.fontCss = {};
				if (type == "rename") {
					nodes[i].name = nodes[i].name.replace(/_[\d]*$/g, "") + "_" + (nameCount++);
				} else if (type == "icon") {
					if (iconCount > 8) {
						nodes[i].iconSkin = null;
						iconCount = 1;
					} else if (nodes[i].isParent) {
						nodes[i].iconSkin = nodes[i].iconSkin ? null : "pIcon01";
					} else {
						nodes[i].iconSkin = "icon0" + (iconCount++);
					}
				} else if (type == "color") {
					color = [0, 0, 0];
					var r1 = Math.round(Math.random()*3 - 0.5);
					color[r1] = 15;
					var r2 = Math.round(Math.random()*3 - 0.5);
					while (r2 === r1) {
						r2 = Math.round(Math.random()*3 - 0.5);
					}
					color[r2] = Math.round(Math.random()*16-0.5);
					zTree.setting.view.fontCss["color"] = "#"+color[0].toString(16)+color[1].toString(16)+color[2].toString(16);
				} else if (type == "font") {
					var style = $("#" + nodes[i].tId + "_a").css("font-style") ;
					style = (style=="italic" ? "normal" : "italic");
					zTree.setting.view.fontCss["font-style"] = style;
				}
				zTree.updateNode(nodes[i]);
			}
		}

		$(document).ready(function(){
			$.fn.zTree.init($("#treeDemo"), setting, zNodes);
			$("#rename").bind("click", {type:"rename"}, updateNode);
			$("#changeIcon").bind("click", {type:"icon"}, updateNode);
			$("#changeColor").bind("click", {type:"color"}, updateNode);
			$("#changeFont").bind("click", {type:"font"}, updateNode);
		});
		//-->
	</SCRIPT>
	<style type="text/css">
.ztree li span.button.pIcon01_ico_open{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/1_open.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.pIcon01_ico_close{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/1_close.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.icon01_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/2.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.icon02_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/3.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.icon03_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/4.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.icon04_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/5.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.icon05_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/6.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.icon06_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/7.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.icon07_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/8.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.icon08_ico_docu{margin-right:2px; background: url(../../../css/zTreeStyle/img/diy/9.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
	</style>
</HEAD>

<BODY>
<h1>Update Node - zTree methods</h1>
<h6>[ File Path: core/update_fun.html ]</h6>
<div class="content_wrap">
	<div class="zTreeDemoBackground left">
		<ul id="treeDemo" class="ztree"></ul>
	</div>
	<div class="right">
		<ul class="info">
			<li class="title"><h2>1, Explanation of updateNode</h2>
				<ul class="list">
				<li class="highlight_red">How to use zTreeObj.updateNode method,  please see the API documentation.</li>
				<li><p>This Demo can only select one parent node<br/>
					Try: [ <a id="rename" href="#" onclick="return false;">Change Name</a> ]
					&nbsp;&nbsp;&nbsp;&nbsp;[ <a id="changeIcon" href="#" onclick="return false;">Change Icon</a> ]<br/>
					&nbsp;&nbsp;&nbsp;&nbsp;[ <a id="changeColor" href="#" onclick="return false;">Change Color</a> ]
					&nbsp;&nbsp;&nbsp;&nbsp;[ <a id="changeFont" href="#" onclick="return false;">Change Italic Font</a> ]</p>
				</li>
				</ul>
			</li>
			<li class="title"><h2>2, Explanation of setting</h2>
				<ul class="list">
				<li>Do not need to set the setting.</li>
				<li class="highlight_red">You can modify the node style with setting.view.fontCss.</li>
				</ul>
			</li>
			<li class="title"><h2>3, Explanation of treeNode</h2>
				<ul class="list">
				<li class="highlight_red">In the API documentation has detail description about the 'zTreeObj.updateNode method'.</li>
				</ul>
			</li>
			<li class="title"><h2>4、Other explanation</h2>
				<ul class="list">
				<li>This demo only made ​by the core js, so does not include the checkbox update.</li>
				</ul>
			</li>
		</ul>
	</div>
</div>
</BODY>
</HTML>