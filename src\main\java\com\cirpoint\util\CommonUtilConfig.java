package com.cirpoint.util;

import com.cirpoint.config.ThingworxConfig;
import com.cirpoint.service.ApplicationConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
public class CommonUtilConfig extends ApplicationConfig {

    @Autowired
    private ThingworxConfig thingworxConfig;

    @PostConstruct
    public void init() {
        CommonUtil.setTempPath(tempPath);
        CommonUtil.setThingworxDomain(thingworxConfig.getDomain());
        CommonUtil.setThingworxKey(thingworxConfig.getKey());
        FileUploadUtil.setTempPath(tempPath);
        FileUploadUtil.setFileUploadPath(fileUploadPath);
    }
}
