!function(E,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.sqlFormatter=e():E.sqlFormatter=e()}(window,(function(){return function(E){var e={};function t(T){if(e[T])return e[T].exports;var R=e[T]={i:T,l:!1,exports:{}};return E[T].call(R.exports,R,R.exports,t),R.l=!0,R.exports}return t.m=E,t.c=e,t.d=function(E,e,T){t.o(E,e)||Object.defineProperty(E,e,{enumerable:!0,get:T})},t.r=function(E){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(E,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(E,"__esModule",{value:!0})},t.t=function(E,e){if(1&e&&(E=t(E)),8&e)return E;if(4&e&&"object"==typeof E&&E&&E.__esModule)return E;var T=Object.create(null);if(t.r(T),Object.defineProperty(T,"default",{enumerable:!0,value:E}),2&e&&"string"!=typeof E)for(var R in E)t.d(T,R,function(e){return E[e]}.bind(null,R));return T},t.n=function(E){var e=E&&E.__esModule?function(){return E.default}:function(){return E};return t.d(e,"a",e),e},t.o=function(E,e){return Object.prototype.hasOwnProperty.call(E,e)},t.p="",t(t.s=0)}([function(E,e,t){"use strict";t.r(e),t.d(e,"format",(function(){return Ze})),t.d(e,"supportedDialects",(function(){return Qe}));var T={WORD:"word",STRING:"string",RESERVED:"reserved",RESERVED_TOP_LEVEL:"reserved-top-level",RESERVED_TOP_LEVEL_NO_INDENT:"reserved-top-level-no-indent",RESERVED_NEWLINE:"reserved-newline",OPERATOR:"operator",OPEN_PAREN:"open-paren",CLOSE_PAREN:"close-paren",LINE_COMMENT:"line-comment",BLOCK_COMMENT:"block-comment",NUMBER:"number",PLACEHOLDER:"placeholder"},R=function(E){return E.replace(/[\t ]+$/,"")},n=function(E){return E.replace(/[\$\(-\+\.\?\[-\^\{-\}]/g,"\\$&")},r=function(E){return E.sort((function(E,e){return e.length-E.length||E.localeCompare(e)}))};function N(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}var I=function(){function E(e){!function(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}(this,E),this.indent=e||"  ",this.indentTypes=[]}var e,t,T;return e=E,(t=[{key:"getIndent",value:function(){return this.indent.repeat(this.indentTypes.length)}},{key:"increaseTopLevel",value:function(){this.indentTypes.push("top-level")}},{key:"increaseBlockLevel",value:function(){this.indentTypes.push("block-level")}},{key:"decreaseTopLevel",value:function(){var E;this.indentTypes.length>0&&"top-level"===(E=this.indentTypes)[E.length-1]&&this.indentTypes.pop()}},{key:"decreaseBlockLevel",value:function(){for(;this.indentTypes.length>0&&"top-level"===this.indentTypes.pop(););}},{key:"resetIndentation",value:function(){this.indentTypes=[]}}])&&N(e.prototype,t),T&&N(e,T),E}();function A(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}var O=function(){function E(){!function(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}(this,E),this.level=0}var e,t,R;return e=E,(t=[{key:"beginIfPossible",value:function(E,e){0===this.level&&this.isInlineBlock(E,e)?this.level=1:this.level>0?this.level++:this.level=0}},{key:"end",value:function(){this.level--}},{key:"isActive",value:function(){return this.level>0}},{key:"isInlineBlock",value:function(E,e){for(var t=0,R=0,n=e;n<E.length;n++){var r=E[n];if((t+=r.value.length)>50)return!1;if(r.type===T.OPEN_PAREN)R++;else if(r.type===T.CLOSE_PAREN&&0==--R)return!0;if(this.isForbiddenToken(r))return!1}return!1}},{key:"isForbiddenToken",value:function(E){var e=E.type,t=E.value;return e===T.RESERVED_TOP_LEVEL||e===T.RESERVED_NEWLINE||e===T.COMMENT||e===T.BLOCK_COMMENT||";"===t}}])&&A(e.prototype,t),R&&A(e,R),E}();function o(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}var S=function(){function E(e){!function(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}(this,E),this.params=e,this.index=0}var e,t,T;return e=E,(t=[{key:"get",value:function(E){var e=E.key,t=E.value;return this.params?e?this.params[e]:this.params[this.index++]:t}}])&&o(e.prototype,t),T&&o(e,T),E}(),L=function(E,e){return function(t){return(null==t?void 0:t.type)===E&&e.test(null==t?void 0:t.value)}},i=L(T.RESERVED_NEWLINE,/^AND$/i),C=L(T.RESERVED,/^BETWEEN$/i),u=L(T.RESERVED_TOP_LEVEL,/^LIMIT$/i),c=L(T.RESERVED_TOP_LEVEL,/^[S\u017F]ET$/i),a=L(T.RESERVED,/^BY$/i),s=L(T.RESERVED_TOP_LEVEL,/^WINDOW$/i),U=L(T.CLOSE_PAREN,/^END$/i);function D(E,e,t){return e in E?Object.defineProperty(E,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):E[e]=t,E}function l(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}var f=function(){function E(e){!function(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}(this,E),this.cfg=e,this.indentation=new I(this.cfg.indent),this.inlineBlock=new O,this.params=new S(this.cfg.params),this.previousReservedToken={},this.tokens=[],this.index=0}var e,t,n;return e=E,(t=[{key:"tokenizer",value:function(){throw new Error("tokenizer() not implemented by subclass")}},{key:"tokenOverride",value:function(E){return E}},{key:"format",value:function(E){return this.tokens=this.tokenizer().tokenize(E),this.getFormattedQueryFromTokens().trim()}},{key:"getFormattedQueryFromTokens",value:function(){var E=this,e="";return this.tokens.forEach((function(t,R){E.index=R,(t=E.tokenOverride(t)).type===T.LINE_COMMENT?e=E.formatLineComment(t,e):t.type===T.BLOCK_COMMENT?e=E.formatBlockComment(t,e):t.type===T.RESERVED_TOP_LEVEL?(e=E.formatTopLevelReservedWord(t,e),E.previousReservedToken=t):t.type===T.RESERVED_TOP_LEVEL_NO_INDENT?(e=E.formatTopLevelReservedWordNoIndent(t,e),E.previousReservedToken=t):t.type===T.RESERVED_NEWLINE?(e=E.formatNewlineReservedWord(t,e),E.previousReservedToken=t):t.type===T.RESERVED?(e=E.formatWithSpaces(t,e),E.previousReservedToken=t):e=t.type===T.OPEN_PAREN?E.formatOpeningParentheses(t,e):t.type===T.CLOSE_PAREN?E.formatClosingParentheses(t,e):t.type===T.PLACEHOLDER?E.formatPlaceholder(t,e):","===t.value?E.formatComma(t,e):":"===t.value?E.formatWithSpaceAfter(t,e):"."===t.value?E.formatWithoutSpaces(t,e):";"===t.value?E.formatQuerySeparator(t,e):E.formatWithSpaces(t,e)})),e}},{key:"formatLineComment",value:function(E,e){return this.addNewline(e+this.show(E))}},{key:"formatBlockComment",value:function(E,e){return this.addNewline(this.addNewline(e)+this.indentComment(E.value))}},{key:"indentComment",value:function(E){return E.replace(/\n[\t ]*/g,"\n"+this.indentation.getIndent()+" ")}},{key:"formatTopLevelReservedWordNoIndent",value:function(E,e){return this.indentation.decreaseTopLevel(),e=this.addNewline(e)+this.equalizeWhitespace(this.show(E)),this.addNewline(e)}},{key:"formatTopLevelReservedWord",value:function(E,e){return this.indentation.decreaseTopLevel(),e=this.addNewline(e),this.indentation.increaseTopLevel(),e+=this.equalizeWhitespace(this.show(E)),this.addNewline(e)}},{key:"formatNewlineReservedWord",value:function(E,e){return i(E)&&C(this.tokenLookBehind(2))?this.formatWithSpaces(E,e):this.addNewline(e)+this.equalizeWhitespace(this.show(E))+" "}},{key:"equalizeWhitespace",value:function(E){return E.replace(/[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+/g," ")}},{key:"formatOpeningParentheses",value:function(E,e){var t,n,r=(D(t={},T.OPEN_PAREN,!0),D(t,T.LINE_COMMENT,!0),D(t,T.OPERATOR,!0),t);return 0!==E.whitespaceBefore.length||r[null===(n=this.tokenLookBehind())||void 0===n?void 0:n.type]||(e=R(e)),e+=this.show(E),this.inlineBlock.beginIfPossible(this.tokens,this.index),this.inlineBlock.isActive()||(this.indentation.increaseBlockLevel(),e=this.addNewline(e)),e}},{key:"formatClosingParentheses",value:function(E,e){return this.inlineBlock.isActive()?(this.inlineBlock.end(),this.formatWithSpaceAfter(E,e)):(this.indentation.decreaseBlockLevel(),this.formatWithSpaces(E,this.addNewline(e)))}},{key:"formatPlaceholder",value:function(E,e){return e+this.params.get(E)+" "}},{key:"formatComma",value:function(E,e){return e=R(e)+this.show(E)+" ",this.inlineBlock.isActive()||u(this.previousReservedToken)?e:this.addNewline(e)}},{key:"formatWithSpaceAfter",value:function(E,e){return R(e)+this.show(E)+" "}},{key:"formatWithoutSpaces",value:function(E,e){return R(e)+this.show(E)}},{key:"formatWithSpaces",value:function(E,e){return e+this.show(E)+" "}},{key:"formatQuerySeparator",value:function(E,e){return this.indentation.resetIndentation(),R(e)+this.show(E)+"\n".repeat(this.cfg.linesBetweenQueries||1)}},{key:"show",value:function(E){var e=E.type,t=E.value;return!this.cfg.uppercase||e!==T.RESERVED&&e!==T.RESERVED_TOP_LEVEL&&e!==T.RESERVED_TOP_LEVEL_NO_INDENT&&e!==T.RESERVED_NEWLINE&&e!==T.OPEN_PAREN&&e!==T.CLOSE_PAREN?t:t.toUpperCase()}},{key:"addNewline",value:function(E){return(E=R(E)).endsWith("\n")||(E+="\n"),E+this.indentation.getIndent()}},{key:"tokenLookBehind",value:function(){var E=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return this.tokens[this.index-E]}},{key:"tokenLookAhead",value:function(){var E=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return this.tokens[this.index+E]}}])&&l(e.prototype,t),n&&l(e,n),E}();function P(E){if(0===E.length)return new RegExp("^\b$","u");var e=r(E).join("|").replace(/ /g,"\\s+");return new RegExp("^(".concat(e,")\\b"),"iu")}function M(E){var e={"``":"((`[^`]*($|`))+)","{}":"((\\{[^\\}]*($|\\}))+)","[]":"((\\[[^\\]]*($|\\]))(\\][^\\]]*($|\\]))*)",'""':'(("[^"\\\\]*(?:\\\\.[^"\\\\]*)*("|$))+)',"''":"(('[^'\\\\]*(?:\\\\.[^'\\\\]*)*('|$))+)","N''":"((N'[^'\\\\]*(?:\\\\.[^'\\\\]*)*('|$))+)","U&''":"((U&'[^'\\\\]*(?:\\\\.[^'\\\\]*)*('|$))+)",'U&""':'((U&"[^"\\\\]*(?:\\\\.[^"\\\\]*)*("|$))+)',$$:"((?<tag>\\$\\w*\\$)[\\s\\S]*?(?:\\k<tag>|$))"};return E.map((function(E){return e[E]})).join("|")}function p(E){return new RegExp("^("+E.map(y).join("|")+")","iu")}function y(E){return 1===E.length?n(E):"\\b"+E+"\\b"}function _(E,e){if(t=E,!Array.isArray(t)||0===t.length)return!1;var t,T=E.map(n).join("|");return new RegExp("^((?:".concat(T,")(?:").concat(e,"))"),"u")}function h(E,e){var t=Object.keys(E);if(Object.getOwnPropertySymbols){var T=Object.getOwnPropertySymbols(E);e&&(T=T.filter((function(e){return Object.getOwnPropertyDescriptor(E,e).enumerable}))),t.push.apply(t,T)}return t}function G(E){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?h(Object(t),!0).forEach((function(e){d(E,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(E,Object.getOwnPropertyDescriptors(t)):h(Object(t)).forEach((function(e){Object.defineProperty(E,e,Object.getOwnPropertyDescriptor(t,e))}))}return E}function d(E,e,t){return e in E?Object.defineProperty(E,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):E[e]=t,E}function F(E){return function(E){if(Array.isArray(E))return v(E)}(E)||function(E){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(E))return Array.from(E)}(E)||function(E,e){if(!E)return;if("string"==typeof E)return v(E,e);var t=Object.prototype.toString.call(E).slice(8,-1);"Object"===t&&E.constructor&&(t=E.constructor.name);if("Map"===t||"Set"===t)return Array.from(E);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return v(E,e)}(E)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(E,e){(null==e||e>E.length)&&(e=E.length);for(var t=0,T=new Array(e);t<e;t++)T[t]=E[t];return T}function H(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}var B=function(){function E(e){var t,T,R;!function(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}(this,E),this.WHITESPACE_REGEX=/^([\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+)/,this.NUMBER_REGEX=/^((\x2D[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?[0-9]+(\.[0-9]+)?([Ee]\x2D?[0-9]+(\.[0-9]+)?)?|0x[0-9A-Fa-f]+|0b[01]+)\b/,this.OPERATOR_REGEX=(t=["<>","<=",">="].concat(F(e.operators||[])),new RegExp("^(".concat(r(t).map(n).join("|"),"|.)"),"u")),this.BLOCK_COMMENT_REGEX=/^(\/\*(?:(?![])[\s\S])*?(?:\*\/|$))/,this.LINE_COMMENT_REGEX=(T=e.lineCommentTypes,new RegExp("^((?:".concat(T.map((function(E){return n(E)})).join("|"),").*?)(?:\r\n|\r|\n|$)"),"u")),this.RESERVED_TOP_LEVEL_REGEX=P(e.reservedTopLevelWords),this.RESERVED_TOP_LEVEL_NO_INDENT_REGEX=P(e.reservedTopLevelWordsNoIndent),this.RESERVED_NEWLINE_REGEX=P(e.reservedNewlineWords),this.RESERVED_PLAIN_REGEX=P(e.reservedWords),this.WORD_REGEX=function(){var E=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return new RegExp("^([\\p{Alphabetic}\\p{Mark}\\p{Decimal_Number}\\p{Connector_Punctuation}\\p{Join_Control}".concat(E.join(""),"]+)"),"u")}(e.specialWordChars),this.STRING_REGEX=(R=e.stringTypes,new RegExp("^("+M(R)+")","u")),this.OPEN_PAREN_REGEX=p(e.openParens),this.CLOSE_PAREN_REGEX=p(e.closeParens),this.INDEXED_PLACEHOLDER_REGEX=_(e.indexedPlaceholderTypes,"[0-9]*"),this.IDENT_NAMED_PLACEHOLDER_REGEX=_(e.namedPlaceholderTypes,"[a-zA-Z0-9._$]+"),this.STRING_NAMED_PLACEHOLDER_REGEX=_(e.namedPlaceholderTypes,M(e.stringTypes))}var e,t,R;return e=E,(t=[{key:"tokenize",value:function(E){for(var e,t=[];E.length;){var T=this.getWhitespace(E);(E=E.substring(T.length)).length&&(e=this.getNextToken(E,e),E=E.substring(e.value.length),t.push(G(G({},e),{},{whitespaceBefore:T})))}return t}},{key:"getWhitespace",value:function(E){var e=E.match(this.WHITESPACE_REGEX);return e?e[1]:""}},{key:"getNextToken",value:function(E,e){return this.getCommentToken(E)||this.getStringToken(E)||this.getOpenParenToken(E)||this.getCloseParenToken(E)||this.getPlaceholderToken(E)||this.getNumberToken(E)||this.getReservedWordToken(E,e)||this.getWordToken(E)||this.getOperatorToken(E)}},{key:"getCommentToken",value:function(E){return this.getLineCommentToken(E)||this.getBlockCommentToken(E)}},{key:"getLineCommentToken",value:function(E){return this.getTokenOnFirstMatch({input:E,type:T.LINE_COMMENT,regex:this.LINE_COMMENT_REGEX})}},{key:"getBlockCommentToken",value:function(E){return this.getTokenOnFirstMatch({input:E,type:T.BLOCK_COMMENT,regex:this.BLOCK_COMMENT_REGEX})}},{key:"getStringToken",value:function(E){return this.getTokenOnFirstMatch({input:E,type:T.STRING,regex:this.STRING_REGEX})}},{key:"getOpenParenToken",value:function(E){return this.getTokenOnFirstMatch({input:E,type:T.OPEN_PAREN,regex:this.OPEN_PAREN_REGEX})}},{key:"getCloseParenToken",value:function(E){return this.getTokenOnFirstMatch({input:E,type:T.CLOSE_PAREN,regex:this.CLOSE_PAREN_REGEX})}},{key:"getPlaceholderToken",value:function(E){return this.getIdentNamedPlaceholderToken(E)||this.getStringNamedPlaceholderToken(E)||this.getIndexedPlaceholderToken(E)}},{key:"getIdentNamedPlaceholderToken",value:function(E){return this.getPlaceholderTokenWithKey({input:E,regex:this.IDENT_NAMED_PLACEHOLDER_REGEX,parseKey:function(E){return E.slice(1)}})}},{key:"getStringNamedPlaceholderToken",value:function(E){var e=this;return this.getPlaceholderTokenWithKey({input:E,regex:this.STRING_NAMED_PLACEHOLDER_REGEX,parseKey:function(E){return e.getEscapedPlaceholderKey({key:E.slice(2,-1),quoteChar:E.slice(-1)})}})}},{key:"getIndexedPlaceholderToken",value:function(E){return this.getPlaceholderTokenWithKey({input:E,regex:this.INDEXED_PLACEHOLDER_REGEX,parseKey:function(E){return E.slice(1)}})}},{key:"getPlaceholderTokenWithKey",value:function(E){var e=E.input,t=E.regex,R=E.parseKey,n=this.getTokenOnFirstMatch({input:e,regex:t,type:T.PLACEHOLDER});return n&&(n.key=R(n.value)),n}},{key:"getEscapedPlaceholderKey",value:function(E){var e=E.key,t=E.quoteChar;return e.replace(new RegExp(n("\\"+t),"gu"),t)}},{key:"getNumberToken",value:function(E){return this.getTokenOnFirstMatch({input:E,type:T.NUMBER,regex:this.NUMBER_REGEX})}},{key:"getOperatorToken",value:function(E){return this.getTokenOnFirstMatch({input:E,type:T.OPERATOR,regex:this.OPERATOR_REGEX})}},{key:"getReservedWordToken",value:function(E,e){if(!e||!e.value||"."!==e.value)return this.getTopLevelReservedToken(E)||this.getNewlineReservedToken(E)||this.getTopLevelReservedTokenNoIndent(E)||this.getPlainReservedToken(E)}},{key:"getTopLevelReservedToken",value:function(E){return this.getTokenOnFirstMatch({input:E,type:T.RESERVED_TOP_LEVEL,regex:this.RESERVED_TOP_LEVEL_REGEX})}},{key:"getNewlineReservedToken",value:function(E){return this.getTokenOnFirstMatch({input:E,type:T.RESERVED_NEWLINE,regex:this.RESERVED_NEWLINE_REGEX})}},{key:"getTopLevelReservedTokenNoIndent",value:function(E){return this.getTokenOnFirstMatch({input:E,type:T.RESERVED_TOP_LEVEL_NO_INDENT,regex:this.RESERVED_TOP_LEVEL_NO_INDENT_REGEX})}},{key:"getPlainReservedToken",value:function(E){return this.getTokenOnFirstMatch({input:E,type:T.RESERVED,regex:this.RESERVED_PLAIN_REGEX})}},{key:"getWordToken",value:function(E){return this.getTokenOnFirstMatch({input:E,type:T.WORD,regex:this.WORD_REGEX})}},{key:"getTokenOnFirstMatch",value:function(E){var e=E.input,t=E.type,T=E.regex,R=e.match(T);return R?{type:t,value:R[1]}:void 0}}])&&H(e.prototype,t),R&&H(e,R),E}();function b(E){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E})(E)}function V(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}function Y(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}function W(E,e){return(W=Object.setPrototypeOf||function(E,e){return E.__proto__=e,E})(E,e)}function m(E){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(E){return!1}}();return function(){var t,T=X(E);if(e){var R=X(this).constructor;t=Reflect.construct(T,arguments,R)}else t=T.apply(this,arguments);return g(this,t)}}function g(E,e){return!e||"object"!==b(e)&&"function"!=typeof e?function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E):e}function X(E){return(X=Object.setPrototypeOf?Object.getPrototypeOf:function(E){return E.__proto__||Object.getPrototypeOf(E)})(E)}var k=["ABS","ACTIVATE","ALIAS","ALL","ALLOCATE","ALLOW","ALTER","ANY","ARE","ARRAY","AS","ASC","ASENSITIVE","ASSOCIATE","ASUTIME","ASYMMETRIC","AT","ATOMIC","ATTRIBUTES","AUDIT","AUTHORIZATION","AUX","AUXILIARY","AVG","BEFORE","BEGIN","BETWEEN","BIGINT","BINARY","BLOB","BOOLEAN","BOTH","BUFFERPOOL","BY","CACHE","CALL","CALLED","CAPTURE","CARDINALITY","CASCADED","CASE","CAST","CCSID","CEIL","CEILING","CHAR","CHARACTER","CHARACTER_LENGTH","CHAR_LENGTH","CHECK","CLOB","CLONE","CLOSE","CLUSTER","COALESCE","COLLATE","COLLECT","COLLECTION","COLLID","COLUMN","COMMENT","COMMIT","CONCAT","CONDITION","CONNECT","CONNECTION","CONSTRAINT","CONTAINS","CONTINUE","CONVERT","CORR","CORRESPONDING","COUNT","COUNT_BIG","COVAR_POP","COVAR_SAMP","CREATE","CROSS","CUBE","CUME_DIST","CURRENT","CURRENT_DATE","CURRENT_DEFAULT_TRANSFORM_GROUP","CURRENT_LC_CTYPE","CURRENT_PATH","CURRENT_ROLE","CURRENT_SCHEMA","CURRENT_SERVER","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_TIMEZONE","CURRENT_TRANSFORM_GROUP_FOR_TYPE","CURRENT_USER","CURSOR","CYCLE","DATA","DATABASE","DATAPARTITIONNAME","DATAPARTITIONNUM","DATE","DAY","DAYS","DB2GENERAL","DB2GENRL","DB2SQL","DBINFO","DBPARTITIONNAME","DBPARTITIONNUM","DEALLOCATE","DEC","DECIMAL","DECLARE","DEFAULT","DEFAULTS","DEFINITION","DELETE","DENSERANK","DENSE_RANK","DEREF","DESCRIBE","DESCRIPTOR","DETERMINISTIC","DIAGNOSTICS","DISABLE","DISALLOW","DISCONNECT","DISTINCT","DO","DOCUMENT","DOUBLE","DROP","DSSIZE","DYNAMIC","EACH","EDITPROC","ELEMENT","ELSE","ELSEIF","ENABLE","ENCODING","ENCRYPTION","END","END-EXEC","ENDING","ERASE","ESCAPE","EVERY","EXCEPTION","EXCLUDING","EXCLUSIVE","EXEC","EXECUTE","EXISTS","EXIT","EXP","EXPLAIN","EXTENDED","EXTERNAL","EXTRACT","FALSE","FENCED","FETCH","FIELDPROC","FILE","FILTER","FINAL","FIRST","FLOAT","FLOOR","FOR","FOREIGN","FREE","FULL","FUNCTION","FUSION","GENERAL","GENERATED","GET","GLOBAL","GOTO","GRANT","GRAPHIC","GROUP","GROUPING","HANDLER","HASH","HASHED_VALUE","HINT","HOLD","HOUR","HOURS","IDENTITY","IF","IMMEDIATE","IN","INCLUDING","INCLUSIVE","INCREMENT","INDEX","INDICATOR","INDICATORS","INF","INFINITY","INHERIT","INNER","INOUT","INSENSITIVE","INSERT","INT","INTEGER","INTEGRITY","INTERSECTION","INTERVAL","INTO","IS","ISOBID","ISOLATION","ITERATE","JAR","JAVA","KEEP","KEY","LABEL","LANGUAGE","LARGE","LATERAL","LC_CTYPE","LEADING","LEAVE","LEFT","LIKE","LINKTYPE","LN","LOCAL","LOCALDATE","LOCALE","LOCALTIME","LOCALTIMESTAMP","LOCATOR","LOCATORS","LOCK","LOCKMAX","LOCKSIZE","LONG","LOOP","LOWER","MAINTAINED","MATCH","MATERIALIZED","MAX","MAXVALUE","MEMBER","MERGE","METHOD","MICROSECOND","MICROSECONDS","MIN","MINUTE","MINUTES","MINVALUE","MOD","MODE","MODIFIES","MODULE","MONTH","MONTHS","MULTISET","NAN","NATIONAL","NATURAL","NCHAR","NCLOB","NEW","NEW_TABLE","NEXTVAL","NO","NOCACHE","NOCYCLE","NODENAME","NODENUMBER","NOMAXVALUE","NOMINVALUE","NONE","NOORDER","NORMALIZE","NORMALIZED","NOT","NULL","NULLIF","NULLS","NUMERIC","NUMPARTS","OBID","OCTET_LENGTH","OF","OFFSET","OLD","OLD_TABLE","ON","ONLY","OPEN","OPTIMIZATION","OPTIMIZE","OPTION","ORDER","OUT","OUTER","OVER","OVERLAPS","OVERLAY","OVERRIDING","PACKAGE","PADDED","PAGESIZE","PARAMETER","PART","PARTITION","PARTITIONED","PARTITIONING","PARTITIONS","PASSWORD","PATH","PERCENTILE_CONT","PERCENTILE_DISC","PERCENT_RANK","PIECESIZE","PLAN","POSITION","POWER","PRECISION","PREPARE","PREVVAL","PRIMARY","PRIQTY","PRIVILEGES","PROCEDURE","PROGRAM","PSID","PUBLIC","QUERY","QUERYNO","RANGE","RANK","READ","READS","REAL","RECOVERY","RECURSIVE","REF","REFERENCES","REFERENCING","REFRESH","REGR_AVGX","REGR_AVGY","REGR_COUNT","REGR_INTERCEPT","REGR_R2","REGR_SLOPE","REGR_SXX","REGR_SXY","REGR_SYY","RELEASE","RENAME","REPEAT","RESET","RESIGNAL","RESTART","RESTRICT","RESULT","RESULT_SET_LOCATOR","RETURN","RETURNS","REVOKE","RIGHT","ROLE","ROLLBACK","ROLLUP","ROUND_CEILING","ROUND_DOWN","ROUND_FLOOR","ROUND_HALF_DOWN","ROUND_HALF_EVEN","ROUND_HALF_UP","ROUND_UP","ROUTINE","ROW","ROWNUMBER","ROWS","ROWSET","ROW_NUMBER","RRN","RUN","SAVEPOINT","SCHEMA","SCOPE","SCRATCHPAD","SCROLL","SEARCH","SECOND","SECONDS","SECQTY","SECURITY","SENSITIVE","SEQUENCE","SESSION","SESSION_USER","SIGNAL","SIMILAR","SIMPLE","SMALLINT","SNAN","SOME","SOURCE","SPECIFIC","SPECIFICTYPE","SQL","SQLEXCEPTION","SQLID","SQLSTATE","SQLWARNING","SQRT","STACKED","STANDARD","START","STARTING","STATEMENT","STATIC","STATMENT","STAY","STDDEV_POP","STDDEV_SAMP","STOGROUP","STORES","STYLE","SUBMULTISET","SUBSTRING","SUM","SUMMARY","SYMMETRIC","SYNONYM","SYSFUN","SYSIBM","SYSPROC","SYSTEM","SYSTEM_USER","TABLE","TABLESAMPLE","TABLESPACE","THEN","TIME","TIMESTAMP","TIMEZONE_HOUR","TIMEZONE_MINUTE","TO","TRAILING","TRANSACTION","TRANSLATE","TRANSLATION","TREAT","TRIGGER","TRIM","TRUE","TRUNCATE","TYPE","UESCAPE","UNDO","UNIQUE","UNKNOWN","UNNEST","UNTIL","UPPER","USAGE","USER","USING","VALIDPROC","VALUE","VARCHAR","VARIABLE","VARIANT","VARYING","VAR_POP","VAR_SAMP","VCAT","VERSION","VIEW","VOLATILE","VOLUMES","WHEN","WHENEVER","WHILE","WIDTH_BUCKET","WINDOW","WITH","WITHIN","WITHOUT","WLM","WRITE","XMLELEMENT","XMLEXISTS","XMLNAMESPACES","YEAR","YEARS"],w=["ADD","AFTER","ALTER COLUMN","ALTER TABLE","DELETE FROM","EXCEPT","FETCH FIRST","FROM","GROUP BY","GO","HAVING","INSERT INTO","INTERSECT","LIMIT","ORDER BY","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","UPDATE","VALUES","WHERE"],K=["INTERSECT","INTERSECT ALL","MINUS","UNION","UNION ALL"],J=["AND","OR","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],j=function(E){!function(E,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(e&&e.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),e&&W(E,e)}(n,E);var e,t,T,R=m(n);function n(){return V(this,n),R.apply(this,arguments)}return e=n,(t=[{key:"tokenizer",value:function(){return new B({reservedWords:k,reservedTopLevelWords:w,reservedNewlineWords:J,reservedTopLevelWordsNoIndent:K,stringTypes:['""',"''","``","[]"],openParens:["("],closeParens:[")"],indexedPlaceholderTypes:["?"],namedPlaceholderTypes:[":"],lineCommentTypes:["--"],specialWordChars:["#","@"],operators:["**","!=","!>","!>","||"]})}}])&&Y(e.prototype,t),T&&Y(e,T),n}(f);function x(E){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E})(E)}function Z(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}function Q(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}function $(E,e){return($=Object.setPrototypeOf||function(E,e){return E.__proto__=e,E})(E,e)}function z(E){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(E){return!1}}();return function(){var t,T=EE(E);if(e){var R=EE(this).constructor;t=Reflect.construct(T,arguments,R)}else t=T.apply(this,arguments);return q(this,t)}}function q(E,e){return!e||"object"!==x(e)&&"function"!=typeof e?function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E):e}function EE(E){return(EE=Object.setPrototypeOf?Object.getPrototypeOf:function(E){return E.__proto__||Object.getPrototypeOf(E)})(E)}var eE=["ACCESSIBLE","ADD","ALL","ALTER","ANALYZE","AND","AS","ASC","ASENSITIVE","BEFORE","BETWEEN","BIGINT","BINARY","BLOB","BOTH","BY","CALL","CASCADE","CASE","CHANGE","CHAR","CHARACTER","CHECK","COLLATE","COLUMN","CONDITION","CONSTRAINT","CONTINUE","CONVERT","CREATE","CROSS","CURRENT_DATE","CURRENT_ROLE","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","CURSOR","DATABASE","DATABASES","DAY_HOUR","DAY_MICROSECOND","DAY_MINUTE","DAY_SECOND","DEC","DECIMAL","DECLARE","DEFAULT","DELAYED","DELETE","DESC","DESCRIBE","DETERMINISTIC","DISTINCT","DISTINCTROW","DIV","DO_DOMAIN_IDS","DOUBLE","DROP","DUAL","EACH","ELSE","ELSEIF","ENCLOSED","ESCAPED","EXCEPT","EXISTS","EXIT","EXPLAIN","FALSE","FETCH","FLOAT","FLOAT4","FLOAT8","FOR","FORCE","FOREIGN","FROM","FULLTEXT","GENERAL","GRANT","GROUP","HAVING","HIGH_PRIORITY","HOUR_MICROSECOND","HOUR_MINUTE","HOUR_SECOND","IF","IGNORE","IGNORE_DOMAIN_IDS","IGNORE_SERVER_IDS","IN","INDEX","INFILE","INNER","INOUT","INSENSITIVE","INSERT","INT","INT1","INT2","INT3","INT4","INT8","INTEGER","INTERSECT","INTERVAL","INTO","IS","ITERATE","JOIN","KEY","KEYS","KILL","LEADING","LEAVE","LEFT","LIKE","LIMIT","LINEAR","LINES","LOAD","LOCALTIME","LOCALTIMESTAMP","LOCK","LONG","LONGBLOB","LONGTEXT","LOOP","LOW_PRIORITY","MASTER_HEARTBEAT_PERIOD","MASTER_SSL_VERIFY_SERVER_CERT","MATCH","MAXVALUE","MEDIUMBLOB","MEDIUMINT","MEDIUMTEXT","MIDDLEINT","MINUTE_MICROSECOND","MINUTE_SECOND","MOD","MODIFIES","NATURAL","NOT","NO_WRITE_TO_BINLOG","NULL","NUMERIC","ON","OPTIMIZE","OPTION","OPTIONALLY","OR","ORDER","OUT","OUTER","OUTFILE","OVER","PAGE_CHECKSUM","PARSE_VCOL_EXPR","PARTITION","POSITION","PRECISION","PRIMARY","PROCEDURE","PURGE","RANGE","READ","READS","READ_WRITE","REAL","RECURSIVE","REF_SYSTEM_ID","REFERENCES","REGEXP","RELEASE","RENAME","REPEAT","REPLACE","REQUIRE","RESIGNAL","RESTRICT","RETURN","RETURNING","REVOKE","RIGHT","RLIKE","ROWS","SCHEMA","SCHEMAS","SECOND_MICROSECOND","SELECT","SENSITIVE","SEPARATOR","SET","SHOW","SIGNAL","SLOW","SMALLINT","SPATIAL","SPECIFIC","SQL","SQLEXCEPTION","SQLSTATE","SQLWARNING","SQL_BIG_RESULT","SQL_CALC_FOUND_ROWS","SQL_SMALL_RESULT","SSL","STARTING","STATS_AUTO_RECALC","STATS_PERSISTENT","STATS_SAMPLE_PAGES","STRAIGHT_JOIN","TABLE","TERMINATED","THEN","TINYBLOB","TINYINT","TINYTEXT","TO","TRAILING","TRIGGER","TRUE","UNDO","UNION","UNIQUE","UNLOCK","UNSIGNED","UPDATE","USAGE","USE","USING","UTC_DATE","UTC_TIME","UTC_TIMESTAMP","VALUES","VARBINARY","VARCHAR","VARCHARACTER","VARYING","WHEN","WHERE","WHILE","WINDOW","WITH","WRITE","XOR","YEAR_MONTH","ZEROFILL"],tE=["ADD","ALTER COLUMN","ALTER TABLE","DELETE FROM","EXCEPT","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","LIMIT","ORDER BY","SELECT","SET","UPDATE","VALUES","WHERE"],TE=["INTERSECT","INTERSECT ALL","UNION","UNION ALL"],RE=["AND","ELSE","OR","WHEN","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","CROSS JOIN","NATURAL JOIN","STRAIGHT_JOIN","NATURAL LEFT JOIN","NATURAL LEFT OUTER JOIN","NATURAL RIGHT JOIN","NATURAL RIGHT OUTER JOIN"],nE=function(E){!function(E,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(e&&e.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),e&&$(E,e)}(n,E);var e,t,T,R=z(n);function n(){return Z(this,n),R.apply(this,arguments)}return e=n,(t=[{key:"tokenizer",value:function(){return new B({reservedWords:eE,reservedTopLevelWords:tE,reservedNewlineWords:RE,reservedTopLevelWordsNoIndent:TE,stringTypes:["``","''",'""'],openParens:["(","CASE"],closeParens:[")","END"],indexedPlaceholderTypes:["?"],namedPlaceholderTypes:[],lineCommentTypes:["--","#"],specialWordChars:["@"],operators:[":=","<<",">>","!=","<>","<=>","&&","||"]})}}])&&Q(e.prototype,t),T&&Q(e,T),n}(f);function rE(E){return(rE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E})(E)}function NE(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}function IE(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}function AE(E,e){return(AE=Object.setPrototypeOf||function(E,e){return E.__proto__=e,E})(E,e)}function OE(E){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(E){return!1}}();return function(){var t,T=SE(E);if(e){var R=SE(this).constructor;t=Reflect.construct(T,arguments,R)}else t=T.apply(this,arguments);return oE(this,t)}}function oE(E,e){return!e||"object"!==rE(e)&&"function"!=typeof e?function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E):e}function SE(E){return(SE=Object.setPrototypeOf?Object.getPrototypeOf:function(E){return E.__proto__||Object.getPrototypeOf(E)})(E)}var LE=["ACCESSIBLE","ADD","ALL","ALTER","ANALYZE","AND","AS","ASC","ASENSITIVE","BEFORE","BETWEEN","BIGINT","BINARY","BLOB","BOTH","BY","CALL","CASCADE","CASE","CHANGE","CHAR","CHARACTER","CHECK","COLLATE","COLUMN","CONDITION","CONSTRAINT","CONTINUE","CONVERT","CREATE","CROSS","CUBE","CUME_DIST","CURRENT_DATE","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","CURSOR","DATABASE","DATABASES","DAY_HOUR","DAY_MICROSECOND","DAY_MINUTE","DAY_SECOND","DEC","DECIMAL","DECLARE","DEFAULT","DELAYED","DELETE","DENSE_RANK","DESC","DESCRIBE","DETERMINISTIC","DISTINCT","DISTINCTROW","DIV","DOUBLE","DROP","DUAL","EACH","ELSE","ELSEIF","EMPTY","ENCLOSED","ESCAPED","EXCEPT","EXISTS","EXIT","EXPLAIN","FALSE","FETCH","FIRST_VALUE","FLOAT","FLOAT4","FLOAT8","FOR","FORCE","FOREIGN","FROM","FULLTEXT","FUNCTION","GENERATED","GET","GRANT","GROUP","GROUPING","GROUPS","HAVING","HIGH_PRIORITY","HOUR_MICROSECOND","HOUR_MINUTE","HOUR_SECOND","IF","IGNORE","IN","INDEX","INFILE","INNER","INOUT","INSENSITIVE","INSERT","INT","INT1","INT2","INT3","INT4","INT8","INTEGER","INTERVAL","INTO","IO_AFTER_GTIDS","IO_BEFORE_GTIDS","IS","ITERATE","JOIN","JSON_TABLE","KEY","KEYS","KILL","LAG","LAST_VALUE","LATERAL","LEAD","LEADING","LEAVE","LEFT","LIKE","LIMIT","LINEAR","LINES","LOAD","LOCALTIME","LOCALTIMESTAMP","LOCK","LONG","LONGBLOB","LONGTEXT","LOOP","LOW_PRIORITY","MASTER_BIND","MASTER_SSL_VERIFY_SERVER_CERT","MATCH","MAXVALUE","MEDIUMBLOB","MEDIUMINT","MEDIUMTEXT","MIDDLEINT","MINUTE_MICROSECOND","MINUTE_SECOND","MOD","MODIFIES","NATURAL","NOT","NO_WRITE_TO_BINLOG","NTH_VALUE","NTILE","NULL","NUMERIC","OF","ON","OPTIMIZE","OPTIMIZER_COSTS","OPTION","OPTIONALLY","OR","ORDER","OUT","OUTER","OUTFILE","OVER","PARTITION","PERCENT_RANK","PRECISION","PRIMARY","PROCEDURE","PURGE","RANGE","RANK","READ","READS","READ_WRITE","REAL","RECURSIVE","REFERENCES","REGEXP","RELEASE","RENAME","REPEAT","REPLACE","REQUIRE","RESIGNAL","RESTRICT","RETURN","REVOKE","RIGHT","RLIKE","ROW","ROWS","ROW_NUMBER","SCHEMA","SCHEMAS","SECOND_MICROSECOND","SELECT","SENSITIVE","SEPARATOR","SET","SHOW","SIGNAL","SMALLINT","SPATIAL","SPECIFIC","SQL","SQLEXCEPTION","SQLSTATE","SQLWARNING","SQL_BIG_RESULT","SQL_CALC_FOUND_ROWS","SQL_SMALL_RESULT","SSL","STARTING","STORED","STRAIGHT_JOIN","SYSTEM","TABLE","TERMINATED","THEN","TINYBLOB","TINYINT","TINYTEXT","TO","TRAILING","TRIGGER","TRUE","UNDO","UNION","UNIQUE","UNLOCK","UNSIGNED","UPDATE","USAGE","USE","USING","UTC_DATE","UTC_TIME","UTC_TIMESTAMP","VALUES","VARBINARY","VARCHAR","VARCHARACTER","VARYING","VIRTUAL","WHEN","WHERE","WHILE","WINDOW","WITH","WRITE","XOR","YEAR_MONTH","ZEROFILL"],iE=["ADD","ALTER COLUMN","ALTER TABLE","DELETE FROM","EXCEPT","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","LIMIT","ORDER BY","SELECT","SET","UPDATE","VALUES","WHERE"],CE=["INTERSECT","INTERSECT ALL","UNION","UNION ALL"],uE=["AND","ELSE","OR","WHEN","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","CROSS JOIN","NATURAL JOIN","STRAIGHT_JOIN","NATURAL LEFT JOIN","NATURAL LEFT OUTER JOIN","NATURAL RIGHT JOIN","NATURAL RIGHT OUTER JOIN"],cE=function(E){!function(E,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(e&&e.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),e&&AE(E,e)}(n,E);var e,t,T,R=OE(n);function n(){return NE(this,n),R.apply(this,arguments)}return e=n,(t=[{key:"tokenizer",value:function(){return new B({reservedWords:LE,reservedTopLevelWords:iE,reservedNewlineWords:uE,reservedTopLevelWordsNoIndent:CE,stringTypes:["``","''",'""'],openParens:["(","CASE"],closeParens:[")","END"],indexedPlaceholderTypes:["?"],namedPlaceholderTypes:[],lineCommentTypes:["--","#"],specialWordChars:["@"],operators:[":=","<<",">>","!=","<>","<=>","&&","||","->","->>"]})}}])&&IE(e.prototype,t),T&&IE(e,T),n}(f);function aE(E){return(aE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E})(E)}function sE(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}function UE(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}function DE(E,e){return(DE=Object.setPrototypeOf||function(E,e){return E.__proto__=e,E})(E,e)}function lE(E){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(E){return!1}}();return function(){var t,T=PE(E);if(e){var R=PE(this).constructor;t=Reflect.construct(T,arguments,R)}else t=T.apply(this,arguments);return fE(this,t)}}function fE(E,e){return!e||"object"!==aE(e)&&"function"!=typeof e?function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E):e}function PE(E){return(PE=Object.setPrototypeOf?Object.getPrototypeOf:function(E){return E.__proto__||Object.getPrototypeOf(E)})(E)}var ME=["ALL","ALTER","ANALYZE","AND","ANY","ARRAY","AS","ASC","BEGIN","BETWEEN","BINARY","BOOLEAN","BREAK","BUCKET","BUILD","BY","CALL","CASE","CAST","CLUSTER","COLLATE","COLLECTION","COMMIT","CONNECT","CONTINUE","CORRELATE","COVER","CREATE","DATABASE","DATASET","DATASTORE","DECLARE","DECREMENT","DELETE","DERIVED","DESC","DESCRIBE","DISTINCT","DO","DROP","EACH","ELEMENT","ELSE","END","EVERY","EXCEPT","EXCLUDE","EXECUTE","EXISTS","EXPLAIN","FALSE","FETCH","FIRST","FLATTEN","FOR","FORCE","FROM","FUNCTION","GRANT","GROUP","GSI","HAVING","IF","IGNORE","ILIKE","IN","INCLUDE","INCREMENT","INDEX","INFER","INLINE","INNER","INSERT","INTERSECT","INTO","IS","JOIN","KEY","KEYS","KEYSPACE","KNOWN","LAST","LEFT","LET","LETTING","LIKE","LIMIT","LSM","MAP","MAPPING","MATCHED","MATERIALIZED","MERGE","MISSING","NAMESPACE","NEST","NOT","NULL","NUMBER","OBJECT","OFFSET","ON","OPTION","OR","ORDER","OUTER","OVER","PARSE","PARTITION","PASSWORD","PATH","POOL","PREPARE","PRIMARY","PRIVATE","PRIVILEGE","PROCEDURE","PUBLIC","RAW","REALM","REDUCE","RENAME","RETURN","RETURNING","REVOKE","RIGHT","ROLE","ROLLBACK","SATISFIES","SCHEMA","SELECT","SELF","SEMI","SET","SHOW","SOME","START","STATISTICS","STRING","SYSTEM","THEN","TO","TRANSACTION","TRIGGER","TRUE","TRUNCATE","UNDER","UNION","UNIQUE","UNKNOWN","UNNEST","UNSET","UPDATE","UPSERT","USE","USER","USING","VALIDATE","VALUE","VALUED","VALUES","VIA","VIEW","WHEN","WHERE","WHILE","WITH","WITHIN","WORK","XOR"],pE=["DELETE FROM","EXCEPT ALL","EXCEPT","EXPLAIN DELETE FROM","EXPLAIN UPDATE","EXPLAIN UPSERT","FROM","GROUP BY","HAVING","INFER","INSERT INTO","LET","LIMIT","MERGE","NEST","ORDER BY","PREPARE","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","UNNEST","UPDATE","UPSERT","USE KEYS","VALUES","WHERE"],yE=["INTERSECT","INTERSECT ALL","MINUS","UNION","UNION ALL"],_E=["AND","OR","XOR","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN"],hE=function(E){!function(E,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(e&&e.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),e&&DE(E,e)}(n,E);var e,t,T,R=lE(n);function n(){return sE(this,n),R.apply(this,arguments)}return e=n,(t=[{key:"tokenizer",value:function(){return new B({reservedWords:ME,reservedTopLevelWords:pE,reservedNewlineWords:_E,reservedTopLevelWordsNoIndent:yE,stringTypes:['""',"''","``"],openParens:["(","[","{"],closeParens:[")","]","}"],namedPlaceholderTypes:["$"],lineCommentTypes:["#","--"],operators:["==","!="]})}}])&&UE(e.prototype,t),T&&UE(e,T),n}(f);function GE(E){return(GE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E})(E)}function dE(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}function FE(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}function vE(E,e){return(vE=Object.setPrototypeOf||function(E,e){return E.__proto__=e,E})(E,e)}function HE(E){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(E){return!1}}();return function(){var t,T=bE(E);if(e){var R=bE(this).constructor;t=Reflect.construct(T,arguments,R)}else t=T.apply(this,arguments);return BE(this,t)}}function BE(E,e){return!e||"object"!==GE(e)&&"function"!=typeof e?function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E):e}function bE(E){return(bE=Object.setPrototypeOf?Object.getPrototypeOf:function(E){return E.__proto__||Object.getPrototypeOf(E)})(E)}var VE=["A","ACCESSIBLE","AGENT","AGGREGATE","ALL","ALTER","ANY","ARRAY","AS","ASC","AT","ATTRIBUTE","AUTHID","AVG","BETWEEN","BFILE_BASE","BINARY_INTEGER","BINARY","BLOB_BASE","BLOCK","BODY","BOOLEAN","BOTH","BOUND","BREADTH","BULK","BY","BYTE","C","CALL","CALLING","CASCADE","CASE","CHAR_BASE","CHAR","CHARACTER","CHARSET","CHARSETFORM","CHARSETID","CHECK","CLOB_BASE","CLONE","CLOSE","CLUSTER","CLUSTERS","COALESCE","COLAUTH","COLLECT","COLUMNS","COMMENT","COMMIT","COMMITTED","COMPILED","COMPRESS","CONNECT","CONSTANT","CONSTRUCTOR","CONTEXT","CONTINUE","CONVERT","COUNT","CRASH","CREATE","CREDENTIAL","CURRENT","CURRVAL","CURSOR","CUSTOMDATUM","DANGLING","DATA","DATE_BASE","DATE","DAY","DECIMAL","DEFAULT","DEFINE","DELETE","DEPTH","DESC","DETERMINISTIC","DIRECTORY","DISTINCT","DO","DOUBLE","DROP","DURATION","ELEMENT","ELSIF","EMPTY","END","ESCAPE","EXCEPTIONS","EXCLUSIVE","EXECUTE","EXISTS","EXIT","EXTENDS","EXTERNAL","EXTRACT","FALSE","FETCH","FINAL","FIRST","FIXED","FLOAT","FOR","FORALL","FORCE","FROM","FUNCTION","GENERAL","GOTO","GRANT","GROUP","HASH","HEAP","HIDDEN","HOUR","IDENTIFIED","IF","IMMEDIATE","IN","INCLUDING","INDEX","INDEXES","INDICATOR","INDICES","INFINITE","INSTANTIABLE","INT","INTEGER","INTERFACE","INTERVAL","INTO","INVALIDATE","IS","ISOLATION","JAVA","LANGUAGE","LARGE","LEADING","LENGTH","LEVEL","LIBRARY","LIKE","LIKE2","LIKE4","LIKEC","LIMITED","LOCAL","LOCK","LONG","MAP","MAX","MAXLEN","MEMBER","MERGE","MIN","MINUTE","MLSLABEL","MOD","MODE","MONTH","MULTISET","NAME","NAN","NATIONAL","NATIVE","NATURAL","NATURALN","NCHAR","NEW","NEXTVAL","NOCOMPRESS","NOCOPY","NOT","NOWAIT","NULL","NULLIF","NUMBER_BASE","NUMBER","OBJECT","OCICOLL","OCIDATE","OCIDATETIME","OCIDURATION","OCIINTERVAL","OCILOBLOCATOR","OCINUMBER","OCIRAW","OCIREF","OCIREFCURSOR","OCIROWID","OCISTRING","OCITYPE","OF","OLD","ON","ONLY","OPAQUE","OPEN","OPERATOR","OPTION","ORACLE","ORADATA","ORDER","ORGANIZATION","ORLANY","ORLVARY","OTHERS","OUT","OVERLAPS","OVERRIDING","PACKAGE","PARALLEL_ENABLE","PARAMETER","PARAMETERS","PARENT","PARTITION","PASCAL","PCTFREE","PIPE","PIPELINED","PLS_INTEGER","PLUGGABLE","POSITIVE","POSITIVEN","PRAGMA","PRECISION","PRIOR","PRIVATE","PROCEDURE","PUBLIC","RAISE","RANGE","RAW","READ","REAL","RECORD","REF","REFERENCE","RELEASE","RELIES_ON","REM","REMAINDER","RENAME","RESOURCE","RESULT_CACHE","RESULT","RETURN","RETURNING","REVERSE","REVOKE","ROLLBACK","ROW","ROWID","ROWNUM","ROWTYPE","SAMPLE","SAVE","SAVEPOINT","SB1","SB2","SB4","SEARCH","SECOND","SEGMENT","SELF","SEPARATE","SEQUENCE","SERIALIZABLE","SHARE","SHORT","SIZE_T","SIZE","SMALLINT","SOME","SPACE","SPARSE","SQL","SQLCODE","SQLDATA","SQLERRM","SQLNAME","SQLSTATE","STANDARD","START","STATIC","STDDEV","STORED","STRING","STRUCT","STYLE","SUBMULTISET","SUBPARTITION","SUBSTITUTABLE","SUBTYPE","SUCCESSFUL","SUM","SYNONYM","SYSDATE","TABAUTH","TABLE","TDO","THE","THEN","TIME","TIMESTAMP","TIMEZONE_ABBR","TIMEZONE_HOUR","TIMEZONE_MINUTE","TIMEZONE_REGION","TO","TRAILING","TRANSACTION","TRANSACTIONAL","TRIGGER","TRUE","TRUSTED","TYPE","UB1","UB2","UB4","UID","UNDER","UNIQUE","UNPLUG","UNSIGNED","UNTRUSTED","USE","USER","USING","VALIDATE","VALIST","VALUE","VARCHAR","VARCHAR2","VARIABLE","VARIANCE","VARRAY","VARYING","VIEW","VIEWS","VOID","WHENEVER","WHILE","WITH","WORK","WRAPPED","WRITE","YEAR","ZONE"],YE=["ADD","ALTER COLUMN","ALTER TABLE","BEGIN","CONNECT BY","DECLARE","DELETE FROM","DELETE","END","EXCEPT","EXCEPTION","FETCH FIRST","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","LIMIT","LOOP","MODIFY","ORDER BY","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","START WITH","UPDATE","VALUES","WHERE"],WE=["INTERSECT","INTERSECT ALL","MINUS","UNION","UNION ALL"],mE=["AND","CROSS APPLY","ELSE","END","OR","OUTER APPLY","WHEN","XOR","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],gE=function(E){!function(E,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(e&&e.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),e&&vE(E,e)}(r,E);var e,t,R,n=HE(r);function r(){return dE(this,r),n.apply(this,arguments)}return e=r,(t=[{key:"tokenizer",value:function(){return new B({reservedWords:VE,reservedTopLevelWords:YE,reservedNewlineWords:mE,reservedTopLevelWordsNoIndent:WE,stringTypes:['""',"N''","''","``"],openParens:["(","CASE"],closeParens:[")","END"],indexedPlaceholderTypes:["?"],namedPlaceholderTypes:[":"],lineCommentTypes:["--"],specialWordChars:["_","$","#",".","@"],operators:["||","**","!=",":="]})}},{key:"tokenOverride",value:function(E){return c(E)&&a(this.previousReservedToken)?{type:T.RESERVED,value:E.value}:E}}])&&FE(e.prototype,t),R&&FE(e,R),r}(f);function XE(E){return(XE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E})(E)}function kE(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}function wE(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}function KE(E,e){return(KE=Object.setPrototypeOf||function(E,e){return E.__proto__=e,E})(E,e)}function JE(E){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(E){return!1}}();return function(){var t,T=xE(E);if(e){var R=xE(this).constructor;t=Reflect.construct(T,arguments,R)}else t=T.apply(this,arguments);return jE(this,t)}}function jE(E,e){return!e||"object"!==XE(e)&&"function"!=typeof e?function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E):e}function xE(E){return(xE=Object.setPrototypeOf?Object.getPrototypeOf:function(E){return E.__proto__||Object.getPrototypeOf(E)})(E)}var ZE=["ABORT","ABSOLUTE","ACCESS","ACTION","ADD","ADMIN","AFTER","AGGREGATE","ALL","ALSO","ALTER","ALWAYS","ANALYSE","ANALYZE","AND","ANY","ARRAY","AS","ASC","ASSERTION","ASSIGNMENT","ASYMMETRIC","AT","ATTACH","ATTRIBUTE","AUTHORIZATION","BACKWARD","BEFORE","BEGIN","BETWEEN","BIGINT","BINARY","BIT","BOOLEAN","BOTH","BY","CACHE","CALL","CALLED","CASCADE","CASCADED","CASE","CAST","CATALOG","CHAIN","CHAR","CHARACTER","CHARACTERISTICS","CHECK","CHECKPOINT","CLASS","CLOSE","CLUSTER","COALESCE","COLLATE","COLLATION","COLUMN","COLUMNS","COMMENT","COMMENTS","COMMIT","COMMITTED","CONCURRENTLY","CONFIGURATION","CONFLICT","CONNECTION","CONSTRAINT","CONSTRAINTS","CONTENT","CONTINUE","CONVERSION","COPY","COST","CREATE","CROSS","CSV","CUBE","CURRENT","CURRENT_CATALOG","CURRENT_DATE","CURRENT_ROLE","CURRENT_SCHEMA","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","CURSOR","CYCLE","DATA","DATABASE","DAY","DEALLOCATE","DEC","DECIMAL","DECLARE","DEFAULT","DEFAULTS","DEFERRABLE","DEFERRED","DEFINER","DELETE","DELIMITER","DELIMITERS","DEPENDS","DESC","DETACH","DICTIONARY","DISABLE","DISCARD","DISTINCT","DO","DOCUMENT","DOMAIN","DOUBLE","DROP","EACH","ELSE","ENABLE","ENCODING","ENCRYPTED","END","ENUM","ESCAPE","EVENT","EXCEPT","EXCLUDE","EXCLUDING","EXCLUSIVE","EXECUTE","EXISTS","EXPLAIN","EXPRESSION","EXTENSION","EXTERNAL","EXTRACT","FALSE","FAMILY","FETCH","FILTER","FIRST","FLOAT","FOLLOWING","FOR","FORCE","FOREIGN","FORWARD","FREEZE","FROM","FULL","FUNCTION","FUNCTIONS","GENERATED","GLOBAL","GRANT","GRANTED","GREATEST","GROUP","GROUPING","GROUPS","HANDLER","HAVING","HEADER","HOLD","HOUR","IDENTITY","IF","ILIKE","IMMEDIATE","IMMUTABLE","IMPLICIT","IMPORT","IN","INCLUDE","INCLUDING","INCREMENT","INDEX","INDEXES","INHERIT","INHERITS","INITIALLY","INLINE","INNER","INOUT","INPUT","INSENSITIVE","INSERT","INSTEAD","INT","INTEGER","INTERSECT","INTERVAL","INTO","INVOKER","IS","ISNULL","ISOLATION","JOIN","KEY","LABEL","LANGUAGE","LARGE","LAST","LATERAL","LEADING","LEAKPROOF","LEAST","LEFT","LEVEL","LIKE","LIMIT","LISTEN","LOAD","LOCAL","LOCALTIME","LOCALTIMESTAMP","LOCATION","LOCK","LOCKED","LOGGED","MAPPING","MATCH","MATERIALIZED","MAXVALUE","METHOD","MINUTE","MINVALUE","MODE","MONTH","MOVE","NAME","NAMES","NATIONAL","NATURAL","NCHAR","NEW","NEXT","NFC","NFD","NFKC","NFKD","NO","NONE","NORMALIZE","NORMALIZED","NOT","NOTHING","NOTIFY","NOTNULL","NOWAIT","NULL","NULLIF","NULLS","NUMERIC","OBJECT","OF","OFF","OFFSET","OIDS","OLD","ON","ONLY","OPERATOR","OPTION","OPTIONS","OR","ORDER","ORDINALITY","OTHERS","OUT","OUTER","OVER","OVERLAPS","OVERLAY","OVERRIDING","OWNED","OWNER","PARALLEL","PARSER","PARTIAL","PARTITION","PASSING","PASSWORD","PLACING","PLANS","POLICY","POSITION","PRECEDING","PRECISION","PREPARE","PREPARED","PRESERVE","PRIMARY","PRIOR","PRIVILEGES","PROCEDURAL","PROCEDURE","PROCEDURES","PROGRAM","PUBLICATION","QUOTE","RANGE","READ","REAL","REASSIGN","RECHECK","RECURSIVE","REF","REFERENCES","REFERENCING","REFRESH","REINDEX","RELATIVE","RELEASE","RENAME","REPEATABLE","REPLACE","REPLICA","RESET","RESTART","RESTRICT","RETURNING","RETURNS","REVOKE","RIGHT","ROLE","ROLLBACK","ROLLUP","ROUTINE","ROUTINES","ROW","ROWS","RULE","SAVEPOINT","SCHEMA","SCHEMAS","SCROLL","SEARCH","SECOND","SECURITY","SELECT","SEQUENCE","SEQUENCES","SERIALIZABLE","SERVER","SESSION","SESSION_USER","SET","SETOF","SETS","SHARE","SHOW","SIMILAR","SIMPLE","SKIP","SMALLINT","SNAPSHOT","SOME","SQL","STABLE","STANDALONE","START","STATEMENT","STATISTICS","STDIN","STDOUT","STORAGE","STORED","STRICT","STRIP","SUBSCRIPTION","SUBSTRING","SUPPORT","SYMMETRIC","SYSID","SYSTEM","TABLE","TABLES","TABLESAMPLE","TABLESPACE","TEMP","TEMPLATE","TEMPORARY","TEXT","THEN","TIES","TIME","TIMESTAMP","TO","TRAILING","TRANSACTION","TRANSFORM","TREAT","TRIGGER","TRIM","TRUE","TRUNCATE","TRUSTED","TYPE","TYPES","UESCAPE","UNBOUNDED","UNCOMMITTED","UNENCRYPTED","UNION","UNIQUE","UNKNOWN","UNLISTEN","UNLOGGED","UNTIL","UPDATE","USER","USING","VACUUM","VALID","VALIDATE","VALIDATOR","VALUE","VALUES","VARCHAR","VARIADIC","VARYING","VERBOSE","VERSION","VIEW","VIEWS","VOLATILE","WHEN","WHERE","WHITESPACE","WINDOW","WITH","WITHIN","WITHOUT","WORK","WRAPPER","WRITE","XML","XMLATTRIBUTES","XMLCONCAT","XMLELEMENT","XMLEXISTS","XMLFOREST","XMLNAMESPACES","XMLPARSE","XMLPI","XMLROOT","XMLSERIALIZE","XMLTABLE","YEAR","YES","ZONE"],QE=["ADD","AFTER","ALTER COLUMN","ALTER TABLE","CASE","DELETE FROM","END","EXCEPT","FETCH FIRST","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","LIMIT","ORDER BY","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","UPDATE","VALUES","WHERE"],$E=["INTERSECT","INTERSECT ALL","UNION","UNION ALL"],zE=["AND","ELSE","OR","WHEN","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],qE=function(E){!function(E,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(e&&e.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),e&&KE(E,e)}(n,E);var e,t,T,R=JE(n);function n(){return kE(this,n),R.apply(this,arguments)}return e=n,(t=[{key:"tokenizer",value:function(){return new B({reservedWords:ZE,reservedTopLevelWords:QE,reservedNewlineWords:zE,reservedTopLevelWordsNoIndent:$E,stringTypes:['""',"''","U&''",'U&""',"$$"],openParens:["(","CASE"],closeParens:[")","END"],indexedPlaceholderTypes:["$"],namedPlaceholderTypes:[":"],lineCommentTypes:["--"],operators:["!=","<<",">>","||/","|/","::","->>","->","~~*","~~","!~~*","!~~","~*","!~*","!~","!!"]})}}])&&wE(e.prototype,t),T&&wE(e,T),n}(f);function Ee(E){return(Ee="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E})(E)}function ee(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}function te(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}function Te(E,e){return(Te=Object.setPrototypeOf||function(E,e){return E.__proto__=e,E})(E,e)}function Re(E){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(E){return!1}}();return function(){var t,T=re(E);if(e){var R=re(this).constructor;t=Reflect.construct(T,arguments,R)}else t=T.apply(this,arguments);return ne(this,t)}}function ne(E,e){return!e||"object"!==Ee(e)&&"function"!=typeof e?function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E):e}function re(E){return(re=Object.setPrototypeOf?Object.getPrototypeOf:function(E){return E.__proto__||Object.getPrototypeOf(E)})(E)}var Ne=["AES128","AES256","ALLOWOVERWRITE","ANALYSE","ARRAY","AS","ASC","AUTHORIZATION","BACKUP","BINARY","BLANKSASNULL","BOTH","BYTEDICT","BZIP2","CAST","CHECK","COLLATE","COLUMN","CONSTRAINT","CREATE","CREDENTIALS","CURRENT_DATE","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","CURRENT_USER_ID","DEFAULT","DEFERRABLE","DEFLATE","DEFRAG","DELTA","DELTA32K","DESC","DISABLE","DISTINCT","DO","ELSE","EMPTYASNULL","ENABLE","ENCODE","ENCRYPT","ENCRYPTION","END","EXPLICIT","FALSE","FOR","FOREIGN","FREEZE","FULL","GLOBALDICT256","GLOBALDICT64K","GRANT","GZIP","IDENTITY","IGNORE","ILIKE","INITIALLY","INTO","LEADING","LOCALTIME","LOCALTIMESTAMP","LUN","LUNS","LZO","LZOP","MINUS","MOSTLY13","MOSTLY32","MOSTLY8","NATURAL","NEW","NULLS","OFF","OFFLINE","OFFSET","OLD","ON","ONLY","OPEN","ORDER","OVERLAPS","PARALLEL","PARTITION","PERCENT","PERMISSIONS","PLACING","PRIMARY","RAW","READRATIO","RECOVER","REFERENCES","REJECTLOG","RESORT","RESTORE","SESSION_USER","SIMILAR","SYSDATE","SYSTEM","TABLE","TAG","TDES","TEXT255","TEXT32K","THEN","TIMESTAMP","TO","TOP","TRAILING","TRUE","TRUNCATECOLUMNS","UNIQUE","USER","USING","VERBOSE","WALLET","WHEN","WITH","WITHOUT","PREDICATE","COLUMNS","COMPROWS","COMPRESSION","COPY","FORMAT","DELIMITER","FIXEDWIDTH","AVRO","JSON","ENCRYPTED","BZIP2","GZIP","LZOP","PARQUET","ORC","ACCEPTANYDATE","ACCEPTINVCHARS","BLANKSASNULL","DATEFORMAT","EMPTYASNULL","ENCODING","ESCAPE","EXPLICIT_IDS","FILLRECORD","IGNOREBLANKLINES","IGNOREHEADER","NULL AS","REMOVEQUOTES","ROUNDEC","TIMEFORMAT","TRIMBLANKS","TRUNCATECOLUMNS","COMPROWS","COMPUPDATE","MAXERROR","NOLOAD","STATUPDATE","MANIFEST","REGION","IAM_ROLE","MASTER_SYMMETRIC_KEY","SSH","ACCEPTANYDATE","ACCEPTINVCHARS","ACCESS_KEY_ID","SECRET_ACCESS_KEY","AVRO","BLANKSASNULL","BZIP2","COMPROWS","COMPUPDATE","CREDENTIALS","DATEFORMAT","DELIMITER","EMPTYASNULL","ENCODING","ENCRYPTED","ESCAPE","EXPLICIT_IDS","FILLRECORD","FIXEDWIDTH","FORMAT","IAM_ROLE","GZIP","IGNOREBLANKLINES","IGNOREHEADER","JSON","LZOP","MANIFEST","MASTER_SYMMETRIC_KEY","MAXERROR","NOLOAD","NULL AS","READRATIO","REGION","REMOVEQUOTES","ROUNDEC","SSH","STATUPDATE","TIMEFORMAT","SESSION_TOKEN","TRIMBLANKS","TRUNCATECOLUMNS","EXTERNAL","DATA CATALOG","HIVE METASTORE","CATALOG_ROLE","VACUUM","COPY","UNLOAD","EVEN","ALL"],Ie=["ADD","AFTER","ALTER COLUMN","ALTER TABLE","DELETE FROM","EXCEPT","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","INTERSECT","TOP","LIMIT","MODIFY","ORDER BY","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","UNION ALL","UNION","UPDATE","VALUES","WHERE","VACUUM","COPY","UNLOAD","ANALYZE","ANALYSE","DISTKEY","SORTKEY","COMPOUND","INTERLEAVED","FORMAT","DELIMITER","FIXEDWIDTH","AVRO","JSON","ENCRYPTED","BZIP2","GZIP","LZOP","PARQUET","ORC","ACCEPTANYDATE","ACCEPTINVCHARS","BLANKSASNULL","DATEFORMAT","EMPTYASNULL","ENCODING","ESCAPE","EXPLICIT_IDS","FILLRECORD","IGNOREBLANKLINES","IGNOREHEADER","NULL AS","REMOVEQUOTES","ROUNDEC","TIMEFORMAT","TRIMBLANKS","TRUNCATECOLUMNS","COMPROWS","COMPUPDATE","MAXERROR","NOLOAD","STATUPDATE","MANIFEST","REGION","IAM_ROLE","MASTER_SYMMETRIC_KEY","SSH","ACCEPTANYDATE","ACCEPTINVCHARS","ACCESS_KEY_ID","SECRET_ACCESS_KEY","AVRO","BLANKSASNULL","BZIP2","COMPROWS","COMPUPDATE","CREDENTIALS","DATEFORMAT","DELIMITER","EMPTYASNULL","ENCODING","ENCRYPTED","ESCAPE","EXPLICIT_IDS","FILLRECORD","FIXEDWIDTH","FORMAT","IAM_ROLE","GZIP","IGNOREBLANKLINES","IGNOREHEADER","JSON","LZOP","MANIFEST","MASTER_SYMMETRIC_KEY","MAXERROR","NOLOAD","NULL AS","READRATIO","REGION","REMOVEQUOTES","ROUNDEC","SSH","STATUPDATE","TIMEFORMAT","SESSION_TOKEN","TRIMBLANKS","TRUNCATECOLUMNS","EXTERNAL","DATA CATALOG","HIVE METASTORE","CATALOG_ROLE"],Ae=[],Oe=["AND","ELSE","OR","OUTER APPLY","WHEN","VACUUM","COPY","UNLOAD","ANALYZE","ANALYSE","DISTKEY","SORTKEY","COMPOUND","INTERLEAVED","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],oe=function(E){!function(E,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(e&&e.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),e&&Te(E,e)}(n,E);var e,t,T,R=Re(n);function n(){return ee(this,n),R.apply(this,arguments)}return e=n,(t=[{key:"tokenizer",value:function(){return new B({reservedWords:Ne,reservedTopLevelWords:Ie,reservedNewlineWords:Oe,reservedTopLevelWordsNoIndent:Ae,stringTypes:['""',"''","``"],openParens:["("],closeParens:[")"],indexedPlaceholderTypes:["?"],namedPlaceholderTypes:["@","#","$"],lineCommentTypes:["--"],operators:["|/","||/","<<",">>","!=","||"]})}}])&&te(e.prototype,t),T&&te(e,T),n}(f);function Se(E){return(Se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E})(E)}function Le(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}function ie(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}function Ce(E,e){return(Ce=Object.setPrototypeOf||function(E,e){return E.__proto__=e,E})(E,e)}function ue(E){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(E){return!1}}();return function(){var t,T=ae(E);if(e){var R=ae(this).constructor;t=Reflect.construct(T,arguments,R)}else t=T.apply(this,arguments);return ce(this,t)}}function ce(E,e){return!e||"object"!==Se(e)&&"function"!=typeof e?function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E):e}function ae(E){return(ae=Object.setPrototypeOf?Object.getPrototypeOf:function(E){return E.__proto__||Object.getPrototypeOf(E)})(E)}var se=["ALL","ALTER","ANALYSE","ANALYZE","ARRAY_ZIP","ARRAY","AS","ASC","AVG","BETWEEN","CASCADE","CASE","CAST","COALESCE","COLLECT_LIST","COLLECT_SET","COLUMN","COLUMNS","COMMENT","CONSTRAINT","CONTAINS","CONVERT","COUNT","CUME_DIST","CURRENT ROW","CURRENT_DATE","CURRENT_TIMESTAMP","DATABASE","DATABASES","DATE_ADD","DATE_SUB","DATE_TRUNC","DAY_HOUR","DAY_MINUTE","DAY_SECOND","DAY","DAYS","DECODE","DEFAULT","DELETE","DENSE_RANK","DESC","DESCRIBE","DISTINCT","DISTINCTROW","DIV","DROP","ELSE","ENCODE","END","EXISTS","EXPLAIN","EXPLODE_OUTER","EXPLODE","FILTER","FIRST_VALUE","FIRST","FIXED","FLATTEN","FOLLOWING","FROM_UNIXTIME","FULL","GREATEST","GROUP_CONCAT","HOUR_MINUTE","HOUR_SECOND","HOUR","HOURS","IF","IFNULL","IN","INSERT","INTERVAL","INTO","IS","LAG","LAST_VALUE","LAST","LEAD","LEADING","LEAST","LEVEL","LIKE","MAX","MERGE","MIN","MINUTE_SECOND","MINUTE","MONTH","NATURAL","NOT","NOW()","NTILE","NULL","NULLIF","OFFSET","ON DELETE","ON UPDATE","ON","ONLY","OPTIMIZE","OVER","PERCENT_RANK","PRECEDING","RANGE","RANK","REGEXP","RENAME","RLIKE","ROW","ROWS","SECOND","SEPARATOR","SEQUENCE","SIZE","STRING","STRUCT","SUM","TABLE","TABLES","TEMPORARY","THEN","TO_DATE","TO_JSON","TO","TRAILING","TRANSFORM","TRUE","TRUNCATE","TYPE","TYPES","UNBOUNDED","UNIQUE","UNIX_TIMESTAMP","UNLOCK","UNSIGNED","USING","VARIABLES","VIEW","WHEN","WITH","YEAR_MONTH"],Ue=["ADD","AFTER","ALTER COLUMN","ALTER DATABASE","ALTER SCHEMA","ALTER TABLE","CLUSTER BY","CLUSTERED BY","DELETE FROM","DISTRIBUTE BY","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","LIMIT","OPTIONS","ORDER BY","PARTITION BY","PARTITIONED BY","RANGE","ROWS","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","TBLPROPERTIES","UPDATE","USING","VALUES","WHERE","WINDOW"],De=["EXCEPT ALL","EXCEPT","INTERSECT ALL","INTERSECT","UNION ALL","UNION"],le=["AND","CREATE OR","CREATE","ELSE","LATERAL VIEW","OR","OUTER APPLY","WHEN","XOR","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN","ANTI JOIN","SEMI JOIN","LEFT ANTI JOIN","LEFT SEMI JOIN","RIGHT OUTER JOIN","RIGHT SEMI JOIN","NATURAL ANTI JOIN","NATURAL FULL OUTER JOIN","NATURAL INNER JOIN","NATURAL LEFT ANTI JOIN","NATURAL LEFT OUTER JOIN","NATURAL LEFT SEMI JOIN","NATURAL OUTER JOIN","NATURAL RIGHT OUTER JOIN","NATURAL RIGHT SEMI JOIN","NATURAL SEMI JOIN"],fe=function(E){!function(E,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(e&&e.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),e&&Ce(E,e)}(r,E);var e,t,R,n=ue(r);function r(){return Le(this,r),n.apply(this,arguments)}return e=r,(t=[{key:"tokenizer",value:function(){return new B({reservedWords:se,reservedTopLevelWords:Ue,reservedNewlineWords:le,reservedTopLevelWordsNoIndent:De,stringTypes:['""',"''","``","{}"],openParens:["(","CASE"],closeParens:[")","END"],indexedPlaceholderTypes:["?"],namedPlaceholderTypes:["$"],lineCommentTypes:["--"],operators:["!=","<=>","&&","||","=="]})}},{key:"tokenOverride",value:function(E){if(s(E)){var e=this.tokenLookAhead();if(e&&e.type===T.OPEN_PAREN)return{type:T.RESERVED,value:E.value}}if(U(E)){var t=this.tokenLookBehind();if(t&&t.type===T.OPERATOR&&"."===t.value)return{type:T.WORD,value:E.value}}return E}}])&&ie(e.prototype,t),R&&ie(e,R),r}(f);function Pe(E){return(Pe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E})(E)}function Me(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}function pe(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}function ye(E,e){return(ye=Object.setPrototypeOf||function(E,e){return E.__proto__=e,E})(E,e)}function _e(E){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(E){return!1}}();return function(){var t,T=Ge(E);if(e){var R=Ge(this).constructor;t=Reflect.construct(T,arguments,R)}else t=T.apply(this,arguments);return he(this,t)}}function he(E,e){return!e||"object"!==Pe(e)&&"function"!=typeof e?function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E):e}function Ge(E){return(Ge=Object.setPrototypeOf?Object.getPrototypeOf:function(E){return E.__proto__||Object.getPrototypeOf(E)})(E)}var de=["ABS","ALL","ALLOCATE","ALTER","AND","ANY","ARE","ARRAY","AS","ASENSITIVE","ASYMMETRIC","AT","ATOMIC","AUTHORIZATION","AVG","BEGIN","BETWEEN","BIGINT","BINARY","BLOB","BOOLEAN","BOTH","BY","CALL","CALLED","CARDINALITY","CASCADED","CASE","CAST","CEIL","CEILING","CHAR","CHAR_LENGTH","CHARACTER","CHARACTER_LENGTH","CHECK","CLOB","CLOSE","COALESCE","COLLATE","COLLECT","COLUMN","COMMIT","CONDITION","CONNECT","CONSTRAINT","CONVERT","CORR","CORRESPONDING","COUNT","COVAR_POP","COVAR_SAMP","CREATE","CROSS","CUBE","CUME_DIST","CURRENT","CURRENT_CATALOG","CURRENT_DATE","CURRENT_DEFAULT_TRANSFORM_GROUP","CURRENT_PATH","CURRENT_ROLE","CURRENT_SCHEMA","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_TRANSFORM_GROUP_FOR_TYPE","CURRENT_USER","CURSOR","CYCLE","DATE","DAY","DEALLOCATE","DEC","DECIMAL","DECLARE","DEFAULT","DELETE","DENSE_RANK","DEREF","DESCRIBE","DETERMINISTIC","DISCONNECT","DISTINCT","DOUBLE","DROP","DYNAMIC","EACH","ELEMENT","ELSE","END","END-EXEC","ESCAPE","EVERY","EXCEPT","EXEC","EXECUTE","EXISTS","EXP","EXTERNAL","EXTRACT","FALSE","FETCH","FILTER","FLOAT","FLOOR","FOR","FOREIGN","FREE","FROM","FULL","FUNCTION","FUSION","GET","GLOBAL","GRANT","GROUP","GROUPING","HAVING","HOLD","HOUR","IDENTITY","IN","INDICATOR","INNER","INOUT","INSENSITIVE","INSERT","INT","INTEGER","INTERSECT","INTERSECTION","INTERVAL","INTO","IS","JOIN","LANGUAGE","LARGE","LATERAL","LEADING","LEFT","LIKE","LIKE_REGEX","LN","LOCAL","LOCALTIME","LOCALTIMESTAMP","LOWER","MATCH","MAX","MEMBER","MERGE","METHOD","MIN","MINUTE","MOD","MODIFIES","MODULE","MONTH","MULTISET","NATIONAL","NATURAL","NCHAR","NCLOB","NEW","NO","NONE","NORMALIZE","NOT","NULL","NULLIF","NUMERIC","OCTET_LENGTH","OCCURRENCES_REGEX","OF","OLD","ON","ONLY","OPEN","OR","ORDER","OUT","OUTER","OVER","OVERLAPS","OVERLAY","PARAMETER","PARTITION","PERCENT_RANK","PERCENTILE_CONT","PERCENTILE_DISC","POSITION","POSITION_REGEX","POWER","PRECISION","PREPARE","PRIMARY","PROCEDURE","RANGE","RANK","READS","REAL","RECURSIVE","REF","REFERENCES","REFERENCING","REGR_AVGX","REGR_AVGY","REGR_COUNT","REGR_INTERCEPT","REGR_R2","REGR_SLOPE","REGR_SXX","REGR_SXY","REGR_SYY","RELEASE","RESULT","RETURN","RETURNS","REVOKE","RIGHT","ROLLBACK","ROLLUP","ROW","ROW_NUMBER","ROWS","SAVEPOINT","SCOPE","SCROLL","SEARCH","SECOND","SELECT","SENSITIVE","SESSION_USER","SET","SIMILAR","SMALLINT","SOME","SPECIFIC","SPECIFICTYPE","SQL","SQLEXCEPTION","SQLSTATE","SQLWARNING","SQRT","START","STATIC","STDDEV_POP","STDDEV_SAMP","SUBMULTISET","SUBSTRING","SUBSTRING_REGEX","SUM","SYMMETRIC","SYSTEM","SYSTEM_USER","TABLE","TABLESAMPLE","THEN","TIME","TIMESTAMP","TIMEZONE_HOUR","TIMEZONE_MINUTE","TO","TRAILING","TRANSLATE","TRANSLATE_REGEX","TRANSLATION","TREAT","TRIGGER","TRIM","TRUE","UESCAPE","UNION","UNIQUE","UNKNOWN","UNNEST","UPDATE","UPPER","USER","USING","VALUE","VALUES","VAR_POP","VAR_SAMP","VARBINARY","VARCHAR","VARYING","WHEN","WHENEVER","WHERE","WIDTH_BUCKET","WINDOW","WITH","WITHIN","WITHOUT","YEAR"],Fe=["ADD","ALTER COLUMN","ALTER TABLE","CASE","DELETE FROM","END","FETCH FIRST","FETCH NEXT","FETCH PRIOR","FETCH LAST","FETCH ABSOLUTE","FETCH RELATIVE","FROM","GROUP BY","HAVING","INSERT INTO","LIMIT","ORDER BY","SELECT","SET SCHEMA","SET","UPDATE","VALUES","WHERE"],ve=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT","EXCEPT","EXCEPT ALL","EXCEPT DISTINCT"],He=["AND","ELSE","OR","WHEN","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],Be=function(E){!function(E,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(e&&e.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),e&&ye(E,e)}(n,E);var e,t,T,R=_e(n);function n(){return Me(this,n),R.apply(this,arguments)}return e=n,(t=[{key:"tokenizer",value:function(){return new B({reservedWords:de,reservedTopLevelWords:Fe,reservedNewlineWords:He,reservedTopLevelWordsNoIndent:ve,stringTypes:['""',"''"],openParens:["(","CASE"],closeParens:[")","END"],indexedPlaceholderTypes:["?"],namedPlaceholderTypes:[],lineCommentTypes:["--"]})}}])&&pe(e.prototype,t),T&&pe(e,T),n}(f);function be(E){return(be="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E})(E)}function Ve(E,e){if(!(E instanceof e))throw new TypeError("Cannot call a class as a function")}function Ye(E,e){for(var t=0;t<e.length;t++){var T=e[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(E,T.key,T)}}function We(E,e){return(We=Object.setPrototypeOf||function(E,e){return E.__proto__=e,E})(E,e)}function me(E){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(E){return!1}}();return function(){var t,T=Xe(E);if(e){var R=Xe(this).constructor;t=Reflect.construct(T,arguments,R)}else t=T.apply(this,arguments);return ge(this,t)}}function ge(E,e){return!e||"object"!==be(e)&&"function"!=typeof e?function(E){if(void 0===E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}(E):e}function Xe(E){return(Xe=Object.setPrototypeOf?Object.getPrototypeOf:function(E){return E.__proto__||Object.getPrototypeOf(E)})(E)}var ke=["ADD","EXTERNAL","PROCEDURE","ALL","FETCH","PUBLIC","ALTER","FILE","RAISERROR","AND","FILLFACTOR","READ","ANY","FOR","READTEXT","AS","FOREIGN","RECONFIGURE","ASC","FREETEXT","REFERENCES","AUTHORIZATION","FREETEXTTABLE","REPLICATION","BACKUP","FROM","RESTORE","BEGIN","FULL","RESTRICT","BETWEEN","FUNCTION","RETURN","BREAK","GOTO","REVERT","BROWSE","GRANT","REVOKE","BULK","GROUP","RIGHT","BY","HAVING","ROLLBACK","CASCADE","HOLDLOCK","ROWCOUNT","CASE","IDENTITY","ROWGUIDCOL","CHECK","IDENTITY_INSERT","RULE","CHECKPOINT","IDENTITYCOL","SAVE","CLOSE","IF","SCHEMA","CLUSTERED","IN","SECURITYAUDIT","COALESCE","INDEX","SELECT","COLLATE","INNER","SEMANTICKEYPHRASETABLE","COLUMN","INSERT","SEMANTICSIMILARITYDETAILSTABLE","COMMIT","INTERSECT","SEMANTICSIMILARITYTABLE","COMPUTE","INTO","SESSION_USER","CONSTRAINT","IS","SET","CONTAINS","JOIN","SETUSER","CONTAINSTABLE","KEY","SHUTDOWN","CONTINUE","KILL","SOME","CONVERT","LEFT","STATISTICS","CREATE","LIKE","SYSTEM_USER","CROSS","LINENO","TABLE","CURRENT","LOAD","TABLESAMPLE","CURRENT_DATE","MERGE","TEXTSIZE","CURRENT_TIME","NATIONAL","THEN","CURRENT_TIMESTAMP","NOCHECK","TO","CURRENT_USER","NONCLUSTERED","TOP","CURSOR","NOT","TRAN","DATABASE","NULL","TRANSACTION","DBCC","NULLIF","TRIGGER","DEALLOCATE","OF","TRUNCATE","DECLARE","OFF","TRY_CONVERT","DEFAULT","OFFSETS","TSEQUAL","DELETE","ON","UNION","DENY","OPEN","UNIQUE","DESC","OPENDATASOURCE","UNPIVOT","DISK","OPENQUERY","UPDATE","DISTINCT","OPENROWSET","UPDATETEXT","DISTRIBUTED","OPENXML","USE","DOUBLE","OPTION","USER","DROP","OR","VALUES","DUMP","ORDER","VARYING","ELSE","OUTER","VIEW","END","OVER","WAITFOR","ERRLVL","PERCENT","WHEN","ESCAPE","PIVOT","WHERE","EXCEPT","PLAN","WHILE","EXEC","PRECISION","WITH","EXECUTE","PRIMARY","WITHIN GROUP","EXISTS","PRINT","WRITETEXT","EXIT","PROC"],we=["ADD","ALTER COLUMN","ALTER TABLE","CASE","DELETE FROM","END","EXCEPT","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","LIMIT","ORDER BY","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","UPDATE","VALUES","WHERE"],Ke=["INTERSECT","INTERSECT ALL","MINUS","UNION","UNION ALL"],Je=["AND","ELSE","OR","WHEN","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN"];function je(E){return(je="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(E){return typeof E}:function(E){return E&&"function"==typeof Symbol&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E})(E)}var xe={db2:j,mariadb:nE,mysql:cE,n1ql:hE,plsql:gE,postgresql:qE,redshift:oe,spark:fe,sql:Be,tsql:function(E){!function(E,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(e&&e.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),e&&We(E,e)}(n,E);var e,t,T,R=me(n);function n(){return Ve(this,n),R.apply(this,arguments)}return e=n,(t=[{key:"tokenizer",value:function(){return new B({reservedWords:ke,reservedTopLevelWords:we,reservedNewlineWords:Je,reservedTopLevelWordsNoIndent:Ke,stringTypes:['""',"N''","''","[]"],openParens:["(","CASE"],closeParens:[")","END"],indexedPlaceholderTypes:[],namedPlaceholderTypes:["@"],lineCommentTypes:["--"],specialWordChars:["#","@"],operators:[">=","<=","<>","!=","!<","!>","+=","-=","*=","/=","%=","|=","&=","^=","::"]})}}])&&Ye(e.prototype,t),T&&Ye(e,T),n}(f)},Ze=function(E){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("string"!=typeof E)throw new Error("Invalid query argument. Extected string, instead got "+je(E));var t=Be;if(void 0!==e.language&&(t=xe[e.language]),void 0===t)throw Error("Unsupported SQL dialect: ".concat(e.language));return new t(e).format(E)},Qe=Object.keys(xe)}])}));
//# sourceMappingURL=sql-formatter.min.js.map