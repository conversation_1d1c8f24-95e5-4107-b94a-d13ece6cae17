// search-manager.js - 搜索功能相关代码

// 搜索文件
function searchFiles() {
    const keyword = $('#searchInput').val().trim();
    if (!keyword) {
        layer.msg('请输入搜索关键词', { icon: 2 });
        return;
    }

    const searchContent = $('#searchContent').prop('checked');
    const folderPath = window.currentSearchFolder || '';

    // 获取文件类型，包括复选框和输入框
    const fileTypes = getFileTypes();

    // 验证文件类型格式
    if (!validateFileTypes()) {
        return;
    }

    // 显示加载中提示
    const loadingIndex = layer.load(1, {
        shade: [0.1, '#000']
    });

    // 先切换到搜索页签，避免用户等待
    window.uiUtils.switchSidebarTab('search');

    // 调用后端搜索API
    $.ajax({
        url: '/editor/search',
        method: 'GET',
        data: {
            keyword: keyword,
            searchContent: searchContent,
            folderPath: folderPath,
            fileTypes: fileTypes
        },
        success: function (results) {
            // 关闭加载提示
            layer.close(loadingIndex);

            // 显示搜索结果
            showSearchResults(results, keyword);
        },
        error: function (xhr) {
            // 关闭加载提示
            layer.close(loadingIndex);

            // 显示错误信息
            $('#searchResultsList').empty().append(`
                <li class="search-result-item">
                    <div class="file-name">搜索失败</div>
                    <div class="file-path">${xhr.responseText || '服务器错误'}</div>
                </li>
            `);

            layer.msg('搜索失败: ' + xhr.responseText, { icon: 2 });
        }
    });
}

// 获取选中的文件类型（包括复选框和输入框）
function getFileTypes() {
    // 获取复选框选中的文件类型
    const checkedTypes = [];
    $('.file-type-cb:checked').each(function () {
        checkedTypes.push($(this).data('ext'));
    });

    // 获取输入框中的文件类型
    const inputTypes = $('#fileTypeFilter').val().trim();
    if (inputTypes) {
        // 分割输入的文件类型并添加到选中类型中
        const types = inputTypes.split(',').map(type => type.trim());
        checkedTypes.push(...types);
    }

    // 返回去重后的文件类型字符串
    return [...new Set(checkedTypes)].join(',');
}

// 验证文件类型格式
function validateFileTypes() {
    const fileTypeInput = $('#fileTypeFilter');
    const fileTypeError = $('#fileTypeError');
    const inputValue = fileTypeInput.val().trim();

    // 如果输入为空，则验证通过
    if (!inputValue) {
        fileTypeInput.removeClass('error');
        fileTypeError.text('');
        return true;
    }

    // 验证文件类型格式
    const types = inputValue.split(',');
    const invalidTypes = [];

    for (const type of types) {
        const trimmedType = type.trim();
        // 检查每个类型是否以点号开头
        if (trimmedType && !trimmedType.startsWith('.')) {
            invalidTypes.push(trimmedType);
        }
    }

    // 如果有无效的文件类型，显示错误信息
    if (invalidTypes.length > 0) {
        fileTypeInput.addClass('error');
        fileTypeError.text(`无效的文件类型格式: ${invalidTypes.join(', ')} (文件类型必须以.开头)`);
        return false;
    }

    // 验证通过
    fileTypeInput.removeClass('error');
    fileTypeError.text('');
    return true;
}

// 更新文件类型输入框
function updateFileTypeInput() {
    const checkedTypes = [];
    $('.file-type-cb:checked').each(function () {
        checkedTypes.push($(this).data('ext'));
    });

    // 获取当前输入框中的自定义类型
    const currentInput = $('#fileTypeFilter').val().trim();
    const customTypes = [];

    if (currentInput) {
        const inputTypes = currentInput.split(',');
        for (const type of inputTypes) {
            const trimmedType = type.trim();
            // 只保留不在复选框中的自定义类型
            if (trimmedType && !checkedTypes.includes(trimmedType)) {
                customTypes.push(trimmedType);
            }
        }
    }

    // 更新文件类型复选框的激活状态
    $('.file-type-checkbox').removeClass('active');
    $('.file-type-cb:checked').each(function () {
        $(this).closest('.file-type-checkbox').addClass('active');
    });

    // 如果有自定义类型，显示在输入框中
    if (customTypes.length > 0) {
        $('#fileTypeFilter').val(customTypes.join(', '));
    } else {
        $('#fileTypeFilter').val('');
    }
}

// 显示搜索结果
function showSearchResults(results, keyword) {
    // 清空搜索结果列表
    const $resultsList = $('#searchResultsList').empty();

    // 如果没有结果，显示提示
    if (!results || results.length === 0) {
        $resultsList.append(`
            <li class="search-result-item">
                <div class="file-name">没有找到匹配的结果</div>
            </li>
        `);
        return;
    }

    // 添加搜索结果
    results.forEach(result => {
        let matchTypeText = '';
        if (result.nameMatches && result.contentMatches) {
            matchTypeText = '匹配: 文件名和内容';
        } else if (result.nameMatches) {
            matchTypeText = '匹配: 文件名';
        } else if (result.contentMatches) {
            matchTypeText = '匹配: 文件内容';
        }

        let matchedLinesHtml = '';
        if (result.matchedLines && result.matchedLines.length > 0) {
            matchedLinesHtml = '<div class="matched-lines">';
            result.matchedLines.forEach(line => {
                // 先对内容进行HTML转义
                const escapedLine = escapeHtml(line);
                // 然后高亮关键词
                const highlightedLine = escapedLine.replace(
                    new RegExp(escapeHtml(keyword), 'gi'),
                    match => `<span style="color: #CE9178;">${match}</span>`
                );
                matchedLinesHtml += `<div class="matched-line">${highlightedLine}</div>`;
            });
            matchedLinesHtml += '</div>';
        }

        const $resultItem = $(`
            <li class="search-result-item" data-path="${result.path}">
                <div class="file-name">${escapeHtml(result.name)}</div>
                <div class="file-path">${escapeHtml(result.path)}</div>
                <div class="match-type">${matchTypeText}</div>
                ${matchedLinesHtml}
            </li>
        `);

        $resultsList.append($resultItem);
    });
}

// HTML转义函数
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

// 绑定搜索相关事件
function bindSearchEvents() {
    // 搜索按钮点击事件
    $('#searchBtn').on('click', function () {
        searchFiles();
    });

    // 搜索框回车事件
    $('#searchInput').on('keypress', function (e) {
        if (e.which === 13) {
            searchFiles();
        }
    });

    // 清除搜索范围按钮点击事件
    $('#clearSearchScope').on('click', function () {
        window.currentSearchFolder = '';
        $('#searchScopeInfo').hide();
        $(this).hide();
    });

    // 文件类型复选框点击事件
    $(document).on('change', '.file-type-cb', function () {
        updateFileTypeInput();
    });

    // 文件类型复选框的父容器点击事件（提高用户体验）
    $(document).on('click', '.file-type-checkbox', function (e) {
        // 如果点击的不是复选框本身，则切换复选框状态
        if (e.target.type !== 'checkbox') {
            const checkbox = $(this).find('input[type="checkbox"]');
            checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
        }
    });

    // 文件类型输入框失去焦点时验证格式
    $('#fileTypeFilter').on('blur', function () {
        validateFileTypes();
    });

    // 搜索结果项点击事件
    $(document).on('click', '.search-result-item', function () {
        const path = $(this).data('path');
        if (path) {
            // 切换到资源管理器页签
            window.uiUtils.switchSidebarTab('explorer');
            treeManager.locateTreeByPath(path);
        }
    });
}

// 导出函数
window.searchManager = {
    searchFiles: searchFiles,
    showSearchResults: showSearchResults,
    bindSearchEvents: bindSearchEvents,
    escapeHtml: escapeHtml,
    validateFileTypes: validateFileTypes,
    getFileTypes: getFileTypes,
    updateFileTypeInput: updateFileTypeInput
};