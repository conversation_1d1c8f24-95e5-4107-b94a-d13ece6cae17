package com.cirpoint.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.util.CommonUtil;
import com.cirpoint.util.FileDownloadUtil;
import com.cirpoint.util.FileUploadUtil;
import com.cirpoint.util.Util;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件服务
 */
@Slf4j
@Service
public class FileService extends ApplicationConfig {

	/**
	 * 从文件仓库下载文件
	 *
	 * @param fileName 文件名称
	 * @param filePath 文件相对路径（相对于文件仓库根目录）
	 * @return 文件下载响应
	 */
	public ResponseEntity<?> downloadFile(String fileName, String filePath) {
		// 构建完整的文件路径
		String fullPath = fileUploadPath + File.separator + filePath.replace("/", File.separator).replace('\\', '/');
		return FileDownloadUtil.fileResponse(fullPath, fileName);
	}

	/**
	 * 删除临时文件夹
	 * wanghq: 优化异常处理机制，支持单个文件删除失败时继续清理其他文件
	 *
	 * @return 删除结果，包含详细的成功/失败统计信息
	 */
	public JSONObject deleteTemporaryFiles() {
		JSONObject result = new JSONObject();
		List<String> successFiles = new ArrayList<>();
		List<String> failedFiles = new ArrayList<>();
		List<String> errorMessages = new ArrayList<>();

		int successCount = 0;
		int failedCount = 0;

		try {
			File tempDir = new File(tempPath);
			if (!tempDir.exists() || !tempDir.isDirectory()) {
				result.set("success", true)
					  .set("msg", "临时文件夹不存在或不是目录: " + tempPath)
					  .set("successCount", 0)
					  .set("failedCount", 0)
					  .set("failedFiles", failedFiles)
					  .set("errorMessages", errorMessages);
				log.info("临时文件夹不存在: {}", tempPath);
				return result;
			}

			// wanghq: 递归删除所有文件和子目录，对每个文件单独处理异常
			deleteDirectoryContents(tempDir, successFiles, failedFiles, errorMessages);

			successCount = successFiles.size();
			failedCount = failedFiles.size();

			result.set("success", true)
				  .set("successCount", successCount)
				  .set("failedCount", failedCount)
				  .set("failedFiles", failedFiles)
				  .set("errorMessages", errorMessages);

			if (failedCount > 0) {
				result.set("msg", String.format("部分文件删除失败: 成功%d个，失败%d个", successCount, failedCount));
				log.warn("临时文件夹清理完成，但有{}个文件删除失败: {}", failedCount, tempPath);
			} else {
				result.set("msg", String.format("所有文件删除成功: 共删除%d个文件", successCount));
				log.info("成功清理临时文件夹: {}，共删除{}个文件", tempPath, successCount);
			}

		} catch (Exception e) {
			log.error("清理临时文件夹时发生未预期的错误: {}", tempPath, e);
			result.set("success", false)
				  .set("msg", "清理临时文件夹时发生错误: " + e.getLocalizedMessage())
				  .set("successCount", successCount)
				  .set("failedCount", failedCount)
				  .set("failedFiles", failedFiles)
				  .set("errorMessages", errorMessages);
		}

		return result;
	}

	/**
	 * 递归删除目录内容
	 * wanghq: 对每个文件/目录单独处理异常，记录详细的失败原因
	 *
	 * @param directory 要清理的目录
	 * @param successFiles 成功删除的文件列表
	 * @param failedFiles 删除失败的文件列表
	 * @param errorMessages 错误信息列表
	 */
	private void deleteDirectoryContents(File directory, List<String> successFiles,
										List<String> failedFiles, List<String> errorMessages) {
		File[] files = directory.listFiles();
		if (files == null) {
			return;
		}

		for (File file : files) {
			try {
				if (file.isDirectory()) {
					// wanghq: 先递归删除子目录内容
					deleteDirectoryContents(file, successFiles, failedFiles, errorMessages);
					// wanghq: 然后删除空目录
					if (file.delete()) {
						successFiles.add(file.getAbsolutePath());
						log.debug("成功删除目录: {}", file.getAbsolutePath());
					} else {
						failedFiles.add(file.getAbsolutePath());
						String errorMsg = "目录删除失败（可能不为空）: " + file.getAbsolutePath();
						errorMessages.add(errorMsg);
						log.warn(errorMsg);
					}
				} else {
					// wanghq: 删除文件
					if (file.delete()) {
						successFiles.add(file.getAbsolutePath());
						log.debug("成功删除文件: {}", file.getAbsolutePath());
					} else {
						failedFiles.add(file.getAbsolutePath());
						String errorMsg = "文件删除失败（可能被占用或权限不足）: " + file.getAbsolutePath();
						errorMessages.add(errorMsg);
						log.warn(errorMsg);
					}
				}
			} catch (Exception e) {
				failedFiles.add(file.getAbsolutePath());
				String errorMsg = "删除文件时发生异常: " + file.getAbsolutePath() + ", 原因: " + e.getLocalizedMessage();
				errorMessages.add(errorMsg);
				log.error("删除文件时发生异常: {}", file.getAbsolutePath(), e);
			}
		}
	}

	/**
	 * 删除临时影像预览文件
	 */
	public void deleteTempPhotoFiles() {
		// 获取文件上传路径
		try {
			// 创建临时影像目录
			String month = "imgTemp";
			String photoPath = fileUploadPath + File.separator + month + File.separator;
			FileUtil.mkdir(photoPath);
			// 删除临时影像目录
			FileUtil.del(photoPath);
			log.info("成功删除临时影像目录: {}", photoPath);
		} catch (Exception e) {
			log.error("删除临时影像目录失败:", e);
			throw e;
		}
	}

	/**
	 * 下载照片文件
	 *
	 * @param url 下载URL
	 * @return 下载结果
	 */
	public JSONObject downloadPhoto(String url) {
		try {
			return downloadFromUrl(url);
		} catch (Exception e) {
			// 创建临时影像目录
			String month = "imgTemp";
			String photoPath = fileUploadPath + File.separator + month + File.separator;
			FileUtil.mkdir(photoPath);

			// 生成唯一文件名
			String uuid = UUID.randomUUID().toString();
			File saveFile = new File(photoPath + uuid);
			// 下载文件
			File distFile = HttpUtil.downloadFileFromUrl(url, saveFile);
			// 构建返回结果
			return JSONUtil.createObj()
					.set("success", true)
					.set("fileFormat", FileNameUtil.getSuffix(distFile))
					.set("fileName", FileNameUtil.getName(distFile))
					.set("filePath", "//" + month + "//" + uuid);
		}
	}

	/**
	 * 从URL下载文件
	 */
	private JSONObject downloadFromUrl(String urlStr) throws Exception {
		// 解析文件名和格式
		String fileName = urlStr.substring(urlStr.indexOf("_") + 1);
		String tempStr = urlStr.substring(0, urlStr.indexOf("_"));
		String fileFormat = tempStr.substring(tempStr.lastIndexOf(".") + 1);

		// 创建HTTP连接
		URL url = new URL(urlStr);
		HttpURLConnection conn = (HttpURLConnection) url.openConnection();
		conn.setRequestProperty("User-Agent", "Mozilla/4.0(compatible MSIE 5.0; Windows NT; DigExt)");
		conn.setRequestMethod("GET");
		conn.setDoOutput(true);

		// 检查文件是否存在
		String contentDisposition = URLDecoder.decode(conn.getHeaderField("content-Disposition"), "UTF-8");
		if (contentDisposition.contains("服务器中找不到该文件")) {
			return JSONUtil.createObj().set("success", false);
		}

		// 读取文件内容
		try (InputStream is = conn.getInputStream()) {
			byte[] fileData = readInputStream(is);

			String month = "imgTemp";
			String photoPath = fileUploadPath + File.separator + month + File.separator;
			FileUtil.mkdir(photoPath);

			// 保存文件
			String uuid = UUID.randomUUID().toString();
			File saveFile = new File(photoPath + uuid);
			FileUtil.writeBytes(fileData, saveFile);

			// 返回结果
			return JSONUtil.createObj()
					.set("success", true)
					.set("fileFormat", fileFormat)
					.set("fileName", fileName)
					.set("filePath", "//" + month + "//" + uuid);
		}
	}

	/**
	 * 读取输入流内容
	 */
	private byte[] readInputStream(InputStream inputStream) throws IOException {
		int bufferSize = 1024;
		byte[] buffer = new byte[bufferSize];
		ByteArrayOutputStream bos = new ByteArrayOutputStream();

		int bytesRead;
		while ((bytesRead = inputStream.read(buffer, 0, bufferSize)) != -1) {
			bos.write(buffer, 0, bytesRead);
		}

		return bos.toByteArray();
	}


	/**
	 * 导出型号研制进度Excel
	 *
	 * @return 生成的Excel文件
	 */
	public File exportModelProcess() {
		// 查询数据
		JSONObject res = Util.postTwxForObject("Thing.Fn.AitScreen", "QueryAllModelProcess",
				JSONUtil.createObj());
		JSONArray sheetData = new JSONArray();

		JSONArray data = res.getJSONArray("data");
		for (int i = 0; i < data.size(); i++) {
			JSONObject obj = data.getJSONObject(i);
			JSONArray row = JSONUtil.createArray();
			row.add("");
			row.add(obj.getStr("modelName"));
			row.add(obj.getStr("currentNode"));
			row.add(obj.getStr("allNode"));
			sheetData.add(row);
		}

		JSONArray headers = JSONUtil.parseArray(Arrays.asList("关联名称", "型号名称", "当前节点", "所有节点"));
		JSONArray columnWidths = JSONUtil.parseArray(Arrays.asList(15, 15, 30, 150));
		return CommonUtil.createExcelFile("型号研制进度", "型号研制进度", headers, sheetData, columnWidths, 28);
	}

	/**
	 * 获取文件总大小
	 *
	 * @return 文件大小信息
	 */
	public String getFileSize() {
		return Util.getFileTotalSize();
	}

	/**
	 * 更新型号文件大小统计
	 */
	public void updateModelFileSize() {
		// 查询所有型号
		JSONArray modelArr = Util.postQuerySql("select * from phase_model");

		for (int i = 0; i < modelArr.size(); i++) {
			JSONObject model = modelArr.getJSONObject(i);
			String modelId = model.getStr("TREEID");
			// 查询文件清单
			String querySql = buildFileListQuery(modelId);
			JSONArray filePaths = Util.postQuerySql(querySql);
			// 计算文件大小
			long fileSizeCount = calculateTotalFileSize(filePaths);
			// 更新统计数据
			updateModelStatistics(modelId, fileSizeCount);
		}
	}

	/**
	 * 构建文件清单查询SQL
	 */
	private String buildFileListQuery(String modelId) {
		return "select PHOTO_PATH FILEPATH " +
				"from QUALITY_SINGLE_PHOTO " +
				"where PHOTO_PATH like '//2%' " +
				"  and TREE_ID in (select TREEID from DATAPACKAGETREE start with TREEID = " + modelId + " connect by prior TREEID = PARENTID) " +
				"union all " +
				"select FILEPATH " +
				"from RESULTGATHER " +
				"where FILEPATH like '//2%' " +
				"  and NODECODE in (select id " +
				"                   from DATA_PACKAGE " +
				"                   where REFTREEID in " +
				"                         (select TREEID from DATAPACKAGETREE start with TREEID = " + modelId + " connect by prior TREEID = PARENTID))";
	}

	/**
	 * 计算文件总大小
	 */
	private long calculateTotalFileSize(JSONArray filePaths) {
		long fileSizeCount = 0;
		for (int j = 0; j < filePaths.size(); j++) {
			String filePath = fileUploadPath + filePaths.getJSONObject(j).getStr("FILEPATH");
			long fileSize = FileUtil.size(FileUtil.file(filePath));
			fileSizeCount += fileSize;
		}
		return fileSizeCount;
	}

	/**
	 * 更新型号统计数据
	 */
	private void updateModelStatistics(String modelId, long fileSizeCount) {
		JSONObject params = JSONUtil.createObj()
				.set("modelId", modelId)
				.set("statType", "文件大小")
				.set("statCount", fileSizeCount);

		Util.postTwxForObject("Thing.Fn.AitScreen", "UpdateModelStatistics", params);
	}

	/**
	 * 处理大文件上传
	 *
	 * @param file      上传的文件
	 * @param chunk     当前分片序号
	 * @param chunks    总分片数
	 * @param fileName  文件名
	 * @param extraData 额外参数
	 * @return 上传结果
	 */
	public JSONObject uploadBigFile(MultipartFile file, Integer chunk, Integer chunks,
									String fileName, String reqIdent, String extraData) {

		// 使用 FileUploadUtil 处理文件上传
		JSONObject result = FileUploadUtil.handleFileUpload(file, chunk, chunks, fileName, reqIdent, extraData);

		// 如果上传完成且成功，处理文件移动
		if (result.getBool("success") && result.getBool("isAll", false)) {
			String srcFile = result.getStr("file");

			// 生成新的UUID路径
			JSONObject newUUIDPath = Util.getNewUUIDPath();
			String distFile = newUUIDPath.getStr("fileAbsolutePath");

			// 移动文件到目标位置
			FileUtil.copy(srcFile, distFile, true);

			// 设置文件相对路径
			result.set("filePath", newUUIDPath.getStr("filePath"));

			// 清理临时文件
			FileUploadUtil.cleanupTemp(srcFile);
		}

		return result;
	}

	/**
	 * 删除指定路径的文件
	 *
	 * @param filePath 文件路径
	 * @return 删除结果消息
	 */
	public boolean deleteFile(String filePath) {
		return FileUtil.del(filePath);
	}

	/**
	 * 上传文件
	 *
	 * @param file 上传的文件数组
	 * @return 上传结果
	 */
	public JSONObject uploadFile(MultipartFile file) throws IOException {
		// 创建月份目录
		String month = new SimpleDateFormat("yyyy-MM").format(new Date());
		String monthPath = fileUploadPath + File.separator + month;
		FileUtil.mkdir(monthPath);

		// 获取原始文件名和格式
		String originalFilename = file.getOriginalFilename();
		String fileFormat = FileNameUtil.extName(originalFilename);

		// 生成唯一文件名
		String uuid = UUID.randomUUID().toString();
		String filePath = monthPath + File.separator + uuid;

		// 保存文件
		File destFile = new File(filePath);
		file.transferTo(destFile);

		// 构建文件信息
		JSONObject fileInfo = new JSONObject()
				.set("fileKey", "file")
				.set("fileFormat", fileFormat)
				.set("fileName", originalFilename)
				.set("filePath", "//" + month + "//" + uuid)
				.set("fileAbsolutePath", filePath);

		// 返回结果
		return new JSONObject()
				.set("success", true)
				.set("msg", "上传成功")
				.set("data", fileInfo);

	}

	/**
	 * 上传Excel模板文件
	 *
	 * @param files 上传的文件列表
	 * @return 上传结果
	 */
	public JSONArray uploadExcelTemplates(MultipartFile[] files) throws IOException {
		JSONArray results = new JSONArray();
		// 获取模板存储路径
		String basePath = excelTplPath + File.separator + "excelTpl" + File.separator + "table";
		FileUtil.mkdir(basePath);
		// 处理每个上传的文件
		for (MultipartFile file : files) {
			String originalFilename = file.getOriginalFilename();
			String fileFormat = FileNameUtil.extName(originalFilename);
			String uuid = UUID.randomUUID().toString();
			String filePath = basePath + File.separator + uuid;

			// 保存文件
			File destFile = new File(filePath);
			file.transferTo(destFile);

			// 记录文件信息
			JSONObject fileInfo = new JSONObject();
			fileInfo.set("fileKey", file.getName());
			fileInfo.set("fileFormat", fileFormat);
			fileInfo.set("fileName", originalFilename);
			fileInfo.set("filePath", filePath);
			results.add(fileInfo);
		}
		return results;
	}
}