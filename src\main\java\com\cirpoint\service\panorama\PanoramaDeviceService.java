package com.cirpoint.service.panorama;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.cirpoint.service.ApplicationConfig;
import com.cirpoint.util.CommonUtil;
import com.cirpoint.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * 全景图设备管理服务
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@Slf4j
@Service
public class PanoramaDeviceService extends ApplicationConfig {

    /**
     * 查询型号下的单机数据并保存到PANORAMA_DEVICE表
     */
    public void queryAndSaveStandAloneDevices(Long taskId, String modelId, String modelName) {
        try {
            // 构建调用参数
            JSONObject params = new JSONObject();
            params.put("modelId", modelId);

            // 调用Thing.Fn.SecondTable.QueryStandAloneByModel获取单机数据
            JSONObject res = Util.postTwxForObject("Thing.Fn.SecondTable", "QueryStandAloneByModel", params);

            if (res != null && res.containsKey("data")) {
                // 根据实际返回格式，data字段直接是JSONArray
                Object dataObj = res.get("data");

                if (dataObj instanceof JSONArray) {
                    JSONArray dataArray = (JSONArray) dataObj;

                    if (dataArray != null && dataArray.size() > 0) {
                        // 遍历单机数据并保存到数据库
                        for (int i = 0; i < dataArray.size(); i++) {
                            Object rowObj = dataArray.get(i);
                            if (rowObj instanceof JSONObject) {
                                JSONObject deviceData = (JSONObject) rowObj;
                                saveStandAloneDeviceToDatabase(taskId, deviceData, modelId, modelName, i + 1);
                            }
                        }
                    }
                } else {
                    log.warn("调用QueryStandAloneByModel返回的数据格式异常，data字段不是JSONArray类型: {}", dataObj.getClass().getSimpleName());
                }
            } else {
                log.warn("调用QueryStandAloneByModel失败或返回数据为空");
            }
        } catch (Exception e) {
            log.error("查询型号下的单机数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 保存单机数据到PANORAMA_DEVICE表
     */
    private void saveStandAloneDeviceToDatabase(Long taskId, JSONObject deviceData, String modelId, String modelName, int sequenceNo) {
        try {
            // 从返回的数据中提取字段
            String deviceCode = deviceData.getStr("devicecode");
            String deviceName = deviceData.getStr("devicename");
            String deviceBatch = deviceData.getStr("devicebatch");

            // 检查设备是否已存在 (基于任务ID和组合键，与Excel上传逻辑保持一致)
            String checkSql = "SELECT DEVICE_ID FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId +
                    " AND " + (deviceName != null ? "DEVICE_NAME = '" + deviceName.replace("'", "''") + "'" : "DEVICE_NAME IS NULL") +
                    " AND " + (deviceCode != null ? "DEVICE_CODE = '" + deviceCode.replace("'", "''") + "'" : "DEVICE_CODE IS NULL") +
                    " AND " + (deviceBatch != null ? "BATCH_NO = '" + deviceBatch.replace("'", "''") + "'" : "BATCH_NO IS NULL");

            JSONArray checkResult = Util.postQuerySql(checkSql);

            if (checkResult != null && !checkResult.isEmpty()) {
                // 设备已存在，执行更新操作
                Long deviceId = checkResult.getJSONObject(0).getLong("DEVICE_ID");
                String updateSql = "UPDATE PANORAMA_DEVICE SET " +
                        "SEQUENCE_NO = " + sequenceNo + ", " +
                        "MODEL_ID = '" + modelId.replace("'", "''") + "', " +
                        "MODEL_NAME = '" + modelName.replace("'", "''") + "', " +
                        "UPDATE_TIME = SYSDATE " +
                        "WHERE DEVICE_ID = " + deviceId;

                Util.postCommandSql(updateSql);
            } else {
                // 设备不存在，执行插入操作
                String insertSql = "INSERT INTO PANORAMA_DEVICE " +
                        "(DEVICE_ID, TASK_ID, DEVICE_NAME, DEVICE_CODE, BATCH_NO, SEQUENCE_NO, MODEL_ID, MODEL_NAME, CREATE_TIME, UPDATE_TIME) " +
                        "VALUES (SEQ_PANORAMA_DEVICE.NEXTVAL, " + taskId + ", " +
                        (deviceName != null ? "'" + deviceName.replace("'", "''") + "'" : "NULL") + ", " +
                        (deviceCode != null ? "'" + deviceCode.replace("'", "''") + "'" : "NULL") + ", " +
                        (deviceBatch != null ? "'" + deviceBatch.replace("'", "''") + "'" : "NULL") + ", " +
                        sequenceNo + ", " +
                        (modelId != null ? "'" + modelId.replace("'", "''") + "'" : "NULL") + ", " +
                        (modelName != null ? "'" + modelName.replace("'", "''") + "'" : "NULL") + ", SYSDATE, SYSDATE)";

                Util.postCommandSql(insertSql);
            }
        } catch (Exception e) {
            log.error("保存自动导入的单机数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 上传单机信息Excel文件
     */
    public JSONObject uploadDeviceExcel(Long taskId, MultipartFile file) throws IOException {
        try {
            // 保存Excel文件
            String tempDir = tempPath + File.separator + "excel_" + System.currentTimeMillis();
            FileUtil.mkdir(tempDir);
            String excelPath = tempDir + File.separator + file.getOriginalFilename();
            file.transferTo(new File(excelPath));

            // 读取Excel文件
            ExcelReader reader = ExcelUtil.getReader(new File(excelPath));
            List<List<Object>> rows = reader.read();

            // 跳过表头，从第二行开始读取数据
            for (int i = 1; i < rows.size(); i++) {
                List<Object> row = rows.get(i);
                if (row.size() >= 4) {
                    saveDeviceToDatabase(taskId, row);
                }
            }

            // 清理临时文件
            FileUtil.del(tempDir);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "Excel文件上传成功");
        } catch (Exception e) {
            log.error("上传Excel文件失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "上传Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 保存单机信息到数据库（支持去重更新）
     */
    private void saveDeviceToDatabase(Long taskId, List<Object> row) {
        try {
            Object sequenceNo = row.get(0);
            Object deviceName = row.get(1);
            Object deviceCode = row.get(2);
            Object batchNo = row.get(3);
            // 移除型号ID和型号名称的处理，Excel中只有4列数据

            // 检查设备是否已存在（基于任务ID、设备名称、设备代号、批次号）
            // 修复NULL值比较问题，使用IS NULL而不是= NULL
            String checkSql = "SELECT COUNT(*) as cnt FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId +
                    " AND " + (deviceName != null ? "DEVICE_NAME = '" + deviceName.toString().replace("'", "''") + "'" : "DEVICE_NAME IS NULL") +
                    " AND " + (deviceCode != null ? "DEVICE_CODE = '" + deviceCode.toString().replace("'", "''") + "'" : "DEVICE_CODE IS NULL") +
                    " AND " + (batchNo != null ? "BATCH_NO = '" + batchNo.toString().replace("'", "''") + "'" : "BATCH_NO IS NULL");

            JSONArray checkResult = Util.postQuerySql(checkSql);
            JSONArray dataArray = checkResult;

            boolean deviceExists = false;
            if (dataArray != null && dataArray.size() > 0) {
                // 修复类型转换错误：从JSONArray中获取JSONObject
                Object firstRowObj = dataArray.get(0);
                if (firstRowObj instanceof JSONObject) {
                    JSONObject firstRow = (JSONObject) firstRowObj;
                    int count = firstRow.getInt("CNT");
                    deviceExists = count > 0;
                }
            }

            if (deviceExists) {
                // 设备已存在，执行更新操作（移除型号ID和型号名称）
                String updateSql = "UPDATE PANORAMA_DEVICE SET " +
                        "SEQUENCE_NO = " + (sequenceNo != null ? sequenceNo.toString() : "NULL") + ", " +
                        "UPDATE_TIME = SYSDATE " +
                        "WHERE TASK_ID = " + taskId +
                        " AND " + (deviceName != null ? "DEVICE_NAME = '" + deviceName.toString().replace("'", "''") + "'" : "DEVICE_NAME IS NULL") +
                        " AND " + (deviceCode != null ? "DEVICE_CODE = '" + deviceCode.toString().replace("'", "''") + "'" : "DEVICE_CODE IS NULL") +
                        " AND " + (batchNo != null ? "BATCH_NO = '" + batchNo.toString().replace("'", "''") + "'" : "BATCH_NO IS NULL");

                Util.postCommandSql(updateSql);
            } else {
                // 设备不存在，执行插入操作（移除型号ID和型号名称）
                String insertSql = "INSERT INTO PANORAMA_DEVICE " +
                        "(DEVICE_ID, TASK_ID, DEVICE_NAME, DEVICE_CODE, BATCH_NO, SEQUENCE_NO, CREATE_TIME, UPDATE_TIME) " +
                        "VALUES (SEQ_PANORAMA_DEVICE.NEXTVAL, " + taskId + ", " +
                        (deviceName != null ? "'" + deviceName.toString().replace("'", "''") + "'" : "NULL") + ", " +
                        (deviceCode != null ? "'" + deviceCode.toString().replace("'", "''") + "'" : "NULL") + ", " +
                        (batchNo != null ? "'" + batchNo.toString().replace("'", "''") + "'" : "NULL") + ", " +
                        (sequenceNo != null ? sequenceNo.toString() : "NULL") + ", SYSDATE, SYSDATE)";

                Util.postCommandSql(insertSql);
            }
        } catch (Exception e) {
            log.error("保存单机信息失败", e);
        }
    }

    /**
     * 获取任务下的设备列表（分页）
     */
    public JSONObject getDeviceList(Long taskId, int page, int limit) {
        try {
            // 计算分页参数
            int offset = (page - 1) * limit;

            // 查询总数
            String countSql = "SELECT COUNT(*) as total FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId;
            JSONArray countResult = Util.postQuerySql(countSql);
            JSONArray countArray = countResult;
            int total = 0;
            if (countArray != null && countArray.size() > 0) {
                // 修复类型转换错误：从JSONArray中获取JSONObject
                Object firstRowObj = countArray.get(0);
                if (firstRowObj instanceof JSONObject) {
                    JSONObject firstRow = (JSONObject) firstRowObj;
                    total = firstRow.getInt("TOTAL");
                }
            }

            // 查询分页数据
            String dataSql = "SELECT * FROM (" +
                    "SELECT ROWNUM rn, t.* FROM (" +
                    "SELECT DEVICE_ID, DEVICE_NAME, DEVICE_CODE, BATCH_NO, SEQUENCE_NO, MODEL_ID, MODEL_NAME, " +
                    "TO_CHAR(CREATE_TIME, 'YYYY-MM-DD HH24:MI:SS') as CREATE_TIME, " +
                    "TO_CHAR(UPDATE_TIME, 'YYYY-MM-DD HH24:MI:SS') as UPDATE_TIME " +
                    "FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId + " " +
                    "ORDER BY SEQUENCE_NO ASC, CREATE_TIME DESC" +
                    ") t WHERE ROWNUM <= " + (offset + limit) +
                    ") WHERE rn > " + offset;

            JSONArray dataResult = Util.postQuerySql(dataSql);
            JSONArray dataArray = dataResult;

            return JSONUtil.createObj()
                    .set("code", 0)
                    .set("msg", "")
                    .set("count", total)
                    .set("data", dataArray != null ? dataArray : new JSONArray());

        } catch (Exception e) {
            log.error("获取设备列表失败", e);
            return JSONUtil.createObj()
                    .set("code", 1)
                    .set("msg", "获取设备列表失败: " + e.getMessage())
                    .set("count", 0)
                    .set("data", new JSONArray());
        }
    }

    /**
     * 导出设备数据为Excel文件
     */
    public File exportDeviceExcel(Long taskId) {
        try {
            // 查询任务信息
            String taskSql = "SELECT TASK_NAME FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray taskResult = Util.postQuerySql(taskSql);
            String taskName = "未知任务";
            if (taskResult != null && taskResult.size() > 0) {
                Object firstRowObj = taskResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    taskName = ((JSONObject) firstRowObj).getStr("TASK_NAME");
                }
            }

            // 查询设备数据（移除型号ID和型号名称列）
            String deviceSql = "SELECT SEQUENCE_NO, DEVICE_NAME, DEVICE_CODE, BATCH_NO " +
                    "FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId + " ORDER BY SEQUENCE_NO ASC, CREATE_TIME ASC";
            JSONArray deviceData = Util.postQuerySql(deviceSql);

            // 准备表头（移除型号ID和型号名称列）
            JSONArray headers = new JSONArray();
            headers.add("序号");
            headers.add("单机名称");
            headers.add("单机代号");
            headers.add("批次号");

            // 准备数据
            JSONArray data = new JSONArray();
            if (deviceData != null && deviceData.size() > 0) {
                for (int i = 0; i < deviceData.size(); i++) {
                    Object rowObj = deviceData.get(i);
                    if (rowObj instanceof JSONObject) {
                        JSONObject row = (JSONObject) rowObj;
                        JSONArray dataRow = new JSONArray();

                        // 序号字段确保为整数格式
                        Object sequenceNo = row.get("SEQUENCE_NO");
                        if (sequenceNo != null) {
                            if (sequenceNo instanceof Number) {
                                dataRow.add(((Number) sequenceNo).intValue());
                            } else {
                                try {
                                    dataRow.add(Integer.parseInt(sequenceNo.toString()));
                                } catch (NumberFormatException e) {
                                    dataRow.add(sequenceNo.toString());
                                }
                            }
                        } else {
                            dataRow.add("");
                        }

                        dataRow.add(row.getStr("DEVICE_NAME"));
                        dataRow.add(row.getStr("DEVICE_CODE"));
                        dataRow.add(row.getStr("BATCH_NO"));
                        data.add(dataRow);
                    }
                }
            }

            // 设置列宽（移除型号ID和型号名称列）
            JSONArray columnWidths = new JSONArray();
            columnWidths.add(8);   // 序号
            columnWidths.add(25);  // 单机名称（增加宽度）
            columnWidths.add(20);  // 单机代号（增加宽度）
            columnWidths.add(20);  // 批次号（增加宽度）

            // 生成文件名
            String timestamp = DateUtil.format(new Date(), "yyyyMMdd_HHmmss");
            String fileName = "设备数据_" + taskName + "_" + timestamp;

            // 使用CommonUtil.createExcelFile创建Excel文件
            File excelFile = CommonUtil.createExcelFile(fileName, headers, data, columnWidths, 25);
            return excelFile;
        } catch (Exception e) {
            log.error("导出设备Excel失败", e);
            return null;
        }
    }

    /**
     * 重新导入指定任务的型号单机数据
     *
     * @param taskId 任务ID
     * @return 操作结果
     */
    public JSONObject refreshDeviceData(Long taskId) {
        try {
            // 1. 根据taskId获取任务信息，特别是modelId和modelName
            String taskQuerySql = "SELECT MODEL_ID, MODEL_NAME FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray taskResult = Util.postQuerySql(taskQuerySql);

            if (taskResult == null || taskResult.isEmpty()) {
                return JSONUtil.createObj().set("success", false).set("msg", "未找到指定任务");
            }

            JSONObject taskInfo = taskResult.getJSONObject(0);
            String modelId = taskInfo.getStr("MODEL_ID");
            String modelName = taskInfo.getStr("MODEL_NAME");

            if (modelId == null || modelId.trim().isEmpty()) {
                return JSONUtil.createObj().set("success", false).set("msg", "任务未关联任何型号，无法导入数据");
            }

            // 2. 调用私有方法来执行去重更新
            queryAndSaveStandAloneDevices(taskId, modelId, modelName);

            return JSONUtil.createObj().set("success", true).set("msg", "单机数据刷新成功");

        } catch (Exception e) {
            log.error("重新导入单机数据失败, taskId={}", taskId, e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "重新导入单机数据失败: " + e.getMessage());
        }
    }
}
