package com.cirpoint.model;

import java.util.Date;

/**
 * 路径条目类，用于多路径管理
 */
public class PathEntry {
    private String id;          // 唯一标识符
    private String path;        // 路径字符串
    private boolean enabled;    // 是否启用
    private Date createTime;    // 创建时间
    private Date updateTime;    // 更新时间

    // 默认构造函数
    public PathEntry() {
    }

    // 带参数的构造函数
    public PathEntry(String id, String path, boolean enabled) {
        this.id = id;
        this.path = path;
        this.enabled = enabled;
        this.createTime = new Date();
        this.updateTime = new Date();
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        this.updateTime = new Date();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
