/**
 * 全景图热点编辑系统 - 预览和通信模块
 * 
 * <AUTHOR>
 * @date 2025-06-10
 * @description 处理全景图预览和iframe通信功能
 * @requires panorama-core.js, panorama-ui.js
 */

/**
 * 全景图编辑器预览和通信模块
 */
var PanoramaPreview = (function() {
    'use strict';
    
    // ==================== 预览管理 ====================
    
    /**
     * 加载预览
     */
    function loadPreview() {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) return;

        var $ = layui.$;
        $.get('/panorama/preview/path', { taskId: currentTaskId }, function (res) {
            if (res.success) {
                showPreview(res.data.previewUrl);
            } else {
                hidePreview();
            }
        });
    }

    /**
     * 显示预览
     * @param {string} previewUrl 预览URL
     */
    function showPreview(previewUrl) {
        var placeholder = document.querySelector('#previewContainer .preview-placeholder');
        var iframe = document.getElementById('panoramaFrame');
        
        if (placeholder) {
            placeholder.style.display = 'none';
        }
        
        if (iframe) {
            iframe.src = previewUrl;
            iframe.style.display = 'block';
        }
    }

    /**
     * 隐藏预览
     */
    function hidePreview() {
        var placeholder = document.querySelector('#previewContainer .preview-placeholder');
        var iframe = document.getElementById('panoramaFrame');
        
        if (iframe) {
            iframe.style.display = 'none';
        }
        
        if (placeholder) {
            placeholder.style.display = 'flex';
        }
    }

    /**
     * 更新预览
     */
    function updatePreview() {
        // 刷新iframe
        var iframe = document.getElementById('panoramaFrame');
        if (iframe && iframe.src) {
            iframe.src = iframe.src;
        }
    }
    
    // ==================== iframe消息通信 ====================
    
    /**
     * 初始化iframe消息监听
     */
    function initMessageListener() {
        // 监听iframe消息
        window.addEventListener('message', function (event) {
            // 处理来自全景图iframe的消息
            if (event.data && event.data.type) {
                handleIframeMessage(event.data);
            }
        });
    }
    
    /**
     * 处理iframe消息
     * @param {Object} data 消息数据
     */
    function handleIframeMessage(data) {
        switch (data.type) {
            case 'hotspotClick':
                // 热点被点击 - 处理热点点击编辑
                handleHotspotClick(data);
                break;
            case 'addHotspot':
                // 双击添加热点
                handleAddHotspot(data);
                break;
            case 'viewChange':
                // 视角改变
                handleViewChange(data);
                break;
            case 'hotspotLocationComplete':
                // 热点定位完成，只在失败时显示错误信息
                if (!data.success) {
                    layui.layer.msg('热点定位失败', { icon: 2 });
                }
                break;
            case 'nodeSwitch':
                // 节点切换事件
                handleNodeSwitch(data);
                break;
            case 'hotspotUpdateComplete':
                // 热点更新完成
                handleHotspotUpdateComplete(data);
                break;
            case 'addHotspotComplete':
                // 热点添加完成
                handleAddHotspotComplete(data);
                break;
            case 'removeHotspotComplete':
                // 热点删除完成
                handleRemoveHotspotComplete(data);
                break;
            case 'hotspotRightClickDelete':
                // 右键菜单删除热点
                handleHotspotRightClickDelete(data);
                break;
            case 'hotspotHighlightStarted':
                // 热点高亮开始
                handleHotspotHighlightStarted(data);
                break;
            case 'hotspotHighlightStopped':
                // 热点高亮停止
                handleHotspotHighlightStopped(data);
                break;
            default:
                // 未知消息类型
                console.log('收到未知类型的iframe消息:', data);
                break;
        }
    }
    
    /**
     * 向iframe发送消息
     * @param {Object} message 消息对象
     */
    function sendMessageToIframe(message) {
        var iframe = document.getElementById('panoramaFrame');
        if (iframe && iframe.contentWindow) {
            try {
                iframe.contentWindow.postMessage(message, '*');
            } catch (e) {
                console.warn('发送消息到iframe失败:', e);
            }
        }
    }
    
    // ==================== 节点切换处理 ====================
    
    /**
     * 处理节点切换事件
     * @param {Object} data 节点切换数据
     */
    function handleNodeSwitch(data) {
        try {
            var newNodeId = data.nodeId;
            var switchType = data.switchType; // 'initial' 或 'switch'

            // 更新当前节点ID
            var previousNodeId = PanoramaCore.getCurrentNodeId();
            PanoramaCore.setCurrentNodeId(newNodeId);

            // 更新节点显示
            PanoramaUI.updateNodeDisplay(newNodeId, switchType);

            // 如果是节点切换（非初始化），刷新热点表格
            var currentTaskId = PanoramaCore.getCurrentTaskId();
            if (switchType === 'switch' && currentTaskId) {
                if (typeof PanoramaHotspot !== 'undefined' && PanoramaHotspot.refreshHotspotTableForNode) {
                    PanoramaHotspot.refreshHotspotTableForNode(newNodeId);
                }

            } else if (switchType === 'initial' && currentTaskId) {
                if (typeof PanoramaHotspot !== 'undefined' && PanoramaHotspot.refreshHotspotTableForNode) {
                    PanoramaHotspot.refreshHotspotTableForNode(newNodeId);
                }
            }

        } catch (error) {
            // 处理节点切换事件失败
            console.warn('处理节点切换事件失败:', error);
        }
    }
    
    /**
     * 处理视角改变事件
     * @param {Object} data 视角数据
     */
    function handleViewChange(data) {
        // 可以在这里处理视角改变的逻辑
        // 例如记录当前视角、更新UI状态等
        console.log('视角改变:', data);
    }
    
    // ==================== 热点点击处理 ====================
    
    /**
     * 处理热点点击事件
     * @param {Object} data 热点点击数据
     */
    function handleHotspotClick(data) {
        // 检查是否有选中的任务
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) {
            layui.layer.msg('请先选择一个任务', { icon: 2 });
            return;
        }

        // 检查热点数据是否有效
        if (!data || !data.hotspot) {
            layui.layer.msg('热点信息无效', { icon: 2 });
            return;
        }

        var hotspotInfo = data.hotspot;
        var nodeId = data.nodeId || PanoramaCore.getCurrentNodeId();

        // 检查是否有坐标信息
        if (!hotspotInfo.pan || !hotspotInfo.tilt) {
            layui.layer.msg('热点坐标信息缺失', { icon: 2 });
            return;
        }

        // 显示加载提示
        var loadingIndex = layer.msg('正在查找热点信息...', { icon: 16, shade: 0.01 });

        // 优先使用坐标查找（最可靠的方式）
        var $ = layui.$;
        $.ajax({
            url: '/panorama/hotspot/findByCoordinates',
            type: 'POST',
            data: {
                taskId: currentTaskId,
                nodeId: nodeId,
                pan: hotspotInfo.pan,
                tilt: hotspotInfo.tilt
            },
            success: function (res) {
                layui.layer.close(loadingIndex);

                if (res.success && res.data) {
                    // 找到对应的热点记录，调用编辑函数
                    if (typeof PanoramaHotspot !== 'undefined' && PanoramaHotspot.editHotspot) {
                        PanoramaHotspot.editHotspot(res.data);
                    }
                } else {
                    layui.layer.msg(res.msg || '未找到对应的热点记录', { icon: 2 });
                }
            },
            error: function () {
                layui.layer.close(loadingIndex);
                layui.layer.msg('查找热点信息失败，请重试', { icon: 2 });
            }
        });
    }

    /**
     * 处理热点更新完成事件
     * @param {Object} data 更新完成数据
     */
    function handleHotspotUpdateComplete(data) {
        if (data.success) {
            // 热点更新成功，可以显示成功提示（可选）
            // layui.layer.msg('热点信息已更新', {icon: 1, time: 1000});
        } else {
            // 热点更新失败，显示错误信息
            layui.layer.msg('热点信息更新失败', { icon: 2 });
        }
    }

    /**
     * 处理热点添加完成事件
     * @param {Object} data 添加完成数据
     */
    function handleAddHotspotComplete(data) {
        if (data.success) {
            console.log('热点添加到视图完成:', data);
            // 热点添加成功，可以显示成功提示（可选）
            // layui.layer.msg('热点已添加到视图', {icon: 1, time: 1000});
        } else {
            // 热点添加失败，显示错误信息
            layui.layer.msg('热点添加到视图失败: ' + (data.error || '未知错误'), { icon: 2 });
        }
    }

    /**
     * 处理热点删除完成事件
     * @param {Object} data 删除完成数据
     */
    function handleRemoveHotspotComplete(data) {
        if (data.success) {
            console.log('热点从视图删除完成:', data);
            // 热点删除成功，可以显示成功提示（可选）
            // layui.layer.msg('热点已从视图删除', {icon: 1, time: 1000});
        } else {
            // 热点删除失败，显示错误信息
            layui.layer.msg('热点从视图删除失败: ' + (data.error || '未知错误'), { icon: 2 });
        }
    }

    /**
     * 处理右键菜单删除热点事件
     * @param {Object} data 右键删除数据
     */
    function handleHotspotRightClickDelete(data) {
        // 检查是否有选中的任务
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) {
            layui.layer.msg('请先选择一个任务', { icon: 2 });
            return;
        }

        // 检查热点数据是否有效
        if (!data || !data.hotspot) {
            layui.layer.msg('热点信息无效', { icon: 2 });
            return;
        }

        var hotspotInfo = data.hotspot;
        var nodeId = data.nodeId || PanoramaCore.getCurrentNodeId();

        // 检查是否有坐标信息
        if (!hotspotInfo.pan || !hotspotInfo.tilt) {
            layui.layer.msg('热点坐标信息缺失', { icon: 2 });
            return;
        }

        // 显示加载提示
        var loadingIndex = layer.msg('正在查找热点信息...', { icon: 16, shade: 0.01 });

        // 优先使用坐标查找（最可靠的方式）
        var $ = layui.$;
        $.ajax({
            url: '/panorama/hotspot/findByCoordinates',
            type: 'POST',
            data: {
                taskId: currentTaskId,
                nodeId: nodeId,
                pan: hotspotInfo.pan,
                tilt: hotspotInfo.tilt
            },
            success: function (res) {
                layui.layer.close(loadingIndex);

                if (res.success && res.data) {
                    // 找到对应的热点记录，调用删除函数
                    if (typeof PanoramaHotspot !== 'undefined' && PanoramaHotspot.deleteHotspot) {
                        PanoramaHotspot.deleteHotspot(res.data);
                    }
                } else {
                    layui.layer.msg(res.msg || '未找到对应的热点记录', { icon: 2 });
                }
            },
            error: function () {
                layui.layer.close(loadingIndex);
                layui.layer.msg('查找热点信息失败，请重试', { icon: 2 });
            }
        });
    }

    /**
     * 处理添加热点事件
     * @param {Object} data 添加热点数据
     */
    function handleAddHotspot(data) {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) {
            layui.layer.msg('请先选择任务', { icon: 2 });
            return;
        }

        var nodeId = data.nodeId;
        if (!nodeId) {
            layui.layer.msg('无法获取当前节点信息', { icon: 2 });
            return;
        }

        // 构造新热点的基础数据
        var newHotspotData = {
            pan: data.pan,
            tilt: data.tilt,
            nodeId: nodeId,
            isNew: true // 标记为新增模式
        };

        // 调用热点添加函数
        if (typeof PanoramaHotspot !== 'undefined' && PanoramaHotspot.addHotspot) {
            PanoramaHotspot.addHotspot(newHotspotData);
        } else {
            layui.layer.msg('热点编辑功能未加载', { icon: 2 });
        }
    }

    /**
     * 处理热点高亮开始事件
     * @param {Object} data 高亮开始数据
     */
    function handleHotspotHighlightStarted(data) {
        console.log('热点高亮已开始:', data);
        // 可以在这里添加UI反馈，比如显示高亮状态指示器
        // layui.layer.msg('热点高亮已开始，共高亮 ' + data.highlightedCount + ' 个热点', { icon: 1, time: 2000 });
    }

    /**
     * 处理热点高亮停止事件
     * @param {Object} data 高亮停止数据
     */
    function handleHotspotHighlightStopped(data) {
        console.log('热点高亮已停止:', data);
        // 可以在这里添加UI反馈，比如隐藏高亮状态指示器
        // layui.layer.msg('热点高亮已停止', { icon: 1, time: 1000 });
    }
    
    // ==================== 预览控制 ====================
    
    /**
     * 设置预览视角
     * @param {number} pan 水平角度
     * @param {number} tilt 垂直角度
     * @param {number} speed 过渡速度
     */
    function setPreviewView(pan, tilt, speed) {
        var message = {
            type: 'setView',
            pan: pan,
            tilt: tilt,
            speed: speed || 1.0
        };
        
        sendMessageToIframe(message);
    }
    
    /**
     * 定位到热点
     * @param {number} pan 水平角度
     * @param {number} tilt 垂直角度
     * @param {number} speed 过渡速度
     */
    function locateToHotspot(pan, tilt, speed) {
        var message = {
            type: 'locateHotspot',
            pan: pan,
            tilt: tilt,
            speed: speed || 1.5
        };
        
        sendMessageToIframe(message);
    }
    
    /**
     * 更新热点信息
     * @param {Object} hotspotInfo 热点信息
     */
    function updateHotspotInPreview(hotspotInfo) {
        var message = {
            type: 'updateHotspot',
            hotspot: hotspotInfo,
            nodeId: PanoramaCore.getCurrentNodeId()
        };
        
        sendMessageToIframe(message);
    }
    
    // ==================== 预览状态管理 ====================
    
    /**
     * 检查预览是否已加载
     * @returns {boolean} 是否已加载
     */
    function isPreviewLoaded() {
        var iframe = document.getElementById('panoramaFrame');
        return iframe && iframe.src && iframe.style.display !== 'none';
    }
    
    /**
     * 获取预览状态
     * @returns {Object} 预览状态信息
     */
    function getPreviewStatus() {
        var iframe = document.getElementById('panoramaFrame');
        
        return {
            loaded: isPreviewLoaded(),
            src: iframe ? iframe.src : null,
            visible: iframe ? iframe.style.display !== 'none' : false,
            currentNodeId: PanoramaCore.getCurrentNodeId()
        };
    }
    
    // ==================== 公共API ====================
    return {
        // 预览管理
        loadPreview: loadPreview,
        showPreview: showPreview,
        hidePreview: hidePreview,
        updatePreview: updatePreview,
        
        // iframe通信
        initMessageListener: initMessageListener,
        handleIframeMessage: handleIframeMessage,
        sendMessageToIframe: sendMessageToIframe,
        
        // 节点切换
        handleNodeSwitch: handleNodeSwitch,
        handleViewChange: handleViewChange,
        
        // 热点处理
        handleHotspotClick: handleHotspotClick,
        handleAddHotspot: handleAddHotspot,
        handleHotspotUpdateComplete: handleHotspotUpdateComplete,
        handleAddHotspotComplete: handleAddHotspotComplete,
        handleRemoveHotspotComplete: handleRemoveHotspotComplete,
        handleHotspotRightClickDelete: handleHotspotRightClickDelete,
        handleHotspotHighlightStarted: handleHotspotHighlightStarted,
        handleHotspotHighlightStopped: handleHotspotHighlightStopped,
        
        // 预览控制
        setPreviewView: setPreviewView,
        locateToHotspot: locateToHotspot,
        updateHotspotInPreview: updateHotspotInPreview,
        
        // 预览状态
        isPreviewLoaded: isPreviewLoaded,
        getPreviewStatus: getPreviewStatus
    };
})();

// 自动初始化消息监听
PanoramaPreview.initMessageListener();

// ==================== 向后兼容性支持 ====================
// 保持原有函数的全局访问方式

window.loadPreview = PanoramaPreview.loadPreview;
window.showPreview = PanoramaPreview.showPreview;
window.hidePreview = PanoramaPreview.hidePreview;
window.updatePreview = PanoramaPreview.updatePreview;
window.handleNodeSwitch = PanoramaPreview.handleNodeSwitch;
window.handleHotspotClick = PanoramaPreview.handleHotspotClick;
window.handleHotspotUpdateComplete = PanoramaPreview.handleHotspotUpdateComplete;
