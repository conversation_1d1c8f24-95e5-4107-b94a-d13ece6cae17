package com.cirpoint.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 日志查看器配置类
 * 使用@ConfigurationProperties自动绑定配置文件中的属性
 */
@Configuration
@ConfigurationProperties(prefix = "log.viewer")
public class LogViewerConfig {
    
    /**
     * 日志目录路径
     */
    private String logPath = "logs";
    
    /**
     * 当前日志文件名
     */
    private String currentLogFile = "application.log";
    
    /**
     * 归档日志路径
     */
    private String archivedPath = "logs/archived";
    
    /**
     * 最大SSE连接数
     */
    private int maxSseClients = 50;
    
    /**
     * 推送间隔(毫秒)
     */
    private int pushIntervalMs = 2000;
    
    /**
     * 默认分页大小
     */
    private int defaultPageSize = 200;
    
    /**
     * 最大分页大小
     */
    private int maxPageSize = 10000;
    
    /**
     * 读取缓冲区大小
     */
    private int readBufferSize = 8192;
    
    /**
     * SSE连接超时时间(毫秒)
     */
    private long sseTimeoutMs = 30 * 60 * 1000; // 30分钟
    
    /**
     * 是否启用日志查看器
     */
    private boolean enabled = true;
    
    /**
     * 最大日志行缓存数量
     */
    private int maxLogLineCache = 1000;
    
    /**
     * 搜索结果最大返回数量
     */
    private int maxSearchResults = 10000;
    
    /**
     * 获取当前日志文件的完整路径
     */
    public String getCurrentLogFilePath() {
        return logPath + "/" + currentLogFile;
    }
    
    /**
     * 获取归档日志目录的完整路径
     */
    public String getArchivedLogPath() {
        return archivedPath;
    }
    
    /**
     * 验证配置参数的有效性
     */
    public void validate() {
        if (maxSseClients <= 0) {
            throw new IllegalArgumentException("maxSseClients must be positive");
        }
        if (pushIntervalMs <= 0) {
            throw new IllegalArgumentException("pushIntervalMs must be positive");
        }
        if (defaultPageSize <= 0) {
            throw new IllegalArgumentException("defaultPageSize must be positive");
        }
        if (maxPageSize < defaultPageSize) {
            throw new IllegalArgumentException("maxPageSize must be >= defaultPageSize");
        }
        if (readBufferSize <= 0) {
            throw new IllegalArgumentException("readBufferSize must be positive");
        }
    }
    
    /**
     * 获取安全的分页大小（确保不超过最大值）
     */
    public int getSafePageSize(int requestedSize) {
        if (requestedSize <= 0) {
            return defaultPageSize;
        }
        return Math.min(requestedSize, maxPageSize);
    }
    
    // Getter和Setter方法
    public String getLogPath() {
        return logPath;
    }
    
    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }
    
    public String getCurrentLogFile() {
        return currentLogFile;
    }
    
    public void setCurrentLogFile(String currentLogFile) {
        this.currentLogFile = currentLogFile;
    }
    
    public String getArchivedPath() {
        return archivedPath;
    }
    
    public void setArchivedPath(String archivedPath) {
        this.archivedPath = archivedPath;
    }
    
    public int getMaxSseClients() {
        return maxSseClients;
    }
    
    public void setMaxSseClients(int maxSseClients) {
        this.maxSseClients = maxSseClients;
    }
    
    public int getPushIntervalMs() {
        return pushIntervalMs;
    }
    
    public void setPushIntervalMs(int pushIntervalMs) {
        this.pushIntervalMs = pushIntervalMs;
    }
    
    public int getDefaultPageSize() {
        return defaultPageSize;
    }
    
    public void setDefaultPageSize(int defaultPageSize) {
        this.defaultPageSize = defaultPageSize;
    }
    
    public int getMaxPageSize() {
        return maxPageSize;
    }
    
    public void setMaxPageSize(int maxPageSize) {
        this.maxPageSize = maxPageSize;
    }
    
    public int getReadBufferSize() {
        return readBufferSize;
    }
    
    public void setReadBufferSize(int readBufferSize) {
        this.readBufferSize = readBufferSize;
    }
    
    public long getSseTimeoutMs() {
        return sseTimeoutMs;
    }
    
    public void setSseTimeoutMs(long sseTimeoutMs) {
        this.sseTimeoutMs = sseTimeoutMs;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public int getMaxLogLineCache() {
        return maxLogLineCache;
    }
    
    public void setMaxLogLineCache(int maxLogLineCache) {
        this.maxLogLineCache = maxLogLineCache;
    }
    
    public int getMaxSearchResults() {
        return maxSearchResults;
    }
    
    public void setMaxSearchResults(int maxSearchResults) {
        this.maxSearchResults = maxSearchResults;
    }
    
    @Override
    public String toString() {
        return "LogViewerConfig{" +
                "logPath='" + logPath + '\'' +
                ", currentLogFile='" + currentLogFile + '\'' +
                ", archivedPath='" + archivedPath + '\'' +
                ", maxSseClients=" + maxSseClients +
                ", pushIntervalMs=" + pushIntervalMs +
                ", defaultPageSize=" + defaultPageSize +
                ", maxPageSize=" + maxPageSize +
                ", readBufferSize=" + readBufferSize +
                ", enabled=" + enabled +
                '}';
    }
} 