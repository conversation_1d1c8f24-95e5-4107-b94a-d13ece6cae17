<div class="apiDetail">
<div>
	<h2><span>String</span><span class="path">setting.data.key.</span>isParent</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.core</span> 核心 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>zTree 节点数据保存节点是否为父节点的属性名称。</p>
			<p>默认值："isParent"</p>
			<p class="highlight_red">v3.5.32+</p>
		</div>
	</div>
	<h3>setting 举例</h3>
	<h4>1. 设置 zTree 显示节点时，将 treeNode 的 parent 属性当做节点是否为父节点的属性名称</h4>
	<pre xmlns=""><code>var setting = {
	data: {
		key: {
			isParent: "parent"
		}
	}
};
......</code></pre>
</div>
</div>