﻿=ZTree v3.x (<PERSON><PERSON><PERSON><PERSON> Tree插件) 更新日志=

<font color="red">为了更好的优化及扩展zTree, 因此决定升级为v3.x，并且对之前的v2.x不兼容，会有很多结构上的修改，对此深感无奈与抱歉，请大家谅解。</font>
<font color="red">

具体修改内容可参考：

   * [http://www.treejs.cn/v3/api.php zTree v3.x API 文档]

   * [http://www.treejs.cn/v3/demo.php#_101 zTree v3.x Demo 演示]

   * [http://www.treejs.cn/v3/faq.php#_101 zTree v3.x 常见问题]

</font>

*2021.10.24* v3.5.48
* fixed issue: moveNode will fixed the level className about childNodes;
* fixed issue: showNodeFocus should operate the A dom;

*2020.11.21* v3.5.46
* Add 'onClick' handler only if it's not empty  Thanks @afilippov1985 

*2020.11.03* v3.5.45
* Support attribute('name' & 'title') dynamic rendering using custom function  Thanks @imyuyu 

*2020.04.29* v3.5.44
* update TypeScript type definition  Thanks @bseddon 
  
*2020.04.04* v3.5.43
  * fixed TypeScript type definition  Thanks @UtillYou 
  * Update demo 'super/keyboard_navigation.html' Thanks @bseddon
  * Update demo 'core/searchNodes.html' Thanks @bseddon
  * add setting.view.nodeClasses Thanks @bseddon

*2020.03.02* v3.5.42
  * add demo 'super/keyboard_navigation.html'  Thanks @bseddon

*2020.01.19* v3.5.42
   * merge PullRequest(fixed 'HTMLElement undefined' error)     Thanks @ChangJin0520
   * merge PullRequest(Add TypeScript type definition)     Thanks @Itroads
   * fixed awesomeStyle    Thanks @Jermyk93

*2020.01.06* v3.5.41
   * [修改] 修正 API

*2019.10.11* v3.5.41
   * [修改] 从 源码中 移除 个人邮箱

*2019.01.18* v3.5.40
   * [修改] fuzzySearch.js    Thanks @yigger

*2019.01.17* v3.5.39
   * [修改] data.nodeChecked & data.isHidden & data.nodeIsParent 的 返回值规范为 boolean   Thanks @xinhochen & @田biubiu & @FelixFly

*2019.01.08* v3.5.38
   * [修改] data.nodeChecked & data.isHidden 的 返回值规范为 boolean   Thanks @xinhochen

*2018.12.10* v3.5.37
   * [修改] fuzzySearch.js & Demo   Thanks @SadWood & @JerryWang24

*2018.08.21* v3.5.37
   * [修改] 替换 scrollIntoViewIfNeeded 方法    Thanks @jocki84
   * [修改] 部分 API 文字错误

*2018.06.26* v3.5.36
   * [增加] setting.async.headers & setting.async.xhrFields

*2018.04.24* v3.5.35
   * [增加] Demo：模糊搜索（Fuzzy Search）  Thanks @bigablecat

*2018.03.30* v3.5.35
   * [修改] 关闭拖拽方法内的 eventMouseDown.preventDefault(); 避免影响 zTree 外部的事件  Thanks @heyusysu

*2018.02.12* v3.5.34
   * [修改] nodeChecked 方法内的错误  Thanks @勇哥

*2018.01.30* v3.5.33
   * [修改] nodeIsParent 方法内的错误  Thanks @netmou

*2018.01.06* v3.5.32
   * [修改] setting.async.autoParam / otherParam 支持 function
   * [修改] isParent / isHidden 允许自定义属性名称 setting.data.key.isParent / isHidden

*2017.12.28* v3.5.31
   * [修改] 初始化节点数据的规则，支持 treeNode { name: "Node2", children: []}, 情况下自动设置为 isParent = true

*2017.11.11* v3.5.30
   * [修改] scrollIntoViewIfNeeded 方法对 IE7 的兼容

*2017.06.23* v3.5.29
   * [修改] 拖拽操作后 body.css("cursor", "auto"); 修改为 body.css("cursor", "");（感谢 Jim）

*2017.06.19* v3.5.29
   * [增加] reAsyncChildNodesPromise(parentNode, reloadType, isSilent) 方法支持 ES6 Promise
   * [修改] reAsyncChildNodes 方法增加 callback 参数

*2017.01.20* v3.5.28
   * [修改] scrollIntoViewIfNeeded 方法对 IE8 的兼容

*2016.12.27* v3.5.27
   * [修改] addNodes 方法设置 index 参数后，导致 treeNode.isLastNode 错误

*2016.11.03* v3.5.26
   * [修改] 使用 scrollIntoViewIfNeeded 替代 scrollIntoView，兼容各种浏览器

*2016.09.27* v3.5.25
   * [修改] ajax 异步加载支持 contentType = 'application/json'

*2016.06.06* v3.5.24
   * [修改] selectNode 方法中 某些情况下，isSilent 参数无效
   * [修改] 数据中 id = 'length'时， 导致 transformTozTreeFormat() 方法异常

*2016.04.08* v3.5.23
   * [修改] expandCollapseNode 方法被 gulp 压缩后，在 IE8 上会导致溢出

*2016.04.06* v3.5.23
   * [修改] 替换 arguments.callee 避免 'use strict' 严格模式下报错

*2016.04.01* v3.5.23
   * [修改] selectNode 方法 增加 isSilent 参数，可以禁止 选中节点时，自动滚动到视图
   
*2016.03.01* v3.5.22
   * [修改] metro Demo 的样式错误
   * [修改] 增加 <a> 的 padding 后， 导致 setting.edit.drag.inner 无效

*2016.02.17* v3.5.21
   * [修改] zTree js 文件名 （为了便于发布到 https://cdnjs.com/）

*2016.01.20* v3.5.20
   * [修改] checkAllNodes() 方法不处理 (chkDisabled = true) 的父节点的子节点的 bug

*2015.12.04* v3.5.19.3
   * [修改] 为避免定位节点时抢焦点， 使用 scrollIntoView 方法替换之前的 focus 方法（对于IE6等旧浏览器仍然使用 focus方法）

*2015.11.15* v3.5.19.2
   * [增加] 给节点名称的 span 标签增加 class，便于用户设置 css

*2015.10.26* v3.5.19.1
   * [修改] addNodes 新方法 导致 拖拽节点时报错
   * [增加] treeNode.getIndex 方法，可以快速获取 节点在 子节点中的位置
   * [增加] treeNode.getPath 方法，可以快速获取 节点的所有父节点

*2015.10.22* v3.5.19
   * [修改] addNodes 方法支持直接添加新节点到任意位置 addNodes(parentNode, index, newNodes, isSilent)
   * [修改] selectstart 事件未解绑导致的内存泄漏

*2015.08.26* v3.5.18
   * [修改] onSelected/onUnSelected 回调参数，由 (event, treeId, node) 修改为 （treeId, node）；另外 删除 已选择的节点时，也会触发 onUnSelected 回调
   * [增加] Allow nodes to specify their own icon using an 'icon' property of the 'setting.data.key'
   * [增加] metro 风格 demo
   * [增加] awesome 风格 demo
   * [增加] 回调 onSelected / onUnSelected

*2015.02.15* v3.5.17
   * [修改] excheck 扩展中 removeClass 与 jQueryUI 1.9 冲突的问题，目前放弃 removeClass 方法
   * [修改] 优化 exhide 扩展包初始化效率，避免数据多时 ie8 假死的 bug（感谢：https://github.com/sarxos）
   * [修改] 若干 Demo & API 的小错误
   * [修改] 异步加载 低版本 IE 缓存严重的问题
   * [修改] 在 onRename 回调中使用 updateNode 方法无效的问题

*2014.03.09* v3.5.16
   * [增加] onDragMove 回调，便于控制 zTree 节点与其他 DOM 的拖拽操作。（感谢 yumi301）
   * [增加] 针对 Mac 系统 Cmd 键的支持， Cmd 键 + 左键 也可以多选节点

   * [修改] 使用 destory 方法销毁树以后，依然可以从 getZTreeObj 方法中获取到 zTree 对象的 bug。
   * [修改] onCheck 回调的 event，恢复为 zTree 自身的 event 事件，同时利用 srcEvent 传递原始 event 对象。（感谢 yumi301）
   * [修改] 拖拽多个节点时，超出 maxShowNodeNum 设置个数的节点会失去 被选择状态的 bug
   * [修改] excheck & exedit 扩展包的 zTree 方法中 setting 数据错误导致 checkNode、updateNode 等方法操作 radio 失效的 bug
   * [修改] 不加载 exedit 扩展包时，使用 removeNode 方法无法触发 beforeRemove 和 onRemove 回调的 bug

*2013.10.19* v3.5.15
   * [增加] setting.view.txtSelectedEnable 属性，满足部分项目中客户对于可以选择节点文本信息的强烈欲望。

   * [修改]  exhide 扩展包导致操作子节点后 isLastNode 属性异常 的bug
   * [修改]  使用 cancelEditName 方法时，beforeRename & onRename 的 isCancel 始终为 false 的bug
   * [修改]  编辑状态， beforeRename 回调 return false 时，提示信息导致 input 失去焦点后， 当树再次得到焦点时，让 input 自动获取焦点
   * [修改]  判断拖拽到节点的 <a> 标签中自定义的无 id 组件判断错误的bug
   * [修改]  async_edit.html demo 中 添加按钮 显示的 bug
   * [修改]  当没有开启异步加载模式下，对于没有子节点的父节点，即使设置 open=true 在初始化时也不会设置为展开状态的bug （对于异步加载模式下依然会强行设置为折叠状态）

*2013.06.28* v3.5.14
   * [修改]  拖拽节点时 iframe 遮罩异常的bug
   * [修改]  清空子节点后重新添加子节点无法显示的bug

*2013.06.02* v3.5.13

   * [增加] beforeRename & onRename 增加 isCancel 参数，可以监控用户 ESC 取消编辑的事件

   * [修改] 初始化时 radioType="all", 父节点未展开 且 子节点有被勾选，点击其他 radio 时，不会取消勾选该子节点的bug
   * [修改] 多棵树拖拽时，拖拽无效后会导致目标书已选择的节点清空的 bug。
   * [修改] 多棵树拖拽时，会触发 addHoverDom 的bug。
   * [修改] 多棵树拖拽时，由于 beforeDrog 或 prev / inner / next 返回 false 后未触发原始节点的 addHoverDom 的bug
   * [修改] 异步加载时，对于未加载子节点的父节点使用 expandNode 方法时， sonSign 设置为 true后，导致异步加载的节点无法正常显示的bug
   * [修改] 一次性加载全部数据，如果父节点 A 未展开，但下一级的父节点 A1 设置了 open=true 的时候，使用 expandAll 方法导致 A1 的下一级父节点出现重复的 bug
   * [修改] 增加对 iframe 的支持，可以只在主页面加载 zTree 的 js，在 iframe 内创建树 [https://github.com/zTree/zTree_v3/issues/7 Issue Info]
   * [修改] 引入 exhide 扩展包 导致页面上同时加载多棵树时，根节点 的 连接线图标出现异常 的 bug [http://tieba.baidu.com/p/2277416574]
   * [修改] excheck & exedit 扩展包中事件代理获取节点 tId 的方法，保证适当修改 DOM 结构也能得到 tId

*2013.03.11* v3.5.12
   * [修改] 由于 jquery 1.9 中移除 event.srcElement 导致的 js 报错的bug。
   * [修改] 在异步加载模式下，使用 moveNode 方法，且 moveType != "inner" 时，也会导致 targetNode 自动加载子节点的 bug
   * [修改] 对已经显示的节点(nocheck=true)使用 showNodes 或 showNode 方法后，导致勾选框出现的bug。
   * [修改] 对已经隐藏的节点(nocheck=false)使用 hideNodes 或 hideNode 方法后，导致勾选框消失的bug。
   * [修改] getNodesByParamFuzzy 支持 大小写模糊。
   * [修改] className 结构，提取 _consts.className.BUTTON / LEVEL / ICO_LOADING / SWITCH，便于快速修改 css 冲突。
     例如：与 WordPress 产生冲突后，直接修改 core 中的 "button" 和 "level" 即可。  Issue: https://github.com/zTree/zTree_v3/issues/2

*2013.01.28* v3.5.02
   * [增加] setting.check.chkDisabledInherit 属性，用于设置 chkDisabled 在初始化时子节点是否可以继承父节点的 chkDisabled 属性
   * [删除] 内部 noSel 方法，使用 selectstart事件 和 "-moz-user-select"样式 处理禁止 节点文字被选择的功能
   * [修改] 不兼容 jQuery 1.9 的bug
   * [修改] onDrop 的触发规则，保证异步加载模式下，可以在延迟加载结束后触发，避免 onDrop 中被拖拽的节点是已经更新后的数据。
   * [修改] setChkDisabled 方法，增加 inheritParent, inheritChildren 参数设置是否让父子节点继承 disabled
   * [修改] 异步加载时 拼接参数的方法，由 string 修改为 json 对象
   * [修正] 1-2-3 3级节点时，如果 2级节点 全部设置为 nocheck 或 chkDisabled后，勾选3级节点时，1级节点的半勾选状态错误的 bug
   * [修改] Demo: checkbox_nocheck.html & checkbox_chkDisabled.html;
   * [修改] Demo: edit_super.html，增加 showRenameBtn & showRemoveBtn 的演示
   * [修改] Demo: asyncForAll, 将 post 修改为 get；为了避免由于 IE10 的 bug 造成的客户端 以及 服务端崩溃
      IE10 ajax Post 无法提交参数的bug (http://bugs.jquery.com/ticket/12790)

*2012.12.21* v3.5.01
   * [优化] clone 方法
   * [修正] 对于初始化无 children 属性的父节点进行 reAsyncChildNodes 操作时出错的 bug
   * [修正] beforeRename 回调中使用 cancelEditName 方法后，再 return false 导致无法重新进行编辑的 bug
   * [修正] exedit 扩展包让 setting.data.key.url 失效的 bug
   * [修正] setting.check.autoCheckTrigger 设置为 true 时，onCheck 回调缺少 event 参数的 bug
   * [修正] singlepath.html Demo 中的 bug

*2012.11.20* v3.5
   * [优化] 原先的 clone 方法 （特别感谢：愚人码头）
   * [修改] 隐藏父节点后，使用 expandAll 方法导致 父节点展开的 bug
   * [修改] 使用 jQuery v1.7 以上时，设置 zTree 容器 ul 隐藏（visibility: hidden;）后， 调用 selectNode 导致 IE 浏览器报错 Can't move focus 的 bug
   * [修改] 正在异步加载时，执行 destory 或 init 方法后，异步加载的节点影响新树的 bug
   * [修改] 方法 reAsyncChildNodes 在 refresh 的时候未清空内部 cache 导致内存泄露 的 bug
   * [修改] 批量节点拖拽到其他父节点内（inner）时，导致顺序反转 的 bug
   * [修改] 对于 使用 html格式的 节点无法触发 双击事件 的 bug
   * [修改] onCheck 回调中的 event ，保证与触发事件中的 event 一致
   * [修改] 异步加载时，在 onNodeCreated 中执行 selectNode 后，导致节点折叠的 bug
   * [修改] API 中 dataFilter 的参数名称 childNodes ->  responseData
   * [修改] API 中 iconSkin 的 举例内容
   * [修改] API 中 chkDisabled 的说明
   * [修改] Demo 中 index.html 内的 loadReady 重复绑定问题

*2012.09.03* v3.4
   * [增加]  Demo —— OutLook 样式的左侧菜单
   * [增加] 清空 zTree 的方法 $.fn.zTree.destory(treeId) & zTree.destory()

   * [修改] core核心文件内 _eventProxy 方法中获取 tId 的方法，提高 DOM 的灵活性
   * [修改] 初始化时 多层父节点的 checkbox 半选状态计算错误的 bug
   * [修改] 同时选中父、子节点后，利用 getSelectedNodes 获取选中节点并利用 removeNode 删除时报错的 bug
   * [修改] treeNode.chkDisabled / nocheck 属性，支持字符串格式的 "false"/"true"
   * [修改] 异步加载模式下无法利用 server 返回 xml 并且 在 dataFilter 中继续处理的 bug
   * [修改] title 只允许设置为 string 类型值的问题。 修正后允许设置为 number 类型的值
   * [修改] zId 计数规则 & Cache 保存，减少 IE9 的 bug 造成的内存泄漏
   * [修改] API 页面搜索功能导致 IE 崩溃的 bug

*2012.07.16* v3.3
   * [增加] 扩展库 exhide -- 节点隐藏功能

   * [修改] getNodesByFilter 方法，添加 invokeParam 自定义参数
   * [修改] 拖拽中测试代码未删除，导致出现黄颜色的 iframe 遮罩层的 bug
   * [修改] 延迟加载方法 对于使用 expandAll 进行全部展开时，导致 onNodeCreated 回调 和 addDiyDom 方法触发过早的 bug
   * [修改] 使用 moveNode 移动尚未生成 DOM 的节点时，视图会出现异常的 bug
   * [修改] 删除节点后，相关节点的 isFirstNode 属性未重置的 bug
   * [修改] getPreNode(),getNextNode() 方法在对于特殊情况时计算错误的 bug
   * [修改] 设置 title 之后，如果重新将 title 内容设置为空后，会导致无法更新 title 的 bug
   * [修改] 针对 setting.check.chkStyle=="radio" && setting.check.radioType=="all" 的情况时，getTreeCheckedNodes方法优化，找到一个结果就 break
   * [修改] zTreeObj.getCheckedNodes(false) 在 radioType = "all" 时计算错误的 bug
   * [修改] 完善 API 中 beforeDrop / onDrop 的关于 treeId 的说明

*2012.05.13* v3.2
   * [增加] setting.data.key.url 允许修改 treeNode.url 属性
   * [增加] getNodesByFilter(filter, isSingle) 方法
   * [增加] "与其他 DOM 拖拽互动" 的 Demo (http://www.treejs.cn/v3/demo.php#_511)
   * [增加] "异步加载模式下全部展开" 的 Demo (http://www.treejs.cn/v3/demo.php#_512)

   * [修改] 代码结构，将 addNodes、removeNode、removeChildNodes 方法 和 beforeRemove、onRemove 回调 转移到 core 内
   * [修改] IE7的环境下无子节点的父节点反复展开出现多余空行的 bug
   * [修改] 异步加载时，如果出现网络异常等，会导致 图标显示错误的 bug
   * [修改] dataFilter中 return null 导致异常 的 bug
   * [修改] removeChildNodes 方法清空子节点后，无法正常添加节点的 bug
   * [修改] moveNode 后节点中的自定义元素的事件丢失的 bug
   * [修改] moveNode 方法中设置 isSilent = true 时，如果移动到已展开的 父节点后，出现异常的 bug
   * [修改] onClick/onDrag/onDrop 回调中 event 不是原始 event 的 bug
   * [修改] onDrop 回调中 当拖拽无效时，无法获得 treeNodes 的 bug
   * [修改] onDrop 无法判断拖拽是 移动还是复制的问题
   * [修改] 未开启异步加载模式时，拖拽节点到子节点为空的父节点内时 出现异常 的 bug
   * [修改] 拖拽过程中，反复在 父节点图标上划动时，会出现停顿的 bug
            (需要css 结构—— button -> span.button)

   * [修改] 拖拽操作时箭头 与 targetNode 背景之间的细节现实问题，便于用户拖拽时更容易区分 prev、next 和 inner 操作
   * [修改] 拖拽操作时IE6/7 下 在 节点<a> 右侧 10px 内会导致 targetNode = root 的 bug
   * [修改] 编辑模式下 默认的编辑按钮、删除按钮点击后，如果相应的 before 回调 return false 时会触发 onClick 回调的 bug

*2012.02.14* v3.1
   * [增加] ajax 的参数 setting.async.contentType ，让提交参数适用于 json 数据提交 （主要适用于 .Net 的开发）。
   * [增加] setting.edit.editNameSelectAll, 用于设定编辑节点名称时初次显示 input 后 text 内容为全选
   * [修改] 异步加载 规则，不再仅仅依靠父节点的子节点数来判定，增加内部属性 zAsync，保证默认状态下父节点及时无子节点也只能异步加载一次，除非使用 reAsyncChildNodes 方法强行控制异步加载。
   * [修改] 放大浏览器后导致 界面出现多余连接线的bug （需要更新：icon 图标和 css ）
   * [修改] 在编辑状态，如果节点名超过编辑框宽度，左右键在框内不起作用的bug（IE 6 7 8 出现）
      CSS 中 filter:alpha(opacity=80) 造成的，应该是 ie 的 bug，需要更新 css 文件
   * [修改] title 设置后，如果属性不存在，则默认为 title 为空，便于数据容错和用户灵活使用
   * [修改] editName 方法如果针对尚未展开的 父节点，会导致该父节点自动展开的 bug
   * [修改] title 中存在标签时导致 title 显示异常的bug（例如：蓝色字22%"'`<input/>`）

*2012.01.10* v3.0
   * [增加] setting.check.autoCheckTrigger 默认值 false，可以设置联动选中时是否触发事件回调函数
   * [增加] setting.callback.beforeEditName 回调函数，以保证用户可以捕获点击编辑按钮的事件
   * [增加] treeNode.chkDisabled 属性，显示 checkbox 但是用户无法修改 checkbox 状态，并且该 checkbox 会影响父节点的 checkbox 的半选状态
   * [增加] setting.check.nocheckInherit 属性，用户设置子节点继承 nocheck 属性，用于批量初始化节点，不适用于已经显示的节点
   * [增加] setting.edit.drag.autoExpandTrigger 默认值 false，可以设置自动展开、折叠操作时是否触发事件回调函数
   * [增加] setting.view.nameIsHTML 默认值 false，允许用户对 name 设置 DOM 对象
   * [增加] treeNode.click 属性的说明文档
   * [增加] treeObj.setChkDisabled 方法用于设置 checkbox / radio disabled 状态
   * [增加] treeNode.halfCheck 属性，用于强制设定节点的半选状态

   * [修改] 异步加载 & 编辑功能 共存时，拖拽节点 或 增加节点 导致 ie 上报错的 bug （apply 方法引起）
   * [修改] zTreeStyle 样式冲突
   * [修改] setting.data.key.title 默认值设置为 ""，初始化时自动赋值为 setting.data.key.name 这样可避免希望 title 与 name 一致的用户反复设置参数
   * [修改] 点击叶子节点的连接线会触发 expand 事件的 bug
   * [修改] IE 下 点击叶子节点连线会出现虚线框的 bug
   * [修改] updateNode 导致 checkbox 半选状态错误的 bug
   * [修改] checkNode 方法实现 toggle 操作, 取消 expandAll 方法的 toggle 操作
   * [修改] zTree 内鼠标移动会抢页面上 input 内的焦点的 bug
   * [修改] beforeRename / onRename 的触发方式——即使名称内容未改变也会触发，便于用户配合 beforeEditName 捕获编辑状态的结束，赋予用户更多调整规则的权利
   * [修改] 与 easyUI 共存时无法拖拽的bug
   * [修改] beforeRename 在 Firefox 下如果利用 alert，会触发两次的 bug
   * [修改] checkNode/expandNode/removeNode 方法，默认不触发回调函数，恢复 v2.6 的默认状态，同时增加 callbackFlag 参数，设置为 true 时，可以触发回调函数
   * [修改] IE9下“根据参数查找节点”的Demo 报错：行14 重新声明常量属性(Demo 自身的问题，定义了history变量)
   * [修改] 初始化 zTree 时 onNodeCreated 事件回调函数中无法 用 getZTreeObj 获取 zTree 对象的 bug
   * [修改] setting.edit.drag.prev / next / inner 参数，增加被拖拽的节点集合
   * [修改] 异步加载模式下，otherParam 使用Array数组会出错的 bug。例如： ["id", "1", "name", "test"]
   * [修改] FireFox 下多棵树拖拽异常的 bug
   * [修改] exedit 中调用 excheck库的方法时没有进行容错处理，导致如果只加入 exedit 而没有 excheck的时候，会出现 js 错误
   * [修改] 显示 checkbox 的 zTree 在编辑模式下，移动节点不会更新父节点半选状态的 bug
   * [修改] treeNode.childs --> children; treeObject.removeChilds --> removeChildNodes; setting.data.key.childs --> children（英文不好惹的祸！抱歉了！）
   * [修改] onRemove 回调中得到的 treeNode 还可以查找 preNode、nextNode 的bug。 修正后，getPreNode 和 getNextNode 都返回 null； 为了便于查找父节点，getParentNode 仍保留
   * [修改] 简单数据模式下，如果 id 与 pId 的值相同会导致该节点无法正常加载的 bug
   * [修改] 移动或删除中间节点会导致最后一个节点连接线图标变小的 bug

*2011.09.05* v3.0 beta
   * [修改] zTree 的 js 代码架构全面修改，并且拆分
   * [修改] zTree 的 css 样式全面修改，对浏览器可以更好地兼容，同时解决了以前1个像素差的问题
   * [优化] 采用延迟加载技术，一次性加载大数据量的节点性能飞速提升
   * [增加] 支持多节点同时选中、拖拽
   * [增加] checkNode、checkAllNodes 等多种方法
   * [增加] IE6 自动取消动画展开、折叠的功能
   * [修正] 异步加载 & 编辑模式 能够更完美的共存
   * [修正] setting 配置更加合理，并且增加了若干项配置参数
   * [修正] treeNode 节点数据的属性更加合理，并且增加了一些方法
   * [修正] 拖拽操作更加灵活方便，更容易制定自己的规则
   * [修正] 其他若干修改，详细对比请参考 url：[http://www.treejs.cn/v3/faq.php#_101 zTree v3.0 常见问题]
