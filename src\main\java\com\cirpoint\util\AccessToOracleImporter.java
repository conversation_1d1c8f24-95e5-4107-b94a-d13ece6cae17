package com.cirpoint.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Access数据库导入Oracle工具类
 */
@Setter
@Slf4j
@Component
public class AccessToOracleImporter {

	/**
	 * -- SETTER --
	 *  设置日志写入器
	 *
	 * @param logWriter 日志写入器
	 */
	// 日志写入器
	private FileWriter logWriter;

	/**
	 * -- SETTER --
	 *  设置是否收集日志
	 *
	 * @param collectLogs 是否收集日志
	 */
	// 是否收集日志
	private boolean collectLogs;

	/**
	 * -- SETTER --
	 *  设置日志列表
	 *
	 * @param logList 日志列表
	 */
	// 日志列表，用于收集执行日志
	private List<String> logList;

	/**
	 * 写入日志到文件
	 * 
	 * @param message 日志消息
	 */
	private void writeLog(String message) {
		try {
			if (logWriter != null) {
				logWriter.write(message + "\n");
				logWriter.flush();
			}
			
			// 如果需要收集日志，添加到日志列表
			if (collectLogs && logList != null) {
				logList.add(message);
			}
		} catch (IOException e) {
			log.error("写入日志失败: {}", message, e);
		}
	}

	/**
	 * 导入Access数据到Oracle
	 *
	 * @param mdbFilePath Access数据库文件路径
	 * @param mappingJsonPath 字段映射配置文件路径
	 * @throws Exception 导入过程中的异常
	 */
	public void importData(String mdbFilePath, String mappingJsonPath) throws Exception {
		// 验证文件存在
		if (!FileUtil.exist(mdbFilePath)) {
			String errorMsg = "Access数据库文件不存在: " + mdbFilePath;
			writeLog(errorMsg);
			throw new IllegalArgumentException(errorMsg);
		}
		
		// 读取映射配置
		String mappingJson;
		try {
			// 尝试先通过URL解码获取正确路径
			String decodedPath = java.net.URLDecoder.decode(mappingJsonPath, "UTF-8");
			if (FileUtil.exist(decodedPath)) {
				mappingJson = FileUtil.readUtf8String(decodedPath);
			} else if (FileUtil.exist(mappingJsonPath)) {
				mappingJson = FileUtil.readUtf8String(mappingJsonPath);
			} else {
				// 尝试从资源路径读取
				try (java.io.InputStream is = getClass().getClassLoader().getResourceAsStream(mappingJsonPath.replace("\\", "/"))) {
					if (is == null) {
						try (java.io.InputStream is2 = getClass().getClassLoader().getResourceAsStream("config/table_mapping.json")) {
							if (is2 == null) {
								throw new IllegalArgumentException("无法从资源路径加载映射配置文件");
							}
							mappingJson = inputStreamToString(is2);
						}
					} else {
						mappingJson = inputStreamToString(is);
					}
				}
			}
		} catch (Exception e) {
			String errorMsg = "读取映射配置文件失败: " + e.getMessage();
			writeLog(errorMsg);
			throw new IllegalArgumentException(errorMsg, e);
		}
		
		JSONObject mapping = JSONUtil.parseObj(mappingJson);
		JSONObject tableMapping = mapping.getJSONObject("tableMapping");
		
		writeLog("读取映射配置完成，共配置 " + tableMapping.size() + " 个表");

		// 连接Access数据库
		String accessUrl = "jdbc:ucanaccess://" + mdbFilePath;
		try (Connection accessConn = DriverManager.getConnection(accessUrl)) {
			writeLog("已成功连接到Access数据库: " + mdbFilePath);
			
			// 遍历配置的表
			for (String accessTableName : tableMapping.keySet()) {
				JSONObject tableMappingConfig = tableMapping.getJSONObject(accessTableName);
				String oracleTableName = tableMappingConfig.getStr("tableName");
				JSONArray fieldsArray = tableMappingConfig.getJSONArray("fields");

				writeLog("\n开始处理表: " + accessTableName + " -> " + oracleTableName);
				importTable(accessConn, accessTableName, oracleTableName, fieldsArray);
			}
			
			writeLog("\n全部表导入完成");
		} catch (Exception e) {
			writeLog("数据导入过程发生错误: " + e.getMessage());
			throw e;
		}
	}

	private void importTable(Connection accessConn, String accessTableName, 
						   String oracleTableName, JSONArray fieldsArray) throws SQLException {
		log.info("开始导入表: {} -> {}", accessTableName, oracleTableName);
		writeLog("开始导入表: " + accessTableName + " -> " + oracleTableName);

		// 日期格式化
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
		
		// 构建查询语句
		StringBuilder selectSql = new StringBuilder("SELECT ");
		List<String> accessFields = new ArrayList<>();
		List<String> oracleFields = new ArrayList<>();
		// 记录日期类型字段
		List<String> dateFields = new ArrayList<>();

		// 构建字段映射
		for (int i = 0; i < fieldsArray.size(); i++) {
			JSONObject fieldMapping = fieldsArray.getJSONObject(i);
			String accessField = fieldMapping.getStr("accessColName");
			String oracleField = fieldMapping.getStr("fieldName");
			
			// 检查是否为日期时间字段
			if (accessField.contains("日期")) {
				dateFields.add(accessField);
			}

			accessFields.add(accessField);
			oracleFields.add(oracleField);
			// 为包含特殊字符的字段名添加方括号
			selectSql.append("[").append(accessField).append("], ");
		}

		// 移除最后的逗号和空格
		selectSql.setLength(selectSql.length() - 2);
		selectSql.append(" FROM ").append(accessTableName);
		
		writeLog("查询SQL: " + selectSql.toString());
		writeLog("字段映射: " + String.join(", ", accessFields) + " -> " + String.join(", ", oracleFields));

		// 获取第一个字段作为主键
		String primaryKeyAccessField = accessFields.get(0);
		String primaryKeyOracleField = oracleFields.get(0);
		writeLog("使用主键字段: " + primaryKeyAccessField + " -> " + primaryKeyOracleField);

		// 查询已存在的记录主键值
		Map<String, Boolean> existingKeys = new HashMap<>();
		try {
			writeLog("查询Oracle表已存在记录...");
			JSONArray existingRecords = Util.postQuerySql("SELECT " + primaryKeyOracleField + " FROM " + oracleTableName);
			for (int i = 0; i < existingRecords.size(); i++) {
				JSONObject record = existingRecords.getJSONObject(i);
				existingKeys.put(record.getStr(primaryKeyOracleField), true);
			}
			writeLog("Oracle表 " + oracleTableName + " 中已存在 " + existingKeys.size() + " 条记录");
			log.info("Oracle表 {} 中已存在 {} 条记录", oracleTableName, existingKeys.size());
		} catch (Exception e) {
			writeLog("查询Oracle表 " + oracleTableName + " 已存在记录失败: " + e.getMessage());
			log.warn("查询Oracle表 {} 已存在记录失败: {}", oracleTableName, e.getMessage());
		}
		
		// 读取Access数据并构建Oracle插入或更新语句
		try (Statement stmt = accessConn.createStatement();
			 ResultSet rs = stmt.executeQuery(selectSql.toString())) {
			int insertCount = 0;
			int updateCount = 0;
			int failCount = 0;
			
			writeLog("开始读取Access数据并处理...");

			while (rs.next()) {
				String primaryKeyValue = rs.getString(primaryKeyAccessField);
				boolean recordExists = existingKeys.containsKey(primaryKeyValue);
				
				// 准备SQL语句（插入或更新）
				StringBuilder sql = new StringBuilder();
				
				if (!recordExists) {
					// 构建插入语句
					sql.append("INSERT INTO ")
					   .append(oracleTableName)
					   .append(" (")
					   .append(String.join(", ", oracleFields))
					   .append(") VALUES (");
					
					for (int i = 0; i < accessFields.size(); i++) {
						String accessField = accessFields.get(i);
						Object value = rs.getObject(accessField);
						
						if (value == null) {
							sql.append("NULL, ");
						} else if (dateFields.contains(accessField)) {
							// 对日期字段特殊处理
							if (value instanceof java.sql.Time) {
								// 只有时间，转换为字符串格式
								sql.append("'").append(timeFormat.format(value)).append("', ");
							} else if (value instanceof java.sql.Timestamp) {
								// 完整日期时间
								sql.append("'").append(dateTimeFormat.format(value)).append("', ");
							} else if (value instanceof java.sql.Date) {
								// 仅日期
								sql.append("'").append(dateFormat.format(value)).append("', ");
							} else {
								// 其他格式直接转字符串
								sql.append("'").append(value.toString().replace("'", "''")).append("', ");
							}
						} else {
							sql.append("'").append(value.toString().replace("'", "''")).append("', ");
						}
					}
					
					sql.setLength(sql.length() - 2);
					sql.append(")");
				} else {
					// 构建更新语句
					sql.append("UPDATE ")
					   .append(oracleTableName)
					   .append(" SET ");
					
					// 跳过主键字段（第一个字段）
					for (int i = 1; i < accessFields.size(); i++) {
						String accessField = accessFields.get(i);
						String oracleField = oracleFields.get(i);
						Object value = rs.getObject(accessField);
						
						sql.append(oracleField).append(" = ");
						
						if (value == null) {
							sql.append("NULL, ");
						} else if (dateFields.contains(accessField)) {
							// 对日期字段特殊处理
							if (value instanceof java.sql.Time) {
								sql.append("'").append(timeFormat.format(value)).append("', ");
							} else if (value instanceof java.sql.Timestamp) {
								// 完整日期时间
								sql.append("'").append(dateTimeFormat.format(value)).append("', ");
							} else if (value instanceof java.sql.Date) {
								// 仅日期
								sql.append("'").append(dateFormat.format(value)).append("', ");
							} else {
								sql.append("'").append(value.toString().replace("'", "''")).append("', ");
							}
						} else {
							sql.append("'").append(value.toString().replace("'", "''")).append("', ");
						}
					}
					
					sql.setLength(sql.length() - 2);
					sql.append(" WHERE ")
					   .append(primaryKeyOracleField)
					   .append(" = '")
					   .append(primaryKeyValue.replace("'", "''"))
					   .append("'");
				}
				
				try {
					int result = Util.postCommandSql(sql.toString());
					if (result <= 0) {
						String errorMsg = (recordExists ? "更新" : "插入") + "记录失败, " + primaryKeyAccessField + "=" + primaryKeyValue;
						writeLog(errorMsg);
						log.error(errorMsg);
						failCount++;
					} else {
						if (recordExists) {
							updateCount++;
							if (updateCount % 100 == 0) {
								String progressMsg = "已成功更新 " + updateCount + " 条记录";
								writeLog(progressMsg);
								log.info(progressMsg);
							}
						} else {
							insertCount++;
							if (insertCount % 100 == 0) {
								String progressMsg = "已成功插入 " + insertCount + " 条记录";
								writeLog(progressMsg);
								log.info(progressMsg);
							}
						}
					}
				} catch (Exception e) {
					String errorMsg = "执行Oracle" + (recordExists ? "更新" : "插入") + "语句时发生错误, " + 
							primaryKeyAccessField + "=" + primaryKeyValue + ", 错误: " + e.getMessage();
					writeLog(errorMsg);
					log.error(errorMsg);
					failCount++;
				}
			}

			String resultMsg = "表 " + accessTableName + " 导入完成: 插入 " + insertCount + " 条, 更新 " + updateCount + " 条, 失败 " + failCount + " 条";
			writeLog(resultMsg);
			log.info(resultMsg);
		}
	}

	private String inputStreamToString(java.io.InputStream is) throws IOException {
		ByteArrayOutputStream result = new ByteArrayOutputStream();
		byte[] buffer = new byte[1024];
		int length;
		while ((length = is.read(buffer)) != -1) {
			result.write(buffer, 0, length);
		}
		return result.toString(java.nio.charset.StandardCharsets.UTF_8.name());
	}
}
