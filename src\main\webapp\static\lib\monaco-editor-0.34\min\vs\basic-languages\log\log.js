"use strict";/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.34.1(547870b6881302c5b4ff32173c16d06009e3588f)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/
define("vs/basic-languages/log/log", ["require","exports"],(require, exports)=>{
    // Export the language definition
    exports.conf = {
        comments: { lineComment: '#' },
        brackets: [
            ['{', '}'],
            ['[', ']'],
            ['(', ')']
        ],
        autoClosingPairs: [
            { open: '{', close: '}' },
            { open: '[', close: ']' },
            { open: '(', close: ')' },
            { open: '"', close: '"' },
            { open: '\'', close: '\'' },
            { open: '`', close: '`' }
        ],
        surroundingPairs: [
            { open: '{', close: '}' },
            { open: '[', close: ']' },
            { open: '(', close: ')' },
            { open: '"', close: '"' },
            { open: '\'', close: '\'' },
            { open: '`', close: '`' }
        ]
    };

    // Export the language definition
    exports.language = {
        defaultToken: '',
        ignoreCase: true,
        tokenPostfix: '.log',
        brackets: [
            { token: 'delimiter.bracket', open: '{', close: '}' },
            { token: 'delimiter.parenthesis', open: '(', close: ')' },
            { token: 'delimiter.square', open: '[', close: ']' }
        ],
        keywords: [
            'ERROR', 'FATAL', 'SEVERE', 'CRITICAL', 'ALERT', 'EMERGENCY', 'FAILURE', 'FAIL',
            'WARNING', 'WARN',
            'INFO', 'INFORMATION', 'NOTICE', 'CONFIG',
            'DEBUG', 'TRACE', 'FINE', 'FINER', 'FINEST',
            'UNKNOWN'
        ],
        builtins: ['true', 'false', 'null', 'undefined'],
        symbols: /[=><!~?&|+\-*\/\^;\.,]+/,
        tokenizer: {
            root: [
                // Log levels - errors
                [/\b(ERROR|FATAL|SEVERE|CRITICAL|ALERT|EMERGENCY|FAILURE|FAIL)\b/, 'keyword.error'],
                // Log levels - warnings
                [/\b(WARNING|WARN)\b/, 'keyword.warning'],
                // Log levels - info
                [/\b(INFO|INFORMATION|NOTICE|CONFIG)\b/, 'keyword.info'],
                // Log levels - debug
                [/\b(DEBUG|TRACE|FINE|FINER|FINEST)\b/, 'keyword.debug'],
                // Common constants
                [/\b(true|false|null|undefined)\b/, 'keyword'],
                // ISO timestamps
                [/\d{4}-\d{2}-\d{2}(T|\s)\d{2}:\d{2}:\d{2}(\.\d+)?(Z|[+-]\d{2}:\d{2})?/, 'number'],
                // Time formats
                [/\d{2}:\d{2}:\d{2}(\.\d+)?/, 'number'],
                // Date formats
                [/\d{4}\/\d{2}\/\d{2}/, 'number'],
                [/\d{2}\/\d{2}\/\d{4}/, 'number'],
                // IP addresses
                [/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/, 'number.ip'],
                // Windows paths
                [/([a-zA-Z]:\\)([\w\\]+)*/, 'string.path'],
                // Unix paths
                [/(\/[\w\-\.\/]+)+/, 'string.path'],
                // Key-value pairs
                [/[a-zA-Z][a-zA-Z0-9_]*=/, 'attribute.name'],
                [/\b([a-zA-Z][a-zA-Z0-9_]*)\s*:/, 'attribute.name'],
                [/\b([a-zA-Z][a-zA-Z0-9_]*)\s*=/, 'attribute.name'],
                // Exception stack traces
                [/\b(Exception|Error|Throwable|Caused by):/, 'keyword.error'],
                [/\b(at)\s+([a-zA-Z0-9_$.]+)\(([^)]+)\)/, 'keyword.error'],
                // URLs
                [/https?:\/\/\S+/, 'string.link'],
                // Identifiers
                [/[a-zA-Z][a-zA-Z0-9_]*/, ''],
                // Whitespace
                [/\s+/, ''],
                // Brackets
                [/[{}()\[\]]/, '@brackets'],
                // Symbols
                [/@symbols/, 'delimiter'],
                { include: '@whitespace' },
                { include: '@strings' },
                { include: '@numbers' }
            ],
            whitespace: [
                [/\s+/, 'white'],
                [/(^#.*$)/, 'comment']
            ],
            strings: [
                [/'/, 'string', '@stringBody'],
                [/"/, 'string', '@dblStringBody']
            ],
            stringBody: [
                [/'/, 'string', '@popall'],
                [/./, 'string']
            ],
            dblStringBody: [
                [/"/, 'string', '@popall'],
                [/./, 'string']
            ],
            numbers: [
                [/\d*\.\d+([eE][\-+]?\d+)?/, 'number.float'],
                [/0[xX][0-9a-fA-F_]*[0-9a-fA-F]/, 'number.hex'],
                [/\d+/, 'number']
            ]
        }
    };

    // Register the language when the module is loaded
    // Check if we're in a browser environment with the global monaco object
    if (typeof self !== 'undefined' && self.monaco) {
        self.monaco.languages.register({ id: 'log' });
        self.monaco.languages.setMonarchTokensProvider('log', exports.language);
        self.monaco.languages.setLanguageConfiguration('log', exports.conf);
    }
    // For AMD environments where monaco is loaded later
    if (typeof self !== 'undefined') {
        self.MonacoEnvironment = self.MonacoEnvironment || {};
        self.MonacoEnvironment.Contrib = self.MonacoEnvironment.Contrib || {};
        self.MonacoEnvironment.Contrib.log = {
            conf: exports.conf,
            language: exports.language
        };
    }
});
