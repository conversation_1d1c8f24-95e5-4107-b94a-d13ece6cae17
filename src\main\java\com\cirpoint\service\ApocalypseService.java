package com.cirpoint.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aspose.words.*;
import com.cirpoint.util.Util;
import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-04-09 10:50
 * @description 生成问题启示录的服务
 **/
@Slf4j
@Service
public class ApocalypseService {

	@Value("${file.upload.path}")
	private String fileUploadPath;

	@Value("${file.temp.path}")
	private String tempPath;

	/**
	 * 生成启示录文档
	 *
	 * @return 生成的文档文件路径
	 * @throws Exception 文档生成过程中的异常
	 */
	public String generateApocalypse(String treeId) throws Exception {

		JSONObject postRes = Util.postTwxForObject("Thing.Fn.QualityOnlineConfirm", "QueryQualityCaseList", new JSONObject().set("treeId", treeId));
		if (!postRes.getBool("success")) {
			throw new Exception(postRes.getStr("msg"));
		}
		// 加载案例数据
		JSONObject postData = postRes.getJSONObject("data");
		JSONArray casesData = postData.getJSONArray("list");
		String year = postData.getStr("year");


		// 创建临时目录
		String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
		String dirPath = tempPath + File.separator + "apocalypse_" + timestamp;
		FileUtil.mkdir(dirPath);
		
		// 处理年份格式：如果包含"年"则去除
		String fileYear = year;
		if (fileYear != null && fileYear.endsWith("年")) {
			fileYear = fileYear.substring(0, fileYear.length() - 1);
		}
		
		// 验证year是否为有效的年份格式（4位数字）
		if (fileYear == null || !fileYear.matches("\\d{4}")) {
			// 如果不是有效年份，则使用当前年份
			Calendar calendar = Calendar.getInstance();
			fileYear = String.valueOf(calendar.get(Calendar.YEAR));
		}
		
		String fileName = fileYear + "年问题启示录.docx";
		String filePath = dirPath + File.separator + fileName;

		// 获取License
		getLicense();

		// 创建文档
		Document doc = new Document();
		DocumentBuilder builder = new DocumentBuilder(doc);

		// 设置页面大小为A4
		PageSetup pageSetup = builder.getPageSetup();
		pageSetup.setPaperSize(PaperSize.A4);
		pageSetup.setOrientation(Orientation.PORTRAIT);

		// 添加页码到页脚
		addPageNumbers(doc);

		// 添加文档结构（目录和标题）
		createDocumentStructure(builder, year);

		// 处理案例数据，按阶段分组
		Map<String, List<JSONObject>> groupedCases = groupCasesByPhase(casesData);

		// 生成案例内容
		generateCasesContent(builder, groupedCases);

		// 更新文档的目录
		doc.updateFields();

		// 保存文档
		doc.save(filePath);

		return filePath;
	}

	/**
	 * 获取Aspose License
	 */
	private void getLicense() {
		try {
			InputStream is = getClass().getClassLoader().getResourceAsStream("license.xml");
			License license = new License();
			license.setLicense(is);
		} catch (Exception e) {
			File licenseFile = new File("resources/license.xml");
			if (licenseFile.exists()) {
				License license = new License();
				try {
					license.setLicense(Files.newInputStream(licenseFile.toPath()));
				} catch (Exception ex) {
					log.error("获取Aspose License失败", e);
				}
			} else {
				log.warn("License文件不存在，将使用评估版");
			}
		}
	}

	/**
	 * 添加页码到页脚
	 */
	private void addPageNumbers(Document doc) {
		// 创建页脚
		HeaderFooter footer = new HeaderFooter(doc, HeaderFooterType.FOOTER_PRIMARY);
		doc.getFirstSection().getHeadersFooters().add(footer);

		// 使用DocumentBuilder插入页脚内容
		DocumentBuilder builder = new DocumentBuilder(doc);
		builder.moveToHeaderFooter(HeaderFooterType.FOOTER_PRIMARY);

		// 居中对齐
		builder.getParagraphFormat().setAlignment(ParagraphAlignment.CENTER);

		// 设置字体为9号
		builder.getFont().clearFormatting();
		builder.getFont().setName("宋体");
		builder.getFont().setSize(9);

		// 插入页码字段 - 只显示数字
		builder.insertField("PAGE", "");
	}

	/**
	 * 创建文档结构
	 */
	private void createDocumentStructure(DocumentBuilder builder, String year) {
		// 创建首页
		createCoverPage(builder, year);

		// 添加分页符
		builder.insertBreak(BreakType.PAGE_BREAK);

		// 添加目录标题 - 使用自定义段落和字体设置
		builder.getParagraphFormat().clearFormatting();
		builder.getParagraphFormat().setAlignment(ParagraphAlignment.CENTER);
		builder.getFont().clearFormatting();
		builder.getFont().setBold(true);
		builder.getFont().setSize(22);
		builder.getFont().setName("宋体");
		builder.writeln("目  录");

		// 应用正文样式
		applyNormalStyle(builder);

		// 添加目录字段
		builder.getParagraphFormat().setAlignment(ParagraphAlignment.LEFT);
		builder.insertTableOfContents("\\o \"1-3\" \\h \\z \\u");

		// 添加分页符
		builder.insertBreak(BreakType.PAGE_BREAK);

		// 添加一级标题：引言
		applyHeading1Style(builder);
		builder.writeln("1、引言");

		// 添加引言内容 - 使用缩进样式
		applyIndentedNormalStyle(builder);
		builder.writeln("本文档汇总了项目过程中发现的问题及解决方案，旨在总结经验教训，避免类似问题再次发生。");
		builder.writeln("通过系统地分析这些问题，我们可以提高产品质量，完善工作流程，加强团队协作。");

		// 添加分页符
		builder.insertBreak(BreakType.PAGE_BREAK);

		// 添加一级标题：质量案例
		applyHeading1Style(builder);
		builder.writeln("2、质量案例");
	}

	/**
	 * 创建首页
	 */
	private void createCoverPage(DocumentBuilder builder, String year) {

		// 处理年份格式：如果包含"年"则去除，如果不是合格的年份格式则使用当前年份
		if (year != null && year.endsWith("年")) {
			year = year.substring(0, year.length() - 1);
		}

		// 验证year是否为有效的年份格式（4位数字）
		if (year == null || !year.matches("\\d{4}")) {
			// 如果不是有效年份，则使用当前年份
			Calendar calendar = Calendar.getInstance();
			year = String.valueOf(calendar.get(Calendar.YEAR));
		}

		// 清除段落和字体格式
		builder.getParagraphFormat().clearFormatting();
		builder.getFont().clearFormatting();

		// 在左上角添加"内部"文字 - 使用正文样式且不缩进
		applyNormalStyle(builder);
		builder.getParagraphFormat().setAlignment(ParagraphAlignment.LEFT);
		builder.getFont().setSize(16);
		builder.getFont().setBold(true);
		builder.writeln("内 部");

		// 添加空行
		builder.writeln("");

		// 设置首页上方内容 - 标题
		builder.getParagraphFormat().setAlignment(ParagraphAlignment.CENTER);

		// "现场问题质量启示录" - 字体加粗，宋体，字体大小44，居中显示
		builder.getFont().setName("宋体");
		builder.getFont().setSize(44);
		builder.getFont().setBold(true);
		builder.writeln("现场问题质量启示录");

		// "（2024年）" - 字体加粗，宋体，字体大小18，居中显示
		builder.getFont().setName("宋体");
		builder.getFont().setSize(18);
		builder.getFont().setBold(true);
		builder.writeln("（" + year + "年）");

		// 添加空行，使底部内容下移
		for (int i = 0; i < 13; i++) {
			builder.writeln("");
		}

		// 设置首页底部内容
		builder.getFont().setName("宋体");
		builder.getFont().setSize(16);
		builder.getFont().setBold(false);
		builder.writeln("XXXXXXX");

		// "2025年1月" - 宋体，字体大小16，居中显示
		builder.getFont().setName("宋体");
		builder.getFont().setSize(16);
		builder.getFont().setBold(false);
		Calendar calendar = Calendar.getInstance();
		int currentYear = calendar.get(Calendar.YEAR);
		int currentMonth = calendar.get(Calendar.MONTH) + 1; // 月份从0开始，需要+1
		builder.write(currentYear + "年" + currentMonth + "月");
	}

	/**
	 * 应用一级标题样式
	 */
	private void applyHeading1Style(DocumentBuilder builder) {
		builder.getParagraphFormat().clearFormatting();
		builder.getFont().clearFormatting();
		builder.getParagraphFormat().setStyle(builder.getDocument().getStyles().get("Heading 1"));
		builder.getFont().setBold(true);
		builder.getFont().setName("宋体");
		builder.getFont().setSize(22);
		builder.getFont().setItalic(false);
		builder.getParagraphFormat().setLineSpacing(2.4 * 12); // 2.4倍行距

	}

	/**
	 * 应用二级标题样式
	 */
	private void applyHeading2Style(DocumentBuilder builder) {
		builder.getParagraphFormat().clearFormatting();
		builder.getFont().clearFormatting();
		builder.getParagraphFormat().setStyle(builder.getDocument().getStyles().get("Heading 2"));
		builder.getFont().setBold(true);
		builder.getFont().setName("宋体");
		builder.getFont().setSize(16);
		builder.getFont().setItalic(false);
		builder.getParagraphFormat().setLineSpacing(2.4 * 12); // 2.4倍行距

	}

	/**
	 * 应用三级标题样式
	 */
	private void applyHeading3Style(DocumentBuilder builder) {
		builder.getParagraphFormat().clearFormatting();
		builder.getFont().clearFormatting();
		builder.getParagraphFormat().setStyle(builder.getDocument().getStyles().get("Heading 3"));
		builder.getFont().setBold(true);
		builder.getFont().setName("宋体");
		builder.getFont().setSize(15);
		builder.getFont().setItalic(false);
		builder.getParagraphFormat().setLineSpacing(2.4 * 12); // 2.4倍行距
		builder.getParagraphFormat().setAlignment(ParagraphAlignment.CENTER);
	}

	/**
	 * 应用正文样式（无缩进）
	 */
	private void applyNormalStyle(DocumentBuilder builder) {
		builder.getParagraphFormat().clearFormatting();
		builder.getFont().clearFormatting();
		builder.getParagraphFormat().setStyle(builder.getDocument().getStyles().get("Normal"));
		builder.getFont().setName("宋体");
		builder.getFont().setSize(12);
		builder.getFont().setBold(false);
		// 设置固定行距为20磅
		builder.getParagraphFormat().setLineSpacing(20);
		// 确保无缩进
		builder.getParagraphFormat().setFirstLineIndent(0);
		// 设置两端对齐
		builder.getParagraphFormat().setAlignment(ParagraphAlignment.JUSTIFY);
	}

	/**
	 * 应用缩进的正文样式（用于引言、现象描述、原因分析等特定内容）
	 */
	private void applyIndentedNormalStyle(DocumentBuilder builder) {
		applyNormalStyle(builder); // 先应用基本样式
		// 添加首行缩进2 字符
		builder.getParagraphFormat().setFirstLineIndent(28); // 缩进2 字符 (14磅 × 2 = 28磅)
	}

	/**
	 * 按阶段分组案例数据
	 */
	private Map<String, List<JSONObject>> groupCasesByPhase(JSONArray casesData) {
		Map<String, List<JSONObject>> groupedCases = new HashMap<>();

		// 遍历案例数据
		for (int i = 0; i < casesData.size(); i++) {
			JSONObject caseItem = casesData.getJSONObject(i);
			String phase = caseItem.getStr("stage") + "案例"; // 阶段字段

			if (!groupedCases.containsKey(phase)) {
				groupedCases.put(phase, new ArrayList<>());
			}

			groupedCases.get(phase).add(caseItem);
		}

		return groupedCases;
	}

	/**
	 * 生成案例内容
	 */
	private void generateCasesContent(DocumentBuilder builder, Map<String, List<JSONObject>> groupedCases) throws Exception {
		// 定义所有阶段标题
		String[] allPhases = {
				"产品交接案例",      // 1
				"外协验收案例",      // 2
				"机电热产品加工案例", // 3
				"单板热控案例",      // 4
				"载荷热控案例",      // 5
				"结构装配案例",      // 6
				"推进热控案例",      // 7
				"电缆敷设案例",      // 8
				"电测试总装案例",    // 9
				"电测试案例",        // 10
				"特性测试案例",      // 11
				"热试验改装案例",    // 12
				"热试验案例",        // 13
				"力学改装案例",      // 14
				"展开试验案例",      // 15
				"星箭对接案例",      // 16
				"力学试验案例",      // 17
				"出厂改装案例",      // 18
				"出厂运输案例",      // 19
				"发射场改装案例"     // 20
		};

		int phaseIndex = 1;

		// 遍历所有阶段
		for (String phase : allPhases) {
			// 除第一个二级标题外，其余二级标题前添加分页符
			if (phaseIndex > 1) {
				builder.insertBreak(BreakType.PAGE_BREAK);
			}

			// 添加二级标题
			applyHeading2Style(builder);
			builder.writeln("2." + phaseIndex + " " + phase);

			List<JSONObject> cases = groupedCases.get(phase);
			if (cases != null && !cases.isEmpty()) {
				int caseNumber = 1;

				// 遍历当前阶段的所有案例
				for (JSONObject caseItem : cases) {
					// 如果不是第一个案例，则添加分页符
					if (caseNumber > 1) {
						builder.insertBreak(BreakType.PAGE_BREAK);
					}

					String problemName = caseItem.getStr("problemName"); // 问题名称

					// 添加三级标题（案例标题）
					applyHeading3Style(builder);
					builder.writeln("案例" + caseNumber + "：" + problemName);

					// 添加现象描述 - 标题使用无缩进样式
					applyNormalStyle(builder);
					builder.getFont().setBold(true);
					builder.write("1.现象描述");
					builder.getFont().setBold(false);
					builder.writeln();

					// 现象描述内容 - 使用缩进样式
					applyIndentedNormalStyle(builder);
					builder.writeln(caseItem.getStr("description")); // 现象描述

					// 处理图片 - 使用无缩进样式
					applyNormalStyle(builder);
					JSONArray photos = caseItem.getJSONArray("photos"); // 照片数组
					if (photos != null && !photos.isEmpty()) {
						// 调用处理图片的方法
						processImages(builder, photos, problemName);
					}
					builder.getParagraphFormat().setAlignment(ParagraphAlignment.LEFT);

					// 添加原因分析 - 标题使用无缩进样式
					applyNormalStyle(builder);
					builder.getFont().setBold(true);
					builder.write("2.原因分析");
					builder.getFont().setBold(false);
					builder.writeln();

					// 原因分类 - 无缩进
					builder.write("原因分类：");
					builder.writeln(caseItem.getStr("reasonCategory")); // 原因分类

					// 具体原因 - 使用缩进样式
					builder.write("具体原因：");
					builder.writeln();
					applyIndentedNormalStyle(builder);
					builder.writeln(caseItem.getStr("reasonAnalysis")); // 原因分析

					// 添加解决措施 - 标题使用无缩进样式
					applyNormalStyle(builder);
					builder.getFont().setBold(true);
					builder.write("3.解决措施");
					builder.getFont().setBold(false);
					builder.writeln();

					// 解决措施内容 - 使用缩进样式
					applyIndentedNormalStyle(builder);
					builder.writeln(caseItem.getStr("solution")); // 解决措施

					// 添加问题启示 - 标题使用无缩进样式
					applyNormalStyle(builder);
					builder.getFont().setBold(true);
					builder.write("4.问题启示");
					builder.getFont().setBold(false);
					builder.writeln();

					// 问题启示内容 - 使用缩进样式
					applyIndentedNormalStyle(builder);
					builder.writeln(caseItem.getStr("problemInsight")); // 问题启示

					caseNumber++;
				}
			} else {
				// 当前阶段没有案例，添加空白提示 - 无缩进
				applyNormalStyle(builder);
				builder.writeln("本阶段暂无案例。");
			}

			phaseIndex++;
		}
	}

	/**
	 * 处理图片并添加到文档中
	 *
	 * @param builder 文档构建器
	 * @param photos 图片数据数组
	 * @param problemName 问题名称（用于图片标题）
	 */
	private void processImages(DocumentBuilder builder, JSONArray photos, String problemName) throws Exception {
		builder.getParagraphFormat().setAlignment(ParagraphAlignment.CENTER);

		// 计算总图片数量
		int totalPhotos = photos.size();

		// 计算需要多少行表格 (每行最多2张图片)
		int rowCount = (totalPhotos + 1) / 2; // 向上取整

		for (int row = 0; row < rowCount; row++) {
			// 判断是否是最后一行且只有一张图片
			boolean isLastRowWithSingleImage = (row == rowCount - 1 && totalPhotos % 2 == 1);

			// 创建一个表格
			builder.startTable();

			// 设置表格和单元格属性 - 无边框
			builder.getRowFormat().setHeight(0);
			builder.getRowFormat().setHeightRule(HeightRule.AT_LEAST);

			// 添加表格第一列 - 第一张图片
			int photoIndex = row * 2;
			if (photoIndex < totalPhotos) {
				builder.insertCell();

				// 如果是最后一行且只有一张图片，设置单元格占据整行宽度
				if (isLastRowWithSingleImage) {
					builder.getCellFormat().setWidth(450); // 设置宽度为整行宽度
				}

				// 设置单元格无边框
				builder.getCellFormat().getBorders().setLineStyle(LineStyle.NONE);

				// 如果是最后一行单图，居中对齐
				if (isLastRowWithSingleImage) {
					builder.getParagraphFormat().setAlignment(ParagraphAlignment.CENTER);
				}

				insertPhotoIntoCell(builder, photos, photoIndex, problemName);
			}

			// 添加表格第二列 - 第二张图片 (如果存在且不是最后一行单图情况)
			int secondPhotoIndex = row * 2 + 1;
			if (secondPhotoIndex < totalPhotos && !isLastRowWithSingleImage) {
				builder.insertCell();
				// 设置单元格无边框
				builder.getCellFormat().getBorders().setLineStyle(LineStyle.NONE);

				insertPhotoIntoCell(builder, photos, secondPhotoIndex, problemName);
			} else if (secondPhotoIndex < totalPhotos && isLastRowWithSingleImage) {
				// 不处理，因为在最后一行单图情况下，我们只创建一个单元格
			} else if (!isLastRowWithSingleImage) {
				// 如果是偶数行且不是最后一行单图，添加空单元格保持布局一致
				builder.insertCell();
				// 设置单元格无边框
				builder.getCellFormat().getBorders().setLineStyle(LineStyle.NONE);
			}

			// 结束当前行并创建新行
			builder.endRow();

			// 结束表格
			builder.endTable();

			// 添加表格下方的空行
			builder.writeln();
		}
	}

	/**
	 * 在单元格中插入图片
	 * 
	 * @param builder 文档构建器
	 * @param photos 图片数据数组
	 * @param photoIndex 图片索引
	 * @param problemName 问题名称（用于图片标题）
	 * @throws Exception 如果处理过程中发生错误
	 */
	private void insertPhotoIntoCell(DocumentBuilder builder, JSONArray photos, int photoIndex, String problemName) throws Exception {
		JSONObject photo = photos.getJSONObject(photoIndex);
		String photoPath = photo.getStr("photoPath");

		// 构建完整的图片路径
		String fullPath = fileUploadPath + photoPath;
		File photoFile = new File(fullPath);

		if (photoFile.exists()) {
			// 插入图片，调整尺寸
			builder.insertImage(fullPath, 200, 130);
			builder.writeln(); // 图片下方空行
			// 添加图片标题
			builder.writeln("图" + (photoIndex + 1) + " " + problemName);
		} else {
			builder.writeln("图片不存在: " + fullPath);
		}
	}
}
