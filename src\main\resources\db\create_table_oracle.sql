CREATE TABLE TX_CALIBRATION_PARAMETERS (
    ID VARCHAR2(50) NOT NULL,
    SENSOR_DISTANCE_TO_ORIGIN NUMBER DEFAULT 0,
    CALIBRATION_LEVEL NUMBER(10) DEFAULT 0,
    PLATFORM_WEIGHT NUMBER DEFAULT 0,
    PLATFORM_P1_COEF NUMBER DEFAULT 0,
    PLATFORM_P2_COEF NUMBER DEFAULT 0,
    PLATFORM_P3_COEF NUMBER DEFAULT 0,
    WEIGHT1 NUMBER DEFAULT 0,
    WEIGHT1_P1_COEF NUMBER DEFAULT 0,
    WEIGHT1_P2_COEF NUMBER DEFAULT 0,
    WEIGHT1_P3_COEF NUMBER DEFAULT 0,
    WEIGHT2 NUMBER DEFAULT 0,
    WEIGHT2_P1_COEF NUMBER DEFAULT 0,
    WEIGHT2_P2_COEF NUMBER DEFAULT 0,
    WEIGHT2_P3_COEF NUMBER DEFAULT 0,
    WEIGHT3 NUMBER DEFAULT 0,
    WEIGHT3_P1_COEF NUMBER DEFAULT 0,
    WEIGHT3_P2_COEF NUMBER DEFAULT 0,
    WEIGHT3_P3_COEF NUMBER DEFAULT 0,
    WEIGHT4 NUMBER DEFAULT 0,
    WEIGHT4_P1_COEF NUMBER DEFAULT 0,
    WEIGHT4_P2_COEF NUMBER DEFAULT 0,
    WEIGHT4_P3_COEF NUMBER DEFAULT 0,
    WEIGHT5 NUMBER DEFAULT 0,
    WEIGHT5_P1_COEF NUMBER DEFAULT 0,
    WEIGHT5_P2_COEF NUMBER DEFAULT 0,
    WEIGHT5_P3_COEF NUMBER DEFAULT 0,
    WEIGHT6 NUMBER DEFAULT 0,
    WEIGHT6_P1_COEF NUMBER DEFAULT 0,
    WEIGHT6_P2_COEF NUMBER DEFAULT 0,
    WEIGHT6_P3_COEF NUMBER DEFAULT 0,
    WEIGHT7 NUMBER DEFAULT 0,
    WEIGHT7_P1_COEF NUMBER DEFAULT 0,
    WEIGHT7_P2_COEF NUMBER DEFAULT 0,
    WEIGHT7_P3_COEF NUMBER DEFAULT 0,
    WEIGHT8 NUMBER DEFAULT 0,
    WEIGHT8_P1_COEF NUMBER DEFAULT 0,
    WEIGHT8_P2_COEF NUMBER DEFAULT 0,
    WEIGHT8_P3_COEF NUMBER DEFAULT 0,
    WEIGHT9 NUMBER DEFAULT 0,
    WEIGHT9_P1_COEF NUMBER DEFAULT 0,
    WEIGHT9_P2_COEF NUMBER DEFAULT 0,
    WEIGHT9_P3_COEF NUMBER DEFAULT 0,
    WEIGHT10 NUMBER DEFAULT 0,
    WEIGHT10_P1_COEF NUMBER DEFAULT 0,
    WEIGHT10_P2_COEF NUMBER DEFAULT 0,
    WEIGHT10_P3_COEF NUMBER DEFAULT 0,
    PLATFORM_P1_CODE NUMBER DEFAULT 0,
    PLATFORM_P2_CODE NUMBER DEFAULT 0,
    PLATFORM_P3_CODE NUMBER DEFAULT 0,
    WEIGHT1_P1_CODE NUMBER DEFAULT 0,
    WEIGHT1_P2_CODE NUMBER DEFAULT 0,
    WEIGHT1_P3_CODE NUMBER DEFAULT 0,
    WEIGHT2_P1_CODE NUMBER DEFAULT 0,
    WEIGHT2_P2_CODE NUMBER DEFAULT 0,
    WEIGHT2_P3_CODE NUMBER DEFAULT 0,
    WEIGHT3_P1_CODE NUMBER DEFAULT 0,
    WEIGHT3_P2_CODE NUMBER DEFAULT 0,
    WEIGHT3_P3_CODE NUMBER DEFAULT 0,
    WEIGHT4_P1_CODE NUMBER DEFAULT 0,
    WEIGHT4_P2_CODE NUMBER DEFAULT 0,
    WEIGHT4_P3_CODE NUMBER DEFAULT 0,
    WEIGHT5_P1_CODE NUMBER DEFAULT 0,
    WEIGHT5_P2_CODE NUMBER DEFAULT 0,
    WEIGHT5_P3_CODE NUMBER DEFAULT 0,
    WEIGHT6_P1_CODE NUMBER DEFAULT 0,
    WEIGHT6_P2_CODE NUMBER DEFAULT 0,
    WEIGHT6_P3_CODE NUMBER DEFAULT 0,
    WEIGHT7_P1_CODE NUMBER DEFAULT 0,
    WEIGHT7_P2_CODE NUMBER DEFAULT 0,
    WEIGHT7_P3_CODE NUMBER DEFAULT 0,
    WEIGHT8_P1_CODE NUMBER DEFAULT 0,
    WEIGHT8_P2_CODE NUMBER DEFAULT 0,
    WEIGHT8_P3_CODE NUMBER DEFAULT 0,
    WEIGHT9_P1_CODE NUMBER DEFAULT 0,
    WEIGHT9_P2_CODE NUMBER DEFAULT 0,
    WEIGHT9_P3_CODE NUMBER DEFAULT 0,
    WEIGHT10_P1_CODE NUMBER DEFAULT 0,
    WEIGHT10_P2_CODE NUMBER DEFAULT 0,
    WEIGHT10_P3_CODE NUMBER DEFAULT 0,
    CALIBRATION_DATE VARCHAR2(20),
    MD_EIGHT_POINT_CONCLUSION VARCHAR2(50),
    EIGHT_POINT_CALIB_DATE VARCHAR2(20),
    MD_MASS_CORRECT_CONCLUSION VARCHAR2(50),
    MASS_CORRECTION_DATE VARCHAR2(20),
    PLATFORM_P1_WEIGHT NUMBER DEFAULT 0,
    PLATFORM_P2_WEIGHT NUMBER DEFAULT 0,
    PLATFORM_P3_WEIGHT NUMBER DEFAULT 0,
    PLATFORM_TOTAL_WEIGHT NUMBER DEFAULT 0,
    PRIMARY KEY (ID)
);

-- 测试数据表创建语句
CREATE TABLE TX_TEST_DATA (
    TEST_ID VARCHAR2(50) NOT NULL,
    PRODUCT_MODEL VARCHAR2(50),
    BATCH VARCHAR2(50),
    PRODUCT_NAME VARCHAR2(50),
    OPERATOR1 VARCHAR2(50),
    OPERATOR2 VARCHAR2(50),
    OPERATOR3 VARCHAR2(50),
    OPERATOR4 VARCHAR2(50),
    TEST_DATE VARCHAR2(20),
    H_PLAT_P1_WEIGHT NUMBER DEFAULT 0,
    H_PLAT_P2_WEIGHT NUMBER DEFAULT 0,
    H_PLAT_P3_WEIGHT NUMBER DEFAULT 0,
    H_PLAT_TOTAL_WEIGHT NUMBER DEFAULT 0,
    H_CLAMP_P1 NUMBER DEFAULT 0,
    H_CLAMP_P2 NUMBER DEFAULT 0,
    H_CLAMP_P3 NUMBER DEFAULT 0,
    H_CLAMP_G NUMBER DEFAULT 0,
    H_CLAMP_YC NUMBER DEFAULT 0,
    H_CLAMP_ZC NUMBER DEFAULT 0,
    H_CLAMP_R NUMBER DEFAULT 0,
    H_CLAMP_ALFA NUMBER DEFAULT 0,
    H_CLAMP_MY NUMBER DEFAULT 0,
    H_CLAMP_MZ NUMBER DEFAULT 0,
    H_CLAMP_BALANCE_ANGLE NUMBER DEFAULT 0,
    H_CLAMP_BALANCE_MOMENT NUMBER DEFAULT 0,
    H_CLAMP_SATELLITE_P1 NUMBER DEFAULT 0,
    H_CLAMP_SATELLITE_P2 NUMBER DEFAULT 0,
    H_CLAMP_SATELLITE_P3 NUMBER DEFAULT 0,
    H_CLAMP_SATELLITE_G NUMBER DEFAULT 0,
    H_CLAMP_SATELLITE_YC NUMBER DEFAULT 0,
    H_CLAMP_SATELLITE_ZC NUMBER DEFAULT 0,
    H_CLAMP_SATELLITE_R NUMBER DEFAULT 0,
    H_CLAMP_SATELLITE_ALFA NUMBER DEFAULT 0,
    H_CLAMP_SATELLITE_MY NUMBER DEFAULT 0,
    H_CLAMP_SATELLITE_MZ NUMBER DEFAULT 0,
    H_CLAMP_SAT_BAL_ANGLE NUMBER DEFAULT 0,
    H_CLAMP_SAT_BAL_MOMENT NUMBER DEFAULT 0,
    H_SATELLITE_P1 NUMBER DEFAULT 0,
    H_SATELLITE_P2 NUMBER DEFAULT 0,
    H_SATELLITE_P3 NUMBER DEFAULT 0,
    H_SATELLITE_G NUMBER DEFAULT 0,
    H_SATELLITE_YC NUMBER DEFAULT 0,
    H_SATELLITE_ZC NUMBER DEFAULT 0,
    H_SATELLITE_R NUMBER DEFAULT 0,
    H_SATELLITE_ALFA NUMBER DEFAULT 0,
    H_SATELLITE_MY NUMBER DEFAULT 0,
    H_SATELLITE_MZ NUMBER DEFAULT 0,
    H_SATELLITE_BALANCE_ANGLE NUMBER DEFAULT 0,
    H_SATELLITE_BALANCE_MOMENT NUMBER DEFAULT 0,
    V_PLAT_P1_WEIGHT NUMBER DEFAULT 0,
    V_PLAT_P2_WEIGHT NUMBER DEFAULT 0,
    V_PLAT_P3_WEIGHT NUMBER DEFAULT 0,
    V_PLAT_TOTAL_WEIGHT NUMBER DEFAULT 0,
    V_CLAMP_P1 NUMBER DEFAULT 0,
    V_CLAMP_P2 NUMBER DEFAULT 0,
    V_CLAMP_P3 NUMBER DEFAULT 0,
    V_CLAMP_G NUMBER DEFAULT 0,
    V_CLAMP_XC NUMBER DEFAULT 0,
    V_CLAMP_ZC NUMBER DEFAULT 0,
    V_CLAMP_R NUMBER DEFAULT 0,
    V_CLAMP_ALFA NUMBER DEFAULT 0,
    V_CLAMP_MX NUMBER DEFAULT 0,
    V_CLAMP_MZ NUMBER DEFAULT 0,
    V_CLAMP_BALANCE_ANGLE NUMBER DEFAULT 0,
    V_CLAMP_BALANCE_MOMENT NUMBER DEFAULT 0,
    V_SATELLITE_P1 NUMBER DEFAULT 0,
    V_SATELLITE_P2 NUMBER DEFAULT 0,
    V_SATELLITE_P3 NUMBER DEFAULT 0,
    V_SATELLITE_G NUMBER DEFAULT 0,
    V_SATELLITE_XC NUMBER DEFAULT 0,
    V_SATELLITE_ZC NUMBER DEFAULT 0,
    V_SATELLITE_R NUMBER DEFAULT 0,
    V_SATELLITE_ALFA NUMBER DEFAULT 0,
    V_SATELLITE_MX NUMBER DEFAULT 0,
    V_SATELLITE_MZ NUMBER DEFAULT 0,
    V_SATELLITE_BALANCE_ANGLE NUMBER DEFAULT 0,
    V_SATELLITE_BALANCE_MOMENT NUMBER DEFAULT 0,
    V_CLAMP_SATELLITE_P1 NUMBER DEFAULT 0,
    V_CLAMP_SATELLITE_P2 NUMBER DEFAULT 0,
    V_CLAMP_SATELLITE_P3 NUMBER DEFAULT 0,
    V_CLAMP_SATELLITE_G NUMBER DEFAULT 0,
    V_CLAMP_SATELLITE_XC NUMBER DEFAULT 0,
    V_CLAMP_SATELLITE_ZC NUMBER DEFAULT 0,
    V_CLAMP_SATELLITE_R NUMBER DEFAULT 0,
    V_CLAMP_SATELLITE_ALFA NUMBER DEFAULT 0,
    V_CLAMP_SATELLITE_MX NUMBER DEFAULT 0,
    V_CLAMP_SATELLITE_MZ NUMBER DEFAULT 0,
    V_CLAMP_SAT_BAL_ANGLE NUMBER DEFAULT 0,
    V_CLAMP_SAT_BAL_MOMENT NUMBER DEFAULT 0,
    PRIMARY KEY (TEST_ID)
);

-- 计算结果表创建语句
CREATE TABLE TX_CALCULATION_RESULTS (
    TEST_ID VARCHAR2(50) NOT NULL,
    PRODUCT_MODEL VARCHAR2(50),
    TEST_DATE VARCHAR2(20),
    XC_TEST VARCHAR2(50),
    YC_TEST VARCHAR2(50),
    ZC_TEST VARCHAR2(50),
    XC_SAT VARCHAR2(50),
    YC_SAT VARCHAR2(50),
    ZC_SAT VARCHAR2(50),
    IX_TEST VARCHAR2(50),
    IY_TEST VARCHAR2(50),
    IZ_TEST VARCHAR2(50),
    IXY_TEST VARCHAR2(50),
    IXZ_TEST VARCHAR2(50),
    IYZ_TEST VARCHAR2(50),
    IX_WCENT VARCHAR2(50),
    IY_WCENT VARCHAR2(50),
    IZ_WCENT VARCHAR2(50),
    IXY_WCENT VARCHAR2(50),
    IXZ_WCENT VARCHAR2(50),
    IYZ_WCENT VARCHAR2(50),
    XC VARCHAR2(50),
    IX_BOTTOM VARCHAR2(50),
    IY_BOTTOM VARCHAR2(50),
    IZ_BOTTOM VARCHAR2(50),
    IXY_BOTTOM VARCHAR2(50),
    IXZ_BOTTOM VARCHAR2(50),
    IYZ_BOTTOM VARCHAR2(50),
    ROTATE_ANGLE VARCHAR2(50),
    WEIGHT VARCHAR2(50),
    TEST_XC VARCHAR2(50),
    PRIMARY KEY (TEST_ID)
);

-- 转动惯量标定表创建语句
CREATE TABLE TX_MOMENT_CALIBRATION (
    ID VARCHAR2(50) NOT NULL,
    MEASURE_CYCLE_NUM NUMBER,
    MEASURE_COUNT NUMBER,
    PLAT_T1 NUMBER,
    PLAT_T2 NUMBER,
    PLAT_T3 NUMBER,
    PLAT_T4 NUMBER,
    PLAT_TA NUMBER,
    STANDARD1_MI VARCHAR2(50),
    STANDARD1_T1 NUMBER,
    STANDARD1_T2 NUMBER,
    STANDARD1_T3 NUMBER,
    STANDARD1_T4 NUMBER,
    STANDARD1_TA NUMBER,
    STANDARD1_C NUMBER,
    STANDARD2_MI VARCHAR2(50),
    STANDARD2_T1 NUMBER,
    STANDARD2_T2 NUMBER,
    STANDARD2_T3 NUMBER,
    STANDARD2_T4 NUMBER,
    STANDARD2_TA NUMBER,
    STANDARD2_C NUMBER,
    STANDARD3_MI VARCHAR2(50),
    STANDARD3_T1 NUMBER,
    STANDARD3_T2 NUMBER,
    STANDARD3_T3 NUMBER,
    STANDARD3_T4 NUMBER,
    STANDARD3_TA NUMBER,
    STANDARD3_C NUMBER,
    CALIBRATION_DATE VARCHAR2(20),
    PRIMARY KEY (ID)
);

-- 转动惯量测试记录表创建语句
CREATE TABLE TX_MOMENT_TEST_RECORD (
    TEST_ID VARCHAR2(50) NOT NULL,
    PRODUCT_MODEL VARCHAR2(50),
    PRODUCT_BATCH VARCHAR2(50),
    PRODUCT_NAME VARCHAR2(50),
    OPERATOR1 VARCHAR2(50),
    OPERATOR2 VARCHAR2(50),
    OPERATOR3 VARCHAR2(50),
    TEST_DATE VARCHAR2(20),
    TEST_PERIOD_COUNT NUMBER,
    TEST_TIMES NUMBER,
    PLAT_TAV NUMBER,
    CLAMP_TAV_X NUMBER,
    J0_KVALUE_X NUMBER,
    CLMP_SAT_I_X NUMBER,
    CLAMP_AND_SAT_TAV_X NUMBER,
    JD_KVALUE_X NUMBER,
    CLAMP_AND_SAT_I_X NUMBER,
    SAT_I_X NUMBER,
    CLAMP_TAV_Y NUMBER,
    J0_KVALUE_Y NUMBER,
    CLMP_SAT_I_Y NUMBER,
    CLAMP_AND_SAT_TAV_Y NUMBER,
    JD_KVALUE_Y NUMBER,
    CLAMP_AND_SAT_I_Y NUMBER,
    SAT_I_Y NUMBER,
    CLAMP_TAV_Z NUMBER,
    J0_KVALUE_Z NUMBER,
    CLMP_SAT_I_Z NUMBER,
    CLAMP_AND_SAT_TAV_Z NUMBER,
    JD_KVALUE_Z NUMBER,
    CLAMP_AND_SAT_I_Z NUMBER,
    SAT_I_Z NUMBER,
    WORK_NUMBER NUMBER,
    CLAMP_TAV_STATU1 NUMBER,
    J0_KVALUE_STATU1 NUMBER,
    CLMP_SAT_I_STATU1 NUMBER,
    CLAMP_AND_SAT_TAV_STATU1 NUMBER,
    JD_KVALUE_STATU1 NUMBER,
    CLAMP_AND_SAT_I_STATU1 NUMBER,
    SAT_I_STATU1 NUMBER,
    CLAMP_TAV_STATU2 NUMBER,
    J0_KVALUE_STATU2 NUMBER,
    CLMP_SAT_I_STATU2 NUMBER,
    CLAMP_AND_SAT_TAV_STATU2 NUMBER,
    JD_KVALUE_STATU2 NUMBER,
    CLAMP_AND_SAT_I_STATU2 NUMBER,
    SAT_I_STATU2 NUMBER,
    CLAMP_TAV_STATU3 NUMBER,
    J0_KVALUE_STATU3 NUMBER,
    CLMP_SAT_I_STATU3 NUMBER,
    CLAMP_AND_SAT_TAV_STATU3 NUMBER,
    JD_KVALUE_STATU3 NUMBER,
    CLAMP_AND_SAT_I_STATU3 NUMBER,
    SAT_I_STATU3 NUMBER,
    CLAMP_TAV_STATU4 NUMBER,
    J0_KVALUE_STATU4 NUMBER,
    CLMP_SAT_I_STATU4 NUMBER,
    CLAMP_AND_SAT_TAV_STATU4 NUMBER,
    JD_KVALUE_STATU4 NUMBER,
    CLAMP_AND_SAT_I_STATU4 NUMBER,
    SAT_I_STATU4 NUMBER,
    CLAMP_TAV_STATU5 NUMBER,
    J0_KVALUE_STATU5 NUMBER,
    CLMP_SAT_I_STATU5 NUMBER,
    CLAMP_AND_SAT_TAV_STATU5 NUMBER,
    JD_KVALUE_STATU5 NUMBER,
    CLAMP_AND_SAT_I_STATU5 NUMBER,
    SAT_I_STATU5 NUMBER,
    SAT_ANGLE NUMBER,
    CLAMP_TAV_STATU6 NUMBER,
    J0_KVALUE_STATU6 NUMBER,
    CLMP_SAT_I_STATU6 NUMBER,
    CLAMP_AND_SAT_TAV_STATU6 NUMBER,
    JD_KVALUE_STATU6 NUMBER,
    CLAMP_AND_SAT_I_STATU6 NUMBER,
    SAT_I_STATU6 NUMBER,
    ROTATE_ANGLE_BATE NUMBER,
    PRIMARY KEY (TEST_ID)
);