package com.cirpoint.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * 配置文件服务类
 * 提供读取系统配置文件的方法
 */
@Slf4j
@Service
public class ConfigService extends ApplicationConfig {

    /**
     * 获取表映射配置
     * 读取 table_mapping.json 文件并返回其内容
     *
     * @return 表映射配置的 JSONObject
     */
    public JSONObject getTableMapping() {
        try {
            // 尝试从类路径资源中读取
            ClassPathResource resource = new ClassPathResource("config/table_mapping.json");
            try (InputStream inputStream = resource.getInputStream()) {
                byte[] bytes = Util.readInputStream(inputStream);
                String content = new String(bytes, StandardCharsets.UTF_8);
                return JSONUtil.parseObj(content);
            }
        } catch (IOException e) {
            log.error("无法从类路径读取表映射配置文件", e);

            // 如果从类路径读取失败，尝试从文件系统读取
            try {
                File configFile = new File("src/main/resources/config/table_mapping.json");
                if (configFile.exists()) {
                    String content = FileUtil.readUtf8String(configFile);
                    return JSONUtil.parseObj(content);
                }
            } catch (Exception ex) {
                log.error("无法从文件系统读取表映射配置文件", ex);
            }

            // 如果所有尝试都失败，返回空对象
            log.warn("返回空的表映射配置");
            return new JSONObject();
        }
    }
}
