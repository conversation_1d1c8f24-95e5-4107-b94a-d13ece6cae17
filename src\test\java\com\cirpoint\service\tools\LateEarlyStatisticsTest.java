package com.cirpoint.service.tools;

import com.cirpoint.model.worktime.AttendanceRecord;
import com.cirpoint.model.worktime.LateEarlyResult;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 迟到早退统计功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class LateEarlyStatisticsTest {

    /**
     * 测试LateEarlyResult数据模型
     */
    @Test
    public void testLateEarlyResultModel() {
        // 测试明细记录构造
        LocalDate workDate = LocalDate.of(2025, 6, 26);
        LateEarlyResult detailResult = new LateEarlyResult("张三", workDate, 8.5);

        assertTrue(detailResult.isValid());
        assertEquals("张三", detailResult.getEmployeeName());
        assertEquals(workDate, detailResult.getWorkDate());
        assertEquals(8.5, detailResult.getDailyHours());
        assertEquals("2025-06-26", detailResult.getWorkDateString());
        assertEquals("8.50", detailResult.getFormattedDailyHours());
        assertFalse(detailResult.isSummaryRecord());

        // 测试汇总记录构造
        LateEarlyResult summaryResult = new LateEarlyResult("李四", 5, 8.0);

        assertTrue(summaryResult.isValid());
        assertEquals("李四", summaryResult.getEmployeeName());
        assertEquals(5, summaryResult.getAttendanceDays());
        assertEquals(8.0, summaryResult.getAverageHours());
        assertEquals("8.00", summaryResult.getFormattedAverageHours());
        assertTrue(summaryResult.isSummaryRecord());

        // 测试工时统计记录构造
        LateEarlyResult workTimeResult = new LateEarlyResult("王五", workDate, 8.0, "910厂房");

        assertTrue(workTimeResult.isValid());
        assertEquals("910厂房", workTimeResult.getLocation());
        assertTrue(workTimeResult.isParticipateInLateEarlyStats());
        assertTrue(workTimeResult.isFactory910Or920());

        // 测试非910/920厂房
        LateEarlyResult otherResult = new LateEarlyResult("赵六", workDate, 8.0, "二号楼");
        assertEquals("二号楼", otherResult.getLocation());
        assertFalse(otherResult.isParticipateInLateEarlyStats());
        assertFalse(otherResult.isFactory910Or920());
    }

    /**
     * 测试迟到早退次数累加
     */
    @Test
    public void testAddCounts() {
        LateEarlyResult result1 = new LateEarlyResult();
        result1.setMorningLateCount(1);
        result1.setMorningEarlyCount(0);
        result1.setAfternoonLateCount(1);
        result1.setAfternoonEarlyCount(1);
        
        LateEarlyResult result2 = new LateEarlyResult();
        result2.setMorningLateCount(0);
        result2.setMorningEarlyCount(1);
        result2.setAfternoonLateCount(1);
        result2.setAfternoonEarlyCount(0);
        
        result1.addCounts(result2);
        
        assertEquals(1, result1.getMorningLateCount());
        assertEquals(1, result1.getMorningEarlyCount());
        assertEquals(2, result1.getAfternoonLateCount());
        assertEquals(1, result1.getAfternoonEarlyCount());
    }

    /**
     * 测试时间格式化
     */
    @Test
    public void testTimeFormatting() {
        LateEarlyResult result = new LateEarlyResult();
        
        LocalDateTime morningEntry = LocalDateTime.of(2025, 6, 26, 8, 10, 30);
        result.setMorningFirstEntry(morningEntry);
        assertEquals("08:10:30", result.getMorningFirstEntryString());
        
        LocalDateTime afternoonExit = LocalDateTime.of(2025, 6, 26, 16, 25, 45);
        result.setAfternoonLastExit(afternoonExit);
        assertEquals("16:25:45", result.getAfternoonLastExitString());
        
        // 测试空时间
        assertEquals("", result.getMorningLastExitString());
        assertEquals("", result.getAfternoonFirstEntryString());
    }

    /**
     * 测试排序键生成
     */
    @Test
    public void testSortKey() {
        // 明细记录排序键
        LocalDate workDate = LocalDate.of(2025, 6, 26);
        LateEarlyResult detailResult = new LateEarlyResult("张三", workDate, 8.0);
        assertEquals("张三|2025-06-26", detailResult.getSortKey());
        
        // 汇总记录排序键
        LateEarlyResult summaryResult = new LateEarlyResult("李四", 5, 8.0);
        assertEquals("李四", summaryResult.getSortKey());
    }

    /**
     * 创建测试用的打卡记录
     */
    private AttendanceRecord createTestRecord(String employeeName, String direction, 
                                            String eventTimeStr, String location) {
        AttendanceRecord record = new AttendanceRecord();
        record.setEmployeeName(employeeName);
        record.setDirection(direction);
        record.setEventTime(LocalDateTime.parse(eventTimeStr));
        record.setLocation(location);
        
        // 根据location设置accessPoint
        if ("910厂房".equals(location)) {
            record.setAccessPoint("910" + direction);
        } else if ("920厂房".equals(location)) {
            record.setAccessPoint("920" + direction);
        }
        
        return record;
    }

    /**
     * 测试数据验证
     */
    @Test
    public void testDataValidation() {
        // 有效数据
        LateEarlyResult validResult = new LateEarlyResult("张三", LocalDate.now(), 8.0);
        assertTrue(validResult.isValid());
        
        // 无效数据 - 空姓名
        LateEarlyResult invalidResult1 = new LateEarlyResult("", LocalDate.now(), 8.0);
        assertFalse(invalidResult1.isValid());
        
        // 无效数据 - 负工时
        LateEarlyResult invalidResult2 = new LateEarlyResult("李四", LocalDate.now(), -1.0);
        assertFalse(invalidResult2.isValid());
        
        // 无效数据 - 负迟到次数
        LateEarlyResult invalidResult3 = new LateEarlyResult("王五", LocalDate.now(), 8.0);
        invalidResult3.setMorningLateCount(-1);
        assertFalse(invalidResult3.isValid());
    }

    /**
     * 测试综合汇总记录构造函数
     */
    @Test
    public void testComprehensiveSummaryConstructor() {
        LateEarlyResult result = new LateEarlyResult("张三", 5, 40.0, 8.0);

        assertEquals("张三", result.getEmployeeName());
        assertEquals(5, result.getAttendanceDays());
        assertEquals(40.0, result.getTotalHours());
        assertEquals(8.0, result.getAverageHours());
        assertEquals("40.00", result.getFormattedTotalHours());
        assertTrue(result.isSummaryRecord());
    }

    /**
     * 测试厂房判定方法
     */
    @Test
    public void testFactoryIdentification() {
        LateEarlyResult result910 = new LateEarlyResult();
        result910.setLocation("910厂房");
        assertTrue(result910.isFactory910Or920());

        LateEarlyResult result920 = new LateEarlyResult();
        result920.setLocation("920厂房");
        assertTrue(result920.isFactory910Or920());

        LateEarlyResult resultMerged = new LateEarlyResult();
        resultMerged.setLocation("910/920厂房");
        assertTrue(resultMerged.isFactory910Or920());

        LateEarlyResult resultOther = new LateEarlyResult();
        resultOther.setLocation("二号楼");
        assertFalse(resultOther.isFactory910Or920());
    }

    /**
     * 测试迟到早退统计参与标识
     */
    @Test
    public void testLateEarlyParticipation() {
        LateEarlyResult result = new LateEarlyResult();

        // 默认不参与
        assertFalse(result.isParticipateInLateEarlyStats());

        // 设置参与
        result.setParticipateInLateEarlyStats(true);
        assertTrue(result.isParticipateInLateEarlyStats());

        // 设置不参与
        result.setParticipateInLateEarlyStats(false);
        assertFalse(result.isParticipateInLateEarlyStats());
    }

    /**
     * 测试重构后的综合统计数据结构
     */
    @Test
    public void testComprehensiveStatisticsStructure() {
        LocalDate workDate = LocalDate.of(2025, 6, 26);

        // 测试910厂房员工（应该参与迟到早退统计）
        LateEarlyResult factory910Result = new LateEarlyResult("张三", workDate, 8.0, "910厂房");
        assertTrue(factory910Result.isParticipateInLateEarlyStats());
        assertTrue(factory910Result.isFactory910Or920());
        assertEquals("910厂房", factory910Result.getLocation());

        // 测试920厂房员工（应该参与迟到早退统计）
        LateEarlyResult factory920Result = new LateEarlyResult("李四", workDate, 8.0, "920厂房");
        assertTrue(factory920Result.isParticipateInLateEarlyStats());
        assertTrue(factory920Result.isFactory910Or920());
        assertEquals("920厂房", factory920Result.getLocation());

        // 测试其他厂房员工（不参与迟到早退统计，但有完整数据结构）
        LateEarlyResult otherFactoryResult = new LateEarlyResult("王五", workDate, 8.0, "二号楼");
        assertFalse(otherFactoryResult.isParticipateInLateEarlyStats());
        assertFalse(otherFactoryResult.isFactory910Or920());
        assertEquals("二号楼", otherFactoryResult.getLocation());

        // 验证所有员工都有迟到早退字段（默认为0）
        assertEquals(0, otherFactoryResult.getMorningLateCount());
        assertEquals(0, otherFactoryResult.getMorningEarlyCount());
        assertEquals(0, otherFactoryResult.getAfternoonLateCount());
        assertEquals(0, otherFactoryResult.getAfternoonEarlyCount());
    }

    /**
     * 测试新的Excel报表结构要求
     */
    @Test
    public void testNewExcelStructureRequirements() {
        LocalDate workDate = LocalDate.of(2025, 6, 26);

        // 创建不同厂房的员工数据
        LateEarlyResult factory910 = new LateEarlyResult("张三", workDate, 8.0, "910厂房");
        LateEarlyResult factory920 = new LateEarlyResult("李四", workDate, 8.0, "920厂房");
        LateEarlyResult otherFactory = new LateEarlyResult("王五", workDate, 8.0, "二号楼");

        // 模拟910厂房员工有迟到早退
        factory910.setMorningLateCount(1);
        factory910.setAfternoonEarlyCount(1);
        factory910.setParticipateInLateEarlyStats(true);

        // 模拟920厂房员工有迟到早退
        factory920.setMorningEarlyCount(1);
        factory920.setAfternoonLateCount(1);
        factory920.setParticipateInLateEarlyStats(true);

        // 验证数据结构
        List<LateEarlyResult> allResults = Arrays.asList(factory910, factory920, otherFactory);

        // 验证所有员工都包含在结果中
        assertEquals(3, allResults.size());

        // 验证910/920厂房员工有迟到早退数据
        assertTrue(factory910.getMorningLateCount() > 0 || factory910.getAfternoonEarlyCount() > 0);
        assertTrue(factory920.getMorningEarlyCount() > 0 || factory920.getAfternoonLateCount() > 0);

        // 验证其他厂房员工的迟到早退数据为0
        assertEquals(0, otherFactory.getMorningLateCount());
        assertEquals(0, otherFactory.getMorningEarlyCount());
        assertEquals(0, otherFactory.getAfternoonLateCount());
        assertEquals(0, otherFactory.getAfternoonEarlyCount());
    }
}
