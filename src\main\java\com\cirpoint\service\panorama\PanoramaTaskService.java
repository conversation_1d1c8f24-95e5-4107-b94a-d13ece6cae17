package com.cirpoint.service.panorama;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.service.ApplicationConfig;
import com.cirpoint.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * 全景图任务管理服务
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@Slf4j
@Service
public class PanoramaTaskService extends ApplicationConfig {

    @Autowired
    private PanoramaDeviceService panoramaDeviceService;

    /**
     * 创建新任务
     */
    public JSONObject createTask(String taskName, String modelId, String modelName, String description, String username) {
        try {
            // 验证用户名参数
            if (username == null || username.trim().isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "用户名不能为空");
            }

            // 检查任务名称是否重复
            String checkSql = "SELECT COUNT(*) AS cnt FROM PANORAMA_TASK WHERE TASK_NAME = '" + taskName.replace("'", "''") + "'";
            JSONArray checkResult = Util.postQuerySql(checkSql);

            if (checkResult != null && checkResult.size() > 0) {
                Object firstRowObj = checkResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    JSONObject firstRow = (JSONObject) firstRowObj;
                    int count = firstRow.getInt("CNT");
                    if (count > 0) {
                        return JSONUtil.createObj()
                                .set("success", false)
                                .set("msg", "任务名称已存在，请使用其他名称");
                    }
                }
            }

            // 生成任务ID
            String taskIdSql = "SELECT SEQ_PANORAMA_TASK.NEXTVAL FROM DUAL";
            JSONArray taskIdResult = Util.postQuerySql(taskIdSql);
            Long taskId = null;
            if (taskIdResult != null && taskIdResult.size() > 0) {
                Object firstRowObj = taskIdResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    taskId = ((JSONObject) firstRowObj).getLong("NEXTVAL");
                }
            }

            // 插入任务记录，使用传入的用户名作为创建者
            String insertSql = "INSERT INTO PANORAMA_TASK (TASK_ID, TASK_NAME, MODEL_ID, MODEL_NAME, DESCRIPTION, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS) " +
                    "VALUES (" + taskId + ", '" + taskName.replace("'", "''") + "', " +
                    (modelId != null ? "'" + modelId.replace("'", "''") + "'" : "NULL") + ", " +
                    (modelName != null ? "'" + modelName.replace("'", "''") + "'" : "NULL") + ", " +
                    (description != null ? "'" + description.replace("'", "''") + "'" : "NULL") + ", '" + username.replace("'", "''") + "', SYSDATE, SYSDATE, 0)";

            Util.postCommandSql(insertSql);

            // 如果提供了型号ID，自动查询并添加单机数据
            if (modelId != null && !modelId.trim().isEmpty()) {
                try {
                    panoramaDeviceService.queryAndSaveStandAloneDevices(taskId, modelId, modelName);
                } catch (Exception e) {
                    log.warn("自动添加单机数据失败，但不影响任务创建: {}", e.getMessage());
                }
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "任务创建成功")
                    .set("data", JSONUtil.createObj().set("taskId", taskId));
        } catch (Exception e) {
            log.error("创建任务失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "创建任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务列表（根据用户权限过滤）
     */
    public JSONObject getTaskList(String username) {
        try {
            // 验证用户名参数
            if (username == null || username.trim().isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "用户名不能为空");
            }

            // 查询用户创建的任务列表
            String sql = "SELECT TASK_ID, TASK_NAME, MODEL_ID, MODEL_NAME, DESCRIPTION, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS " +
                    "FROM PANORAMA_TASK WHERE CREATE_USER = '" + username.replace("'", "''") + "' ORDER BY CREATE_TIME DESC";
            JSONArray taskList = Util.postQuerySql(sql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", taskList != null ? taskList : new JSONArray());
        } catch (Exception e) {
            log.error("获取任务列表失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务详情
     */
    public JSONObject getTaskDetail(Long taskId) {
        try {
            String sql = "SELECT * FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray result = Util.postQuerySql(sql);

            if (result.isEmpty()) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            // 安全获取第一行数据
            Object firstRowObj = result.get(0);
            JSONObject taskData = null;
            if (firstRowObj instanceof JSONObject) {
                taskData = (JSONObject) firstRowObj;
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", taskData);
        } catch (Exception e) {
            log.error("获取任务详情失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取任务详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务管理列表（分页，根据用户权限过滤）
     */
    public JSONObject getTaskManagementList(Integer page, Integer limit, String username) {
        try {
            // 验证用户名参数
            if (username == null || username.trim().isEmpty()) {
                return JSONUtil.createObj()
                        .set("code", 1)
                        .set("msg", "用户名不能为空")
                        .set("count", 0)
                        .set("data", new JSONArray());
            }

            // 计算分页参数
            int offset = (page - 1) * limit;

            // 查询总数（仅统计用户创建的任务）
            String countSql = "SELECT COUNT(*) as total FROM PANORAMA_TASK WHERE CREATE_USER = '" + username.replace("'", "''") + "'";
            JSONArray countResult = Util.postQuerySql(countSql);
            int total = 0;
            if (countResult != null && countResult.size() > 0) {
                Object firstRowObj = countResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    total = ((JSONObject) firstRowObj).getInt("TOTAL");
                }
            }

            // 查询分页数据，包含状态文本转换（仅查询用户创建的任务）
            String dataSql = "SELECT * FROM (" +
                    "SELECT ROWNUM rn, t.* FROM (" +
                    "SELECT TASK_ID, TASK_NAME, MODEL_ID, MODEL_NAME, DESCRIPTION, " +
                    "CASE STATUS " +
                    "  WHEN 0 THEN '创建中' " +
                    "  WHEN 1 THEN '已完成' " +
                    "  WHEN 2 THEN '已导出' " +
                    "  ELSE '未知' " +
                    "END AS STATUS_TEXT, " +
                    "STATUS, CREATE_USER, " +
                    "TO_CHAR(CREATE_TIME, 'YYYY-MM-DD HH24:MI:SS') as CREATE_TIME, " +
                    "TO_CHAR(UPDATE_TIME, 'YYYY-MM-DD HH24:MI:SS') as UPDATE_TIME " +
                    "FROM PANORAMA_TASK WHERE CREATE_USER = '" + username.replace("'", "''") + "' ORDER BY CREATE_TIME DESC" +
                    ") t WHERE ROWNUM <= " + (offset + limit) +
                    ") WHERE rn > " + offset;

            JSONArray dataResult = Util.postQuerySql(dataSql);

            return JSONUtil.createObj()
                    .set("code", 0)
                    .set("msg", "")
                    .set("count", total)
                    .set("data", dataResult != null ? dataResult : new JSONArray());

        } catch (Exception e) {
            log.error("获取任务管理列表失败", e);
            return JSONUtil.createObj()
                    .set("code", 1)
                    .set("msg", "获取任务管理列表失败: " + e.getMessage())
                    .set("count", 0)
                    .set("data", new JSONArray());
        }
    }

    /**
     * 更新任务信息（仅任务名称和描述）
     */
    public JSONObject updateTask(Long taskId, String taskName, String description) {
        try {
            // 检查任务是否存在
            String checkSql = "SELECT COUNT(*) AS cnt FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray checkResult = Util.postQuerySql(checkSql);
            if (checkResult == null || checkResult.size() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            Object firstRowObj = checkResult.get(0);
            if (firstRowObj instanceof JSONObject) {
                int count = ((JSONObject) firstRowObj).getInt("CNT");
                if (count == 0) {
                    return JSONUtil.createObj()
                            .set("success", false)
                            .set("msg", "任务不存在");
                }
            }

            // 检查任务名称是否与其他任务重复
            String duplicateCheckSql = "SELECT COUNT(*) AS cnt FROM PANORAMA_TASK " +
                    "WHERE TASK_NAME = '" + taskName.replace("'", "''") + "' AND TASK_ID != " + taskId;
            JSONArray duplicateResult = Util.postQuerySql(duplicateCheckSql);
            if (duplicateResult != null && duplicateResult.size() > 0) {
                Object duplicateRowObj = duplicateResult.get(0);
                if (duplicateRowObj instanceof JSONObject) {
                    int duplicateCount = ((JSONObject) duplicateRowObj).getInt("CNT");
                    if (duplicateCount > 0) {
                        return JSONUtil.createObj()
                                .set("success", false)
                                .set("msg", "任务名称已存在，请使用其他名称");
                    }
                }
            }

            // 更新任务信息
            String updateSql = "UPDATE PANORAMA_TASK SET " +
                    "TASK_NAME = '" + taskName.replace("'", "''") + "', " +
                    "DESCRIPTION = " + (description != null ? "'" + description.replace("'", "''") + "'" : "NULL") + ", " +
                    "UPDATE_TIME = SYSDATE " +
                    "WHERE TASK_ID = " + taskId;

            Util.postCommandSql(updateSql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "任务信息更新成功");

        } catch (Exception e) {
            log.error("更新任务信息失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "更新任务信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务删除前的统计信息
     */
    public JSONObject getTaskDeleteStats(Long taskId) {
        try {
            // 检查任务是否存在
            String taskSql = "SELECT TASK_NAME, STATUS FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray taskResult = Util.postQuerySql(taskSql);
            if (taskResult == null || taskResult.size() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            Object taskRowObj = taskResult.get(0);
            JSONObject taskInfo = null;
            if (taskRowObj instanceof JSONObject) {
                taskInfo = (JSONObject) taskRowObj;
            }

            // 统计热点数量
            String hotspotCountSql = "SELECT COUNT(*) AS cnt FROM PANORAMA_HOTSPOT WHERE TASK_ID = " + taskId;
            JSONArray hotspotResult = Util.postQuerySql(hotspotCountSql);
            int hotspotCount = 0;
            if (hotspotResult != null && hotspotResult.size() > 0) {
                Object hotspotRowObj = hotspotResult.get(0);
                if (hotspotRowObj instanceof JSONObject) {
                    hotspotCount = ((JSONObject) hotspotRowObj).getInt("CNT");
                }
            }

            // 统计设备数量
            String deviceCountSql = "SELECT COUNT(*) AS cnt FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId;
            JSONArray deviceResult = Util.postQuerySql(deviceCountSql);
            int deviceCount = 0;
            if (deviceResult != null && deviceResult.size() > 0) {
                Object deviceRowObj = deviceResult.get(0);
                if (deviceRowObj instanceof JSONObject) {
                    deviceCount = ((JSONObject) deviceRowObj).getInt("CNT");
                }
            }

            // 检查是否有文件
            boolean hasFiles = false;
            if (taskInfo != null) {
                String zipFilePath = taskInfo.getStr("ZIP_FILE_PATH");
                String extractPath = taskInfo.getStr("EXTRACT_PATH");
                hasFiles = (zipFilePath != null && !zipFilePath.trim().isEmpty()) ||
                          (extractPath != null && !extractPath.trim().isEmpty());
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", JSONUtil.createObj()
                            .set("taskName", taskInfo != null ? taskInfo.getStr("TASK_NAME") : "")
                            .set("hotspotCount", hotspotCount)
                            .set("deviceCount", deviceCount)
                            .set("hasFiles", hasFiles));

        } catch (Exception e) {
            log.error("获取任务删除统计信息失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除任务（级联删除相关数据）
     */
    public JSONObject deleteTask(Long taskId) {
        try {
            // 检查任务是否存在并获取文件路径信息
            String taskSql = "SELECT TASK_NAME, ZIP_FILE_PATH, EXTRACT_PATH FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            JSONArray taskResult = Util.postQuerySql(taskSql);
            if (taskResult == null || taskResult.size() == 0) {
                return JSONUtil.createObj()
                        .set("success", false)
                        .set("msg", "任务不存在");
            }

            Object taskRowObj = taskResult.get(0);
            JSONObject taskInfo = null;
            if (taskRowObj instanceof JSONObject) {
                taskInfo = (JSONObject) taskRowObj;
            }

            // 开始事务性删除操作
            // 1. 删除热点数据
            String deleteHotspotSql = "DELETE FROM PANORAMA_HOTSPOT WHERE TASK_ID = " + taskId;
            Util.postCommandSql(deleteHotspotSql);

            // 2. 删除设备数据
            String deleteDeviceSql = "DELETE FROM PANORAMA_DEVICE WHERE TASK_ID = " + taskId;
            Util.postCommandSql(deleteDeviceSql);

            // 3. 删除文件系统中的相关文件
            if (taskInfo != null) {
                String zipFilePath = taskInfo.getStr("ZIP_FILE_PATH");
                String extractPath = taskInfo.getStr("EXTRACT_PATH");

                // 删除ZIP文件
                if (zipFilePath != null && !zipFilePath.trim().isEmpty()) {
                    try {
                        File zipFile = new File(zipFilePath);
                        if (zipFile.exists()) {
                            FileUtil.del(zipFile);
                        }
                    } catch (Exception e) {
                        log.warn("删除ZIP文件失败: {}, 错误: {}", zipFilePath, e.getMessage());
                    }
                }

                // 删除解压目录
                if (extractPath != null && !extractPath.trim().isEmpty()) {
                    try {
                        File extractDir = new File(extractPath);
                        if (extractDir.exists()) {
                            FileUtil.del(extractDir);
                        }
                    } catch (Exception e) {
                        log.warn("删除解压目录失败: {}, 错误: {}", extractPath, e.getMessage());
                    }
                }
            }

            // 4. 删除panorama路径下以任务ID命名的整个文件夹
            try {
                String taskDir = fileUploadPath + File.separator + "panorama" + File.separator + taskId;
                File taskDirectory = new File(taskDir);
                if (taskDirectory.exists()) {
                    FileUtil.del(taskDirectory);
                }
            } catch (Exception e) {
                log.warn("删除任务目录失败: taskId={}, 错误: {}", taskId, e.getMessage());
            }

            // 5. 最后删除任务记录
            String deleteTaskSql = "DELETE FROM PANORAMA_TASK WHERE TASK_ID = " + taskId;
            Util.postCommandSql(deleteTaskSql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "任务删除成功");

        } catch (Exception e) {
            log.error("删除任务失败: taskId={}", taskId, e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "删除任务失败: " + e.getMessage());
        }
    }

    /**
     * 检查任务是否已存在热点数据
     */
    public JSONObject checkExistingHotspots(Long taskId) {
        try {
            String countSql = "SELECT COUNT(*) AS TOTAL FROM PANORAMA_HOTSPOT WHERE TASK_ID = " + taskId;
            JSONArray countResult = Util.postQuerySql(countSql);
            int total = 0;
            if (countResult != null && countResult.size() > 0) {
                Object firstRowObj = countResult.get(0);
                if (firstRowObj instanceof JSONObject) {
                    total = ((JSONObject) firstRowObj).getInt("TOTAL");
                }
            }

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("hasData", total > 0)
                    .set("count", total);
        } catch (Exception e) {
            log.error("检查热点数据失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "检查热点数据失败: " + e.getMessage());
        }
    }

    /**
     * 清理任务的热点数据和文件信息
     */
    public JSONObject clearTaskData(Long taskId) {
        try {
            // 删除热点数据
            String deleteHotspotSql = "DELETE FROM PANORAMA_HOTSPOT WHERE TASK_ID = " + taskId;
            Util.postCommandSql(deleteHotspotSql);

            // 清空任务的文件路径信息
            String updateTaskSql = "UPDATE PANORAMA_TASK SET ZIP_FILE_PATH = NULL, EXTRACT_PATH = NULL, UPDATE_TIME = SYSDATE WHERE TASK_ID = " + taskId;
            Util.postCommandSql(updateTaskSql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("msg", "任务数据清理成功");
        } catch (Exception e) {
            log.error("清理任务数据失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "清理任务数据失败: " + e.getMessage());
        }
    }
}
