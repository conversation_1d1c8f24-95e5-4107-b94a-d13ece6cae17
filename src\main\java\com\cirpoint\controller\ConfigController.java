package com.cirpoint.controller;

import com.cirpoint.model.Result;
import com.cirpoint.service.ConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 配置文件控制器
 * 提供访问系统配置文件的接口
 */
@Slf4j
@RestController
@RequestMapping("/api/config")
@Tag(name = "配置管理", description = "提供系统配置文件访问接口")
public class ConfigController {

    private final ConfigService configService;

    @Autowired
    public ConfigController(ConfigService configService) {
        this.configService = configService;
    }

    /**
     * 获取表映射配置
     * 
     * @return 表映射配置的JSON数据
     */
    @GetMapping(value = "/table-mapping", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(
            summary = "获取表映射配置",
            description = "读取并返回table_mapping.json文件的内容",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "成功获取表映射配置",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = Result.class))
                    )
            }
    )
    public ResponseEntity<?> getTableMapping() {
        try {
            log.info("获取表映射配置");
            return Result.ok("获取表映射配置成功", configService.getTableMapping());
        } catch (Exception e) {
            log.error("获取表映射配置失败", e);
            return Result.error("获取表映射配置失败：" + e.getMessage());
        }
    }
}
