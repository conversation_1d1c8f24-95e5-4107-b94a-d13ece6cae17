package com.cirpoint.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import lombok.extern.slf4j.Slf4j;

/**
 * DlService测试类
 */
@Slf4j
@SpringBootTest
public class DlServiceTest {

    @Autowired
    private DlService dlService;
    @Test
    public void testSyncDlReport() {
        log.info("开始测试同步电缆报告");
        dlService.syncDlReport("-1");
        log.info("测试同步电缆报告完成");
    }

    @Test
    public void testGenerateMockFiles() {
        dlService.generateMockFiles();
    }
} 