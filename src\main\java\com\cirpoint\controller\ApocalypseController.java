 package com.cirpoint.controller;

import com.cirpoint.service.ApocalypseService;
import com.cirpoint.util.FileDownloadUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 问题启示录控制器
 * 用于生成和下载问题启示录文档
 */
@RestController
@RequestMapping("/apocalypse")
public class ApocalypseController {

    private final ApocalypseService apocalypseService;

    @Autowired
    public ApocalypseController(ApocalypseService apocalypseService) {
        this.apocalypseService = apocalypseService;
    }

    /**
     * 生成问题启示录文档并提供下载
     *
     * @param treeId 树节点ID
     * @return 文档下载响应
     */
    @PostMapping("/generate")
    public ResponseEntity<?> generateApocalypse(@RequestParam("treeId") String treeId) {
        try {
            // 调用服务生成文档，返回文件路径
            String filePath = apocalypseService.generateApocalypse(treeId);
            
            // 使用工具类处理文件下载并在下载后删除临时文件
            return FileDownloadUtil.fileResponseAndDelete(filePath);
        } catch (Exception e) {
            // 返回错误信息
            return ResponseEntity.badRequest().body("生成启示录文档失败：" + e.getMessage());
        }
    }
}