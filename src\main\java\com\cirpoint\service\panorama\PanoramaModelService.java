package com.cirpoint.service.panorama;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 全景图型号管理服务
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
@Slf4j
@Service
public class PanoramaModelService {

    /**
     * 获取型号列表
     */
    public JSONObject getModelList() {
        try {
            String sql = "SELECT NODENAME AS model_name, TREEID AS model_id " +
                    "FROM DATAPACKAGETREE " +
                    "WHERE NODETYPE = 'product' " +
                    "ORDER BY NLSSORT(NODENAME,'NLS_SORT=SCHINESE_PINYIN_M')";

            JSONArray modelList = Util.postQuerySql(sql);

            return JSONUtil.createObj()
                    .set("success", true)
                    .set("data", modelList != null ? modelList : new JSONArray());
        } catch (Exception e) {
            log.error("获取型号列表失败", e);
            return JSONUtil.createObj()
                    .set("success", false)
                    .set("msg", "获取型号列表失败: " + e.getMessage())
                    .set("data", new JSONArray());
        }
    }
}
