package com.cirpoint.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.util.Util;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 院数据包文件服务类
 */
@Slf4j
@Service
public class SuperiorService extends ApplicationConfig {


	/**
	 * 下载文档文件
	 *
	 * @param filePath 文件在服务器上的路径
	 * @param fileName 文件名称
	 * @return 下载的文件
	 */
	public File downloadDocFile(String filePath, String fileName) {
		// 获取下载URL
		String url = Util.postTwx("Thing.Fn.Interface", "GetReqUrl",
						new JSONObject().set("actionName", "downloadFile"))
				.getJSONObject(0)
				.getStr("result");

		String localPath = tempPath + File.separator + fileName;
		File resFile = new File(localPath);

		try (FileOutputStream fos = new FileOutputStream(resFile);
			 BufferedOutputStream bos = new BufferedOutputStream(fos)) {

			// 构建请求参数
			JSONObject requestBody = new JSONObject()
					.set("fileName", fileName)
					.set("filePath", filePath);

			// 发送HTTP请求
			HttpResponse response = HttpRequest.post(url)
					.method(Method.POST)
					.header("Content-Type", "application/json")
					.body(requestBody.toString(), "application/json")
					.execute();

			// 处理响应
			if (response.isOk()) {
				String resultStr = response.header("Result");
				log.info("下载文件响应结果: {}", resultStr);

				// 将文件保存到本地
				byte[] bytes = response.bodyBytes();
				bos.write(bytes);
			} else {
				throw new RuntimeException("文件下载失败，HTTP响应码：" + response.getStatus());
			}

		} catch (IOException e) {
			log.error("下载文件失败", e);
			throw new RuntimeException("下载文件失败：" + e.getMessage());
		}

		return resFile;
	}

	/**
	 * 上传文档文件
	 *
	 * @param file 上传的文件
	 * @return JSONObject 上传结果
	 */
	public JSONObject uploadDocFile(MultipartFile file) {
		JSONObject res = new JSONObject();

		try {
			// 将MultipartFile转换为临时文件
			String tempFilePath = tempPath + File.separator + file.getOriginalFilename();
			File tempFile = new File(tempFilePath);
			file.transferTo(tempFile);

			// 调用上传方法
			String uploadResStr = uploadFileToServer(tempFilePath, file.getOriginalFilename());
			JSONObject uploadRes = JSONUtil.parseObj(uploadResStr);

			// 构建返回结果
			if (uploadRes.getInt("code") == 200) {
				res.set("success", true)
						.set("msg", "上传成功")
						.set("data", uploadRes);
			} else {
				res.set("success", false)
						.set("msg", uploadRes.getStr("message"));
			}

			// 清理临时文件
			FileUtil.del(tempFile);

		} catch (Exception e) {
			log.error("上传文件失败", e);
			res.set("success", false)
					.set("msg", "上传错误，原因：" + e.getLocalizedMessage());
		}

		return res;
	}

	/**
	 * 上传文件到服务器
	 *
	 * @param filePath 文件路径
	 * @param fileName 文件名
	 * @return 上传结果
	 */
	private String uploadFileToServer(String filePath, String fileName) {
		String url = Util.postTwx("Thing.Fn.Interface", "GetReqUrl",
						new JSONObject().set("actionName", "uploadFile"))
				.getJSONObject(0)
				.getStr("result");

		String res = "";

		try (InputStream inputStream = Files.newInputStream(Paths.get(filePath))) {
			File file = new File(filePath);
			long fileSize = file.length();
			long blockSize = 1048576L * 10; // 10M
			long blockOffset = 0;
			String md5 = SecureUtil.md5(filePath);

			while (blockOffset != fileSize) {
				// 计算当前块大小
				if (blockOffset + blockSize > fileSize) {
					blockSize = fileSize - blockOffset;
				}

				// 读取文件块
				byte[] fileBlock = new byte[(int) blockSize];
				//noinspection ResultOfMethodCallIgnored
				inputStream.read(fileBlock, 0, fileBlock.length);

				// 构建请求参数
				JSONObject requestBody = new JSONObject()
						.set("fileName", fileName)
						.set("unit", "812")
						.set("fileBlock", fileBlock)
						.set("blockOffset", blockOffset)
						.set("lastBlock", (blockOffset + blockSize) == fileSize)
						.set("fileId", md5);

				// 发送请求
				res = HttpRequest.post(url)
						.method(Method.POST)
						.header("Content-Type", "application/json")
						.body(requestBody.toString(), "application/json")
						.execute()
						.body();

				blockOffset += blockSize;
			}
		} catch (IOException e) {
			log.error("上传文件失败", e);
			throw new RuntimeException("上传文件失败：" + e.getMessage());
		}

		return res;
	}

}