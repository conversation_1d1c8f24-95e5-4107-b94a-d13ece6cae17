package com.cirpoint.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.annotation.DisableLogging;
import com.cirpoint.model.Result;
import com.cirpoint.service.archive.ArchiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 档案管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/archive")
public class ArchiveController {

	private final ArchiveService archiveService;

	public ArchiveController(ArchiveService archiveService) {
		this.archiveService = archiveService;
	}

	/**
	 * 发起数据包收集
	 *
	 * @param phaseTreeId      阶段树ID
	 * @param pushUserName     推送人用户名
	 * @param pushUserFullname 推送人全名
	 * @return 处理结果
	 */
	@RequestMapping(value = "/collect", method = {RequestMethod.POST, RequestMethod.GET})
	public ResponseEntity<?> collect(@RequestParam(required = false) String phaseTreeId,
									 @RequestParam(required = false) String pushUserName,
									 @RequestParam(required = false) String pushUserFullname) {
		try {
			archiveService.collectPackage(phaseTreeId, pushUserName, pushUserFullname);
			return Result.ok("数据包收集任务已启动");
		} catch (Exception e) {
			return Result.error(e.getMessage());
		}
	}

	/**
	 * 查询数据包状态
	 *
	 * @param phaseTreeId 阶段树ID
	 * @return 数据包状态列表
	 */
	@DisableLogging
	@RequestMapping(value = "/status", method = {RequestMethod.POST, RequestMethod.GET})
	public ResponseEntity<?> queryStatus(@RequestParam(required = false) String phaseTreeId) {
		try {
			JSONArray data = archiveService.queryPackageStatus(phaseTreeId);
			JSONObject result = new JSONObject();
			result.set("code", 0);
			result.set("msg", "");
			result.set("count", data.size());
			result.set("data", data);
			return ResponseEntity.ok(result);
		} catch (Exception e) {
			JSONObject result = new JSONObject();
			result.set("code", 1);
			result.set("msg", e.getMessage());
			result.set("count", 0);
			result.set("data", new JSONArray());
			return ResponseEntity.ok(result);
		}
	}


	/**
	 * 通过日志ID删除日志
	 *
	 * @param logId 日志ID
	 * @return 处理结果
	 */
	@RequestMapping(value = "/deleteByLogId", method = {RequestMethod.POST, RequestMethod.GET})
	public ResponseEntity<?> deleteByLogId(@RequestParam String logId) {
		try {
			archiveService.deleteLogRecord(logId);
			return Result.ok("删除成功");
		} catch (Exception e) {
			return Result.error(e.getMessage());
		}
	}

} 