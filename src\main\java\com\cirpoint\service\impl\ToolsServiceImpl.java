package com.cirpoint.service.impl;

import com.cirpoint.service.ToolsService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工具服务实现类
 */
@Service
public class ToolsServiceImpl implements ToolsService {

    /**
     * 获取工具列表
     *
     * @return 工具列表
     */
    @Override
    public List<Map<String, Object>> getToolsList() {
        List<Map<String, Object>> toolsList = new ArrayList<>();
        
        // 添加DOCX批量转PDF工具
        Map<String, Object> docx2pdfTool = new HashMap<>();
        docx2pdfTool.put("id", "docx2pdf");
        docx2pdfTool.put("name", "DOCX批量转PDF工具");
        docx2pdfTool.put("description", "支持将多个Word文档批量转换为PDF格式");
        docx2pdfTool.put("icon", "static/images/docx2pdf.png");
        docx2pdfTool.put("url", "tools/docx2pdf.html");
        docx2pdfTool.put("category", "document");
        
        toolsList.add(docx2pdfTool);
        
        return toolsList;
    }
    
    /**
     * 获取工具详情
     *
     * @param toolId 工具ID
     * @return 工具详情
     */
    @Override
    public Map<String, Object> getToolDetail(String toolId) {
        // 根据工具ID获取相应的工具详情
        if ("docx2pdf".equals(toolId)) {
            Map<String, Object> toolDetail = new HashMap<>();
            toolDetail.put("id", "docx2pdf");
            toolDetail.put("name", "DOCX批量转PDF工具");
            toolDetail.put("description", "支持将多个Word文档批量转换为PDF格式");
            toolDetail.put("icon", "static/images/docx2pdf.png");
            toolDetail.put("url", "tools/docx2pdf.html");
            toolDetail.put("category", "document");
            toolDetail.put("features", new String[]{
                "支持批量转换多个Word文档",
                "保留原始文档格式和样式",
                "转换速度快",
                "提供转换进度和结果统计"
            });
            return toolDetail;
        }
        
        return new HashMap<>();
    }
}