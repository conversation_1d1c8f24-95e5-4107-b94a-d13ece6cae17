package com.cirpoint.task;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 打印PDF文件清理定时任务
 * 清理pdf-print目录下的临时PDF文件
 * 每天凌晨1点执行一次，删除指定目录下一天前的文件
 */
@Slf4j
@Component
public class CleanupPrintPdfTask {

    @Value("${file.upload.path}")
    private String fileUploadPath;

    /**
     * 定时任务：每天凌晨1点执行一次
     * cron表达式：秒 分 时 日 月 星期
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void cleanupPrintPdfFiles() {
        log.info("开始执行打印PDF文件清理定时任务");

        try {
            // 构建pdf-print目录的完整路径
            String pdfPrintPath = fileUploadPath + "/pdf-print";
            Path dirPath = Paths.get(pdfPrintPath);

            // 检查目录是否存在
            if (!Files.exists(dirPath)) {
                log.warn("pdf-print目录不存在: {}", pdfPrintPath);
                return;
            }

            // 计算一天前的时间
            LocalDateTime oneDayAgo = LocalDateTime.now().minusHours(24);

            // 用于统计删除的文件数量
            AtomicInteger deletedCount = new AtomicInteger(0);
            AtomicInteger totalCount = new AtomicInteger(0);

            // 遍历目录下的所有文件
            try (Stream<Path> pathStream = Files.walk(dirPath, 1)) {
                pathStream.filter(Files::isRegularFile)
                        .filter(path -> path.toString().toLowerCase().endsWith(".pdf"))
                        .forEach(filePath -> {
                            totalCount.incrementAndGet();
                            try {
                                BasicFileAttributes attrs = Files.readAttributes(filePath, BasicFileAttributes.class);
                                LocalDateTime fileCreationTime = LocalDateTime.ofInstant(
                                        attrs.creationTime().toInstant(), ZoneId.systemDefault());

                                // 如果文件创建时间早于一天前
                                if (fileCreationTime.isBefore(oneDayAgo)) {
                                    // 删除文件
                                    Files.delete(filePath);
                                    deletedCount.incrementAndGet();
                                    log.debug("已删除过期PDF文件: {}", filePath.getFileName());
                                }
                            } catch (Exception e) {
                                log.error("处理打印PDF文件时出错: {}", filePath, e);
                            }
                        });
            }

            log.info("打印PDF文件清理任务执行成功，共扫描 {} 个文件，删除 {} 个文件", 
                    totalCount.get(), deletedCount.get());

        } catch (Exception e) {
            log.error("打印PDF文件清理任务执行失败", e);
        }
    }
} 