package com.cirpoint.util.ws.interceptor;

import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.apache.cxf.binding.soap.SoapMessage;
import org.apache.cxf.headers.Header;
import org.apache.cxf.helpers.DOMUtils;
import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.phase.AbstractPhaseInterceptor;
import org.apache.cxf.phase.Phase;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.xml.namespace.QName;

@Setter
@Getter
public class ClientLoginInterceptor extends AbstractPhaseInterceptor<SoapMessage> {
	
	private String username;
	private String password;

	public ClientLoginInterceptor(String username, String password) {
		super(Phase.PREPARE_SEND);
		this.username = username;
		this.password = password;
	}

	@Override
	public void handleMessage(SoapMessage soapMessage) throws Fault {
		List<Header> headers = soapMessage.getHeaders();
		Document doc = DOMUtils.createDocument();
		
		Element auth = doc.createElement("authrity");
		Element usernameElement = doc.createElement("username");
		Element passwordElement = doc.createElement("password");
		
		usernameElement.setTextContent(username);
		passwordElement.setTextContent(password);
		
		auth.appendChild(usernameElement);
		auth.appendChild(passwordElement);
		
		headers.add(0, new Header(new QName("tiamaes"),auth));
		
	}
}
