/**
 * jQuery File Download Plugin
 * 简化版本，用于支持文件下载功能
 */
(function($) {
    $.fileDownload = function(url, options) {
        var settings = $.extend({
            httpMethod: 'GET',
            data: {},
            prepareCallback: function() {},
            successCallback: function() {},
            failCallback: function() {},
            abortCallback: function() {}
        }, options);

        // 调用准备回调
        if (typeof settings.prepareCallback === 'function') {
            settings.prepareCallback(url);
        }

        // 创建表单进行文件下载
        var $form = $('<form>', {
            method: settings.httpMethod,
            action: url,
            style: 'display: none;'
        });

        // 添加数据参数
        if (settings.data) {
            $.each(settings.data, function(key, value) {
                $form.append($('<input>', {
                    type: 'hidden',
                    name: key,
                    value: value
                }));
            });
        }

        // 添加到页面并提交
        $('body').append($form);
        
        try {
            $form.submit();
            
            // 延迟调用成功回调
            setTimeout(function() {
                if (typeof settings.successCallback === 'function') {
                    settings.successCallback(url);
                }
            }, 1000);
            
        } catch (e) {
            console.error('文件下载失败:', e);
            if (typeof settings.failCallback === 'function') {
                settings.failCallback('', url);
            }
        } finally {
            // 清理表单
            setTimeout(function() {
                $form.remove();
            }, 2000);
        }
    };
})(jQuery);
