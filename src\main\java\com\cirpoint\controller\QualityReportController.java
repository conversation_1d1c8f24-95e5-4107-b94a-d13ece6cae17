package com.cirpoint.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.model.PdfOptions;
import com.cirpoint.model.Result;
import com.cirpoint.service.QualityReportService;
import com.cirpoint.util.FileDownloadUtil;
import com.cirpoint.util.FileUploadUtil;
import com.cirpoint.util.Util;
import java.io.File;
import java.io.IOException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * AIT质量确认
 */
@RestController
@RequestMapping("/report")
public class QualityReportController {

	private final QualityReportService qualityReportService;

	@Autowired
	public QualityReportController(QualityReportService qualityReportService) {
		this.qualityReportService = qualityReportService;
	}

	/**
	 * 导出单张表的Excel
	 *
	 * @param id 节点ID
	 * @return Excel文件下载响应
	 */
	@PostMapping("/export/excel")
	public ResponseEntity<?> exportTableExcel(@RequestParam("id") String id) {
		return FileDownloadUtil.fileResponseAndDelete(qualityReportService.exportTableExcel(id));
	}

	/**
	 * 导出单张表的PDF
	 *
	 * @param id      节点ID
	 * @param options PDF导出选项对象，包含页面大小和方向等配置
	 * @return PDF文件下载响应
	 */
	@PostMapping("/export/pdf")
	public ResponseEntity<?> exportTablePdf(
			@RequestParam("id") String id,
			@ModelAttribute PdfOptions options) {
		// 如果options为null，创建默认选项对象
		if (options == null) {
			options = new PdfOptions();
		}

		JSONObject result = qualityReportService.exportTablePdf(id, options);
		return FileDownloadUtil.fileResponseAndDelete(result.getStr("data"));
	}

	/**
	 * 导出多个表的Excel（结构树）
	 *
	 * @param id  节点ID
	 * @param pid 父节点ID
	 * @return ZIP文件下载响应
	 */
	@PostMapping("/export/more/excel")
	public ResponseEntity<?> exportMoreExcel(
			@RequestParam("id") String id,
			@RequestParam("pid") String pid) {
		return FileDownloadUtil.fileResponseAndDelete(qualityReportService.exportMoreExcel(id, pid));
	}

	/**
	 * 导出多个表格为PDF（结构树）
	 *
	 * @param id      节点ID
	 * @param pid     父节点ID
	 * @param creator 创建者
	 * @param options PDF导出选项对象，包含页面大小和方向等配置
	 * @return PDF文件下载响应
	 */
	@PostMapping("/export/more/pdf")
	public ResponseEntity<?> exportMorePdf(
			@RequestParam("id") String id,
			@RequestParam("pid") String pid,
			@RequestParam(value = "creator", required = false, defaultValue = "admin") String creator,
			@ModelAttribute PdfOptions options) {
		// 如果options为null，创建默认选项对象
		if (options == null) {
			options = new PdfOptions();
		}

		// 默认输出图片为true
		boolean isOutImg = true;
		JSONObject result = qualityReportService.exportMorePdf(id, pid, Util.getTempPath(), creator, isOutImg, options);
		return FileDownloadUtil.fileResponseAndDelete(result.getStr("data"));
	}

	/**
	 * 生成多个表格的PDF（结构树）
	 *
	 * @param downloadId 下载ID
	 * @param id         节点ID
	 * @param pid        父节点ID
	 * @param creator    创建者
	 * @param module     模块
	 * @param options    PDF导出选项对象，包含页面大小和方向等配置
	 * @return 生成结果
	 */
	@PostMapping("/generate/more/pdf")
	public ResponseEntity<?> generateMorePdf(
			@RequestParam("downloadId") String downloadId,
			@RequestParam("id") String id,
			@RequestParam("pid") String pid,
			@RequestParam(value = "creator", required = false, defaultValue = "admin") String creator,
			@RequestParam("module") String module,
			@ModelAttribute PdfOptions options) {
		// 如果options为null，创建默认选项对象
		if (options == null) {
			options = new PdfOptions();
		}

		JSONObject result = qualityReportService.generateMorePdf(downloadId, id, pid, creator, module, options);
		return ResponseEntity.ok(result);
	}

	/**
	 * 导出PDF并打包成ZIP
	 *
	 * @param id      节点ID
	 * @param name    文件名
	 * @param options PDF导出选项对象，包含页面大小和方向等配置
	 * @return ZIP文件下载响应
	 */
	@PostMapping("/export/pdf/zip")
	public ResponseEntity<?> exportPdfZip(
			@RequestParam("id") String id,
			@RequestParam("name") String name,
			@ModelAttribute PdfOptions options) {
		// 如果options为null，创建默认选项对象
		if (options == null) {
			options = new PdfOptions();
		}

		JSONObject result = qualityReportService.exportPdfZip(id, options);
		return FileDownloadUtil.fileResponseAndDelete(result.getStr("data"), name + ".zip");
	}

	/**
	 * 导出ZIP文件
	 *
	 * @param id 节点ID
	 * @return ZIP文件下载响应
	 */
	@PostMapping("/export/zip")
	public ResponseEntity<?> exportZip(
			@RequestParam("id") String id) {
		JSONObject result = qualityReportService.exportZip(id);
		return FileDownloadUtil.fileResponseAndDelete(result.getStr("data"));
	}

	/**
	 * 导入Excel文件
	 *
	 * @param file     Excel文件
	 * @param id       节点ID
	 * @param saveUser 保存用户
	 * @return 导入结果
	 */
	@PostMapping("/import/excel")
	public ResponseEntity<?> importExcel(@RequestParam("uploadFile") MultipartFile file,
										 @RequestParam("id") String id,
										 @RequestParam("saveUser") String saveUser) {
		JSONObject result = qualityReportService.importExcel(file, id, saveUser);
		return ResponseEntity.ok(result);
	}

	/**
	 * 导入PDF文件
	 *
	 * @param file PDF文件
	 * @param id   节点ID
	 * @return 导入结果
	 */
	@PostMapping("/import/pdf")
	public ResponseEntity<?> importPdf(@RequestParam("uploadFile") MultipartFile file,
									   @RequestParam("id") String id) {
		JSONObject result = qualityReportService.importPdf(file, id);
		return ResponseEntity.ok(result);
	}

	@SuppressWarnings("MismatchedQueryAndUpdateOfCollection")
	@PostMapping("/import/big/zip")
	public ResponseEntity<?> importBigZip(
			@RequestParam(value = "file") MultipartFile file,
			@RequestParam(value = "chunk", required = false) Integer chunk,
			@RequestParam(value = "chunks", required = false) Integer chunks,
			@RequestParam("name") String name,
			@RequestParam("reqIdent") String reqIdent,
			@RequestParam("extraData") String extraData) {

		// 处理件上传
		JSONObject uploadResult = FileUploadUtil.handleFileUpload(file, chunk, chunks, name, reqIdent, extraData);

		// 如果上传完成且成功，则处理导入
		if (uploadResult.getBool("success") && uploadResult.getBool("isAll", false)) {
			JSONObject extraDataObj = new JSONObject(extraData);
			JSONObject result = qualityReportService.importBigZip(
					uploadResult.getStr("file"),
					extraDataObj.getStr("tableId"),
					extraDataObj.getStr("tablePId"),
					extraDataObj.getStr("treeId"),
					extraDataObj.getStr("srcType"));
			return ResponseEntity.ok(result);
		}

		return ResponseEntity.ok(uploadResult);
	}

	/**
	 * 导入Excel模板
	 *
	 * @param file     Excel文件
	 * @param id       节点ID
	 * @param saveUser 保存用户
	 * @return 导入结果
	 */
	@PostMapping("/import/tpl/excel")
	public ResponseEntity<?> importTplExcel(
			@RequestParam("uploadFile") MultipartFile file,
			@RequestParam("id") String id,
			@RequestParam("saveUser") String saveUser) {
		JSONObject result = qualityReportService.importTplExcel(file, id, saveUser);
		return ResponseEntity.ok(result);
	}

	/**
	 * 处理Excel文件并导入照片数据
	 *
	 * @param excelPath Excel文件相对路径
	 * @param photos    照片或签名JSON数组
	 * @return 处理结果
	 */
	@PostMapping("/get/excel")
	public ResponseEntity<?> importTestExcel(
			@RequestParam("excelPath") String excelPath,
			@RequestParam("photos") String photos) {
		JSONObject result = qualityReportService.importTestExcel(excelPath, photos);
		return ResponseEntity.ok(result);
	}

	/**
	 * 结构件确认表生成合格证
	 *
	 * @param id 节点ID
	 * @return 生成结果
	 */
	@PostMapping("/create/certificate")
	public ResponseEntity<?> createCertificate(
			@RequestParam("id") String id,
			@RequestParam("certificateTypes") String certificateTypes) throws IOException {
		return FileDownloadUtil.fileResponseAndDelete(qualityReportService.createCertificate(id, certificateTypes));
	}

	/**
	 * 结构件确认表生成合格证
	 *
	 * @param id               节点ID
	 * @param certificateTypes 证书类型
	 * @param selectedIds      选中的B表ID列表，格式如："2059,2062,2065"
	 * @return 生成结果
	 */
	@PostMapping("/batch/create/certificate")
	public ResponseEntity<?> batchCreateCertificates(
			@RequestParam("id") String id,
			@RequestParam("certificateTypes") String certificateTypes,
			@RequestParam(value = "selectedIds", required = false) String selectedIds) {
		return FileDownloadUtil.fileResponseAndDelete(qualityReportService.batchCreateCertificates(id, certificateTypes, selectedIds));
	}

	/**
	 * 比对结构件数据
	 *
	 * @param id 节点ID
	 * @return 生成结果
	 */
	@PostMapping("/compare/table/data")
	public ResponseEntity<?> compareTableData(
			@RequestParam("id") String id,
			@RequestParam("saveUser") String saveUser) {
		return ResponseEntity.ok(qualityReportService.compareTableData(id, saveUser));
	}

	/**
	 * 锁定表格时更新数据
	 *
	 * @param id       表格ID
	 * @param saveUser 保存用户
	 * @return 更新结果
	 */
	@PostMapping("/lock/component")
	public ResponseEntity<?> updateTableDataWhenLock(
			@RequestParam("id") String id,
			@RequestParam("saveUser") String saveUser) {
		return ResponseEntity.ok(qualityReportService.updateTableDataWhenLock(id, saveUser));
	}

	/**
	 * 结构件确认表生成合格证 - 打印模式
	 *
	 * @param id              节点ID
	 * @param certificateType 证书类型（单选：public/internal）
	 * @return 生成的PDF文件路径，用于前端直接打开
	 */
	@PostMapping("/create/certificate/print")
	public ResponseEntity<?> createCertificateForPrint(
			@RequestParam("id") String id,
			@RequestParam("certificateType") String certificateType) throws IOException {
		File file = qualityReportService.createCertificateForPrint(id, certificateType);
		// 获取相对路径，用于前端直接访问
		String relativePath = "/File/pdf-print/" + file.getName();
		return ResponseEntity.ok(JSONUtil.createObj()
				.set("success", true)
				.set("filePath", relativePath));
	}

	/**
	 * 结构件确认表批量生成合格证 - 打印模式
	 *
	 * @param id              节点ID
	 * @param certificateType 证书类型（单选：public/internal）
	 * @param selectedIds     选中的B表ID列表，格式如："2059,2062,2065"
	 * @return 生成的合并PDF文件路径，用于前端直接打开
	 */
	@PostMapping("/batch/create/certificate/print")
	public ResponseEntity<?> batchCreateCertificatesForPrint(
			@RequestParam("id") String id,
			@RequestParam("certificateType") String certificateType,
			@RequestParam(value = "selectedIds", required = false) String selectedIds) throws IOException {
		File file = qualityReportService.batchCreateCertificatesForPrint(id, certificateType, selectedIds);
		// 获取相对路径，用于前端直接访问
		String relativePath = "/File/pdf-print/" + file.getName();
		return ResponseEntity.ok(JSONUtil.createObj()
				.set("success", true)
				.set("filePath", relativePath));
	}

	/**
	 * 结构件确认表生成二维码
	 *
	 * @param id 节点ID
	 * @return 生成结果
	 */
	@PostMapping("/create/qrcode")
	public ResponseEntity<?> createQrCode(
			@RequestParam("id") String id) throws Exception {
		return FileDownloadUtil.fileResponseAndDelete(qualityReportService.createQrCode(id));
	}

	/**
	 * 结构件确认表批量生成二维码
	 *
	 * @param id          节点ID
	 * @param selectedIds 选中的B表ID列表，格式如："2059,2062,2065"
	 * @return 生成结果
	 */
	@PostMapping("/batch/create/qrcode")
	public ResponseEntity<?> batchCreateQrCodes(
			@RequestParam("id") String id,
			@RequestParam(value = "selectedIds", required = false) String selectedIds) {
		return FileDownloadUtil.fileResponseAndDelete(qualityReportService.batchCreateQrCodes(id, selectedIds));
	}

	/**
	 * 结构件确认表生成二维码 - 打印模式
	 *
	 * @param id 节点ID
	 * @return 生成的PDF文件路径，用于前端直接打开
	 */
	@PostMapping("/create/qrcode/print")
	public ResponseEntity<?> createQrCodeForPrint(
			@RequestParam("id") String id) throws Exception {
		File file = qualityReportService.createQrCodeForPrint(id);
		// 获取相对路径，用于前端直接访问
		String relativePath = "/File/pdf-print/" + file.getName();
		return ResponseEntity.ok(JSONUtil.createObj()
				.set("success", true)
				.set("filePath", relativePath));
	}

	/**
	 * 结构件确认表批量生成二维码 - 打印模式
	 *
	 * @param id          节点ID
	 * @param selectedIds 选中的B表ID列表，格式如："2059,2062,2065"
	 * @return 生成的合并PDF文件路径，用于前端直接打开
	 */
	@PostMapping("/batch/create/qrcode/print")
	public ResponseEntity<?> batchCreateQrCodesForPrint(
			@RequestParam("id") String id,
			@RequestParam(value = "selectedIds", required = false) String selectedIds) throws Exception {
		File file = qualityReportService.batchCreateQrCodesForPrint(id, selectedIds);
		// 获取相对路径，用于前端直接访问
		String relativePath = "/File/pdf-print/" + file.getName();
		return ResponseEntity.ok(JSONUtil.createObj()
				.set("success", true)
				.set("filePath", relativePath));
	}

	/**
	 * 导出确认表锁定统计Excel
	 *
	 * @param nodeId 节点ID
	 * @return Excel文件下载响应
	 */
	@PostMapping("/export/unlocked/excel")
	public ResponseEntity<?> exportUnlockedTablesExcel(@RequestParam("nodeId") String nodeId) {
		return FileDownloadUtil.fileResponseAndDelete(qualityReportService.exportUnlockedTablesExcel(nodeId));
	}

	/**
	 * 导出确认表签署统计Excel
	 *
	 * @param nodeId 节点ID
	 * @return Excel文件下载响应
	 */
	@PostMapping("/export/unsigned/excel")
	public ResponseEntity<?> exportUnsignedTablesExcel(@RequestParam("nodeId") String nodeId) {
		return FileDownloadUtil.fileResponseAndDelete(qualityReportService.exportUnsignedTablesExcel(nodeId));
	}

	/**
	 * 导入AIT映射模板
	 *
	 * @param file     Excel文件
	 * @param username 导入用户
	 * @return 导入结果
	 */
	@PostMapping("/import/ait/mapping")
	public ResponseEntity<?> importAitMappingTemplate(@RequestParam("file") MultipartFile file,
													  @RequestParam("username") String username) throws IOException {
		return Result.ok(qualityReportService.importAitMappingTemplate(file, username));
	}

	/**
	 * 导出AIT映射数据
	 *
	 * @return Excel文件
	 */
	@PostMapping("/export/ait/mapping")
	public ResponseEntity<?> exportAitMappingData() {
		File file = qualityReportService.exportAitMappingData();
		return FileDownloadUtil.fileResponseAndDelete(file);
	}

	/**
	 * 导出确认表列表
	 *
	 * @param nodeId   节点ID
	 * @param nodeType 节点类型
	 * @return Excel文件下载响应
	 */
	@PostMapping("/export/confirm/table/list")
	public ResponseEntity<?> exportConfirmTableList(
			@RequestParam("nodeName") String nodeName,
			@RequestParam("nodeId") String nodeId,
			@RequestParam("nodeType") String nodeType) {
		return FileDownloadUtil.fileResponseAndDelete(qualityReportService.exportConfirmTableList(nodeName, nodeId, nodeType));
	}

}
