package com.cirpoint.service;

import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.cirpoint.model.PdfOptions;
import com.cirpoint.model.TreeNode;
import com.cirpoint.util.*;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.*;
import com.itextpdf.kernel.pdf.action.PdfAction;
import com.itextpdf.kernel.pdf.navigation.PdfDestination;
import com.itextpdf.kernel.pdf.navigation.PdfExplicitDestination;
import com.itextpdf.kernel.pdf.xobject.PdfFormXObject;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
public class OnlineConfirmService extends ApplicationConfig {

	/**
	 * 导入Excel文件
	 *
	 * @param file     Excel文件
	 * @param id       节点ID
	 * @param thing    事物名称
	 * @param saveUser 保存用户
	 * @return 导入结果
	 */
	public JSONObject importExcel(MultipartFile file, String id, String thing, String saveUser) {
		FileUploadUtil.UploadResult uploadResult = FileUploadUtil.uploadToTemp(file);
		try {
			// 转换Excel为HandsonTable格式
			JSONObject res = Util.excel2HandsonTable(uploadResult.getAbsolutePath());
			if (res.getBool("success")) {
				String tableData = res.getJSONObject("data").toString();
				// 验证是否可以导出PDF
				if (Util.isExportPdf(tableData, tempPath, pdfFontPath)) {
					// 保存数据到TWX
					return Util.postTwxForObject(thing, "SaveTableData",
							JSONUtil.createObj()
									.set("id", id)
									.set("tableData", tableData)
									.set("saveUser", saveUser));
				} else {
					res.set("success", false);
					res.set("msg", "上传的excel存在多余的合并单元格，请调整后重新上传！");
				}
			}
			return res;
		} finally {
			// 清理临时文件
			FileUploadUtil.cleanupTemp(uploadResult.getAbsolutePath());
		}
	}

	/**
	 * 导入PDF文件
	 *
	 * @param file  PDF文件
	 * @param id    节点ID
	 * @param thing 事物名称
	 * @return 导入结果
	 */
	public JSONObject importPdf(MultipartFile file, String id, String thing) {
		FileUploadUtil.UploadResult uploadResult = FileUploadUtil.uploadToStorage(file);

		// 调用TWX保存PDF信息
		return Util.postTwxForObject(thing, "ImportPdf",
				JSONUtil.createObj()
						.set("id", id)
						.set("fileName", uploadResult.getOriginalFilename())
						.set("filePath", uploadResult.getStoragePath())
						.set("fileFormat", uploadResult.getFileFormat()));
	}

	/**
	 * 处理表格导入的通用方法
	 *
	 * @param filePath     文件路径
	 * @param importParams 导入参数
	 * @return 导入结果
	 */
	private JSONObject handleTableImport(String filePath, JSONObject importParams) {
		// 转换Excel为HandsonTable格式
		JSONObject res = Util.excel2HandsonTable(filePath);
		if (res.getBool("success")) {
			String tableData = res.getJSONObject("data").toString();
			// 验证是否可以导出PDF
			if (Util.isExportPdf(tableData, Util.getTempPath(), pdfFontPath)) {
				// 保存数据到TWX
				importParams.set("tableData", tableData);
				return Util.postTwxForObject(importParams.getStr("thing"), "ImportTable", importParams);
			} else {
				return JSONUtil.createObj()
						.set("success", false)
						.set("msg", "上传的excel存在多余的合并单元格，请调整后重新上传！");
			}
		}
		return res;
	}

	/**
	 * 导入表格数据
	 *
	 * @param file      上传的Excel文件
	 * @param pid       父级ID
	 * @param treeId    树ID
	 * @param tableName 表格名称
	 * @param tableNum  表格编号
	 * @param type      类型
	 * @param level     级别
	 * @param thing     事物名称
	 * @param saveUser  保存用户
	 * @return 导入结果
	 */
	public JSONObject importTable(MultipartFile file, String pid, String treeId,
								  String tableName, String tableNum, String type,
								  String level, String thing, String saveUser) {
		// 调用支持密级参数的重载方法，默认密级为内部(1)
		return importTable(file, pid, treeId, tableName, tableNum, type, level, thing, "1", saveUser);
	}

	/**
	 * 导入表格数据（支持密级参数）
	 *
	 * @param file      上传的Excel文件
	 * @param pid       父级ID
	 * @param treeId    树ID
	 * @param tableName 表格名称
	 * @param tableNum  表格编号
	 * @param type      类型
	 * @param level     级别
	 * @param thing     事物名称
	 * @param security  密级
	 * @param saveUser  保存用户
	 * @return 导入结果
	 */
	public JSONObject importTable(MultipartFile file, String pid, String treeId,
								  String tableName, String tableNum, String type,
								  String level, String thing, String security, String saveUser) {
		// 验证密级参数
		if (!isValidSecurityLevel(security)) {
			return JSONUtil.createObj()
					.set("success", false)
					.set("msg", "无效的密级参数：" + security);
		}

		FileUploadUtil.UploadResult uploadResult = null;
		try {
			uploadResult = FileUploadUtil.uploadToTemp(file);
			JSONObject importParams = JSONUtil.createObj()
					.set("pid", pid)
					.set("treeId", treeId)
					.set("saveUser", saveUser)
					.set("tableName", tableName)
					.set("tableNum", tableNum)
					.set("type", type)
					.set("level", level)
					.set("security", security)
					.set("thing", thing);

			return handleTableImport(uploadResult.getAbsolutePath(), importParams);
		} catch (Exception e) {
			log.error("导入表格数据失败", e);
			return JSONUtil.createObj()
					.set("success", false)
					.set("msg", "导入表格数据失败：" + e.getMessage());
		} finally {
			if (uploadResult != null) {
				FileUploadUtil.cleanupTemp(uploadResult.getAbsolutePath());
			}
		}
	}

	/**
	 * 验证密级参数是否有效
	 * @param security 密级字符串
	 * @return 是否有效
	 */
	private boolean isValidSecurityLevel(String security) {
		if (security == null) {
			return false;
		}
		// 验证密级是否在允许范围内（0-3）
		try {
			int level = Integer.parseInt(security);
			return level >= 0 && level <= 3;
		} catch (NumberFormatException e) {
			return false;
		}
	}

	/**
	 * 导入表格数据（内部方法，供批量导入使用）
	 */
	private JSONObject importTable(String filePath, String pid, String treeId,
								   String thing, String type, String level, String tableNum, String tableName, String saveUser) {
		try {
			JSONObject importParams = JSONUtil.createObj()
					.set("pid", pid)
					.set("treeId", treeId)
					.set("saveUser", saveUser)
					.set("tableName", tableName)
					.set("tableNum", tableNum)
					.set("type", type)
					.set("level", level)
					.set("thing", thing);

			return handleTableImport(filePath, importParams);
		} catch (Exception e) {
			log.error("导入表格数据失败", e);
			return JSONUtil.createObj()
					.set("success", false)
					.set("msg", "导入表格数据失败：" + e.getMessage());
		}
	}

	/**
	 * 导入表格数据（内部方法，供批量导入使用，支持密级参数）
	 */
	private JSONObject importTable(String filePath, String pid, String treeId,
								   String thing, String type, String level, String tableNum, String tableName, String security, String saveUser) {
		try {
			JSONObject importParams = JSONUtil.createObj()
					.set("pid", pid)
					.set("treeId", treeId)
					.set("saveUser", saveUser)
					.set("tableName", tableName)
					.set("tableNum", tableNum)
					.set("type", type)
					.set("level", level)
					.set("security", security)
					.set("thing", thing);

			return handleTableImport(filePath, importParams);
		} catch (Exception e) {
			log.error("导入表格数据失败", e);
			return JSONUtil.createObj()
					.set("success", false)
					.set("msg", "导入表格数据失败：" + e.getMessage());
		}
	}

	/**
	 * 批量导入表格数据
	 *
	 * @param file         上传的Excel文件
	 * @param extraDataStr 额外的JSON数据字符串
	 * @return 导入结果
	 */
	@SuppressWarnings("MismatchedQueryAndUpdateOfCollection")
	public JSONObject batchImportTable(MultipartFile file, String extraDataStr) throws Exception {
		if (file == null || file.isEmpty()) {
			throw new IllegalArgumentException("上传文件不能为空");
		}

		// 解析额外数据
		JSONObject extraData = new JSONObject(extraDataStr);
		String pid = extraData.getStr("pid");
		String treeId = extraData.getStr("treeId");
		String thing = extraData.getStr("thing");
		String type = extraData.getStr("type");
		String level = extraData.getStr("level");
		String tableNum = extraData.getStr("tableNum");
		String tableName = extraData.getStr("tableName");
		String saveUser = extraData.getStr("saveUser");
		String security = extraData.getStr("security", "1"); // 默认密级为内部
		
		// 验证密级参数
		if (!isValidSecurityLevel(security)) {
			return JSONUtil.createObj()
					.set("success", false)
					.set("msg", "无效的密级参数：" + security);
		}

		// 创建临时文件
		String originalFilename = file.getOriginalFilename();
		File tempFile = File.createTempFile("upload_", "_" + originalFilename);
		try {
			// 将上传的文件保存到临时文件
			file.transferTo(tempFile);

			// 调用现有的导入表格方法（支持密级参数）
			return importTable(tempFile.getAbsolutePath(), pid, treeId, thing, type,
					level, tableNum, tableName, security, saveUser);
		} finally {
			// 清理临时文件
			if (tempFile.exists()) {
				FileUtil.del(tempFile);
			}
		}
	}

	/**
	 * 导出Excel文件
	 *
	 * @param id    节点ID
	 * @param thing 事物名称
	 * @return 生成的Excel文件
	 */
	public File exportToExcel(String id, String thing) {
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
		FileUtil.mkdir(thisTempPath);
		JSONObject obj = Util.postTwxForObject(thing, "QueryNodeById",
				JSONUtil.createObj().set("id", id));
		JSONObject data = obj.getJSONObject("data");
		return Util.tableData2Excel(data, thisTempPath);
	}

	/**
	 * 导出PDF文件
	 *
	 * @param id       节点ID
	 * @param thing    事物名称
	 * @param options  PDF导出选项对象，包含页面大小、方向等配置
	 * @return 导出结果
	 */
	public JSONObject exportToPdf(String id, String thing, PdfOptions options) {
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
		FileUtil.mkdir(thisTempPath);
		JSONObject obj = Util.postTwxForObject(thing, "QueryNodeById",
				JSONUtil.createObj().set("id", id));
		JSONObject data = obj.getJSONObject("data");

		// 使用传入的PdfOptions对象生成PDF
		String tableStr = data.getStr("HTML_DATA", "");
		String saveDataStr = data.getStr("SAVE_DATA", "");
		String headerRow = data.getStr("TABLE_HEADER", "0");
		String tableNum = data.getStr("TABLE_NUM", "");
		String security = data.getStr("SECURITY_NAME", "");
		String tableName = data.getStr("TABLE_NUM") + "：" + data.getStr("NAME");
		if (StrUtil.isEmpty(tableNum)) {
			tableName = data.getStr("NAME");
		}
		tableName = tableName.replaceAll("/", "、").replaceAll("\\\\", "、");
		tableName += "（" + security + "）";

		// 创建临时目录
		FileUtil.mkdir(thisTempPath);
		String path = thisTempPath + File.separator + tableName + ".pdf";

		JSONObject res = new JSONObject();
		File pdfFile = new File(path);

		try {
			if (!pdfFile.exists()) {
				pdfFile.createNewFile();
			}

			// 创建PDF文档
			PdfDocument pdfDoc = new PdfDocument(new PdfWriter(path));

			// 使用PdfOptions创建Document
			Document doc = options.createDocument(pdfDoc);

			// 创建字体
			PdfFont font = PdfFontFactory.createFont(pdfFontPath + File.separator + "simhei.ttf", PdfEncodings.IDENTITY_H);

			// 添加标题
			doc.add(new Paragraph(tableName).setFirstLineIndent(8).setMultipliedLeading(1.2f).setFont(font).setBold())
					.setTextAlignment(TextAlignment.LEFT);

			// 添加表格内容
			if (StrUtil.isNotEmpty(saveDataStr)) {
				Util.tableDataToPdfTable(doc, tableStr, saveDataStr, headerRow, font);
			}

			// 关闭文档
			doc.close();

			res.set("success", true);
			res.set("data", pdfFile.getAbsolutePath());
		} catch (Exception e) {
			log.error("生成PDF失败", e);
			res.set("success", false);
			res.set("msg", e.getLocalizedMessage());
			res.set("nodeName", tableName);
		}

		return res;
	}

	/**
	 * 导出图片压缩包
	 *
	 * @param id    节点ID
	 * @param thing 事物名称
	 * @return 生成的ZIP文件
	 */
	public File exportToImgZip(String id, String thing) {
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
		FileUtil.mkdir(thisTempPath);
		JSONObject obj = Util.postTwxForObject(thing, "QueryNodeById",
				JSONUtil.createObj().set("id", id));
		JSONObject data = obj.getJSONObject("data");
		return Util.tableData2ImgZip(data, thisTempPath);
	}

	/**
	 * 生成压缩包
	 *
	 * @param downloadId 下载ID
	 * @param id         节点ID
	 * @param thing      事物名称
	 * @param creator    创建者
	 * @param module     模块
	 */
	public void generateZip(String downloadId, String id, String thing, String creator, String module) {
		String tempPath = fileUploadPath + File.separator + "confirmDownload"
				+ File.separator + creator + File.separator + module + File.separator;
		// 导出压缩文件
		JSONObject res = exportZip(id, thing, tempPath);

		// 完成文件生成
		HandSonTableUtil.GenerateFileComplete(downloadId, fileUploadPath, res);
	}

	public JSONObject exportZip(String postId, String thing) {
		log.info("开始导出ZIP文件，postId: {}, thing: {}", postId, thing);
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
		log.debug("创建临时目录: {}", thisTempPath);
		JSONObject result = exportZip(postId, thing, thisTempPath);
		log.info("ZIP文件导出完成，结果: {}", result);
		return result;
	}

	/**
	 * 导出数据压缩包
	 *
	 * @param postId       节点ID
	 * @param thing        事物名称
	 * @param thisTempPath 临时目录路径
	 * @return 导出结果
	 */
	public JSONObject exportZip(String postId, String thing, String thisTempPath) {
		log.info("开始导出ZIP文件，postId: {}, thing: {}, 临时目录: {}", postId, thing, thisTempPath);
		FileUtil.mkdir(thisTempPath);
		log.info("已创建临时目录: {}", thisTempPath);

		JSONObject res = new JSONObject();
		File zipFile = null;
		String currentTempPath = null;
		try {
			log.info("开始执行ZIP打包逻辑...");
			log.info("正在调用QueryTreeById接口获取数据，postId: {}, thing: {}", postId, thing);
			JSONObject obj = Util.postTwxForObject(thing, "QueryTreeById",
					JSONUtil.createObj().set("id", postId));
			boolean success = obj.getBool("success");
			log.info("QueryTreeById接口调用结果: {}", success);

			if (success) {
				JSONArray data = obj.getJSONArray("data");
				log.info("成功获取到数据, 数据长度: {}", data.size());

				log.info("开始获取需要复制的文件列表");
				List<String> copyFiles = Util.getCopyFiles(data);
				log.info("需要复制的文件数量: {}", copyFiles.size());
				String dataStr = data.toString();

				String folderName = System.currentTimeMillis() + "";
				currentTempPath = thisTempPath + File.separator + folderName;
				log.info("创建当前操作的临时目录: {}", currentTempPath);
				FileUtil.mkdir(currentTempPath);

				// 使用try-with-resources确保文件流被正确关闭
				log.info("开始写入data.json文件");
				try (FileWriter writer = new FileWriter(currentTempPath + File.separator + "data.json")) {
					writer.write(dataStr);
					log.info("data.json文件写入成功");
				}

				String uploadPath = Util.getFileUploadPath();
				log.info("获取到上传路径: {}", uploadPath);

				// 日志查询
				log.info("开始调用QueryLogById接口获取日志数据");
				JSONObject logObj = Util.postTwxForObject(thing, "QueryLogById",
						JSONUtil.createObj().set("id", postId));
				if (logObj.getBool("success")) {
					JSONArray logData = logObj.getJSONArray("data");
					log.info("成功获取到日志数据, 数据长度: {}", logData.size());
					try (FileWriter writer = new FileWriter(currentTempPath + File.separator + "log.json")) {
						writer.write(logData.toString());
						log.info("log.json文件写入成功");
					}
				} else {
					log.warn("获取日志数据失败, 错误信息: {}", logObj.getStr("msg"));
				}

				// 签名查询
				log.info("开始调用QuerySignById接口获取签名数据");
				JSONObject signObj = Util.postTwxForObject(thing, "QuerySignById",
						JSONUtil.createObj().set("id", postId));
				if (signObj.getBool("success")) {
					JSONArray signData = signObj.getJSONArray("data");
					log.info("成功获取到签名数据, 数据长度: {}", signData.size());
					try (FileWriter writer = new FileWriter(currentTempPath + File.separator + "sign.json")) {
						writer.write(signData.toString());
						log.info("sign.json文件写入成功");
					}
				} else {
					log.warn("获取签名数据失败, 错误信息: {}", signObj.getStr("msg"));
				}

				// 图片查询
				log.info("开始调用QueryPhotoById接口获取图片数据");
				JSONObject photoObj = Util.postTwxForObject(thing, "QueryPhotoById",
						JSONUtil.createObj().set("id", postId));
				if (photoObj.getBool("success")) {
					JSONArray photoData = photoObj.getJSONArray("data");
					log.info("成功获取到图片数据, 数据长度: {}", photoData.size());
					try (FileWriter writer = new FileWriter(currentTempPath + File.separator + "photo.json")) {
						writer.write(photoData.toString());
						log.info("photo.json文件写入成功");
					}
				} else {
					log.warn("获取图片数据失败, 错误信息: {}", photoObj.getStr("msg"));
				}

				// 复制图片文件
				log.info("开始复制图片文件, 总文件数: {}", copyFiles.size());
				int successCount = 0;
				int failCount = 0;

				for (int i = 0; i < copyFiles.size(); i++) {
					String src = copyFiles.get(i);
					log.info("正在处理第 {}/{} 个文件: {}", (i+1), copyFiles.size(), src);
					String srcFilepath = uploadPath + src;
					String descFilepath = currentTempPath + File.separator + "photo" + src;
					int retry = 0;
					boolean copied = false;

					while (retry < 3 && !copied) {
						try {
							File srcFile = new File(srcFilepath);

							// 新增文件可用性检查
							if (!isFileAvailable(srcFile)) {
								log.info("文件被占用，等待重试 ({}/3)... 文件路径: {}", (retry+1), srcFilepath);
								Thread.sleep(1000);
								retry++;
								continue;
							}

							log.info("开始复制文件: {} -> {}", srcFilepath, descFilepath);
							// 使用NIO的Files.copy替代FileUtil.copy
							Path source = Paths.get(srcFilepath);
							Path target = Paths.get(descFilepath);

							// 创建目标目录
							Files.createDirectories(target.getParent());
							log.info("已创建目标目录: {}", target.getParent());

							// 尝试以独占方式打开文件
							try (FileChannel inChannel = FileChannel.open(source, StandardOpenOption.READ);
								 FileChannel outChannel = FileChannel.open(target,
										 StandardOpenOption.CREATE,
										 StandardOpenOption.WRITE,
										 StandardOpenOption.TRUNCATE_EXISTING)) {

								long size = inChannel.size();
								log.info("开始传输文件内容, 文件大小: {} 字节", size);
								inChannel.transferTo(0, size, outChannel);
								copied = true;
								successCount++;
								log.info("文件复制成功: {}", descFilepath);
							}
						} catch (AccessDeniedException e) {
							log.info("文件被占用，等待重试 ({}/3)... 文件路径: {} 错误: {}", (retry+1), srcFilepath, e.getMessage());
							Thread.sleep(1000);
							retry++;
						} catch (Exception e) {
							log.error("复制文件出错, 文件路径: {}, 原因: {}", srcFilepath, e.getMessage(), e);
							break;
						}
					}

					if (!copied) {
						log.error("文件复制失败（已重试{}次）: {}", retry, srcFilepath);
						failCount++;
					}
				}

				log.info("文件复制完成, 成功: {}, 失败: {}", successCount, failCount);

				log.info("开始创建ZIP文件: {}", currentTempPath);
				zipFile = ZipUtil.zip(currentTempPath, StandardCharsets.UTF_8);
				log.info("ZIP文件创建成功: {}, 文件大小: {} 字节", zipFile.getAbsolutePath(), zipFile.length());

				log.info("开始加密ZIP文件");
				File encryptFile = new File(zipFile.getParent() + File.separator + System.currentTimeMillis());
				Util.encryptFile(zipFile, encryptFile);
				log.info("ZIP文件加密成功: {}, 文件大小: {} 字节", encryptFile.getAbsolutePath(), encryptFile.length());

				res.set("success", true);
				res.set("data", encryptFile.getAbsolutePath());
				log.info("导出ZIP文件操作成功完成, 结果文件: {}", encryptFile.getAbsolutePath());
			} else {
				log.error("获取数据失败, 错误信息: {}", obj.getStr("msg"));
				res.set("success", false);
				res.set("msg", "获取数据失败: " + obj.getStr("msg"));
			}
		} catch (Exception e) {
			log.error("exportZip方法发生异常: ", e);
			String msg = e.getMessage();
			res.set("success", false);
			res.set("msg", msg);
			log.error("导出ZIP文件操作失败, 原因: {}", msg);
		} finally {
			log.info("ZIP文件打包完成，准备返回结果...");
			// 确保文件已经不再使用后再删除
			if (zipFile != null) {
				try {
					log.info("准备删除临时ZIP文件: {}", zipFile.getAbsolutePath());
					Thread.sleep(100); // 给一点时间确保文件操作完成
					FileUtil.del(zipFile);
					log.info("临时ZIP文件删除成功");
				} catch (Exception e) {
					log.warn("删除临时zip文件失败: {}", zipFile.getAbsolutePath(), e);
				}
			}
			if (currentTempPath != null) {
				try {
					log.info("准备删除临时目录: {}", currentTempPath);
					Thread.sleep(100); // 给一点时间确保文件操作完成
					FileUtil.del(currentTempPath);
					log.info("临时目录删除成功");
				} catch (Exception e) {
					log.warn("删除临时目录失败: {}", currentTempPath, e);
				}
			}
		}

		log.info("exportZip方法执行完毕, 返回结果: {}", res);
		return res;
	}


	/**
	 * 导出多个PDF（支持自定义PDF选项）
	 *
	 * @param postId   节点ID
	 * @param postPId  父节点ID
	 * @param thing    Thing名称
	 * @param options  PDF导出选项对象
	 * @return PDF文件路径
	 */
	public String exportMore(String postId, String postPId, String thing, PdfOptions options) {
		JSONObject res = exportMorePdf(postId, postPId, thing, tempPath, pdfFontPath, true, options);
		return res.getStr("data");
	}

	/**
	 * 从PDF中提取页码信息
	 *
	 * @param pdfPath PDF文件路径
	 * @param bTables B表信息的JSON字符串
	 * @return 包含页码信息的JSONObject
	 */
	public JSONObject extractPdfPageNumbers(String pdfPath, String bTables) {
		JSONObject result = new JSONObject();
		try {
			// 解析B表信息
			JSONArray bTablesArray = JSONUtil.parseArray(bTables);

			// 记录所有B表的名称和表号，用于后续匹配
			Map<String, String> bTableMap = new HashMap<>(); // 表名 -> 表号

			// 创建表号到表名的映射（考虑表号可能重复的情况）
			Map<String, List<String>> bTableNumMap = new HashMap<>(); // 表号 -> 表名列表

			// 创建表号+表名的组合键映射，用于精确匹配
			Map<String, String> bTableCombinedMap = new HashMap<>(); // "表号:表名" -> 表名

			for (int i = 0; i < bTablesArray.size(); i++) {
				JSONObject bTable = bTablesArray.getJSONObject(i);
				String tableName = bTable.getStr("NAME");
				String tableNum = bTable.getStr("TABLE_NUM");

				// 记录表名到表号的映射
				bTableMap.put(tableName, tableNum);

				// 记录表号到表名列表的映射（处理表号重复的情况）
				if (!bTableNumMap.containsKey(tableNum)) {
					bTableNumMap.put(tableNum, new ArrayList<>());
				}
				bTableNumMap.get(tableNum).add(tableName);

				// 记录组合键映射
				String combinedKey = tableNum + ":" + tableName;
				bTableCombinedMap.put(combinedKey, tableName);
			}

			// 创建页码映射表
			JSONObject pageNumbers = new JSONObject();

			// 打开PDF文档
			try (PdfReader reader = new PdfReader(pdfPath);
				 PdfDocument pdfDoc = new PdfDocument(reader)) {

				// 获取PDF文档的大纲（目录结构）
				PdfOutline rootOutline = pdfDoc.getOutlines(false);

				// 如果没有大纲，则无法提取页码
				if (rootOutline == null || rootOutline.getAllChildren().isEmpty()) {
					result.set("success", false);
					result.set("msg", "PDF文档没有大纲结构，无法提取页码");
					return result;
				}

				// 记录大纲项数量
				List<PdfOutline> allOutlines = rootOutline.getAllChildren();

				// 创建一个列表存储所有大纲项（包括子项）
				List<Map<String, Object>> allOutlineItems = new ArrayList<>();

				// 递归遍历所有大纲项（包括子项）
				for (PdfOutline outline : allOutlines) {
					processOutline(outline, allOutlineItems, 1);
				}

				// 遍历所有大纲项，提取页码信息
				for (Map<String, Object> item : allOutlineItems) {
					String title = (String) item.get("title");
					int pageNumber = (int) item.get("pageNumber");

					// 提取B表名称
					Map<String, String> extractResult = extractTableNameAndNum(title);
					String tableName = extractResult.get("tableName");
					String tableNum = extractResult.get("tableNum");

					if (tableName != null) {
						// 1. 首先尝试使用表号和表名的组合进行精确匹配
						if (tableNum != null) {
							String combinedKey = tableNum + ":" + tableName;
							if (bTableCombinedMap.containsKey(combinedKey)) {
								String matchedTableName = bTableCombinedMap.get(combinedKey);
								pageNumbers.set(matchedTableName, pageNumber);
								continue; // 匹配成功，处理下一个大纲项
							}
						}

						// 2. 如果组合匹配失败，尝试通过表号和表名进行匹配
						if (tableNum != null && bTableNumMap.containsKey(tableNum)) {
							List<String> possibleTableNames = bTableNumMap.get(tableNum);

							// 如果表号只对应一个表名，直接使用
							if (possibleTableNames.size() == 1) {
								String matchedTableName = possibleTableNames.get(0);
								pageNumbers.set(matchedTableName, pageNumber);
								continue; // 匹配成功，处理下一个大纲项
							}

							// 如果表号对应多个表名，尝试找到最匹配的那个
							boolean tableNameMatched = false;
							for (String possibleTableName : possibleTableNames) {
								// 如果表名完全匹配
								if (possibleTableName.equals(tableName)) {
									pageNumbers.set(possibleTableName, pageNumber);
									tableNameMatched = true;
									break;
								}

								// 如果表名部分匹配（包含关系）
								if (possibleTableName.contains(tableName) || tableName.contains(possibleTableName)) {
									pageNumbers.set(possibleTableName, pageNumber);
									tableNameMatched = true;
									break;
								}
							}

							if (tableNameMatched) {
								continue; // 匹配成功，处理下一个大纲项
							}
						}

						// 3. 如果通过表号匹配失败，尝试直接通过表名匹配
						if (bTableMap.containsKey(tableName)) {
							pageNumbers.set(tableName, pageNumber);
							continue; // 匹配成功，处理下一个大纲项
						}

						// 4. 最后尝试模糊匹配
						for (String key : bTableMap.keySet()) {
							// 如果B表名称包含提取的表名，或者提取的表名包含B表名称
							if (key.contains(tableName) || tableName.contains(key)) {
								pageNumbers.set(key, pageNumber);
								break;
							}
						}
					}
				}

				// 检查是否所有B表都找到了页码
				for (int i = 0; i < bTablesArray.size(); i++) {
					JSONObject bTable = bTablesArray.getJSONObject(i);
					String tableName = bTable.getStr("NAME");

					// 如果没有找到页码，设置为null
					if (!pageNumbers.containsKey(tableName)) {
						pageNumbers.set(tableName, null);
					}
				}

				// 添加封面页码
				if (!pageNumbers.containsKey("封面")) {
					pageNumbers.set("封面", 1);
				}

				// 添加目录页码
				if (!pageNumbers.containsKey("目录")) {
					// 目录通常在第2页
					pageNumbers.set("目录", 2);
				}

				result.set("success", true);
				result.set("data", pageNumbers);
				result.set("msg", "页码提取成功");
			}
		} catch (Exception e) {
			log.error("提取PDF页码失败", e);
			result.set("success", false);
			result.set("msg", "提取PDF页码失败：" + e.getMessage());
		}

		return result;
	}

	/**
	 * 递归处理大纲项及其子项
	 *
	 * @param outline 当前大纲项
	 * @param allOutlineItems 存储所有大纲项的列表
	 * @param level 当前大纲项的级别
	 */
	private void processOutline(PdfOutline outline, List<Map<String, Object>> allOutlineItems, int level) {
		if (outline == null) {
			return;
		}

		String title = outline.getTitle();
		int pageNumber = getOutlinePageNumber(outline);

		if (pageNumber > 0) {
			Map<String, Object> item = new HashMap<>();
			item.put("title", title);
			item.put("pageNumber", pageNumber);
			item.put("level", level);
			allOutlineItems.add(item);
		}

		// 递归处理子项
		List<PdfOutline> children = outline.getAllChildren();
		if (children != null && !children.isEmpty()) {
			for (PdfOutline child : children) {
				processOutline(child, allOutlineItems, level + 1);
			}
		}
	}

	/**
	 * 从大纲标题中提取表名和表号
	 *
	 * @param title 大纲标题
	 * @return 包含表名和表号的Map
	 */
	private Map<String, String> extractTableNameAndNum(String title) {
		Map<String, String> result = new HashMap<>();
		result.put("tableName", null);
		result.put("tableNum", null);

		if (title == null || title.isEmpty()) {
			return result;
		}

		// 处理特殊情况：如果标题就是"封面"或"目录"
		if (title.contains("封面")) {
			result.put("tableName", "封面");
			return result;
		}

		if (title.contains("目录")) {
			result.put("tableName", "目录");
			return result;
		}

		// 处理特殊情况：如果标题包含"卫星产品履历书"或"卫星产品证明书"
		if (title.contains("卫星产品履历书")) {
			result.put("tableName", "卫星产品履历书");
			return result;
		}

		if (title.contains("卫星产品证明书")) {
			result.put("tableName", "卫星产品证明书");
			return result;
		}

		// 尝试匹配表号模式：表b5-1-1
		Pattern tableNumPattern = Pattern.compile("表([a-zA-Z0-9\\-]+)");
		Matcher tableNumMatcher = tableNumPattern.matcher(title);
		if (tableNumMatcher.find()) {
			// 例如：表b5-1-1
			String tableNum = tableNumMatcher.group(0); // 完整表号
			result.put("tableNum", tableNum);
		}

		// 标题格式通常为 "表号:表名（密级）" 或 "表号：表名（内部）"
		int colonIndex = title.indexOf(':');
		if (colonIndex < 0) {
			// 尝试中文冒号
			colonIndex = title.indexOf('：');
		}

		int bracketIndex = title.lastIndexOf('（');
		if (bracketIndex < 0) {
			// 尝试英文括号
			bracketIndex = title.lastIndexOf('(');
		}

		// 如果找到了冒号和括号
		if (colonIndex > 0 && bracketIndex > colonIndex) {
			String tableName = title.substring(colonIndex + 1, bracketIndex).trim();
			result.put("tableName", tableName);
			return result;
		}

		// 如果只有冒号没有括号，取冒号后面的所有内容
		if (colonIndex > 0) {
			String tableName = title.substring(colonIndex + 1).trim();
			result.put("tableName", tableName);
			return result;
		}

		// 如果没有冒号但有括号，取括号前面的内容
		if (bracketIndex > 0) {
			// 如果前面有表号部分，需要去掉
			String beforeBracket = title.substring(0, bracketIndex).trim();
			String tableName = beforeBracket;

			// 如果包含表号，去掉表号部分
			tableNumMatcher = tableNumPattern.matcher(beforeBracket);
			if (tableNumMatcher.find()) {
				int endIndex = tableNumMatcher.end();
				if (endIndex < beforeBracket.length()) {
					tableName = beforeBracket.substring(endIndex).trim();
					// 去掉可能的冒号
					if (tableName.startsWith(":") || tableName.startsWith("：")) {
						tableName = tableName.substring(1).trim();
					}
				}
			}

			result.put("tableName", tableName);
			return result;
		}

		// 如果没有特殊格式，尝试从标题中提取有意义的部分
		// 例如：从"表b5-1-1:发射场精度（内部）"提取"发射场精度"
		tableNumMatcher = tableNumPattern.matcher(title);
		if (tableNumMatcher.find()) {
			int endIndex = tableNumMatcher.end();
			if (endIndex < title.length()) {
				String restPart = title.substring(endIndex).trim();
				// 去掉可能的冒号
				if (restPart.startsWith(":") || restPart.startsWith("：")) {
					restPart = restPart.substring(1).trim();
				}
				// 去掉可能的括号部分
				int restBracketIndex = restPart.lastIndexOf('（');
				if (restBracketIndex < 0) {
					restBracketIndex = restPart.lastIndexOf('(');
				}
				if (restBracketIndex > 0) {
					restPart = restPart.substring(0, restBracketIndex).trim();
				}

				if (!restPart.isEmpty()) {
					result.put("tableName", restPart);
					return result;
				}
			}
		}

		// 如果所有方法都失败，返回整个标题作为表名
		result.put("tableName", title.trim());
		return result;
	}

	/**
	 * 获取大纲项对应的页码
	 *
	 * @param outline 大纲项
	 * @return 页码
	 */
	private int getOutlinePageNumber(PdfOutline outline) {
		try {
			// 获取大纲项的目标
			PdfDestination destination = null;
			try {
				destination = outline.getDestination();
			} catch (Exception e) {
				// 忽略异常，尝试其他方法
			}

			if (destination != null) {
				PdfObject destObject = destination.getPdfObject();
				if (destObject != null && destObject.isArray()) {
					PdfArray destArray = (PdfArray) destObject;
					if (!destArray.isEmpty()) {
						PdfDictionary page = destArray.getAsDictionary(0);
						if (page != null) {
							// 获取文档对象
							PdfDocument doc = null;
							try {
								// 尝试多种可能的字段名称
								java.lang.reflect.Field docField = null;

								// 尝试获取PdfOutline类的所有字段，查找PdfDocument类型的字段
								for (java.lang.reflect.Field field : PdfOutline.class.getDeclaredFields()) {
									field.setAccessible(true);
									if (field.getType().equals(PdfDocument.class)) {
										docField = field;
										break;
									}
								}

								// 如果找到了字段，获取文档对象
								if (docField != null) {
									docField.setAccessible(true);
									doc = (PdfDocument) docField.get(outline);
								} else {
									// 尝试常见的字段名称
									String[] possibleFieldNames = {"pdfDocument", "document", "doc"};
									for (String fieldName : possibleFieldNames) {
										try {
											docField = PdfOutline.class.getDeclaredField(fieldName);
											docField.setAccessible(true);
											doc = (PdfDocument) docField.get(outline);
											if (doc != null) break;
										} catch (Exception ex) {
											// 继续尝试下一个字段名
										}
									}
								}
							} catch (Exception e) {
								log.debug("通过反射获取文档对象失败: {}", e.getMessage());
							}

							if (doc != null) {
								return doc.getPageNumber(page);
							}
						}
					}
				}
			}

			// 如果没有目标，尝试从动作中获取
			PdfAction action = null;
			try {
				// 在iText 7.2.6中，PdfOutline没有直接的getAction方法和getContent方法
				// 使用反射获取PdfOutline的内部字典
				PdfDictionary outlineDict = null;
				try {
					// 尝试多种可能的字段名称
					java.lang.reflect.Field dictField = null;

					// 尝试获取PdfOutline类的所有字段，查找PdfDictionary类型的字段
					for (java.lang.reflect.Field field : PdfOutline.class.getDeclaredFields()) {
						field.setAccessible(true);
						if (field.getType().equals(PdfDictionary.class)) {
							dictField = field;
							break;
						}
					}

					// 如果找到了字段，获取字典对象
					if (dictField != null) {
						dictField.setAccessible(true);
						outlineDict = (PdfDictionary) dictField.get(outline);
					} else {
						// 尝试常见的字段名称
						String[] possibleFieldNames = {"dictionary", "dict", "outlineDict", "content"};
						for (String fieldName : possibleFieldNames) {
							try {
								dictField = PdfOutline.class.getDeclaredField(fieldName);
								dictField.setAccessible(true);
								outlineDict = (PdfDictionary) dictField.get(outline);
								if (outlineDict != null) break;
							} catch (Exception ex) {
								// 继续尝试下一个字段名
							}
						}
					}
				} catch (Exception e) {
					// 忽略异常
				}

				if (outlineDict != null && outlineDict.containsKey(PdfName.A)) {
					PdfObject actionObj = outlineDict.get(PdfName.A);
					if (actionObj != null && actionObj.isDictionary()) {
						action = new PdfAction((PdfDictionary) actionObj);
					}
				}
			} catch (Exception e) {
				// 忽略异常
			}

			if (action != null) {
				// 检查动作类型
				PdfName actionType = null;
				try {
					// 在iText 7.2.6中，PdfAction没有直接的getType方法
					// 从PdfAction的字典中获取S属性作为类型
					PdfDictionary actionDict = action.getPdfObject();
					if (actionDict != null && actionDict.containsKey(PdfName.S)) {
						actionType = actionDict.getAsName(PdfName.S);
					}
				} catch (Exception e) {
					// 忽略异常
				}

				if (PdfName.GoTo.equals(actionType)) {
					// 获取目标对象
					PdfObject dest = null;
					try {
						dest = action.getPdfObject().get(PdfName.D);
					} catch (Exception e) {
						// 忽略异常
					}

					if (dest != null && dest.isArray()) {
						PdfArray destArray = (PdfArray) dest;
						if (!destArray.isEmpty()) {
							PdfDictionary page = destArray.getAsDictionary(0);
							if (page != null) {
								// 获取文档对象
								PdfDocument doc = null;
								try {
									// 尝试多种可能的字段名称
									java.lang.reflect.Field docField = null;

									// 尝试获取PdfOutline类的所有字段，查找PdfDocument类型的字段
									for (java.lang.reflect.Field field : PdfOutline.class.getDeclaredFields()) {
										field.setAccessible(true);
										if (field.getType().equals(PdfDocument.class)) {
											docField = field;
											break;
										}
									}

									// 如果找到了字段，获取文档对象
									if (docField != null) {
										docField.setAccessible(true);
										doc = (PdfDocument) docField.get(outline);
									} else {
										// 尝试常见的字段名称
										String[] possibleFieldNames = {"pdfDocument", "document", "doc"};
										for (String fieldName : possibleFieldNames) {
											try {
												docField = PdfOutline.class.getDeclaredField(fieldName);
												docField.setAccessible(true);
												doc = (PdfDocument) docField.get(outline);
												if (doc != null) break;
											} catch (Exception ex) {
												// 继续尝试下一个字段名
											}
										}
									}
								} catch (Exception e) {
									// 忽略异常
								}

								if (doc != null) {
									return doc.getPageNumber(page);
								}
							}
						}
					}
				}
			}
		} catch (Exception e) {
			log.error("获取大纲页码失败", e);
		}

		return -1;
	}

	/**
	 * 导出多个PDF文件（使用默认PDF选项）
	 */
	public JSONObject exportMorePdf(String postId, String postPId, String thing, String tempPath, String fontPath, boolean isOutImg) {
		// 使用默认选项调用带PdfOptions参数的重载方法
		return exportMorePdf(postId, postPId, thing, tempPath, fontPath, isOutImg, new PdfOptions("A4", "landscape"));
	}

	/**
	 * 导出多个PDF文件（支持自定义PDF选项）
	 *
	 * @param postId          节点ID
	 * @param postPId         父节点ID
	 * @param thing           事物名称
	 * @param tempPath        临时路径
	 * @param fontPath        字体路径
	 * @param isOutImg        是否输出图片
	 * @param options         PDF导出选项对象
	 * @return 导出结果
	 */
	public JSONObject exportMorePdf(String postId, String postPId, String thing, String tempPath, String fontPath, boolean isOutImg, PdfOptions options) {
		JSONObject res = new JSONObject();
		try {
			String path = tempPath + File.separator + System.currentTimeMillis() + ".pdf";
			File f = new File(path);
			if (!f.exists()) {
				File parent = f.getParentFile();
				if (parent != null && !parent.exists()) {
					if (!parent.mkdirs()) {
						log.error("创建目录失败: {}", parent.getAbsolutePath());
						throw new IOException("无法创建目录: " + parent.getAbsolutePath());
					}
				}
				try {
					if (!f.createNewFile()) {
						log.error("文件已存在: {}", f.getAbsolutePath());
						throw new IOException("文件已存在: " + f.getAbsolutePath());
					}
				} catch (IOException e) {
					log.error("创建文件失败: {}", f.getAbsolutePath(), e);
					throw e;
				}
			}

			JSONObject obj = Util.postTwxForObject(thing, "QueryTreeById",
					JSONUtil.createObj().set("id", postId));
			JSONArray errorNode = new JSONArray();
			boolean success = obj.getBool("success");
			if (success) {
				PdfDocument pdfDoc = new PdfDocument(new PdfWriter(path));
				PdfOutline rootOutline = pdfDoc.getOutlines(false);

				// 使用PdfOptions创建Document
				Document doc = options.createDocument(pdfDoc);
				PdfFont font = PdfFontFactory.createFont(fontPath + File.separator + "simhei.ttf", PdfEncodings.IDENTITY_H);
				pdfDoc.addEventHandler(PdfDocumentEvent.END_PAGE, new PageMarker(font));

				JSONArray data = obj.getJSONArray("data");
				List<TreeNode> nodes = TreeNode.fromJsonArray(data);
				List<TreeNode> nodeList = TreeUtil.build(nodes, postPId);
				dealNodes(errorNode, pdfDoc, doc, rootOutline, nodeList, font, isOutImg);

				if (pdfDoc.getNumberOfPages() != 0) {
					pdfDoc.removePage(pdfDoc.getNumberOfPages());
				}
				doc.close();
				pdfDoc.close();

				if (errorNode.isEmpty()) {
					res.set("success", true);
					if (!nodes.isEmpty()) {
						String tableNum = nodes.get(0).getTableNum();
						String tableName = nodes.get(0).getName();
						if (StrUtil.isNotBlank(tableNum)) {
							tableName = tableNum + "：" + tableName;
						}
						tableName += "（" + nodes.get(0).getSecurity() + "）";
						f = FileUtil.rename(f, tableName, true, true);
					}
					res.set("data", f.getAbsolutePath());
				} else {
					res.set("success", false);
					res.set("msg", HandSonTableUtil.exportPdfErrorMsg(errorNode));
				}
			} else {
				String msg = obj.getStr("msg");
				res.set("success", false);
				res.set("msg", msg);
			}
		} catch (Exception e) {
			log.error("生成PDF文件失败", e);
			res.set("success", false);
			res.set("msg", e.getMessage());
		}
		return res;
	}

	public void dealNodes(JSONArray errorNode, PdfDocument pdfDoc, Document doc, PdfOutline rootOutline, List<TreeNode> nodes,
						  PdfFont font, boolean isOutImg) {
		if (nodes == null || nodes.isEmpty()) {
			return;
		}

		for (TreeNode node : nodes) {
			if (node == null) {
				continue;
			}

			String nodeType = node.getType();
			if (nodeType == null) {
				continue;
			}

			switch (nodeType) {
				case "model":
					if (node.getChildren() != null && !node.getChildren().isEmpty()) {
						dealNodes(errorNode, pdfDoc, doc, rootOutline, node.getChildren(), font, isOutImg);
					}
					break;
				case "project": {
					String title = node.getSort() + "-" +
							(node.getName() != null ? node.getName() : "");
					doc.add(new Paragraph(title).setFirstLineIndent(0).setMultipliedLeading(1.2f).setFont(font).setBold())
							.setTextAlignment(TextAlignment.LEFT);
					PdfOutline projectOutLine = rootOutline.addOutline(title);
					projectOutLine.addAction(PdfAction.createGoTo(PdfExplicitDestination.createFitH(pdfDoc.getLastPage(),
							pdfDoc.getLastPage().getPageSize().getTop())));
					if (node.getChildren() != null && !node.getChildren().isEmpty()) {
						dealNodes(errorNode, pdfDoc, doc, projectOutLine, node.getChildren(), font, isOutImg);
					}
					break;
				}
				case "a": {
					String tableNum = node.getTableNum() != null ? node.getTableNum() : "";
					String name = node.getName() != null ? node.getName() : "";
					String security = node.getSecurity() != null ? node.getSecurity() : "";
					String title = tableNum + ":" + name + "（" + security + "）";

					doc.add(new Paragraph(title).setFirstLineIndent(8).setMultipliedLeading(1.2f).setFont(font).setBold())
							.setTextAlignment(TextAlignment.LEFT);
					PdfOutline aOutLine = rootOutline.addOutline(title);
					aOutLine.addAction(PdfAction.createGoTo(PdfExplicitDestination.createFitH(pdfDoc.getLastPage(),
							pdfDoc.getLastPage().getPageSize().getTop())));
					boolean flag = addTable(doc, font, node, isOutImg);
					if (!flag) {
						errorNode.add(title);
					}
					if (node.getChildren() != null && !node.getChildren().isEmpty()) {
						dealNodes(errorNode, pdfDoc, doc, aOutLine, node.getChildren(), font, isOutImg);
					}
					break;
				}
				case "b": {
					String tableNum = node.getTableNum() != null ? node.getTableNum() : "";
					String name = node.getName() != null ? node.getName() : "";
					String security = node.getSecurity() != null ? node.getSecurity() : "";
					String title = tableNum + ":" + name + "（" + security + "）";

					doc.add(new Paragraph(title).setFirstLineIndent(16).setMultipliedLeading(1.2f).setFont(font).setBold())
							.setTextAlignment(TextAlignment.LEFT);

					PdfOutline bOutLine = rootOutline.addOutline(title);
					bOutLine.addAction(PdfAction.createGoTo(PdfExplicitDestination.createFitH(pdfDoc.getLastPage(),
							pdfDoc.getLastPage().getPageSize().getTop())));
					boolean flag = false;
					if ("pdf".equalsIgnoreCase(node.getFileFormat()) && StrUtil.isNotBlank(node.getFilePath())) {
						String filePath = fileUploadPath + node.getFilePath();
						try {
							PdfDocument pdfToInsert = new PdfDocument(new PdfReader(filePath));
							for (int pageNumber = 1; pageNumber <= pdfToInsert.getNumberOfPages(); pageNumber++) {
								PdfPage page = pdfToInsert.getPage(pageNumber);
								PdfFormXObject pdfFormXObject = page.copyAsFormXObject(pdfDoc);
								Image image = new Image(pdfFormXObject);
								image.setHorizontalAlignment(HorizontalAlignment.CENTER);
								if (image.getImageHeight() > image.getImageWidth()) {
									image.setRotationAngle(Math.toRadians(90));
								}
								doc.add(image);
							}
							pdfToInsert.close();
							doc.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
							flag = true;
						} catch (IOException e) {
							log.error("插入PDF文件失败: {}", filePath, e);
						}
					} else {
						flag = addTable(doc, font, node, isOutImg);
					}
					if (!flag) {
						errorNode.add(title);
					}

					if (node.getChildren() != null && !node.getChildren().isEmpty()) {
						dealNodes(errorNode, pdfDoc, doc, bOutLine, node.getChildren(), font, isOutImg);
					}
					break;
				}
			}
		}
	}

	public static boolean addTable(Document doc, PdfFont font, TreeNode node, boolean isOutImg) {
		if (node == null) {
			return false;
		}

		String saveDataStr = node.getSaveData();
		boolean flag = true;
		if (StrUtil.isNotBlank(saveDataStr)) {
			String tableStr = node.getSignHtml();
			String headerRow = node.getTableHeader();
			try {
				Util.tableDataToPdfTable(doc, tableStr, saveDataStr, headerRow, font, isOutImg);
				doc.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
			} catch (Exception e) {
				log.error("添加表格失败: {}", e.getMessage(), e);
				flag = false;
			}
		}
		return flag;
	}

	/**
	 * 生成多个PDF文件（支持自定义PDF选项）
	 *
	 * @param id         节点ID
	 * @param pid        父节点ID
	 * @param thing      事物名称
	 * @param creator    创建者
	 * @param module     模块名称
	 * @param downloadId 下载ID
	 * @param options    PDF导出选项对象
	 * @return 生成结果
	 */
	public JSONObject generateMorePdf(String id, String pid, String thing, String creator, String module, String downloadId, PdfOptions options) {
		// 创建临时目录
		String tempDownloadPath = fileUploadPath + File.separator + "confirmDownload"
				+ File.separator + creator + File.separator + module + File.separator;

		// 生成PDF文件，传入自定义选项
		JSONObject result = exportMorePdf(id, pid, thing, tempDownloadPath, pdfFontPath, true, options);

		// 更新下载状态
		HandSonTableUtil.GenerateFileComplete(downloadId, fileUploadPath, result);

		return result;
	}

	/**
	 * 导入大型ZIP文件
	 *
	 * @param filePath ZIP文件路径
	 * @param thing    事物名称
	 * @param tableId  表格ID
	 * @param tablePId 表格父ID
	 * @param srcType  源类型
	 * @return 导入结果
	 */
	public JSONObject importBigZip(String filePath, String thing, String tableId, String tablePId, String srcType) {
		log.info("开始导入大型ZIP文件，文件路径: {}, 事物名称: {}, 表格ID: {}, 表格父ID: {}, 源类型: {}", filePath, thing, tableId, tablePId, srcType);
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis();
		JSONObject result = new JSONObject();
		result.set("success", true);

		log.info("创建临时文件夹: {}", thisTempPath);
		try {
			// 尝试解压文件
			log.info("尝试解压文件: {}", filePath);
			try {
				CommonUtil.customUnzip(filePath, thisTempPath);
				log.info("文件 {} 普通解压成功，解压到: {}", filePath, thisTempPath);
			} catch (Exception e) {
				log.warn("文件 {} 普通解压失败，尝试解密后解压", filePath, e);
				// 如果普通解压失败，尝解密后解压
				String decryptFile = filePath + "decrypt";
				log.info("解密文件: {} -> {}", filePath, decryptFile);
				Util.decryptFile(FileUtil.file(filePath), FileUtil.file(decryptFile));
				log.info("文件 {} 解密完成", filePath);
				CommonUtil.customUnzip(decryptFile, thisTempPath);
				log.info("文件 {} 解密后解压成功，解压到: {}", decryptFile, thisTempPath);
			}

			// 处理照片文件
			String srcPhotoPath = thisTempPath + File.separator + "photo";
			log.info("检查照片文件夹是否存在: {}", srcPhotoPath);
			if (FileUtil.exist(srcPhotoPath)) {
				log.info("照片文件夹 {} 存在，开始处理照片", srcPhotoPath);
				File[] photos = new File(srcPhotoPath).listFiles();
				if (photos != null) {
					log.info("照片文件夹 {} 包含 {} 个文件或文件夹", srcPhotoPath, photos.length);
					for (File f : photos) {
						if (FileUtil.isDirectory(f)) {
							log.info("复制照片文件夹: {} -> {}", f.getAbsolutePath(), fileUploadPath);
							try {
								FileUtil.copy(f.getAbsolutePath(), fileUploadPath, true);
								log.info("照片文件夹 {} 复制成功", f.getAbsolutePath());
							} catch (Exception e) {
								// 记录错误但继续处理
								log.error("复制文件 {} 失败", f.getAbsolutePath(), e);
							}
						}
					}
				} else {
					log.info("照片文件夹 {} 为空", srcPhotoPath);
				}
			} else {
				log.info("照片文件夹 {} 不存在，跳过照片处理", srcPhotoPath);
			}

			// 导入数据JSON
			String dataPath = thisTempPath + File.separator + "data.json";
			int resultId;
			int rootId;
			log.info("检查数据JSON文件是否存在: {}", dataPath);
			if (FileUtil.exist(dataPath)) {
				log.info("数据JSON文件 {} 存在，开始导入数据", dataPath);
				String dataStr = FileUtil.readString(dataPath, StandardCharsets.UTF_8);
				JSONObject obj = Util.postTwxForObject(thing, "ImportModelData",
						JSONUtil.createObj()
								.set("id", tableId)
								.set("pid", tablePId)
								.set("type", srcType)
								.set("dataStr", dataStr));
				log.info("调用 ImportModelData 接口完成，接口返回: {}", obj);
				if (obj.getBool("success")) {
					resultId = obj.getJSONObject("data").getInt("resultId");
					rootId = obj.getJSONObject("data").getInt("rootId");
					log.info("数据导入成功，resultId: {}, rootId: {}", resultId, rootId);
				} else {
					log.error("数据导入失败，接口返回错误信息: {}", obj.getStr("msg"));
					return JSONUtil.createObj()
							.set("success", false)
							.set("msg", obj.getStr("msg"));
				}
			} else {
				log.error("导入失败，原因：未发现数据JSON文件: {}", dataPath);
				return JSONUtil.createObj()
						.set("success", false)
						.set("msg", "导入失败，原因：未发现数据JSON文件！");
			}

			// 导入照片数据
			String photoPath = thisTempPath + File.separator + "photo.json";
			log.info("检查照片数据JSON文件是否存在: {}", photoPath);
			if (FileUtil.exist(photoPath)) {
				log.info("照片数据JSON文件 {} 存在，开始导入照片数据", photoPath);
				String photoStr = FileUtil.readString(photoPath, StandardCharsets.UTF_8);
				JSONObject obj = Util.postTwxForObject(thing, "ImportPhotoData",
						JSONUtil.createObj()
								.set("id", resultId)
								.set("dataStr", photoStr));
				log.info("调用 ImportPhotoData 接口完成，接口返回: {}", obj);
				if (!obj.getBool("success")) {
					log.error("照片数据导入失败，接口返回错误信息: {}", obj.getStr("msg"));
					return JSONUtil.createObj()
							.set("success", false)
							.set("msg", obj.getStr("msg"));
				} else {
					log.info("照片数据导入成功");
				}
			} else {
				log.info("照片数据JSON文件 {} 不存在，跳过照片数据导入", photoPath);
			}

			// 导入签名数据
			String signPath = thisTempPath + File.separator + "sign.json";
			log.info("检查签名数据JSON文件是否存在: {}", signPath);
			if (FileUtil.exist(signPath)) {
				log.info("签名数据JSON文件 {} 存在，开始导入签名数据", signPath);
				String signStr = FileUtil.readString(signPath, StandardCharsets.UTF_8);
				JSONObject obj = Util.postTwxForObject(thing, "ImportSignData",
						JSONUtil.createObj()
								.set("id", resultId)
								.set("dataStr", signStr));
				log.info("调用 ImportSignData 接口完成，接口返回: {}", obj);
				if (!obj.getBool("success")) {
					log.error("签名数据导入失败，接口返回错误信息: {}", obj.getStr("msg"));
					return JSONUtil.createObj()
							.set("success", false)
							.set("msg", obj.getStr("msg"));
				} else {
					log.info("签名数据导入成功");
				}
			} else {
				log.info("签名数据JSON文件 {} 不存在，跳过签名数据导入", signPath);
			}

			// 导入日志数据
			String logPath = thisTempPath + File.separator + "log.json";
			log.info("检查日志数据JSON文件是否存在: {}", logPath);
			if (FileUtil.exist(logPath)) {
				log.info("日志数据JSON文件 {} 存在，开始导入日志数据", logPath);
				String logStr = FileUtil.readString(logPath, StandardCharsets.UTF_8);
				JSONObject obj = Util.postTwxForObject(thing, "ImportLogData",
						JSONUtil.createObj()
								.set("id", resultId)
								.set("dataStr", logStr));
				log.info("调用 ImportLogData 接口完成，接口返回: {}", obj);
				if (!obj.getBool("success")) {
					log.error("日志数据导入失败，接口返回错误信息: {}", obj.getStr("msg"));
					return JSONUtil.createObj()
							.set("success", false)
							.set("msg", obj.getStr("msg"));
				} else {
					log.info("日志数据导入成功");
				}
			} else {
				log.info("日志数据JSON文件 {} 不存在，跳过日志数据导入", logPath);
			}

			result.set("rootId", rootId);
			log.info("大型ZIP文件导入完成，rootId: {}, 结果: {}", rootId, result);
			return result;
		} catch (Exception e) {
			log.error("导入大型ZIP文件失败", e);
			return JSONUtil.createObj()
					.set("success", false)
					.set("msg", "导入失败：" + e.getMessage());
		} finally {
			// 清理临时文件
			log.info("开始清理临时文件夹: {}", thisTempPath);
			try {
				FileUtil.del(thisTempPath);
				log.info("临时文件夹 {} 删除成功", thisTempPath);
			} catch (Exception e) {
				log.warn("删除临时文件夹 {} 失败", thisTempPath, e);
			}
		}
	}

	/**
	 * 导出PDF压缩包（支持自定义PDF选项）
	 *
	 * @param id       节点ID
	 * @param thing    事物名称
	 * @param options  PDF导出选项对象
	 * @return 导出结果
	 */
	public JSONObject exportPdfZip(String id, String thing, PdfOptions options) {
		String thisTempPath = tempPath + File.separator + System.currentTimeMillis() + File.separator;
		FileUtil.mkdir(thisTempPath);
		return HandSonTableUtil.exportPdfZip(id, thisTempPath, pdfFontPath, thing, options);
	}

	/**
	 * 导出Excel压缩包
	 *
	 * @param id    节点ID
	 * @param pid   父节点ID
	 * @param thing 事物名称
	 * @return 生成的ZIP文件
	 * @throws IOException 如果文件处理失败
	 */
	public File exportExcelZip(String id, String pid, String thing) throws IOException {
		JSONObject obj = Util.postTwxForObject(thing, "QueryTreeById",
				JSONUtil.createObj().set("id", id));

		if (!obj.getBool("success")) {
			throw new IOException(obj.getStr("msg", "查询树结构失败"));
		}

		JSONArray data = obj.getJSONArray("data");

		// 构建node列表
		List<TreeNode> nodes = TreeNode.fromJsonArray(data);

		// 创建临时目录
		String folderName = System.currentTimeMillis() + "";
		String currentTempPath = tempPath + File.separator + folderName;
		FileUtil.mkdir(currentTempPath);

		try {
			// 构建树结构并处理节点
			List<TreeNode> nodeList = TreeUtil.build(nodes, pid);
			dealExcelNodes(currentTempPath, nodeList);

			// 创建ZIP文件
			File zipFile = ZipUtil.zip(currentTempPath, Charset.forName("GBK"));

			// 重命名ZIP文件
			if (!nodes.isEmpty()) {
				TreeNode firstNode = nodes.get(0);
				String tableNum = firstNode.getTableNum();
				String tableName = StrUtil.isBlank(tableNum) ?
						firstNode.getName() :
						tableNum + "：" + firstNode.getName();
				tableName += "（" + firstNode.getSecurity() + "）";
				zipFile = FileUtil.rename(zipFile, tableName, true, true);
			}

			return zipFile;
		} finally {
			// 清理临时目录
			try {
				FileUtil.del(currentTempPath);
			} catch (Exception e) {
				log.warn("删除临时目录失败: {}", currentTempPath, e);
			}
		}
	}

	/**
	 * 处理Excel节点，生成Excel文件
	 *
	 * @param folder 目标文件夹
	 * @param nodes  节点列表
	 * @throws IOException 如果文件处理失败
	 */
	private void dealExcelNodes(String folder, List<TreeNode> nodes) throws IOException {
		for (TreeNode node : nodes) {
			switch (node.getType()) {
				case "model":
					String modelPath = folder + File.separator + node.getName();
					FileUtil.mkdir(modelPath);
					if (!node.getChildren().isEmpty()) {
						dealExcelNodes(modelPath, node.getChildren());
					}
					break;

				case "project":
					String projectPath = folder + File.separator + node.getSort() + "-" + node.getName();
					FileUtil.mkdir(projectPath);
					if (!node.getChildren().isEmpty()) {
						dealExcelNodes(projectPath, node.getChildren());
					}
					break;

				case "a":
					String aName = node.getTableNum() + "：" + node.getName() + "（" + node.getSecurity() + "）";
					String aPath = folder + File.separator + aName;
					FileUtil.mkdir(aPath);
					exportNodeToExcel(aPath, aName, node);
					if (!node.getChildren().isEmpty()) {
						dealExcelNodes(aPath, node.getChildren());
					}
					break;

				case "b":
					String bName = node.getTableNum() + "：" + node.getName() + "（" + node.getSecurity() + "）";
					exportNodeToExcel(folder, bName, node);
					break;
			}
		}
	}

	/**
	 * 将节点数据导出为Excel文件
	 *
	 * @param folder   目标文件夹
	 * @param fileName 文件名
	 * @param node     节点数据
	 * @throws IOException 如果文件处理失败
	 */
	private void exportNodeToExcel(String folder, String fileName, TreeNode node) throws IOException {
		String saveDataStr = node.getSaveData();
		if (StrUtil.isNotBlank(saveDataStr)) {
			try {
				String baseFileName = fileName;
				String path = folder + File.separator + baseFileName + ".xlsx";
				File file = new File(path);

				// 如果文件已存在，在文件名后添加序号
				int counter = 1;
				while (file.exists()) {
					baseFileName = fileName + "(" + counter + ")";
					path = folder + File.separator + baseFileName + ".xlsx";
					file = new File(path);
					counter++;
				}

				String headerRow = node.getTableHeader();
				Util.tableData2Excel(saveDataStr, path, headerRow, baseFileName);
			} catch (Exception e) {
				String errorMsg = String.format("导出节点[%s:%s]失败: %s",
						node.getTableNum(), node.getName(), e.getMessage());
				throw new IOException(errorMsg, e);
			}
		}
	}

	/**
	 * 导出工时统计
	 *
	 * @param id    节点ID
	 * @param name  文件名
	 * @param thing 事物名称
	 * @return 包含导出文件路径的JSON对象
	 */
	public JSONObject exportWorkHours(String id, String name, String thing) {
		JSONObject res = new JSONObject();
		JSONArray data = new JSONArray();

		// 查询节点数据
		JSONArray nodes = Util.postQuerySql("select * from " + Util.getThingProperty(thing, "tableName")
				+ " start with id=" + id + " connect by prior id=pid");

		// 处理节点数据
		for (int x = 0; x < nodes.size(); x++) {
			TreeNode node = TreeNode.fromJson(nodes.getJSONObject(x));
			String saveDataStr = node.getSaveData();
			if (StrUtil.isBlank(saveDataStr)) {
				continue;
			}

			JSONObject saveData = JSONUtil.parseObj(saveDataStr);
			String workType = saveData.getStr("workType", "");
			if (StrUtil.isBlank(workType)) {
				continue;
			}

			JSONArray meta = saveData.getJSONArray("meta");
			if (ObjectUtil.isNull(meta)) {
				continue;
			}

			// 处理元数据
			for (int i = 0; i < meta.size(); i++) {
				JSONObject cell = meta.getJSONObject(i);
				String postType = cell.getStr("postType", "");
				JSONArray eles = cell.getJSONArray("eles");
				if (ObjectUtil.isNull(eles)) {
					continue;
				}

				// 处理签名数据
				for (int j = 0; j < eles.size(); j++) {
					JSONObject ele = eles.getJSONObject(j);
					String type = ele.getStr("type");
					if (type.equals("sign")) {
						String date = ele.getStr("date");
						String signName = ele.getStr("signName");
						if (StrUtil.isNotBlank(signName) &&
								StrUtil.isNotBlank(postType) &&
								(!StrUtil.equals(postType, "取消设置")) &&
								StrUtil.isNotBlank(workType) &&
								StrUtil.isNotBlank(date)) {
							JSONObject set = new JSONObject()
									.set("date", date)
									.set("signName", signName)
									.set("postType", postType)
									.set("workType", workType);
							data.put(set);
						}
					}
				}
			}
		}

		if (!data.isEmpty()) {
			// 获取工时工作类型字典
			JSONArray workTypes = Util.postTwx("Thing.Fn.SystemDic", "getDictionaryDataByName",
					new JSONObject().set("name", "工时工作类型"));
			JSONObject obj = new JSONObject();
			String dateStr;
			long startTime = 0, endTime = 0;

			// 计算时间范围并统计数据
			for (int i = 0; i < data.size(); i++) {
				JSONObject object = data.getJSONObject(i);
				Date date = object.getDate("date");
				long time = date.getTime();
				if (i == 0) {
					startTime = time;
					endTime = time;
				}
				if (startTime >= time) {
					startTime = time;
				}
				if (endTime <= time) {
					endTime = time;
				}

				String key = object.getStr("signName") + "_" + object.getStr("postType");
				String workType = object.getStr("workType");
				JSONObject countObj = obj.getJSONObject(key);
				if (ObjectUtil.isNull(countObj)) {
					countObj = new JSONObject();
				}
				int count = countObj.getInt(workType, 0);
				countObj.set(workType, count + 1);
				obj.set(key, countObj);
			}
			dateStr = DateUtil.format(new Date(startTime), "yyyyMMdd") + " 到" +
					DateUtil.format(new Date(endTime), "yyyyMMdd");

			// 构建Excel数据
			ArrayList<Map<String, Object>> rows = new ArrayList<>();
			int index = 0;
			for (String key : obj.keySet()) {
				index++;
				List<String> strings = StrUtil.split(key, "_");
				Map<String, Object> row = new LinkedHashMap<>();
				row.put("序号", index);
				row.put("姓名", strings.get(0));
				row.put("时间", dateStr);
				row.put("岗位", strings.get(1));
				for (int w = 0; w < workTypes.size(); w++) {
					row.put(workTypes.getJSONObject(w).getStr("NAME"), "");
				}
				JSONObject countObj = obj.getJSONObject(key);
				for (String workType : countObj.keySet()) {
					row.put(workType, countObj.get(workType));
				}
				rows.add(row);
			}

			// 排序数据
			rows.sort((o1, o2) -> {
				int result = CompareUtil.compare(o1.get("姓名").toString(), o2.get("姓名").toString());
				if (result == 0) {
					result = CompareUtil.compare(o1.get("岗位").toString(), o2.get("岗位").toString());
				}
				return result;
			});

			// 重新设置序号
			for (int i = 0; i < rows.size(); i++) {
				rows.get(i).put("序号", i + 1);
			}

			// 生成Excel文件
			String resFile = Util.getTempPath() + System.currentTimeMillis() + "//" + name + "(内部).xlsx";
			ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter(resFile);

			// 写入表头
			writer.writeRow(Arrays.asList("序号", "姓名", "时间", "岗位", "工作项目"));
			writer.write(rows, true);
			writer.writeRow(Collections.singletonList(""));

			// 合并单元格并设置样式
			writer.merge(0, 1, 0, 0, "序号", false)
					.merge(0, 1, 1, 1, "姓名", false)
					.merge(0, 1, 2, 2, "时间", false)
					.merge(0, 1, 3, 3, "岗位", false)
					.merge(0, 0, 4, (4 + workTypes.size() - 1), "工作项目", false)
					.merge(rows.size() + 2, rows.size() + 2, 0, (4 + workTypes.size() - 1),
							"注：（1）膜粘贴包括漆膜、F46膜、聚酰亚胺镀铝膜、导热膜；\n" +
									"  （2）导热填料包括导热硅橡胶、导热硅脂、导热垫、铟箔等。", false);

			CellStyle cellStyle = writer.getCellStyle();
			writer.setRowStyleIfHasData(0, cellStyle);
			writer.setRowStyleIfHasData(1, cellStyle);
			writer.setRowHeight(rows.size() + 2, 37);

			cellStyle.setWrapText(true);
			writer.setRowStyleIfHasData(rows.size() + 2, cellStyle);

			// 设置列宽
			writer.setColumnWidth(0, 5);
			writer.setColumnWidth(1, 7);
			writer.setColumnWidth(2, 22);
			writer.setColumnWidth(3, 5);
			for (int w = 0; w < workTypes.size(); w++) {
				writer.setColumnWidth(w + 4, (workTypes.getJSONObject(w).getStr("NAME").length() * 2) + 2);
			}

			writer.close();

			res.set("data", resFile)
					.set("success", true);
		} else {
			res.set("msg", "未发现有效的工时签名！")
					.set("success", false);
		}

		return res;
	}

	/**
	 * 处理Base64签名文件
	 *
	 * @param thing Base64编码的内容
	 * @param id    节点ID
	 * @return 处理结果
	 */
	public JSONObject dealBase64(String thing, String id) {
		return HandSonTableUtil.dealSignFile(thing, id);
	}

	/**
	 * 处理签名HTML并转换为元数据
	 *
	 * @param thing HTML内容
	 * @param id    节点ID
	 * @return 元数据数组
	 */
	public JSONArray dealSignHtml(String thing, String id) {
		return HandSonTableUtil.signHtmlToMetas(thing, id);
	}

	/**
	 * 将Base64字符串转换为图片文件
	 *
	 * @param base64Str Base64编码的字符串
	 * @return 文件路径
	 */
	public String convertBase64ToImage(String base64Str) {
		// 使用工具类处理Base64转图片
		return Util.base64ToImg(base64Str);
	}

	/**
	 * 检查文件是否可用
	 *
	 * @param file 文件对象
	 * @return true表示文件可用，false表示文件被占用
	 */
	public static boolean isFileAvailable(File file) {
		try (FileInputStream fis = new FileInputStream(file);
			 FileChannel channel = fis.getChannel()) {
			FileLock lock = channel.tryLock(0L, Long.MAX_VALUE, true);
			if (lock != null) {
				lock.release();
				return true;
			}
		} catch (Exception e) {
			return false;
		}
		return false;
	}

	/**
	 * 导出确认表列表
	 *
	 * @param nodeId   节点ID
	 * @return Excel文件
	 */
	public File exportConfirmTableList(String nodeName, String nodeId, String thing) {
		JSONObject result = Util.postTwxForObject(thing, "QueryTreeNodeByPid", JSONUtil.createObj().set("ID", nodeId));
		JSONArray dataList = result.getJSONArray("data");
		JSONArray data = new JSONArray();
		if (dataList != null) {
			for (int i = 0; i < dataList.size(); i++) {
				JSONObject obj = dataList.getJSONObject(i);
				JSONArray row = new JSONArray();
				row.add(obj.getStr("NAME", ""));
				data.add(row);
			}
		}
		JSONArray headers = JSONUtil.parseArray(Arrays.asList("确认表名称"));
		JSONArray columnWidths = JSONUtil.parseArray(Arrays.asList(70));
		return CommonUtil.createExcelFile(nodeName + "确认表列表", headers, data, columnWidths, 25);
	}
}
