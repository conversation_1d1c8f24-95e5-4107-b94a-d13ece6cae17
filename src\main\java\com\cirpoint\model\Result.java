package com.cirpoint.model;

import lombok.Getter;
import org.springframework.http.ResponseEntity;

@Getter
public class Result {
    private boolean success;
    private String msg;
    private Object data;

    public Result(boolean success, String msg, Object data) {
        this.success = success;
        this.msg = msg;
        this.data = data;
    }

    public static ResponseEntity<?> ok(Object data) {
        return ResponseEntity.ok(new Result(true, "操作成功", data));
    }

    public static ResponseEntity<?> ok(String msg, Object data) {
        return ResponseEntity.ok(new Result(true, msg, data));
    }

    public static ResponseEntity<?> error(String msg) {
        return ResponseEntity.ok(new Result(false, msg, null));
    }
}
