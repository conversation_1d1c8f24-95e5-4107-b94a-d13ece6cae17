package com.cirpoint.controller;

import com.cirpoint.annotation.DisableLogging;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.*;

/**
 * 实时日志查看器控制器
 * 这个类实现了一个基于Web的实时日志查看器，主要功能包括：
 * 1. 提供Web界面实时展示日志内容
 * 2. 支持日志文件的实时监控和推送
 * 3. 提供日志文件的下载功能
 * 4. 使用SSE（Server-Sent Events）技术实现服务器向客户端的实时推送
 * <p>
 * 技术特点：
 * - 使用Spring Boot框架开发
 * - 采用SSE技术实现实时推送，相比WebSocket更轻量
 * - 支持多用户同时在线查看日志
 * - 提供美观的Web界面，支持日志级别颜色区分
 */
@Slf4j  // 启用Lombok的日志功能
@RestController  // 标记这是一个REST API控制器
@RequestMapping("/logs")  // 所有接口都以/logs开头
public class LiveLogController {

    /**
     * 日志文件的完整路径
     * 在构造函数中会被初始化为应用根目录下的/logs/application.log
     */
    private final String logFilePath;

    /**
     * 用于存储所有已连接客户端的SSE发射器
     * 键：唯一的发射器ID（使用时间戳生成）
     * 值：对应的SSE发射器实例
     * 使用ConcurrentHashMap确保线程安全
     */
    private final ConcurrentHashMap<String, SseEmitter> emitters = new ConcurrentHashMap<>();

    /**
     * 定时任务调度器
     * 用于创建一个后台线程，定期检查日志文件是否有新内容
     * 使用单线程执行器以确保日志读取的顺序性
     */
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    /**
     * 记录上次读取日志文件的位置（字节偏移量）
     * 用于实现增量读取，避免重复读取已经推送过的日志内容
     */
    private long lastPosition = 0;

    /**
     * 构造函数
     * 完成两个初始化工作：
     * 1. 设置日志文件的路径
     * 2. 启动后台的日志监控任务
     */
    public LiveLogController() {
        this.logFilePath = new File("").getAbsolutePath() + "/logs/application.log";
        startLogWatcher();
    }

    /**
     * 提供日志查看的Web页面
     * 这个接口返回一个完整的HTML页面，包含以下功能：
     * 1. 实时显示日志内容
     * 2. 支持自动滚动控制
     * 3. 支持清空当前显示的日志
     * 4. 支持下载完整日志文件
     * 5. 根据日志级别显示不同颜色（ERROR红色、WARN黄色、INFO绿色）
     * 
     * @return 返回包含完整HTML内容的字符串
     */
    @GetMapping("/view")
    public String viewLogs() {
        return "<!DOCTYPE html>\n" +
               "<html>\n" +
               "<head>\n" +
               "    <meta charset=\"UTF-8\">\n" +
               "    <title>实时日志查看器</title>\n" +
               "    <style>\n" +
               "        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }\n" +
               "        .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n" +
               "        h1 { color: #333; text-align: center; margin-bottom: 30px; font-size: 24px; }\n" +
               "        .log-container { background-color: #2b2b2b; color: #e6e6e6; padding: 15px; border-radius: 5px; font-family: monospace; height: 600px; overflow-y: auto; }\n" +
               "        .log-line { margin: 5px 0; white-space: pre-wrap; word-wrap: break-word; }\n" +
               "        .log-error { color: #ff6b6b; }\n" +
               "        .log-warn { color: #ffd93d; }\n" +
               "        .log-info { color: #6bff6b; }\n" +
               "        .toolbar { margin-bottom: 10px; text-align: right; }\n" +
               "        .toolbar button { padding: 8px 16px; margin-left: 10px; border-radius: 4px; border: none; cursor: pointer; font-weight: bold; transition: all 0.3s ease; }\n" +
               "        .toolbar button:hover { opacity: 0.8; transform: translateY(-1px); }\n" +
               "        .auto-scroll { background-color: #4CAF50; color: white; }\n" +
               "        .clear-logs { background-color: #f44336; color: white; }\n" +
               "        .download-logs { background-color: #2196F3; color: white; }\n" +
               "    </style>\n" +
               "</head>\n" +
               "<body>\n" +
               "    <div class=\"container\">\n" +
               "        <h1>📝 实时日志查看器</h1>\n" +
               "        <div class=\"toolbar\">\n" +
               "            <button class=\"download-logs\" onclick=\"downloadLogs()\">下载日志</button>\n" +
               "            <button class=\"auto-scroll\" id=\"toggleScroll\">自动滚动: 开</button>\n" +
               "            <button class=\"clear-logs\" onclick=\"clearLogs()\">清空日志</button>\n" +
               "        </div>\n" +
               "        <div class=\"log-container\" id=\"logContainer\"></div>\n" +
               "    </div>\n" +
               "    <script>\n" +
               "        var logContainer = document.getElementById('logContainer');\n" +
               "        var autoScroll = true;\n" +
               "        var toggleButton = document.getElementById('toggleScroll');\n" +
               "\n" +
               "        // 连接SSE\n" +
               "        var eventSource = new EventSource('/logs/stream');\n" +
               "        eventSource.onmessage = function(event) {\n" +
               "            var logLine = document.createElement('div');\n" +
               "            logLine.className = 'log-line';\n" +
               "            \n" +
               "            // 根据日志级别设置颜色\n" +
               "            if (event.data.includes('ERROR')) {\n" +
               "                logLine.classList.add('log-error');\n" +
               "            } else if (event.data.includes('WARN')) {\n" +
               "                logLine.classList.add('log-warn');\n" +
               "            } else if (event.data.includes('INFO')) {\n" +
               "                logLine.classList.add('log-info');\n" +
               "            }\n" +
               "            \n" +
               "            logLine.textContent = event.data;\n" +
               "            logContainer.appendChild(logLine);\n" +
               "            \n" +
               "            // 自动滚动到底部\n" +
               "            if (autoScroll) {\n" +
               "                logContainer.scrollTop = logContainer.scrollHeight;\n" +
               "            }\n" +
               "            \n" +
               "            // 保持最大日志行数\n" +
               "            while (logContainer.children.length > 1000) {\n" +
               "                logContainer.removeChild(logContainer.firstChild);\n" +
               "            }\n" +
               "        };\n" +
               "\n" +
               "        eventSource.onerror = function(error) {\n" +
               "            console.error('SSE连接错误:', error);\n" +
               "            // 尝试重新连接\n" +
               "            setTimeout(function() {\n" +
               "                eventSource.close();\n" +
               "                eventSource = new EventSource('/logs/stream');\n" +
               "            }, 5000);\n" +
               "        };\n" +
               "\n" +
               "        // 切换自动滚动\n" +
               "        toggleButton.onclick = function() {\n" +
               "            autoScroll = !autoScroll;\n" +
               "            toggleButton.textContent = '自动滚动: ' + (autoScroll ? '开' : '关');\n" +
               "        };\n" +
               "\n" +
               "        // 清空日志\n" +
               "        function clearLogs() {\n" +
               "            logContainer.innerHTML = '';\n" +
               "        }\n" +
               "\n" +
               "        // 下载日志\n" +
               "        function downloadLogs() {\n" +
               "            window.location.href = '/logs/download';\n" +
               "        }\n" +
               "    </script>\n" +
               "</body>\n" +
               "</html>";
    }

    /**
     * 创建SSE连接，用于向客户端推送实时日志
     * 当客户端访问此接口时，会建立一个长连接，服务器可以持续推送数据
     * <p>
     * 工作流程：
     * 1. 创建新的SSE发射器
     * 2. 将发射器存储到emitters集合中
     * 3. 发送现有的日志内容（最后100行）
     * 4. 设置连接的完成、超时和错误处理
     * 
     * @return 返回SSE发射器实例
     */
    @DisableLogging
    @GetMapping(path = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamLogs() {
        // 创建SSE发射器，设置超时时间为最大值，实际上永不超时
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        // 使用时间戳作为发射器的唯一标识
        String emitterId = String.valueOf(System.currentTimeMillis());
        // 将发射器存入集合
        emitters.put(emitterId, emitter);

        // 首次连接时，发送现有的日志内容
        try {
            sendExistingLogs(emitter);
        } catch (Exception e) {
            log.error("发送现有日志失败", e);
        }

        // 设置连接终止时的清理操作
        emitter.onCompletion(() -> emitters.remove(emitterId));  // 连接完成时移除发射器
        emitter.onTimeout(() -> emitters.remove(emitterId));     // 连接超时时移除发射器
        emitter.onError(throwable -> emitters.remove(emitterId)); // 发生错误时移除发射器

        return emitter;
    }

    /**
     * 提供日志文件下载功能
     * <p>
     * 特点：
     * 1. 文件名包含时间戳，避免重名
     * 2. 设置正确的HTTP头，确保浏览器正确处理下载
     * 3. 禁用缓存，确保每次都下载最新的日志文件
     * 
     * @return 包含日志文件的HTTP响应
     */
    @GetMapping("/download")
    public ResponseEntity<Resource> downloadLogs() {
        try {
            // 检查日志文件是否存在
            File logFile = new File(logFilePath);
            if (!logFile.exists()) {
                return ResponseEntity.notFound().build();
            }

            // 生成带时间戳的文件名，格式：application_20240101_235959.log
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String filename = "application_" + timestamp + ".log";

            // 设置HTTP响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename);  // 指定下载文件名
            headers.add(HttpHeaders.CONTENT_TYPE, "text/plain");  // 设置文件类型
            // 禁用缓存，确保每次都下载最新的文件
            headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            headers.add(HttpHeaders.PRAGMA, "no-cache");
            headers.add(HttpHeaders.EXPIRES, "0");

            // 创建文件资源并构建响应
            Resource resource = new FileSystemResource(logFile);
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(logFile.length())
                    .contentType(MediaType.parseMediaType("text/plain"))
                    .body(resource);
        } catch (Exception e) {
            log.error("下载日志文件失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 启动日志文件监控器
     * 创建一个定时任务，每秒检查一次日志文件是否有新内容
     * 只有当有客户端连接时才会执行检查，避免无谓的资源消耗
     */
    private void startLogWatcher() {
        scheduler.scheduleWithFixedDelay(() -> {
            try {
                // 只有当有客户端连接时才检查新日志
                if (!emitters.isEmpty()) {
                    checkNewLogs();
                }
            } catch (Exception e) {
                log.error("监控日志文件失败", e);
            }
        }, 0, 1000, TimeUnit.MILLISECONDS);  // 初始延迟0ms，每1000ms执行一次
    }

    /**
     * 发送已存在的日志内容
     * 当新客户端连接时，发送最近的100行日志，避免发送过多历史内容
     * 
     * @param emitter 目标SSE发射器
     */
    private void sendExistingLogs(SseEmitter emitter) {
        try {
            Path path = Paths.get(logFilePath);
            if (Files.exists(path)) {
                // 读取文件的所有行，但只发送最后100行
                java.util.List<String> lines = Files.readAllLines(path);
                int start = Math.max(0, lines.size() - 100);  // 确保不会出现负数索引
                for (int i = start; i < lines.size(); i++) {
                    emitter.send(lines.get(i));
                }
            }
        } catch (Exception e) {
            log.error("读取现有日志失败", e);
        }
    }

    /**
     * 检查并发送新的日志内容
     * 使用RandomAccessFile实现文件的增量读取
     * 记录上次读取位置，每次只读取新增的内容
     */
    private void checkNewLogs() {
        try (RandomAccessFile file = new RandomAccessFile(logFilePath, "r")) {
            long fileLength = file.length();
            
            // 如果文件大小小于上次位置，说明文件被重置了，从头开始读取
            if (fileLength < lastPosition) {
                lastPosition = 0;
            }
            
            // 如果有新内容
            if (fileLength > lastPosition) {
                // 移动到上次读取的位置
                file.seek(lastPosition);
                String line;
                // 读取所有新增的行
                while ((line = file.readLine()) != null) {
                    // 转换编码，确保中文正确显示
                    String logLine = new String(line.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
                    // 向所有连接的客户端发送新日志
                    for (SseEmitter emitter : emitters.values()) {
                        try {
                            emitter.send(logLine);
                        } catch (Exception e) {
                            log.error("发送日志失败", e);
                        }
                    }
                }
                // 更新读取位置
                lastPosition = file.getFilePointer();
            }
        } catch (Exception e) {
            log.error("检查新日志失败", e);
        }
    }
}
