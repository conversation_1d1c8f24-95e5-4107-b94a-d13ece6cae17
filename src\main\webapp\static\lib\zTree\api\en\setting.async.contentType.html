<div class="apiDetail">
<div>
	<h2><span>String</span><span class="path">setting.async.</span>contentType</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>When Ajax sends data to the server, it uses this content-type. 
                            It is used when <span class="highlight_red">[setting.async.enable = true]</span></p>
			<p>Default："application/x-www-form-urlencoded"</p>
		</div>
	</div>
	<h3>String Format</h3>
	<div class="desc">
	<p> contentType = "application/x-www-form-urlencoded", means: the sending data format is "form" format.</p>
	<p> contentType = "application/json",  means: the sending data format is "json" format. (for .Net)</p>
	</div>
	<h3>Examples of setting</h3>
	<h4>1. set the sending data format to "json" format.</h4>
	<pre xmlns=""><code>var setting = {
	async: {
		enable: true,
		contentType: "application/json",
		url: "http://host/getNode.php",
		autoParam: ["id", "name"]
	}
};
......</code></pre>
</div>
</div>