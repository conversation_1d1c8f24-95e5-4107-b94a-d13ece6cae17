/* VSCode风格的主题样式 */
html,
body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", sans-serif;
    background-color: #1e1e1e;
    color: #d4d4d4;
}

.layui-layout-admin .layui-side {
    width: 300px;
    background-color: #252526;
    top: 0;
    position: absolute;
    left: 0;
    transition: none; /* 移除过渡效果，使拖动更流畅 */
    z-index: 10;
}

.layui-layout-admin .layui-body {
    top: 0;
    bottom: 0;
    left: 300px;
    background-color: #1e1e1e;
    overflow: hidden;
    transition: none; /* 移除过渡效果，使拖动更流畅 */
}

.layui-side-scroll {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.file-explorer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 9px;
    border-bottom: 1px solid #333;
    background-color: #252526;
}

.file-explorer-header span {
    color: #cccccc;
    font-size: 14px;
}

.file-explorer-header .layui-btn {
    padding: 0 8px;
    background-color: #0e639c;
}

.file-explorer-header .layui-btn:hover {
    background-color: #1177bb;
}

.file-explorer-content {
    height: 100%;
    overflow: auto;
    background-color: #252526;
}

/* 文件树样式 - 基本样式已移至vscode-tree.css */
.ztree {
    padding: 5px;
}

/* 文件树节点样式已移至vscode-tree.css */

/* 编辑器容器 */
.editor-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    position: relative;
}

/* 编辑器工具栏 */
.editor-toolbar {
    background-color: #252526;
    border-bottom: 1px solid #333;
    flex-shrink: 0;
    z-index: 100;
    position: relative;
}

.editor-toolbar .layui-btn {
    background-color: #0e639c;
    border: none;
    height: 28px;
    line-height: 28px;
    padding: 0 12px;
    font-size: 12px;
}

.editor-toolbar .layui-btn:hover {
    background-color: #1177bb;
}

.editor-toolbar .layui-btn .layui-icon {
    font-size: 14px;
    margin-right: 4px;
}

/* Monaco编辑器容器 */
#editor {
    flex: 1;
    min-height: 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: #1e1e1e;
}

::-webkit-scrollbar-thumb {
    background: #424242;
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: #4f4f4f;
}

/* 右键菜单样式 */
.context-menu-list {
    background-color: #252526 !important;
    border: 1px solid #454545 !important;
    padding: 4px 0 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.context-menu-item {
    background-color: transparent !important;
    color: #d4d4d4 !important;
    padding: 4px 12px !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", sans-serif !important;
}

.context-menu-item:hover {
    background-color: #37373d !important;
    color: #fff !important;
}

.context-menu-separator {
    border-bottom: 1px solid #454545 !important;
    margin: 4px 0 !important;
}

/* zTree右键菜单样式 */
.right-menu {
    position: fixed;
    background: #252526;
    border: 1px solid #454545;
    border-radius: 3px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.4);
    min-width: 170px; /* 增加最小宽度 */
    z-index: 19999; /* 提高z-index确保显示在最上层 */
    max-height: 90vh; /* 最大高度为视口高度的90% */
    overflow-y: auto; /* 如果菜单项太多则显示滚动条 */
    transition: opacity 0.15s ease-in-out; /* 添加过渡效果 */
}

.right-menu ul {
    margin: 5px 0;
    padding: 0;
}

.right-menu li {
    padding: 8px 15px;
    font-size: 14px;
    color: #d4d4d4;
    cursor: pointer;
    transition: background 0.2s;
    list-style: none;
    white-space: nowrap; /* 防止文字换行 */
}

.right-menu li:hover {
    background-color: #37373d;
}

.right-menu .layui-icon {
    margin-right: 8px;
    font-size: 14px;
    width: 16px; /* 固定图标宽度 */
    display: inline-block; /* 确保图标对齐 */
    text-align: center; /* 确保图标居中 */
}

.layui-layout-admin .layui-body {
    padding-bottom: 0 !important;
}

/* 标签页样式 */
.tabs-container {
    overflow-x: auto;
    white-space: nowrap;
    height: 48px; /* 修改为与左侧sidebar-tabs相同的高度 */
    width: 100%;
    background-color: #252526;
    border-bottom: 1px solid #3c3c3c;
}

.tabs-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    height: 100%;
}

.tab-item {
    display: inline-flex;
    align-items: center;
    padding: 0 15px;
    height: 100%;
    background-color: #2d2d2d;
    border-right: 1px solid #3c3c3c;
    color: #cccccc;
    cursor: pointer;
    position: relative;
    user-select: none;
}

.tab-item.active {
    background-color: #1e1e1e;
    color: #ffffff;
}

.tab-item.ui-sortable-helper {
    cursor: move;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.tab-item.ui-sortable-placeholder {
    visibility: visible !important;
    background-color: #3c3c3c;
    opacity: 0.5;
}

.tab-title {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tab-close {
    margin-left: 8px;
    font-size: 16px;
    line-height: 16px;
    width: 16px;
    height: 16px;
    text-align: center;
    border-radius: 50%;
}

.tab-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 修改原有保存按钮样式 */
#saveBtnContainer .layui-btn {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    border-radius: 15px;
    padding: 0 15px;
    height: 30px;
    line-height: 30px;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 密码验证层样式 */
.password-layer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #1e1e1e;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.password-container {
    background-color: #252526;
    padding: 30px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    width: 300px;
}

.password-container h2 {
    color: #d4d4d4;
    margin-bottom: 20px;
    text-align: center;
}

.password-container .layui-input {
    background-color: #3c3c3c;
    border: 1px solid #404040;
    color: #d4d4d4;
    margin-bottom: 15px;
}

.password-container .layui-btn {
    width: 100%;
    background-color: #0e639c;
}

.password-container .layui-btn:hover {
    background-color: #1177bb;
}

/* 路径输入框容器 */
.path-input-container {
    padding: 10px;
    border-bottom: 1px solid #333;
}

.path-input-container .layui-input {
    background-color: #3c3c3c;
    border: 1px solid #404040;
    color: #d4d4d4;
    margin-bottom: 8px;
    height: 30px;
}

.path-input-container .layui-btn {
    width: 100%;
    height: 30px;
    line-height: 30px;
    padding: 0;
    background-color: #0e639c;
}

.path-input-container .layui-btn:hover {
    background-color: #1177bb;
}

.path-input-container .layui-btn .layui-icon {
    font-size: 14px;
    margin-right: 4px;
}

/* 路径设置对话框样式 */
.path-dialog {
    width: 100%;
    height: 100%;
    background-color: #252526;
    color: #cccccc;
}

.path-dialog-container {
    display: flex;
    height: 100%;
}

.path-history-container {
    border-right: 1px solid #3c3c3c;
    overflow-y: auto;
    height: 100%;
    box-sizing: border-box;
    background-color: #1e1e1e;
}

.path-input-container {
    width: 50%;
    padding: 15px;
    height: 100%;
    box-sizing: border-box;
    background-color: #252526;
}

.path-input-section {
    width: 100%;
}

.path-dialog .dialog-label {
    font-weight: bold;
    margin-bottom: 5px;
    display: block;
    color: #cccccc;
}

.path-dialog .layui-textarea {
    margin-bottom: 12px;
    background-color: #1e1e1e;
    border: 1px solid #3c3c3c;
    color: #d4d4d4;
    font-family: Consolas, 'Courier New', monospace;
    resize: none;
}

.path-dialog .layui-textarea[readonly] {
    background-color: #2d2d2d;
    cursor: not-allowed;
}

/* 路径历史记录列表样式 */
.path-history-list {
    height: 100%;
}

.path-history-list h3 {
    font-size: 16px;
    margin-bottom: 8px;
    color: #cccccc;
    padding-left: 5px;
}

.path-history-list .layui-menu {
    height: calc(100% - 30px);
    overflow-y: auto;
    border: 1px solid #3c3c3c;
    border-radius: 2px;
    background-color: #1e1e1e;
}

.path-history-item {
    padding: 6px 8px;
    cursor: pointer;
    border-radius: 3px;
    margin-bottom: 2px;
    color: #d4d4d4;
    font-size: 13px;
    display: flex;
    align-items: flex-start;
    position: relative;
}

.path-history-item:hover {
    background-color: #37373d;
}

.path-history-item.default-path {
    background-color: #2d3748;
    font-weight: bold;
}

.path-history-item.default-path:hover {
    background-color: #37475c;
}

.path-history-item span {
    flex: 1;
    white-space: normal;
    word-break: break-all;
    line-height: 1.4;
}

/* 删除悬停提示，因为现在完整显示路径 */
.path-history-item[title]:hover:after {
    display: none;
}

.path-history-item .layui-icon {
    margin-right: 5px;
    color: #858585;
    margin-top: 2px;
}

.delete-path-btn {
    display: none;
    background: none;
    border: none;
    color: #858585;
    cursor: pointer;
    padding: 2px 5px;
    margin-left: 5px;
    border-radius: 3px;
}

.delete-path-btn:hover {
    color: #ff5252;
    background-color: rgba(255, 82, 82, 0.1);
}

.path-history-item:hover .delete-path-btn {
    display: inline-block;
}

/* 默认路径项不显示删除按钮 */
.path-history-item.default-path .delete-path-btn {
    display: none;
}

/* 修改layui对话框样式以适应暗色主题 */
.layui-layer {
    background-color: #252526 !important;
    color: #cccccc !important;
    border: 1px solid #3c3c3c !important;
}

.layui-layer-title {
    background-color: #333333 !important;
    color: #ffffff !important;
    border-bottom: 1px solid #3c3c3c !important;
}

.layui-layer-btn {
    background-color: #252526 !important;
    border-top: 1px solid #3c3c3c !important;
}

.layui-layer-btn a {
    background-color: #0e639c !important;
    color: #ffffff !important;
    border: 1px solid #0e639c !important;
}

.layui-layer-btn .layui-layer-btn1 {
    background-color: #333333 !important;
    color: #cccccc !important;
    border: 1px solid #3c3c3c !important;
}

/* 添加分隔线样式 */
.resizer {
    position: absolute;
    top: 0;
    left: 300px;
    width: 5px;
    height: 100%;
    background-color: #333;
    cursor: col-resize;
    z-index: 100;
    opacity: 0.5;
    transition: opacity 0.3s;
}

.resizer:hover, .resizer.active {
    opacity: 1;
    background-color: #0e639c;
}

/* 拖动时禁用文本选择 */
body.resizing {
    cursor: col-resize;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 图片预览容器 */
.image-preview {
    display: block;
    width: 100%;
    height: 100%;
    background-color: #1e1e1e;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

/* Viewer.js 容器样式优化 */
.image-preview .viewer-container {
    width: 100%;
    height: 100%;
    background-color: #1e1e1e;
}

/* 隐藏viewer.js的一些默认元素，使用我们自定义的样式 */
.image-preview .viewer-canvas {
    background-color: #1e1e1e;
}

.image-preview .viewer-footer {
    background-color: rgba(0, 0, 0, 0.7);
}

/* 优化工具栏按钮样式 */
.image-preview .viewer-toolbar > li {
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    margin: 0 2px;
}

.image-preview .viewer-toolbar > li:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

/* 当图片很小时的显示效果 */
.image-preview img {
    max-width: 100%;
    max-height: 100%;
}

/* 图片标签页图标 */
.tab-item[data-is-image="true"] .tab-title:before {
    content: "\e64a";
    font-family: "layui-icon" !important;
    margin-right: 5px;
    color: #1E9FFF;
}

/* 搜索容器 */
.search-container {
    padding: 10px;
    border-bottom: 1px solid #333;
    background-color: #252526;
    flex-shrink: 0;
}

.search-input-group {
    display: flex;
    margin-bottom: 5px;
}

.search-input-group .layui-input {
    flex: 1;
    height: 30px;
    background-color: #3c3c3c;
    border: 1px solid #3c3c3c;
    color: #cccccc;
    border-radius: 0;
}

.search-input-group .layui-btn {
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
}

.search-options {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #cccccc;
}

.search-options input[type="checkbox"] {
    margin-right: 5px;
}

/* 文件类型选择器样式 */
.file-type-container {
    flex-direction: column;
    align-items: flex-start;
    margin-top: 10px;
    padding-top: 5px;
    border-top: 1px solid #3c3c3c;
}

.file-type-label {
    margin-bottom: 8px;
    font-weight: 500;
    color: #cccccc;
}

.file-type-checkboxes {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.file-type-checkbox {
    display: flex;
    align-items: center;
    background-color: #3c3c3c;
    border-radius: 3px;
    padding: 4px 8px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.file-type-checkbox:hover {
    background-color: #4c4c4c;
}

.file-type-checkbox.active {
    background-color: #0e639c;
}

.file-type-checkbox input[type="checkbox"] {
    margin-right: 4px;
}

.file-type-input-container {
    width: 100%;
    position: relative;
}

.file-type-input-container .layui-input {
    background-color: #3c3c3c;
    border: 1px solid #3c3c3c;
    color: #cccccc;
    border-radius: 3px;
    height: 30px;
    width: 100%;
    transition: border-color 0.3s;
}

.file-type-input-container .layui-input:focus {
    border-color: #0e639c;
}

.file-type-input-container .layui-input.error {
    border-color: #e83120;
}

.file-type-error {
    color: #e83120;
    font-size: 12px;
    margin-top: 4px;
    min-height: 16px;
}

/* 搜索结果容器 */
.search-results-container {
    flex: 1;
    overflow: auto;
    background-color: #252526;
    display: flex;
    flex-direction: column;
}

.search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #333;
    background-color: #252526;
}

.search-results-header span {
    font-weight: bold;
    color: #cccccc;
}

.search-results-content {
    flex: 1;
    overflow: auto;
}

.search-results-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.search-result-item {
    padding: 8px 10px;
    border-bottom: 1px solid #333;
    cursor: pointer;
}

.search-result-item:hover {
    background-color: #2a2d2e;
}

.search-result-item .file-name {
    font-weight: bold;
    color: #cccccc;
    margin-bottom: 5px;
}

.search-result-item .file-path {
    font-size: 12px;
    color: #888888;
    margin-bottom: 5px;
}

.search-result-item .match-type {
    font-size: 12px;
    color: #569cd6;
    margin-bottom: 5px;
}

.search-result-item .matched-lines {
    margin-top: 5px;
    border-left: 2px solid #569cd6;
    padding-left: 10px;
}

.search-result-item .matched-line {
    font-size: 12px;
    color: #cccccc;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 2px 0;
}

/* 侧边栏页签 */
.sidebar-tabs {
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #333;
    position: relative;
}

.sidebar-actions {
    display: flex;
    align-items: center;
    margin-left: auto;
    padding-right: 10px;
}

.sidebar-actions button {
    margin-left: 5px;
}

.sidebar-tab {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #8c8c8c;
    position: relative;
    box-sizing: border-box;
    transition: width 0.2s ease;
    overflow: hidden;
}

.sidebar-tab-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    padding: 0 10px;
}

.sidebar-tab-text {
    font-size: 13px;
    margin-left: 8px;
    white-space: nowrap;
    overflow: hidden;
    opacity: 0;
    transition: opacity 0.2s ease;
    display: none;
}

.sidebar-tab:hover {
    color: #cccccc;
}

.sidebar-tab.active {
    color: #ffffff;
    width: 150px;
}

.sidebar-tab.active .sidebar-tab-text {
    opacity: 1;
    display: inline-block;
}

.sidebar-tab.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #0e639c;
}

.sidebar-tab i {
    font-size: 20px;
    flex-shrink: 0;
}

/* 侧边栏面板 */
.sidebar-panels {
    height: calc(100% - 48px);
    position: relative;
}

.sidebar-panel {
    display: none;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #252526;
    z-index: 1; /* 确保面板有正确的层叠顺序 */
}

.sidebar-panel.active {
    display: flex;
    z-index: 2; /* 活动面板在最上层 */
}

/* 搜索范围提示 */
.search-scope-info {
    padding: 5px 10px;
    background-color: #2d2d2d;
    color: #cccccc;
    font-size: 12px;
    border-bottom: 1px solid #333;
    display: flex;
    align-items: center;
}

.search-scope-info:before {
    content: "\e615";
    font-family: "layui-icon" !important;
    margin-right: 5px;
    color: #0e639c;
}

/* 搜索面板头部 */
.search-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #252526;
    border-bottom: 1px solid #333;
    flex-shrink: 0;
}

.search-panel-header span {
    font-weight: bold;
    color: #cccccc;
}

/* 路径管理面板样式 */
.path-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 9px;
    border-bottom: 1px solid #333;
    background-color: #252526;
}

.path-panel-header span {
    color: #cccccc;
    font-size: 14px;
}

.path-settings-container {
    padding: 10px;
    overflow-y: auto;
}

.path-section {
    margin-bottom: 15px;
}

.path-label {
    color: #cccccc;
    font-size: 13px;
    margin-bottom: 5px;
    font-weight: 500;
}

.path-section .layui-textarea {
    background-color: #1e1e1e;
    border: 1px solid #3c3c3c;
    color: #d4d4d4;
    font-size: 13px;
    height: 60px;
    resize: none;
}

.path-section .layui-textarea[readonly] {
    background-color: #252526;
    cursor: default;
}

.path-section .layui-input {
    background-color: #1e1e1e;
    border: 1px solid #3c3c3c;
    color: #d4d4d4;
    font-size: 13px;
}

/* 多路径管理面板样式 */
.multi-path-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 9px;
    border-bottom: 1px solid #333;
    background-color: #252526;
}

.multi-path-panel-header span {
    color: #cccccc;
    font-size: 14px;
}

.multi-path-panel-header .layui-btn {
    min-width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.multi-path-panel-header .layui-icon {
    font-size: 16px;
}

.add-path-container {
    padding: 10px;
    border-bottom: 1px solid #333;
}

.add-path-input-group {
    display: flex;
}

.add-path-input-group .layui-input,
.add-path-input-group .layui-textarea {
    flex: 1;
    background-color: #1e1e1e;
    border: 1px solid #3c3c3c;
    color: #d4d4d4;
}

.add-path-input-group .layui-input {
    height: 32px;
}

.add-path-input-group .layui-textarea {
    height: 60px;
    resize: none;
}

.path-list-container, .enabled-path-list-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
}

.enabled-path-list-container {
    margin-top: 10px;
    border-top: 1px solid #333;
}

.enabled-path-item {
    background-color: #1e2a3a;
}

.enabled-path-item:hover {
    background-color: #263345;
}

.path-list-header, .enabled-path-list-header {
    padding: 10px;
    font-weight: bold;
    border-bottom: 1px solid #333;
    color: #cccccc;
    background-color: #252526;
}

.enabled-path-list-header {
    background-color: #1e2a3a;
    color: #ffffff;
}

.path-list-content, .enabled-path-list-content {
    flex: 1;
    overflow-y: auto;
    padding: 5px 0;
}

.path-list, .enabled-path-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.path-item, .enabled-path-item {
    display: flex;
    align-items: flex-start;
    padding: 8px 10px;
    border-bottom: 1px solid #333;
    transition: background-color 0.2s ease;
}

.path-item:hover, .enabled-path-item:hover {
    background-color: #2d2d2d;
}

.path-item-text {
    flex: 1;
    color: #d4d4d4;
    word-break: break-all;
    white-space: normal;
    line-height: 1.4;
}

.path-item-actions {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    margin-left: 8px;
}

.path-item-actions button {
    margin-left: 5px;
    min-width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
}

.path-item-actions .layui-icon {
    font-size: 16px;
}

/* 统一按钮样式 */
.path-item-actions .layui-btn-normal {
    background-color: #0e639c;
}

.path-item-actions .layui-btn-normal:hover {
    background-color: #1177bb;
}

.path-item-actions .layui-btn-danger {
    background-color: #4d4d4d;
    color: #cccccc;
}

.path-item-actions .layui-btn-danger:hover {
    background-color: #e83120;
    color: #ffffff;
}

.empty-list {
    color: #858585;
    font-style: italic;
    justify-content: center;
    padding: 15px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    margin: 5px;
}

.path-section .layui-btn {
    background-color: #0e639c;
    border: none;
    color: #ffffff;
    font-size: 13px;
    padding: 0 15px;
    height: 32px;
    line-height: 32px;
}

.path-section .layui-btn:hover {
    background-color: #1177bb;
}

.path-history-container {
    margin-top: 10px;
    border-top: 1px solid #333;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.path-history-header {
    padding: 9px;
    background-color: #252526;
    border-bottom: 1px solid #333;
}

.path-history-header span {
    color: #cccccc;
    font-size: 14px;
}

/* 添加历史路径搜索框样式 */
.path-search-container {
    padding: 8px;
    background-color: #252526;
    border-bottom: 1px solid #333;
}

.path-search-container .layui-input {
    background-color: #1e1e1e;
    border: 1px solid #3c3c3c;
    color: #d4d4d4;
    font-size: 13px;
    height: 30px;
    padding-left: 28px;
    position: relative;
}

/* 添加搜索图标 */
.path-search-container {
    position: relative;
}

.path-search-container:before {
    content: "\e615"; /* layui搜索图标 */
    font-family: "layui-icon" !important;
    position: absolute;
    left: 16px;
    top: 15px;
    color: #858585;
    font-size: 16px;
    z-index: 1;
}

/* 高亮显示默认路径项 */
.path-history-item.default-path {
    background-color: #2d3748;
    font-weight: bold;
}

.path-history-item.default-path:hover {
    background-color: #37475c;
}

.path-history-content {
    flex: 1;
    overflow-y: auto;
    padding: 5px;
}

.path-history-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.path-history-item {
    padding: 6px 8px;
    cursor: pointer;
    border-radius: 3px;
    margin-bottom: 2px;
    color: #d4d4d4;
    font-size: 13px;
    display: flex;
    align-items: flex-start;
}

.path-history-item:hover {
    background-color: #37373d;
}

.path-history-item .layui-icon {
    margin-right: 5px;
    color: #858585;
    margin-top: 2px;
}

/* 调整侧边栏面板布局 */
#pathManagerPanel {
    display: none;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}