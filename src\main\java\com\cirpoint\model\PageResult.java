package com.cirpoint.model;

import java.util.Collections;
import java.util.List;

/**
 * 分页结果封装类
 * 用于封装分页查询的结果数据和分页信息
 * 
 * @param <T> 数据类型
 */
public class PageResult<T> {
    
    private List<T> content;         // 当前页的数据内容
    private int page;                // 当前页码（从0开始）
    private int size;                // 每页大小
    private long total;              // 总记录数
    private int totalPages;          // 总页数
    private boolean hasNext;         // 是否有下一页
    private boolean hasPrevious;     // 是否有上一页
    private boolean first;           // 是否为第一页
    private boolean last;            // 是否为最后一页
    
    /**
     * 构造函数
     */
    public PageResult(List<T> content, int page, int size, long total) {
        this.content = content != null ? content : Collections.emptyList();
        this.page = Math.max(0, page);  // 确保页码不为负数
        this.size = Math.max(1, size);  // 确保页面大小至少为1
        this.total = Math.max(0, total); // 确保总数不为负数
        
        // 计算分页信息
        calculatePaginationInfo();
    }
    
    /**
     * 计算分页相关信息
     */
    private void calculatePaginationInfo() {
        this.totalPages = (int) Math.ceil((double) total / size);
        this.hasNext = page < totalPages - 1;
        this.hasPrevious = page > 0;
        this.first = page == 0;
        this.last = page >= totalPages - 1;
    }
    
    /**
     * 创建空的分页结果
     */
    public static <T> PageResult<T> empty(int page, int size) {
        return new PageResult<>(Collections.emptyList(), page, size, 0);
    }
    
    /**
     * 创建单页结果（不分页）
     */
    public static <T> PageResult<T> of(List<T> content) {
        return new PageResult<>(content, 0, content.size(), content.size());
    }
    
    /**
     * 获取下一页页码
     */
    public int getNextPage() {
        return hasNext ? page + 1 : page;
    }
    
    /**
     * 获取上一页页码
     */
    public int getPreviousPage() {
        return hasPrevious ? page - 1 : page;
    }
    
    /**
     * 获取当前页的起始记录索引（从1开始）
     */
    public long getStartIndex() {
        if (content.isEmpty()) {
            return 0;
        }
        return (long) page * size + 1;
    }
    
    /**
     * 获取当前页的结束记录索引
     */
    public long getEndIndex() {
        if (content.isEmpty()) {
            return 0;
        }
        return Math.min(getStartIndex() + content.size() - 1, total);
    }
    
    /**
     * 判断是否为空结果
     */
    public boolean isEmpty() {
        return content.isEmpty();
    }
    
    /**
     * 获取当前页的记录数
     */
    public int getNumberOfElements() {
        return content.size();
    }
    
    /**
     * 获取分页信息摘要
     */
    public String getSummary() {
        if (isEmpty()) {
            return "没有找到记录";
        }
        return String.format("第 %d-%d 条，共 %d 条记录，第 %d/%d 页",
                getStartIndex(), getEndIndex(), total, page + 1, totalPages);
    }
    
    // Getter和Setter方法
    public List<T> getContent() {
        return content;
    }
    
    public void setContent(List<T> content) {
        this.content = content;
    }
    
    public int getPage() {
        return page;
    }
    
    public void setPage(int page) {
        this.page = page;
    }
    
    public int getSize() {
        return size;
    }
    
    public void setSize(int size) {
        this.size = size;
    }
    
    public long getTotal() {
        return total;
    }
    
    public void setTotal(long total) {
        this.total = total;
    }
    
    public int getTotalPages() {
        return totalPages;
    }
    
    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }
    
    public boolean isHasNext() {
        return hasNext;
    }
    
    public void setHasNext(boolean hasNext) {
        this.hasNext = hasNext;
    }
    
    public boolean isHasPrevious() {
        return hasPrevious;
    }
    
    public void setHasPrevious(boolean hasPrevious) {
        this.hasPrevious = hasPrevious;
    }
    
    public boolean isFirst() {
        return first;
    }
    
    public void setFirst(boolean first) {
        this.first = first;
    }
    
    public boolean isLast() {
        return last;
    }
    
    public void setLast(boolean last) {
        this.last = last;
    }
    
    @Override
    public String toString() {
        return "PageResult{" +
                "content.size=" + content.size() +
                ", page=" + page +
                ", size=" + size +
                ", total=" + total +
                ", totalPages=" + totalPages +
                ", hasNext=" + hasNext +
                ", hasPrevious=" + hasPrevious +
                '}';
    }
} 