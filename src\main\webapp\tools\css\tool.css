/* 整体白色主题 */
body {
    background-color: #fff;
}
.layui-layout-admin .layui-header {
    background-color: #fff;
    color: #333;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
}
.layui-layout-admin .layui-logo {
    color: #333;
    background-color: #fff;
    width: 140px;
    font-weight: bold;
    border-right: 1px solid #f0f0f0;
}
.layui-layout-admin .layui-body {
    background-color: #fff;
    left: 0;
}

/* 修复顶部导航布局 */
.layui-layout-admin .layui-header .category-nav {
    position: absolute;
    left: 160px; /* 留出Logo的空间 */
    line-height: 60px;
    color: #333;
    font-size: 14px;
}

/* 工具区域样式 */
.tools-container {
    padding: 15px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    margin: 15px;
}
.tools-title {
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e6e6e6;
}
.tool-card {
    height: 100%;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
}
.tool-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-3px);
}
.tool-card .tool-header {
    padding: 10px;
    text-align: center;
}
.tool-card .tool-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 8px;
}
.tool-card .tool-body {
    padding: 8px 12px;
    border-top: 1px solid #f6f6f6;
}
.tool-card .tool-title {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: bold;
    border-bottom: none;
}
.tool-card .tool-desc {
    color: #666;
    min-height: 40px;
    font-size: 12px;
    line-height: 1.4;
}
.tool-card .tool-footer {
    padding: 8px 12px;
    text-align: right;
    border-top: 1px solid #f6f6f6;
}
.category-filter {
    margin-bottom: 20px;
}

/* 按钮样式调整 */
.layui-btn-sm {
    height: 28px;
    line-height: 28px;
    padding: 0 12px;
    font-size: 12px;
}

@media screen and (max-width: 768px) {
    .layui-col-xs12 {
        margin-bottom: 15px;
    }
    .layui-layout-admin .layui-body {
        top: 60px;
    }
}

.layui-nav .layui-nav-item a{
    color: #333;
}

.layui-nav .layui-nav-item a:hover, .layui-nav .layui-this a{
    color: #000;
}
