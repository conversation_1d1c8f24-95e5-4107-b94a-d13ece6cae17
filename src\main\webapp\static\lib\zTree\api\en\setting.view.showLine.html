<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">setting.view.</span>showLine</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Set to show or hide line.</p>
			<p>Default: true</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p> true means: show line.</p>
	<p> false means: hide line.</p>
	</div>
	<h3>Examples of setting</h3>
	<h4>1. Hide line</h4>
	<pre xmlns=""><code>var setting = {
	view: {
		showLine: false
	}
};
......</code></pre>
</div>
</div>