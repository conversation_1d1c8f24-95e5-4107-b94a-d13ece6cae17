<div class="apiDetail">
<div>
	<h2><span>Function(editable)</span><span class="path">zTreeObj.</span>setEditable</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.exedit</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Edit mode and normal mode switch.</p>
			<p class="highlight_red">To use edit mode, please set the attributes in 'setting.edit'</p>
			<p class="highlight_red">Please use zTree object to executing the method.</p>
		</div>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>editable</b><span>Boolean</span></h4>
	<p>true means: set zTree to edit mode.</p>
	<p>false means: set zTree to normal mode.</p>
	<h4><b>Return </b><span>none</span></h4>
	<p>no return value</p>
	</div>
	<h3>Examples of function</h3>
	<h4>1. set zTree to edit mode</h4>
	<pre xmlns=""><code>var treeObj = $.fn.zTree.getZTreeObj("tree");
treeObj.setEditable(true);
</code></pre>
</div>
</div>