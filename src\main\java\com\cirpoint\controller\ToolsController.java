package com.cirpoint.controller;

import com.cirpoint.model.Result;
import com.cirpoint.service.ToolsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工具集合控制器
 */
@RestController
@RequestMapping("/tools")
public class ToolsController {

    private final ToolsService toolsService;

    @Autowired
    public ToolsController(ToolsService toolsService) {
        this.toolsService = toolsService;
    }

    /**
     * 获取工具列表
     *
     * @return 工具列表
     */
    @GetMapping("/list")
    public ResponseEntity<?> getToolsList() {
        return ResponseEntity.ok(toolsService.getToolsList());
    }

    /**
     * 获取工具详情
     *
     * @param toolId 工具ID
     * @return 工具详情
     */
    @GetMapping("/detail")
    public ResponseEntity<?> getToolDetail(String toolId) {
        return ResponseEntity.ok(toolsService.getToolDetail(toolId));
    }
} 