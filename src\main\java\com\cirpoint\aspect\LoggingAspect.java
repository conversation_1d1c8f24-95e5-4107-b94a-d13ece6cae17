package com.cirpoint.aspect;

import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

import com.cirpoint.annotation.DisableLogging;

@Aspect
@Component
@Slf4j
public class LoggingAspect {

    @Around("execution(* com.cirpoint.controller.*.*(..))")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        // 检查是否禁用日志
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        if (method.isAnnotationPresent(DisableLogging.class)) {
            return joinPoint.proceed();
        }
        long start = System.currentTimeMillis();
        String className = joinPoint.getSignature().getDeclaringTypeName();
        String methodName = joinPoint.getSignature().getName();
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.info("开始请求 - {} - 参数: {}", 
                    className + "." + methodName,
                    Arrays.toString(joinPoint.getArgs()));
            return joinPoint.proceed();
        }
        HttpServletRequest request = attributes.getRequest();
        
        // 记录请求开始
        log.info("开始请求 - [{}] {} {} - 参数: {}", 
                request.getMethod(),
                request.getRequestURI(),
                className + "." + methodName,
                Arrays.toString(joinPoint.getArgs()));

        try {
            Object result = joinPoint.proceed();
            // 记录请求结束
            log.info("结束请求 - [{}] {} - 耗时: {}ms", 
                    request.getMethod(),
                    request.getRequestURI(),
                    System.currentTimeMillis() - start);
            return result;
        } catch (Exception e) {
            // 记录异常
            log.error("请求异常 - [{}] {} - 异常信息: {}", 
                    request.getMethod(),
                    request.getRequestURI(),
                    e.getMessage(), e);
            throw e;
        }
    }
}
