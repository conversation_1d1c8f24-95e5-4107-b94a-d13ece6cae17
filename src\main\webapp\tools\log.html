<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统日志查看器</title>
    <link rel="stylesheet" href="css/log-viewer.css">
</head>
<body>
    <div class="container">
        <!-- 头部导航 -->
        <header class="header">
            <h1>系统日志查看器</h1>
            <div class="header-controls">
                <button id="toggleRealtime" class="btn btn-primary">
                    <span id="realtimeStatus">启动实时</span>
                </button>
                <button id="clearLogs" class="btn btn-secondary">清空显示</button>
                <div class="connection-status">
                    <span id="connectionIndicator" class="status-indicator offline"></span>
                    <span id="connectionText">未连接</span>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 左侧面板 -->
            <aside class="sidebar">
                <!-- 日志文件列表 -->
                <div class="panel">
                    <h3>日志文件</h3>
                    <div class="file-list-container">
                        <div id="fileList" class="file-list">
                            <div class="loading">加载中...</div>
                        </div>
                        <button id="refreshFiles" class="btn btn-small">刷新列表</button>
                    </div>
                </div>

                <!-- 搜索过滤 -->
                <div class="panel">
                    <h3>搜索过滤</h3>
                    <div class="search-form">
                        <input type="text" id="searchKeyword" placeholder="关键词搜索..." class="form-input">
                        <select id="logLevel" class="form-select">
                            <option value="">所有级别</option>
                            <option value="ERROR">ERROR</option>
                            <option value="WARN">WARN</option>
                            <option value="INFO">INFO</option>
                            <option value="DEBUG">DEBUG</option>
                            <option value="TRACE">TRACE</option>
                        </select>
                        <input type="datetime-local" id="startTime" class="form-input">
                        <input type="datetime-local" id="endTime" class="form-input">
                        <button id="searchBtn" class="btn btn-primary">搜索</button>
                        <button id="clearSearch" class="btn btn-secondary">清空</button>
                    </div>
                </div>

                <!-- 系统状态 -->
                <div class="panel">
                    <h3>系统状态</h3>
                    <div id="systemStatus" class="status-info">
                        <div class="status-item">
                            <span>连接数:</span>
                            <span id="connectionCount">0</span>
                        </div>
                        <div class="status-item">
                            <span>当前文件:</span>
                            <span id="currentFile">-</span>
                        </div>
                        <div class="status-item">
                            <span>文件大小:</span>
                            <span id="fileSize">-</span>
                        </div>
                        <div class="status-item">
                            <span>最后更新:</span>
                            <span id="lastUpdate">-</span>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 右侧日志显示区域 -->
            <section class="content-area">
                <!-- 标签页 -->
                <div class="tabs">
                    <button class="tab-button active" data-tab="realtime">实时日志</button>
                    <button class="tab-button" data-tab="history">历史日志</button>
                </div>

                <!-- 实时日志标签页 -->
                <div id="realtimeTab" class="tab-content active">
                    <div class="log-controls">
                        <label>
                            <input type="checkbox" id="autoScroll" checked>
                            自动滚动
                        </label>
                        <label>
                            最大显示行数:
                            <select id="maxLines">
                                <option value="1000">1000</option>
                                <option value="2000" selected>2000</option>
                                <option value="5000">5000</option>
                                <option value="10000">10000</option>
                            </select>
                        </label>
                        <span class="log-count">显示: <span id="displayedLines">0</span> 行</span>
                    </div>
                    <div id="realtimeLogContainer" class="log-container">
                        <div class="log-content" id="realtimeLogContent">
                            <div class="log-placeholder">点击"启动实时"开始查看实时日志...</div>
                        </div>
                    </div>
                </div>

                <!-- 历史日志标签页 -->
                <div id="historyTab" class="tab-content">
                    <div class="history-controls">
                        <div class="file-selector">
                            <label>选择文件:</label>
                            <select id="historyFileSelect" class="form-select">
                                <option value="">请选择日志文件</option>
                            </select>
                            <button id="downloadFile" class="btn btn-small" disabled>下载文件</button>
                        </div>
                        <div class="pagination-info">
                            <span>每页显示:</span>
                            <select id="pageSize">
                                <option value="1000">1000</option>
                                <option value="2000" selected>2000</option>
                                <option value="5000">5000</option>
                                <option value="10000">10000</option>
                            </select>
                            <span>行</span>
                        </div>
                    </div>
                    <div id="historyLogContainer" class="log-container">
                        <div class="log-content" id="historyLogContent">
                            <div class="log-placeholder">请选择日志文件查看历史记录...</div>
                        </div>
                    </div>
                    <div id="pagination" class="pagination">
                        <!-- 分页控件将通过JavaScript动态生成 -->
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">加载中...</div>
        </div>
    </div>

    <!-- 错误提示 -->
    <div id="errorToast" class="toast error hidden">
        <span class="toast-message"></span>
        <button class="toast-close">&times;</button>
    </div>

    <!-- 成功提示 -->
    <div id="successToast" class="toast success hidden">
        <span class="toast-message"></span>
        <button class="toast-close">&times;</button>
    </div>

    <script src="js/log-viewer-enhanced.js"></script>
</body>
</html> 