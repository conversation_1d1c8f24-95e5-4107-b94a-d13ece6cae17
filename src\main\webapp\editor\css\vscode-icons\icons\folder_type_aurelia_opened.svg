<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><defs><linearGradient id="a" x1="-72.301" y1="-38.015" x2="-69.078" y2="-40.974" gradientTransform="matrix(7.886, 0, 0, -8.589, 578.084, -327.095)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#c06fbb"/><stop offset="1" stop-color="#6e4d9b"/></linearGradient><linearGradient id="b" x1="-75.573" y1="-30.081" x2="-76.448" y2="-28.886" gradientTransform="matrix(15.701, 0, 0, -16.956, 1213.064, -480.525)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#6e4d9b"/><stop offset="0.14" stop-color="#77327a"/><stop offset="0.29" stop-color="#b31777"/><stop offset="0.84" stop-color="#cd0f7e"/><stop offset="1" stop-color="#ed2c89"/></linearGradient><linearGradient id="c" x1="-73.519" y1="-35.988" x2="-70.578" y2="-40.083" gradientTransform="matrix(8.637, 0, 0, -7.94, 632.817, -284.546)" xlink:href="#a"/><linearGradient id="d" x1="4.839" y1="40.432" x2="31.752" y2="18.356" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#a"/><linearGradient id="e" x1="-71.327" y1="-42.491" x2="-69.09" y2="-44.326" gradientTransform="matrix(6.504, 0, 0, -6.517, 478.263, -265.393)" xlink:href="#a"/><linearGradient id="f" x1="-73.13" y1="-35.351" x2="-70.25" y2="-37.889" gradientTransform="matrix(10.02, 0, 0, -10.013, 732.69, -346.247)" xlink:href="#a"/><linearGradient id="g" x1="-74.446" y1="-31.641" x2="-75.325" y2="-30.439" gradientTransform="matrix(15.678, 0, 0, -16.922, 1195.287, -503.63)" xlink:href="#b"/><linearGradient id="h" x1="-72.166" y1="-37.84" x2="-68.944" y2="-41.407" gradientTransform="matrix(7.887, 0, 0, -8.589, 578.148, -327.094)" xlink:href="#a"/><linearGradient id="i" x1="-77.917" y1="-25.095" x2="-77.511" y2="-24.693" gradientTransform="matrix(37.627, 7.508, 7.477, -37.474, 3130.474, -328.745)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#6e4d9b"/><stop offset="0.14" stop-color="#77327a"/><stop offset="0.53" stop-color="#b31777"/><stop offset="0.79" stop-color="#cd0f7e"/><stop offset="1" stop-color="#ed2c89"/></linearGradient></defs><title>folder_type_aurelia_opened</title><path d="M27.4,5.5H18.2L16.1,9.7H4.3V26.5H29.5V5.5Zm0,18.7H6.6V11.8H27.4Zm0-14.5H19.2l1-2.1h7.1V9.7Z" style="fill:#db7ba9"/><polygon points="25.7 13.7 0.5 13.7 4.3 26.5 29.5 26.5 25.7 13.7" style="fill:#db7ba9"/><path d="M23.537,13.888l-2,1.335-2.06-3.087,2-1.335,2.06,3.087Z" style="fill:url(#a)"/><path d="M25.162,23.163l3.417,5.12L24.509,31l-3.417-5.12-.6-.893,4.07-2.716.6.893Z" style="fill:url(#b)"/><path d="M20.036,26.585l.752,1.127-3.1,2.068-1.348-2.021.677-.452,2.421-1.616.6.893Z" style="fill:url(#c)"/><polygon points="26.855 20.743 27.724 20.163 29.072 22.184 27.072 23.518 26.32 22.391 27.451 21.636 26.855 20.743 26.855 20.743" style="fill:url(#d)"/><polygon points="26.32 22.391 25.724 21.498 26.855 20.743 27.451 21.636 26.32 22.391 26.32 22.391" style="fill:url(#e)"/><path d="M12.831,21.033l-.677.452L10.094,18.4l3.1-2.068,1.444,2.164-2.421,1.616,2.421-1.616.616.923-2.421,1.616Z" style="fill:url(#f)"/><path d="M19.763,15.072l.616.923-4.07,2.716-.616-.923-3.385-5.072L16.378,10l3.385,5.072Z" style="fill:url(#g)"/><path d="M22.668,14.468l-1.131.755-.616-.923-1.444-2.164,2-1.335,2.06,3.087-.869.58Z" style="fill:url(#h)"/><path d="M17.614,28.2l-.6-.893,2.421-1.616.6.893L17.614,28.2Z" style="fill:#714896"/><path d="M26.32,22.391l-.6-.893,1.131-.755.6.893-1.131.755Z" style="fill:#6f4795"/><path d="M12.831,21.033l-.616-.923,2.421-1.616.616.923-2.421,1.616Z" style="fill:#88519f"/><path d="M21.537,15.223l-.616-.923,1.131-.755.616.923-1.131.755Z" style="fill:#85509e"/><path d="M25.162,23.163l-4.07,2.716-.6-.893,4.07-2.716.6.893Z" style="fill:#8d166a"/><path d="M19.763,15.072l.616.923-4.07,2.716-.616-.923,4.07-2.716Z" style="fill:#a70d6f"/><rect x="10.793" y="14.602" width="1.384" height="1.384" transform="translate(-6.558 8.948) rotate(-33.716)" style="fill:#9e61ad"/><rect x="14.942" y="28.404" width="1.384" height="1.384" transform="translate(-13.521 13.572) rotate(-33.716)" style="fill:#8053a3"/><path d="M12.8,29.3,9.426,24.2,27.439,12.156l3.534,5.011L12.8,29.3Z" style="fill:url(#i)"/></svg>