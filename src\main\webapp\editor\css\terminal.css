/* 终端样式 */
.terminal-container {
    position: absolute;
    bottom: 13px;
    left: 0;
    right: 0;
    height: 300px;
    background-color: #1e1e1e;
    color: #f0f0f0;
    border-top: 1px solid #333;
    display: none;
    z-index: 1000;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.terminal-header {
    height: 30px;
    background-color: #252526;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
}

.terminal-title {
    color: #cccccc;
    font-size: 14px;
    font-weight: bold;
}

.terminal-actions {
    display: flex;
    gap: 5px;
}

.terminal-action-btn {
    background: none;
    border: none;
    color: #cccccc;
    cursor: pointer;
    padding: 2px 5px;
    font-size: 12px;
    border-radius: 3px;
}

.terminal-action-btn:hover {
    background-color: #3a3a3a;
}

.terminal-content {
    height: calc(100% - 60px);
    overflow-y: auto;
    padding: 5px 10px;
}

.terminal-input-area {
    height: 30px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    background-color: #252526;
    border-top: 1px solid #333;
}

.terminal-prompt {
    color: #0a0;
    margin-right: 5px;
}

.terminal-input {
    flex: 1;
    background: none;
    border: none;
    color: #f0f0f0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    outline: none;
}

.terminal-line {
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-all;
    margin: 2px 0;
}

.terminal-line.command {
    color: #0a0;
    font-weight: bold;
}

.terminal-line.output {
    color: #f0f0f0;
}

.terminal-line.error {
    color: #f44;
}

.terminal-line.info {
    color: #44f;
}

/* ANSI 颜色支持 */
.ansi-red {
    color: #f44;
}

.ansi-green {
    color: #4f4;
}

.ansi-yellow {
    color: #ff4;
}

.ansi-blue {
    color: #44f;
}

.ansi-magenta {
    color: #f4f;
}

.ansi-cyan {
    color: #4ff;
}

/* 终端按钮样式 */
.terminal-toggle-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: #1e1e1e;
    color: #f0f0f0;
    border: 1px solid #333;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 999;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.terminal-toggle-btn:hover {
    background-color: #252526;
}

/* 适配暗色主题 */
@media (prefers-color-scheme: dark) {
    .terminal-container {
        background-color: #1a1a1a;
    }
    
    .terminal-header {
        background-color: #202020;
    }
    
    .terminal-input-area {
        background-color: #202020;
    }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .terminal-container {
        height: 250px;
    }
}
