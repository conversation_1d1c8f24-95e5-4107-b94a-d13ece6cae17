package com.cirpoint.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import com.actionsoft.bpms.api.OpenApiClient;
import com.actionsoft.sdk.service.model.FormFileModel;
import com.actionsoft.sdk.service.response.ListMapResponse;
import com.actionsoft.sdk.service.response.MapResponse;
import com.actionsoft.sdk.service.response.bo.FormFilesInfoResponse;
import com.actionsoft.sdk.service.response.dc.FileResponse;
import com.actionsoft.sdk.service.response.org.DepartmentResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * description
 *
 * <AUTHOR> 2025/05/29 08:45
 */
@Slf4j
public class BpmDataUtil {

	/**
	 * BPM配置信息封装类
	 */
	@Getter
	private static class BmpConfig {
		private final String apiServer;
		private final String accessKey;
		private final String secret;

		public BmpConfig(String apiServer, String accessKey, String secret) {
			this.apiServer = apiServer;
			this.accessKey = accessKey;
			this.secret = secret;
		}

	}

	/**
	 * 获取BPM配置参数
	 * 每次调用都从Thing.Fn.BPM中实时获取最新配置值，不缓存
	 *
	 * @return BmpConfig 配置对象
	 */
	private static BmpConfig getBmpConfig() {
		String apiServer = StrUtil.trim(Convert.toStr(Util.getThingProperty("Thing.Fn.BPM", "bpm_url")));
		String accessKey = StrUtil.trim(Convert.toStr(Util.getThingProperty("Thing.Fn.BPM", "accessKey")));
		String secret = StrUtil.trim(Convert.toStr(Util.getThingProperty("Thing.Fn.BPM", "secret")));
		return new BmpConfig(apiServer, accessKey, secret);
	}

	public static void main(String[] args) {
		BmpConfig config = getBmpConfig();
		Console.log(DateUtil.now(), "apiServer:", config.getApiServer());
		Console.log(DateUtil.now(), "accessKey:", config.getAccessKey());
		Console.log(DateUtil.now(), "secret:", config.getSecret());
	}

	public void testParam() {
		// 2. 组装请求参数
		Map<String, Object> param = new HashMap<>();
		param.put("boName", "table"); // 必填：BO 表名称

		// 3. 查询条件
		JSONArray querys = new JSONArray();

		// — 编号查询
		List<String> query1 = new ArrayList<>();
		query1.add("ZCBH=");                  // 字段＋运算符
		query1.add("102060201201500018");     // 值
		querys.add(query1);

		// — 名称查询
		List<String> query2 = new ArrayList<>();
		query2.add("ZCMC like ");             // 模糊查询
		query2.add("%试验厂房%");
		querys.add(query2);

		param.put("querys", querys.toJSONString()); // 非必填：查询条件 (JSON 字符串)
		// 条件格式示例：按 F1 > 0.2 查询 —— [[\"F1 >\",\"0.2\"]]

		// 4. 其它可选参数
		param.put("selectClause", "");   // 自定义查询语句
		param.put("orderBy", "");        // 排序字段，多个字段用 , 分隔，例如：F1,F2 DESC
		param.put("firstRow", "0");      // 首记录 (>=0)
		param.put("rowCount", "100");    // 返回记录数

		System.out.print(param);
	}

	public static String post(String boName) {
		// 调用完整参数的post方法，其他参数传入null
		return post(boName, null, null, null, null, null);
	}

	public static String post(String boName, String querys, String selectClause, String orderBy, String firstRow, String rowCount) {
		// 这里可以实现具体的业务逻辑，例如调用外部API、处理数据等
		// 目前返回一个简单的字符串作为示例

		// 1. 基本连接参数
		BmpConfig config = getBmpConfig();
		String apiMethod = "bo.query";

		Console.log(DateUtil.now(), "apiServer:", config.getApiServer());
		Console.log(DateUtil.now(), "accessKey:", config.getAccessKey());
		Console.log(DateUtil.now(), "secret:", config.getSecret());

		// 2. 组装请求参数
		Map<String, Object> param = new HashMap<>();
		param.put("boName", boName); // 必填：BO 表名称

		// 3. 添加可选参数（只有当参数不为空时才添加）
		if (querys != null && !querys.trim().isEmpty()) {
			param.put("querys", querys); // 非必填：查询条件 (JSON 字符串)
		}
		// 条件格式示例：按 F1 > 0.2 查询 —— [[\"F1 >\",\"0.2\"]]

		// 4. 其它可选参数
		if (selectClause != null && !selectClause.trim().isEmpty()) {
			param.put("selectClause", selectClause);   // 自定义查询语句
		}
		if (orderBy != null && !orderBy.trim().isEmpty()) {
			param.put("orderBy", orderBy);        // 排序字段，多个字段用 , 分隔，例如：F1,F2 DESC
		}
		if (firstRow != null && !firstRow.trim().isEmpty()) {
			param.put("firstRow", firstRow);      // 首记录 (>=0)
		}
		if (rowCount != null && !rowCount.trim().isEmpty()) {
			param.put("rowCount", rowCount);    // 返回记录数
		}

		// 5. 调用 OpenAPI
		OpenApiClient client = new OpenApiClient(config.getApiServer(), config.getAccessKey(), config.getSecret());
		ListMapResponse r = client.exec(apiMethod, param, ListMapResponse.class);

		// 6. 输出结果
		return JSONObject.toJSONString(r.getData());
	}

	/**
	 * 获取指定业务对象的文件信息
	 *
	 * @param boId      业务对象ID
	 * @param fieldName 字段名称
	 * @return 文件信息JSON字符串
	 */
	public static String getFiles(String boId, String fieldName) {
		BmpConfig config = getBmpConfig();
		String apiMethod = "bo.files.get";

		Map<String, Object> param = new HashMap<>();
		param.put("boId", boId);
		param.put("fieldName", fieldName);

		OpenApiClient client = new OpenApiClient(config.getApiServer(), config.getAccessKey(), config.getSecret());
		FormFilesInfoResponse r = client.exec(apiMethod, param, FormFilesInfoResponse.class);
		List<FormFileModel> datas = r.getData();

		for (FormFileModel data : datas) {
			String fileId = data.getId();
			String storagePath = "";

			try {
				// 1. 去重检查：查询BMP_FILE_STORAGE表，检查是否已存在该文件记录
				String checkSql = "SELECT STORAGE_PATH FROM BMP_FILE_STORAGE WHERE BMP_FILE_ID = '" + fileId + "'";
				cn.hutool.json.JSONArray existingRecords = Util.postQuerySql(checkSql);

				if (existingRecords != null && !existingRecords.isEmpty()) {
					// 文件已存在，直接获取存储路径
					storagePath = existingRecords.getJSONObject(0).getStr("STORAGE_PATH");
					log.info("文件已存在，使用现有存储路径: {}", storagePath);
				} else {
					// 文件不存在，需要下载并存储
					log.info("开始下载文件: {}", fileId);

					// 2. 文件下载：调用downloadFile方法下载文件到本地
					File downloadedFile = downloadFile(fileId);

					try {
						// 3. 文件上传：调用FileUploadUtil.uploadFileToStorage()方法将下载的文件保存到系统文件库
						FileUploadUtil.UploadResult uploadResult = FileUploadUtil.uploadFileToStorage(downloadedFile);
						storagePath = uploadResult.getStoragePath();

						// 4. 数据库记录：将文件信息插入到BMP_FILE_STORAGE表中
						String insertSql = "INSERT INTO BMP_FILE_STORAGE (ID, BMP_FILE_ID, BO_ID, FIELD_NAME, FILE_NAME, FILE_SIZE, STORAGE_PATH, CREATE_DATE, CREATE_USER, REMARK) " +
								"VALUES ('" + java.util.UUID.randomUUID() + "', '" + fileId + "', '" + boId + "', '" + fieldName + "', '" +
								data.getFileName().replace("'", "''") + "', " + data.getFileSize() + ", '" + storagePath + "', SYSDATE, 'SYSTEM', '通过BMP接口自动下载存储')";

						int insertResult = Util.postCommandSql(insertSql);
						if (insertResult > 0) {
							log.info("文件信息已成功插入数据库，存储路径: {}", storagePath);
						} else {
							log.warn("文件信息插入数据库失败");
						}

					} finally {
						// 5. 清理临时文件：删除下载的临时文件
						if (downloadedFile.exists()) {
							boolean deleted = downloadedFile.delete();
							log.info("临时文件删除结果: {}", deleted);
						}
					}
				}

			} catch (Exception e) {
				log.error("处理文件存储时发生错误: {}", e.getMessage(), e);
				// 发生错误时，存储路径设为空字符串
				storagePath = "";
			}

			// 6. 路径返回：通过data.setExt1("STORAGE_PATH")将存储路径值设置到返回结果的ext1字段中
			data.setExt1(storagePath);
		}

		return JSONObject.toJSONString(r.getData());
	}

	/**
	 * 下载指定文件ID的文件
	 *
	 * @param fileId 文件ID
	 * @return 下载的文件对象
	 * @throws IOException 文件下载或写入失败时抛出异常
	 */
	public static File downloadFile(String fileId) throws IOException {
		BmpConfig config = getBmpConfig();
		String apiMethod = "bo.file.down";

		Map<String, Object> param = new HashMap<>();
		param.put("fileId", fileId);

		OpenApiClient client = new OpenApiClient(config.getApiServer(), config.getAccessKey(), config.getSecret());
		FileResponse r = client.exec(apiMethod, param, FileResponse.class);

		// 从FileResponse中提取文件信息
		if (r != null && r.getData() != null) {
			String fileName = r.getData().getName();
			byte[] content = r.getData().getContent();

			// 如果文件名为空，使用默认文件名
			if (StrUtil.isBlank(fileName)) {
				fileName = "download_" + fileId;
			}

			// 在系统临时目录创建文件
			String tempDir = System.getProperty("java.io.tmpdir");
			File tempFile = new File(tempDir, fileName);

			// 写入文件内容
			try (FileOutputStream fos = new FileOutputStream(tempFile)) {
				fos.write(content);
			} catch (IOException e) {
				throw new RuntimeException(e);
			}

			return tempFile;
		}
		throw new IOException("下载文件失败：无法获取文件内容");
	}

	/**
	 * 获取部门信息
	 *
	 * @param id 部门ID
	 * @return 部门信息响应对象
	 */
	public static DepartmentResponse getDepartment(String id) {
		BmpConfig config = getBmpConfig();
		String apiMethod = "org.department.get";

		Map<String, Object> param = new HashMap<>();
		param.put("id", id);

		OpenApiClient client = new OpenApiClient(config.getApiServer(), config.getAccessKey(), config.getSecret());
		return client.exec(apiMethod, param, DepartmentResponse.class);
	}

	/**
	 * 获取部门名称
	 *
	 * @param id 部门ID
	 * @return 部门名称
	 */
	public static String getDepartmentName(String id) {
		return getDepartment(id).getData().getName();
	}

	/**
	 * 通过向BPM服务发送请求创建一个签名任务
	 *
	 * @param taskTitle       任务标题
	 * @param taskDescription 任务描述，提供额外细节
	 * @param assigneeWorkno  任务分配人工号
	 * @param assigneeName    任务分配人姓名
	 * @param callbackUrl     状态变更回调URL
	 * @return 从BPM服务创建任务后获得的响应字符串
	 * 返回格式示例:
	 * {"msg":"",
	 * "result":"ok",
	 * "data":{
	 * "taskInstId":"7e89eb50-8322-45c0-87ef-1946fdecfc60"
	 * },
	 * "id":":RO;"}
	 */
	public static String createSignTask(String taskTitle, String taskDescription, String assigneeWorkno, String assigneeName, String pushWorkno, String pushName, String callbackUrl) {
		BmpConfig config = getBmpConfig();
		String apiMethod = "dataPackage.task.create";

		Map<String, Object> param = new HashMap<>();
		param.put("taskTitle", taskTitle);
		param.put("taskDescription", taskDescription);
		param.put("assigneeWorkno", assigneeWorkno);
		param.put("assigneeName", assigneeName);
		param.put("pushWorkno", pushWorkno);
		param.put("pushName", pushName);
		param.put("callbackUrl", callbackUrl);

		OpenApiClient client = new OpenApiClient(config.getApiServer(), config.getAccessKey(), config.getSecret());
		return client.exec(apiMethod, param, String.class);
	}

	/**
	 * 完成签名任务
	 *
	 * @param taskInstId     任务实例ID
	 * @param assigneeWorkno 任务办理人工号
	 * @return 任务完成后的响应数据JSON字符串
	 */
	public static boolean completeSignTask(String taskInstId, String assigneeWorkno, boolean isBranch, boolean isBreakUserTask) {
		// 获取BPM配置信息
		BmpConfig config = getBmpConfig();
		// 设置API方法名称为完成任务
		String apiMethod = "task.complete";

		// 组装请求参数
		Map<String, Object> param = new HashMap<>();
		param.put("taskInstId", taskInstId);     // 任务实例ID
		param.put("uid", assigneeWorkno);        // 任务处理人工号
		param.put("isBranch", isBranch);        // 任务处理人工号
		param.put("isBreakUserTask", isBreakUserTask);        // 任务处理人工号


		// 创建API客户端并执行请求
		OpenApiClient client = new OpenApiClient(config.getApiServer(), config.getAccessKey(), config.getSecret());
		MapResponse r = client.exec(apiMethod, param, MapResponse.class);
		return r.isSuccess();
	}

	public static String queryUser() {
		BmpConfig config = getBmpConfig();
		String apiMethod = "dataPackage.user.query";

		Map<String, Object> param = new HashMap<>();

		OpenApiClient client = new OpenApiClient(config.getApiServer(), config.getAccessKey(), config.getSecret());
		String userMaps = client.exec(apiMethod, param, String.class);
		JSONArray data = JSONObject.parseObject(userMaps).getJSONArray("data");
		return data.toJSONString();
	}
}
