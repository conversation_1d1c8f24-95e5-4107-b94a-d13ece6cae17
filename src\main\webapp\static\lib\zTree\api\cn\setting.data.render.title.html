<div class="apiDetail">
  <div>
    <h2>
      <span>Function(title, treeNode)</span
      ><span class="path">setting.data.render.</span>title
    </h2>
    <h3>
      概述<span class="h3_info"
        >[ 依赖 <span class="highlight_green">jquery.ztree.core</span> 核心 js
        ]</span
      >
    </h3>
    <div class="desc">
      <p></p>
      <div class="longdesc">
        <p>用于使用自定义的方法动态渲染节点标题</p>
        <p>默认值：null</p>
        <p class="highlight_red">v3.5.45+</p>
      </div>
    </div>
    <h3>Function 参数说明</h3>
    <div class="desc">
      <h4><b>title</b><span>String</span></h4>
      <p>对应 zTree 利用 setting.key.title 获取到的节点标题</p>
      <h4 class="topLine"><b>treeNode</b><span>JSON</span></h4>
      <p>待渲染节点的 JSON 数据对象</p>
      <p class="highlight_red">针对根进行渲染时，treeNode = null</p>
      <h4 class="topLine"><b>返回值</b><span>Boolean</span></h4>
      <p>返回值是 渲染后的节点标题</p>
    </div>
    <h3>setting & function 举例</h3>
    <pre xmlns=""><code>
	var setting = {
		data: {
			render: function(title,treeNode){
				return title + treeNode.id;
			}
		}
	};
	......</code></pre>
  </div>
</div>
