package com.cirpoint.controller;

import com.cirpoint.service.BomService;
import com.cirpoint.util.FileDownloadUtil;
import java.io.File;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * BOM管理
 */
@RestController
@RequestMapping("/bom")
public class BomController {

	private final BomService bomService;

	@Autowired
	public BomController(BomService bomService) {
		this.bomService = bomService;
	}

	/**
	 * 导出BOM模板
	 *
	 * @return Excel文件下载响应
	 */
	@PostMapping("/export/tpl")
	public ResponseEntity<?> exportBomTemplate() {
		File file = bomService.exportBomTemplate();
		return FileDownloadUtil.fileResponseAndDelete(file);
	}

	/**
	 * 导入BOM数据
	 *
	 * @param file Excel文件
	 * @param pid  父节点ID
	 * @param user 用户名
	 * @return 导入结果
	 */
	@PostMapping("/import")
	public ResponseEntity<?> importBom(
			@RequestParam("uploadFile") MultipartFile file,
			@RequestParam("pid") String pid,
			@RequestParam("user") String user) {
		return ResponseEntity.ok(bomService.importBom(file, pid, user));
	}
} 