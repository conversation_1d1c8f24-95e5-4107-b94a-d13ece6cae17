package com.cirpoint.service.tools;

import cn.hutool.json.JSONObject;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Word文档(DOC/DOCX)转PDF服务
 */
@Service
public class DocxToPdfService {

	private static final Logger logger = LoggerFactory.getLogger(DocxToPdfService.class);

	@Value("${file.temp.path}")
	private String tempDir;

	// 记录转换任务的状态
	private final ConcurrentHashMap<String, ConversionTask> conversionTasks = new ConcurrentHashMap<>();

	// 线程池，用于并行处理文档转换
	private final ExecutorService executorService = Executors.newFixedThreadPool(5);

	/**
	 * 处理上传的ZIP文件，解压并获取Word文档(DOC/DOCX)文件列表
	 *
	 * @param zipFilePath ZIP文件路径
	 * @return 处理结果
	 */
	public JSONObject processZipFile(String zipFilePath) {
		JSONObject result = new JSONObject();
		List<JSONObject> wordFiles = new ArrayList<>();

		String taskId = UUID.randomUUID().toString();
		String extractDir = tempDir + File.separator + taskId;
		File zipFile = null;

		try {
			// 创建解压目录
			Files.createDirectories(Paths.get(extractDir));
			zipFile = new File(zipFilePath);

			// 检查文件是否存在
			if (!zipFile.exists() || !zipFile.isFile()) {
				throw new IOException("ZIP文件不存在或不是一个有效的文件");
			}

			// 文件大小检查
			long fileSize = zipFile.length();
			if (fileSize == 0) {
				throw new IOException("ZIP文件为空");
			}
			if (fileSize > 500 * 1024 * 1024) { // 500MB限制
				throw new IOException("ZIP文件过大，超过最大允许大小(500MB)");
			}

			// 尝试不同的字符编码读取ZIP文件
			boolean success = processZipWithEncoding(zipFile, extractDir, wordFiles, "UTF-8");
			if (!success) {
				// 如果UTF-8失败，尝试GBK编码（常用于中文Windows系统）
				success = processZipWithEncoding(zipFile, extractDir, wordFiles, "GBK");
			}
			if (!success) {
				// 再尝试系统默认编码
				success = processZipWithEncoding(zipFile, extractDir, wordFiles,
						System.getProperty("sun.jnu.encoding", Charset.defaultCharset().displayName()));
			}

			if (!success) {
				throw new IOException("无法处理ZIP文件，可能使用了不支持的字符编码或文件已损坏");
			}

			// 检查是否找到任何Word文档文件
			if (wordFiles.isEmpty()) {
				throw new IOException("ZIP文件中未找到任何DOC或DOCX文件");
			}

			// 创建转换任务
			ConversionTask task = new ConversionTask();
			task.setTaskId(taskId);
			task.setExtractDir(extractDir);
			task.setTotalFiles(wordFiles.size());
			task.setWordFiles(wordFiles);

			conversionTasks.put(taskId, task);

			result.set("code", 0);
			result.set("msg", "ZIP文件处理成功");
			result.set("data", new JSONObject()
					.set("taskId", taskId)
					.set("docxFiles", wordFiles)
					.set("path", zipFilePath));

		} catch (IllegalArgumentException e) {
			// 处理特定的格式错误异常
			logger.error("处理ZIP文件格式错误", e);
			result.set("code", 1);
			result.set("msg", "ZIP文件格式错误: " + e.getMessage());
			// 清理资源
			cleanupResources(extractDir);
		} catch (java.util.zip.ZipException e) {
			// 处理ZIP格式错误
			logger.error("ZIP文件格式无效", e);
			result.set("code", 1);
			result.set("msg", "ZIP文件格式无效或已损坏，请检查文件完整性");
			// 清理资源
			cleanupResources(extractDir);
		} catch (SecurityException e) {
			// 处理安全相关异常
			logger.error("处理ZIP文件安全检查未通过", e);
			result.set("code", 1);
			result.set("msg", "ZIP文件安全检查未通过: " + e.getMessage());
			// 清理资源
			cleanupResources(extractDir);
		} catch (IOException e) {
			// 处理IO异常
			logger.error("处理ZIP文件IO错误", e);
			result.set("code", 1);
			result.set("msg", e.getMessage());
			// 清理资源
			cleanupResources(extractDir);
		} catch (Exception e) {
			// 处理其他未预期的异常
			logger.error("处理ZIP文件出错", e);
			result.set("code", 1);
			result.set("msg", "处理ZIP文件出错: " + e.getMessage());
			// 清理资源
			cleanupResources(extractDir);
		}

		return result;
	}

	/**
	 * 使用指定编码处理ZIP文件
	 *
	 * @param zipFile    ZIP文件
	 * @param extractDir 解压目录
	 * @param wordFiles  Word文档(DOC/DOCX)文件列表
	 * @param encoding   字符编码
	 * @return 是否处理成功
	 */
	private boolean processZipWithEncoding(File zipFile, String extractDir, List<JSONObject> wordFiles, String encoding) {
		java.util.zip.ZipFile zip = null;

		try {
			// 使用指定编码打开ZIP文件
			zip = new java.util.zip.ZipFile(zipFile, java.nio.charset.Charset.forName(encoding));

			// 安全检查 - 限制条目数量
			int entryCount = zip.size();
			if (entryCount > 1000) {
				logger.warn("ZIP文件包含过多条目: {}", entryCount);
				throw new SecurityException("ZIP文件包含过多条目，可能是恶意文件");
			}

			// ZIP炸弹检测 - 检查压缩比
			long compressedSize = zipFile.length();
			long estimatedUncompressedSize = 0;

			// 预扫描条目以检测ZIP炸弹
			java.util.Enumeration<? extends java.util.zip.ZipEntry> entriesForScan = zip.entries();
			while (entriesForScan.hasMoreElements()) {
				java.util.zip.ZipEntry entry = entriesForScan.nextElement();
				long entrySize = entry.getSize();

				// 忽略目录和过小的文件
				if (entry.isDirectory() || entrySize < 0) {
					continue;
				}

				estimatedUncompressedSize += entrySize;

				// 检查单个文件的压缩比
				long entryCompressedSize = entry.getCompressedSize();
				if (entryCompressedSize > 0 && entrySize > 0) {
					double ratio = (double) entrySize / entryCompressedSize;
					if (ratio > 100) { // 压缩比超过1:100
						logger.warn("检测到可能的ZIP炸弹，文件: {}, 压缩比: {}", entry.getName(), ratio);
						throw new SecurityException("检测到可能的ZIP炸弹攻击，文件包含异常高压缩比");
					}
				}
			}

			// 检查总体压缩比
			if (compressedSize > 0 && estimatedUncompressedSize > 0) {
				double totalRatio = (double) estimatedUncompressedSize / compressedSize;
				if (totalRatio > 200) { // 总体压缩比超过1:200
					logger.warn("检测到可能的ZIP炸弹，总体压缩比: {}", totalRatio);
					throw new SecurityException("检测到可能的ZIP炸弹攻击，文件包含异常高压缩比");
				}
			}

			// 总解压大小限制 (1GB)
			if (estimatedUncompressedSize > 1024 * 1024 * 1024) {
				logger.warn("ZIP文件解压后估计大小超过限制: {}MB", estimatedUncompressedSize / (1024 * 1024));
				throw new SecurityException("ZIP文件解压后大小超过限制(1GB)");
			}

			// 遍历ZIP文件条目
			java.util.Enumeration<? extends java.util.zip.ZipEntry> entries = zip.entries();
			int wordCount = 0;

			while (entries.hasMoreElements()) {
				java.util.zip.ZipEntry entry = entries.nextElement();

				// 安全检查 - 文件名长度
				if (entry.getName().length() > 255) {
					throw new SecurityException("ZIP中的文件名过长，可能存在安全风险");
				}

				// 安全检查 - 路径遍历攻击
				if (entry.getName().contains("..") || entry.getName().startsWith("/") || entry.getName().startsWith("\\")) {
					throw new SecurityException("ZIP中的文件路径不安全，包含'..'序列或绝对路径");
				}

				// 安全检查 - 深度嵌套路径
				int pathDepth = entry.getName().split("[/\\\\]").length;
				if (pathDepth > 8) { // 限制目录深度
					throw new SecurityException("ZIP中的文件路径嵌套过深，可能存在安全风险");
				}

				if (entry.isDirectory()) {
					continue;
				}

				String entryName = entry.getName().toLowerCase();
				// 处理DOC和DOCX文件
				if (entryName.endsWith(".docx") || entryName.endsWith(".doc")) {
					wordCount++;
					String filePath = extractDir + File.separator + entry.getName();

					// 确保目录存在
					File outputFile = new File(filePath);

					// 额外安全检查 - 确保解压路径在目标目录内
					if (!outputFile.getCanonicalPath().startsWith(new File(extractDir).getCanonicalPath())) {
						throw new SecurityException("检测到ZIP滑动攻击尝试，文件路径超出目标目录");
					}

					if (!outputFile.getParentFile().exists()) {
						outputFile.getParentFile().mkdirs();
					}

					// 提取文件
					try (java.io.InputStream in = zip.getInputStream(entry);
						 java.io.FileOutputStream out = new java.io.FileOutputStream(outputFile)) {

						byte[] buffer = new byte[8192]; // 增加缓冲区大小以提高性能
						int read;
						long totalBytes = 0;
						long maxFileSize = 50 * 1024 * 1024; // 单个文件最大50MB

						while ((read = in.read(buffer)) != -1) {
							totalBytes += read;

							// 防止解压过大的文件
							if (totalBytes > maxFileSize) {
								out.close();
								outputFile.delete();
								throw new SecurityException("ZIP中的文件过大，超过允许的最大文件大小(50MB)");
							}

							out.write(buffer, 0, read);
						}

						// 文件格式验证
						boolean isValidFormat = true;
						if (entryName.endsWith(".docx")) {
							// DOCX文件格式验证 - 简单检查文件头部
							try (java.io.FileInputStream fis = new java.io.FileInputStream(outputFile)) {
								byte[] header = new byte[4];
								if (fis.read(header) == 4) {
									// DOCX文件应以PK\003\004开头（ZIP格式）
									if (!(header[0] == 'P' && header[1] == 'K' && header[2] == 3 && header[3] == 4)) {
										logger.warn("文件{}不是有效的DOCX文件格式", entry.getName());
										isValidFormat = false;
									}
								}
							}
						} else if (entryName.endsWith(".doc")) {
							// DOC文件格式验证 - 检查文件头部
							try (java.io.FileInputStream fis = new java.io.FileInputStream(outputFile)) {
								byte[] header = new byte[8];
								if (fis.read(header) == 8) {
									// DOC文件通常以D0CF11E0开头（复合文档格式）
									if (!(header[0] == (byte) 0xD0 && header[1] == (byte) 0xCF &&
											header[2] == (byte) 0x11 && header[3] == (byte) 0xE0)) {
										logger.warn("文件{}不是有效的DOC文件格式", entry.getName());
										isValidFormat = false;
									}
								}
							}
						}

						if (!isValidFormat) {
							outputFile.delete();
							continue; // 跳过这个文件，继续处理其他文件
						}

						// 收集Word文档文件信息
						JSONObject fileInfo = new JSONObject();
						fileInfo.set("name", entry.getName());
						fileInfo.set("size", outputFile.length());
						fileInfo.set("path", filePath);
						wordFiles.add(fileInfo);
					}
				}
			}

			zip.close();
			zip = null;

			// 确保找到了Word文档文件
			if (wordCount == 0) {
				logger.info("ZIP文件中未找到任何DOC或DOCX文件");
				// 即使没有找到Word文档文件，也返回true表示解析成功
				// 在processZipFile方法中会检查wordFiles.isEmpty()
				return true;
			}

			logger.info("使用编码{}成功处理ZIP文件，找到{}个Word文档文件", encoding, wordCount);
			return true;

		} catch (Exception e) {
			logger.debug("使用编码{}处理ZIP文件失败: {}", encoding, e.getMessage());
			return false;
		} finally {
			// 确保ZIP文件关闭
			if (zip != null) {
				try {
					zip.close();
				} catch (Exception ex) {
					logger.error("关闭ZIP文件失败", ex);
				}
			}
		}
	}

	/**
	 * 清理解压资源
	 *
	 * @param extractDir 解压目录
	 */
	private void cleanupResources(String extractDir) {
		if (extractDir == null) {
			return;
		}

		try {
			File dir = new File(extractDir);
			if (dir.exists()) {
				logger.info("清理临时目录: {}", extractDir);
				deleteDirectory(dir);
			}
		} catch (Exception e) {
			logger.error("清理资源出错: {}", extractDir, e);
		}
	}

	/**
	 * 递归删除目录
	 *
	 * @param directory 要删除的目录
	 */
	private void deleteDirectory(File directory) {
		if (directory.exists()) {
			File[] files = directory.listFiles();
			if (files != null) {
				for (File file : files) {
					if (file.isDirectory()) {
						deleteDirectory(file);
					} else {
						file.delete();
					}
				}
			}
			directory.delete();
		}
	}

	/**
	 * 开始批量转换任务
	 *
	 * @param taskId 任务ID
	 * @return 转换任务结果
	 */
	public JSONObject startConversion(String taskId) {
		JSONObject result = new JSONObject();

		ConversionTask task = conversionTasks.get(taskId);
		if (task == null) {
			result.set("code", 1);
			result.set("msg", "任务不存在或已过期");
			return result;
		}

		try {
			// 创建输出目录
			String outputDir = tempDir + File.separator + taskId + File.separator + "output";
			Files.createDirectories(Paths.get(outputDir));
			task.setOutputDir(outputDir);

			// 开始计时
			task.setStartTime(System.currentTimeMillis());

			// 获取文件列表
			List<JSONObject> wordFiles = task.getWordFiles();
			AtomicInteger processedCount = task.getProcessedCount();
			AtomicInteger successCount = task.getSuccessCount();
			AtomicInteger failedCount = task.getFailedCount();
			List<JSONObject> errors = task.getErrors();

			// 并行处理转换任务
			for (JSONObject file : wordFiles) {
				executorService.submit(() -> {
					try {
						String inputPath = file.getStr("path");
						String fileName = file.getStr("name");
						String outputPath = outputDir + File.separator
								+ fileName.substring(0, fileName.lastIndexOf(".")) + ".pdf";

						// 使用Aspose.Words进行转换
						boolean success = convertWordToPdf(inputPath, outputPath);

						if (success) {
							successCount.incrementAndGet();
						} else {
							failedCount.incrementAndGet();
							synchronized (errors) {
								JSONObject error = new JSONObject();
								error.set("file", fileName);
								error.set("message", "转换失败，可能是文档格式不兼容或内容损坏");
								errors.add(error);
							}
						}

					} catch (Exception e) {
						logger.error("转换文件出错", e);
						failedCount.incrementAndGet();
						synchronized (errors) {
							JSONObject error = new JSONObject();
							error.set("file", file.getStr("name"));
							error.set("message", e.getMessage());
							errors.add(error);
						}
					} finally {
						processedCount.incrementAndGet();
					}
				});
			}

			// 启动监控线程，等待所有转换完成
			executorService.submit(() -> {
				try {
					// 等待所有转换完成
					while (processedCount.get() < wordFiles.size()) {
						Thread.sleep(100);
					}

					// 转换完成，打包结果
					String pdfZipPath = packResults(task);
					task.setPdfZipPath(pdfZipPath);
					task.setEndTime(System.currentTimeMillis());
					task.setStatus("completed");

				} catch (Exception e) {
					logger.error("监控转换任务出错", e);
					task.setStatus("failed");
				}
			});

			result.set("code", 0);
			result.set("msg", "转换任务已启动");
			result.set("data", new JSONObject()
					.set("taskId", taskId));

		} catch (Exception e) {
			logger.error("启动转换任务出错", e);
			result.set("code", 1);
			result.set("msg", "启动转换任务出错: " + e.getMessage());
		}

		return result;
	}

	/**
	 * 获取转换任务状态
	 *
	 * @param taskId 任务ID
	 * @return 任务状态
	 */
	public JSONObject getConversionStatus(String taskId) {
		JSONObject result = new JSONObject();

		ConversionTask task = conversionTasks.get(taskId);
		if (task == null) {
			result.set("code", 1);
			result.set("msg", "任务不存在或已过期");
			return result;
		}

		int total = task.getTotalFiles();
		int processed = task.getProcessedCount().get();
		int success = task.getSuccessCount().get();
		int failed = task.getFailedCount().get();

		JSONObject data = new JSONObject();
		data.set("taskId", taskId);
		data.set("total", total);
		data.set("processed", processed);
		data.set("success", success);
		data.set("failed", failed);
		data.set("progress", total == 0 ? 0 : (int) ((processed * 100.0) / total));
		data.set("status", task.getStatus());

		if ("completed".equals(task.getStatus())) {
			data.set("pdfZipPath", task.getPdfZipPath());
			data.set("errors", task.getErrors());
			data.set("duration", (task.getEndTime() - task.getStartTime()) / 1000);
		}

		result.set("code", 0);
		result.set("msg", "获取任务状态成功");
		result.set("data", data);

		return result;
	}

	/**
	 * 打包转换结果
	 *
	 * @param task 转换任务
	 * @return PDF文件压缩包路径
	 */
	private String packResults(ConversionTask task) throws Exception {
		// 文件名格式：任务ID_pdf.zip
		String zipFileName = task.getTaskId() + "_pdf.zip";
		String pdfZipPath = tempDir + File.separator + zipFileName;

		// 收集转换失败的word文件路径
		List<JSONObject> errors = task.getErrors();
		List<String> failedWordPaths = new ArrayList<>();
		if (errors != null && !errors.isEmpty()) {
			for (JSONObject err : errors) {
				String fileName = err.getStr("file");
				// 在wordFiles中查找对应路径
				for (JSONObject wordFile : task.getWordFiles()) {
					if (fileName.equals(wordFile.getStr("name"))) {
						failedWordPaths.add(wordFile.getStr("path"));
					}
				}
			}
		}

		try (ZipOutputStream zipOut = new ZipOutputStream(Files.newOutputStream(Paths.get(pdfZipPath)))) {
			Path outputDirPath = Paths.get(task.getOutputDir());
			Files.walk(outputDirPath)
					.filter(Files::isRegularFile)
					.filter(path -> path.toString().toLowerCase().endsWith(".pdf"))
					.forEach(path -> {
						try {
							String entryName = outputDirPath.relativize(path).toString();
							ZipEntry entry = new ZipEntry(entryName);
							zipOut.putNextEntry(entry);
							Files.copy(path, zipOut);
							zipOut.closeEntry();
						} catch (Exception e) {
							logger.error("添加文件到ZIP包出错", e);
						}
					});
			// 将失败的word文件也加入压缩包，放在failed/目录下
			for (String wordPath : failedWordPaths) {
				File wordFile = new File(wordPath);
				if (wordFile.exists() && wordFile.isFile()) {
					try {
						String entryName = "转换失败的word文档/" + wordFile.getName();
						ZipEntry entry = new ZipEntry(entryName);
						zipOut.putNextEntry(entry);
						Files.copy(wordFile.toPath(), zipOut);
						zipOut.closeEntry();
					} catch (Exception e) {
						logger.error("添加失败word文件到ZIP包出错", e);
					}
				}
			}
		}

		// 检查文件是否生成成功
		File zipFile = new File(pdfZipPath);
		if (!zipFile.exists() || zipFile.length() == 0) {
			logger.error("PDF压缩包创建失败或为空文件: {}", pdfZipPath);
			throw new IOException("PDF压缩包创建失败");
		}

		logger.info("成功创建PDF压缩包: {}, 文件大小: {} bytes", pdfZipPath, zipFile.length());

		// 返回文件名（而非完整路径），前端将通过文件名构建下载链接
		return zipFileName;
	}

	/**
	 * Word文档(DOC/DOCX)转PDF转换过程
	 *
	 * @param inputPath  Word文档文件路径
	 * @param outputPath PDF文件路径
	 * @return 是否转换成功
	 */
	private boolean convertWordToPdf(String inputPath, String outputPath) {
		try {
			// 确保输出目录存在
			File outputFile = new File(outputPath);
			if (!outputFile.getParentFile().exists()) {
				outputFile.getParentFile().mkdirs();
			}

			// 获取Aspose License
			getLicense();

			// 使用Aspose.Words进行文档转换
			com.aspose.words.Document doc = new com.aspose.words.Document(inputPath);
			doc.save(outputPath, com.aspose.words.SaveFormat.PDF);

			return true;
		} catch (Exception e) {
			logger.error("转换文件出错: {}", e.getMessage(), e);
			return false;
		}
	}

	/**
	 * 获取Aspose License
	 */
	private void getLicense() {
		try {
			java.io.InputStream is = getClass().getClassLoader().getResourceAsStream("license.xml");
			com.aspose.words.License license = new com.aspose.words.License();
			license.setLicense(is);
		} catch (Exception e) {
			File licenseFile = new File("resources/license.xml");
			if (!licenseFile.exists()) {
				logger.warn("License file not found at: {}", licenseFile.getAbsolutePath());
				return; // 继续使用试用版
			}

			try {
				com.aspose.words.License license = new com.aspose.words.License();
				license.setLicense(Files.newInputStream(licenseFile.toPath()));
			} catch (Exception ex) {
				logger.error("获取Aspose License失败: {}", e.getMessage(), e);
			}
		}
	}

	/**
	 * 转换任务类
	 */
	@Data
	private static class ConversionTask {
		private String taskId;
		private String extractDir;
		private String outputDir;
		private String pdfZipPath;
		private int totalFiles;
		private long startTime;
		private long endTime;
		private String status = "pending";
		private List<JSONObject> wordFiles;
		private final AtomicInteger processedCount = new AtomicInteger(0);
		private final AtomicInteger successCount = new AtomicInteger(0);
		private final AtomicInteger failedCount = new AtomicInteger(0);
		private final List<JSONObject> errors = new ArrayList<>();
	}
} 