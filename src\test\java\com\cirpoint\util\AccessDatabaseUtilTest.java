package com.cirpoint.util;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import java.io.File;
import java.nio.file.Path;

/**
 * AccessDatabaseUtil的单元测试类
 */
class AccessDatabaseUtilTest {

    @TempDir
    Path tempDir;

    @Test
    void testCreateAccessDatabase_成功创建数据库文件() {
        // 准备测试数据
        String dbName = "testDb.mdb";
        File dbFile = tempDir.resolve(dbName).toFile();
        String dbPath = dbFile.getAbsolutePath();

        // 执行测试
        boolean result = AccessDatabaseUtil.createAccessDatabase(dbPath);

        // 验证结果
        assertTrue(result, "数据库文件应该创建成功");
        assertTrue(dbFile.exists(), "数据库文件应该存在");
        assertTrue(dbFile.length() > 0, "数据库文件不应该为空");
    }

    @Test
    void testCreateAccessDatabase_路径为空() {
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> AccessDatabaseUtil.createAccessDatabase(null),
            "应该抛出IllegalArgumentException异常"
        );

        assertEquals("文件路径不能为空", exception.getMessage());
    }

    @Test
    void testCreateAccessDatabase_文件已存在() {
        // 准备测试数据
        String dbName = "existingDb.mdb";
        File dbFile = tempDir.resolve(dbName).toFile();
        String dbPath = dbFile.getAbsolutePath();

        // 第一次创建
        boolean firstResult = AccessDatabaseUtil.createAccessDatabase(dbPath);
        assertTrue(firstResult, "第一次创建应该成功");

        // 尝试再次创建同名文件
        boolean secondResult = AccessDatabaseUtil.createAccessDatabase(dbPath);
        assertFalse(secondResult, "对已存在的文件应该返回false");
    }

    @Test
    void testCreateAccessDatabase_自动添加MDB扩展名() {
        // 准备测试数据
        String dbName = "testDbWithoutExtension";
        File dbFile = tempDir.resolve(dbName + ".mdb").toFile();
        String dbPath = tempDir.resolve(dbName).toFile().getAbsolutePath();

        // 执行测试
        boolean result = AccessDatabaseUtil.createAccessDatabase(dbPath);

        // 验证结果
        assertTrue(result, "数据库文件应该创建成功");
        assertTrue(dbFile.exists(), "应该创建带.mdb扩展名的文件");
    }
}
