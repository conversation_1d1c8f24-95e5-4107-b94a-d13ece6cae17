package com.cirpoint.model;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 日志条目模型
 * 用于封装单条日志记录的所有信息
 */
public class LogEntry {
    
    private String timestamp;      // 时间戳：yyyy-MM-dd HH:mm:ss.SSS
    private String thread;         // 线程名：[main]
    private String level;          // 日志级别：DEBUG/INFO/WARN/ERROR
    private String logger;         // Logger名称：com.cirpoint.service
    private String message;        // 日志消息内容
    private String rawLine;        // 原始日志行（用于搜索和显示）
    
    // 日志格式正则表达式：%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
    private static final Pattern LOG_PATTERN = Pattern.compile(
        "^(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d{3}) " +  // 时间戳
        "\\[([^\\]]+)\\] " +                                       // 线程名
        "([A-Z]+)\\s+" +                                           // 日志级别
        "([^ ]+) - " +                                             // Logger名称
        "(.*)$"                                                    // 消息内容
    );
    
    /**
     * 构造函数 - 从原始日志行创建LogEntry
     */
    public LogEntry(String rawLine) {
        this.rawLine = rawLine != null ? rawLine : "";
        parseLogLine();
    }
    
    /**
     * 默认构造函数
     */
    public LogEntry() {
        this.rawLine = "";
        this.timestamp = "";
        this.thread = "";
        this.level = "UNKNOWN";
        this.logger = "";
        this.message = "";
    }
    
    /**
     * 解析日志行的核心算法
     */
    private void parseLogLine() {
        if (rawLine == null || rawLine.trim().isEmpty()) {
            setDefaultValues();
            return;
        }
        
        Matcher matcher = LOG_PATTERN.matcher(rawLine);
        if (matcher.matches()) {
            this.timestamp = matcher.group(1);
            this.thread = matcher.group(2);
            this.level = matcher.group(3).trim();
            this.logger = matcher.group(4);
            this.message = matcher.group(5);
        } else {
            // 解析失败时的处理 - 可能是多行日志或异常堆栈
            setDefaultValues();
            this.message = rawLine;  // 将整行作为消息内容
        }
    }
    
    /**
     * 设置默认值
     */
    private void setDefaultValues() {
        this.timestamp = "";
        this.thread = "";
        this.level = "UNKNOWN";
        this.logger = "";
        this.message = rawLine;
    }
    
    /**
     * 判断是否为有效的日志条目
     */
    public boolean isValid() {
        return timestamp != null && !timestamp.isEmpty() && 
               level != null && !level.equals("UNKNOWN");
    }
    
    /**
     * 获取格式化的显示文本
     */
    public String getFormattedText() {
        if (isValid()) {
            return String.format("%s [%s] %-5s %s - %s", 
                timestamp, thread, level, logger, message);
        } else {
            return rawLine;
        }
    }
    
    // Getter和Setter方法
    public String getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getThread() {
        return thread;
    }
    
    public void setThread(String thread) {
        this.thread = thread;
    }
    
    public String getLevel() {
        return level;
    }
    
    public void setLevel(String level) {
        this.level = level;
    }
    
    public String getLogger() {
        return logger;
    }
    
    public void setLogger(String logger) {
        this.logger = logger;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getRawLine() {
        return rawLine;
    }
    
    public void setRawLine(String rawLine) {
        this.rawLine = rawLine;
    }
    
    @Override
    public String toString() {
        return "LogEntry{" +
                "timestamp='" + timestamp + '\'' +
                ", thread='" + thread + '\'' +
                ", level='" + level + '\'' +
                ", logger='" + logger + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
} 