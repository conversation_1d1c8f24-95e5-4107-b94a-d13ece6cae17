package com.cirpoint.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.util.Util;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * &#064;date  2025-02-20 15:07
 * &#064;description 处理电缆业务的service
 **/
@Slf4j
@Service
public class DlService extends ApplicationConfig {

	/**
	 * 生成模拟测试文件
	 * @return 生成的文件数量
	 */
	public int generateMockFiles() {
		log.info("开始生成模拟测试文件");
		int count = 0;
		try {
			// 确保目录存在
			Path dirPath = Paths.get(dlSyncPath);
			FileUtil.mkdir(dirPath.toString());
			log.info("目标目录: {}", dirPath.toAbsolutePath());

			// 获取当前日期，格式为yyyyMMdd
			String currentDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
			
			// 生成约100个文件
			for (int i = 0; i < 100; i++) {
				// 随机选择电缆型号 (DL_1 到 DL_10)
				String dlCode = "DL_" + RandomUtil.randomInt(1, 11);
				
				// 随机选择文件类型 (pdf 或 xlsx)
				String fileExt = RandomUtil.randomBoolean() ? "pdf" : "xlsx";
				
				// 生成6位随机数字
				String randomNum = String.format("%06d", RandomUtil.randomInt(0, 1000000));
				
				// 生成文件名 (时间-随机6位数字-电缆型号-随机数.扩展名)
				String fileName = String.format("%s-%s-%s-%d.%s", 
						currentDate, 
						randomNum, 
						dlCode, 
						RandomUtil.randomInt(1000, 10000), 
						fileExt);
				
				// 创建空文件
				File file = new File(dirPath.toString(), fileName);
				if (!file.exists()) {
					// 创建文件并写入一些内容
					String content = String.format("这是%s的模拟%s文件，创建时间：%s", 
							dlCode, 
							"xlsx".equals(fileExt) ? "记录表" : "跟踪卡",
							new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
					FileUtil.writeUtf8String(content, file);
					count++;
					log.info("已生成文件: {}", fileName);
				}
			}
			
			// 额外生成一些特殊文件用于测试过滤
			// 获取当前日期，格式为yyyyMMdd
			String currentDateForSpecial = new SimpleDateFormat("yyyyMMdd").format(new Date());
			String[] specialFiles = {
				currentDateForSpecial + "-" + String.format("%06d", RandomUtil.randomInt(0, 1000000)) + "-热敏电阻-test.pdf",
				currentDateForSpecial + "-" + String.format("%06d", RandomUtil.randomInt(0, 1000000)) + "-DL_1-test.doc",
				currentDateForSpecial + "-" + String.format("%06d", RandomUtil.randomInt(0, 1000000)) + "-test.txt"
			};
			
			for (String specialFile : specialFiles) {
				File file = new File(dirPath.toString(), specialFile);
				if (!file.exists()) {
					FileUtil.writeUtf8String("这是测试用的特殊文件", file);
					log.info("已生成特殊测试文件: {}", specialFile);
				}
			}
			
			log.info("模拟文件生成完成，共生成 {} 个文件", count);
		} catch (Exception e) {
			log.error("生成模拟文件时发生错误", e);
		}
		return count;
	}

	// 电缆检测报告同步
	@SuppressWarnings("null")
	public JSONArray syncDlReport(String processTreeId) {
		log.info("开始同步电缆检测报告");
		JSONArray resultFiles = new JSONArray();

		try {
			// 确保路径编码正确
			Path dirPath = Paths.get(dlSyncPath);
			log.info("原始路径: {}", dlSyncPath);
			log.info("转换后的路径: {}", dirPath.toAbsolutePath());

			//查询所有的电缆型号
			JSONArray allDlCode = Util.postTwxForObject("Thing.Fn.Dl", "QueryDlCode",
					new JSONObject().set("processTreeId",processTreeId)).getJSONArray("data");
			log.info("查询到的电缆型号数量: {}", allDlCode.size());
			if(allDlCode.isEmpty()) {
				log.warn("未查询到电缆型号");
				return resultFiles;
			}
			// 获取目录下所有文件
			File dir = dirPath.toFile();
			if (!dir.exists()) {
				log.warn("目录不存在: {}", dirPath);
				return resultFiles;
			}
			if (!dir.isDirectory()) {
				log.warn("路径不是目录: {}", dirPath);
				return resultFiles;
			}

			File[] files = dir.listFiles();
			if (files == null) {
				log.warn("目录为空: {}", dirPath);
				return resultFiles;
			}
			log.info("扫描目录 {} 中的文件，共发现 {} 个文件", dirPath, files.length);

			// 遍历处理文件
			for (File file : files) {
				String decodedFileName = file.getName();
				log.info("开始处理文件: {}", decodedFileName);

				// 排除热敏电阻文件
				if (decodedFileName.contains("热敏电阻")) {
					log.info("跳过热敏电阻文件: {}", decodedFileName);
					continue;
				}

				// 检查文件格式
				String fileExt = decodedFileName.substring(decodedFileName.lastIndexOf(".") + 1).toLowerCase();
				if (!("pdf".equals(fileExt) || "xlsx".equals(fileExt))) {
					log.info("跳过不支持的文件格式: {}, 文件: {}", fileExt, decodedFileName);
					continue;
				}

				// 获取电缆型号
				String[] fileNameParts = decodedFileName.split("-");
				if (fileNameParts.length < 3) {
					log.warn("文件名格式不正确，无法解析电缆型号: {}", decodedFileName);
					continue;
				}
				String dlCode = fileNameParts[2];
				log.info("解析到电缆型号: {}", dlCode);

				// 检查电缆型号是否在allDlCode中
				boolean found = false;
				JSONObject matchedDl = null;
				for (int i = 0; i < allDlCode.size(); i++) {
					JSONObject dlObj = allDlCode.getJSONObject(i);
					if (dlCode.equals(dlObj.getStr("DL_CODE"))) {
						found = true;
						matchedDl = dlObj;
						break;
					}
				}
				if (!found) {
					log.warn("未找到匹配的电缆型号记录: {}", dlCode);
					continue;
				}
				log.info("找到匹配的电缆记录: {}", matchedDl);
				
				//复制文件到文件仓库
				// 创建月份目录
				String month = new SimpleDateFormat("yyyy-MM").format(new Date());
				String monthPath = fileUploadPath + File.separator + month;
				FileUtil.mkdir(monthPath);
				// 生成唯一文件名
				String uuid = UUID.randomUUID().toString();
				String filePath = monthPath + File.separator + uuid;

				FileUtil.copy(file, new File(filePath), true);

				// 构建结果对象
				JSONObject fileInfo = new JSONObject();
				fileInfo.set("absolutePath", file.getAbsolutePath());
				fileInfo.set("filePath", "//" + month + "//" + uuid);
				fileInfo.set("fileName", FileNameUtil.mainName(file));
				fileInfo.set("fileFormat", fileExt);
				fileInfo.set("treeId", matchedDl.getStr("TREEID"));
				fileInfo.set("dlCode", matchedDl.getStr("DL_CODE"));
				resultFiles.add(fileInfo);
				log.info("成功处理文件: {}, 信息: {}", decodedFileName, fileInfo);
			}
			log.info("电缆检测报告同步完成，共处理 {} 个有效文件: {}", resultFiles.size(), resultFiles);
		} catch (Exception e) {
			log.error("同步电缆检测报告时发生错误", e);
		}
		return resultFiles;
	}

}
