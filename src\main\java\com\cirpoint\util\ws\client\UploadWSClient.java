package com.cirpoint.util.ws.client;

import cn.hutool.core.io.FileUtil;
import cn.hutool.crypto.SecureUtil;
import com.cirpoint.util.ws.interceptor.ClientLoginInterceptor;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.apache.cxf.transport.http.HTTPConduit;
import org.apache.cxf.transports.http.configuration.HTTPClientPolicy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Setter
@Getter
public class UploadWSClient {

	private static Logger logger = LoggerFactory.getLogger(UploadWSClient.class);
	
	private int fileSize;	//每个文件大小
	private String wsUrl;	//WS的地址
	private String method;	//WS的方法名
	
	private String auth;	//调用凭证
	private String dataPackage;//采集数据转换器

	public static void main(String[] args) {
		String filePath = "C:\\TestOut\\1678861141671\\card-22-1678861142574.zip";

		UploadWSClient client = new UploadWSClient();
		client.setFileSize(1024*1024*50);
		client.setWsUrl("http://***********:8085/avfms/ws/collect?wsdl");
		client.setMethod("UploadDIMonitorFile");
		client.setAuth("avfms");	
		client.setDataPackage("avfms");
		
		long begin = System.currentTimeMillis();
		client.upload(filePath);
		long end = System.currentTimeMillis();
		logger.info("调用服务服务【{}】,耗时【{}】毫秒",client.getWsUrl(),end-begin);
		
	}

	public List<String> upload(String filePath) {
		File file = FileUtil.file(filePath);
		return this.splitFile(file);		
	}
	private List<String> splitFile(File file) {
		List<String> result = new ArrayList<>();
		int size = this.getFileSize();
		String md5 = SecureUtil.md5(file);	
		
		try {
			try (FileInputStream fis = new FileInputStream(file)) {
				byte[] byteArray=new byte[size];
				int len=0;
				int i=0;
				while((len=fis.read(byteArray))!=-1){
					logger.info("offset={},size={}",i*size,len);	        		
					
					//最后一块
					if(len<size) {	        		
						//截取长度len的字节
						byte[] byteArray2=new byte[len];
						System.arraycopy(byteArray,0,byteArray2,0,len);
						result.add(this.upload(file.getName(), "", byteArray2, 1L*i*size, true, md5));
					}else {
						result.add(this.upload(file.getName(), "", byteArray, 1L*i*size, false, md5));
					}
					i++;	        	
				}
			}
	        
		} catch (FileNotFoundException e) {			
			e.printStackTrace();
		} catch (IOException e) {			
			e.printStackTrace();
		}
		return result;
	}
	
	private String upload(String fileName, String originalFileDir, byte[] buffer, long offset, boolean isLastBolck,
			String fileMD5) {
		
		logger.info("file={},offset={},len={},isLastBolck={}", fileName, offset, buffer.length, isLastBolck);
		
		 try{
			 
			//创建动态客户端
	        JaxWsDynamicClientFactory factory = JaxWsDynamicClientFactory.newInstance();
	        Client client = factory.createClient(this.getWsUrl());
	        
	        // 需要密码的情况需要加上用户名和密码
	        client.getOutInterceptors().add(new ClientLoginInterceptor(this.getAuth(),null));
	        HTTPConduit conduit = (HTTPConduit) client.getConduit();
	        HTTPClientPolicy httpClientPolicy = new HTTPClientPolicy();
	        httpClientPolicy.setConnectionTimeout(2000); //连接超时
	        httpClientPolicy.setAllowChunking(false); //取消块编码
	        httpClientPolicy.setReceiveTimeout(120000); //响应超时
	        conduit.setClient(httpClientPolicy);
	        //client.getOutInterceptors().addAll(interceptors);//设置拦截器
       
            Object[] objects = new Object[0];
            // invoke("方法名",参数1,参数2,参数3....);
            objects = client.invoke(this.getMethod(),this.getDataPackage(),fileName, "", buffer, offset, isLastBolck,fileMD5);
            logger.info("WS调用返回数据:【{}】",objects[0]);
            return (String) objects[0];
        }catch (Exception e){
            e.printStackTrace();
        	logger.error(e.getLocalizedMessage());
        	return "E0000";
        }
			
	}
}
