package com.cirpoint.util;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * Access数据库测试数据生成器
 */
@Slf4j
public class AccessDatabaseTestDataGenerator {
    private static final String DB_PATH = "C:/TestOut/Database1.mdb";
    private static final Random random = new Random();
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Test
    public void generateTestData() {
        try {
            // 首先创建数据库文件
            AccessDatabaseUtil.createAccessDatabase(DB_PATH);
            
            // 创建所有表
            createAllTables();
            
            // 生成测试数据
//            generate标定参数Data(100);
//            generate测试数据打印表Data(100);
//            generate测试数据表Data(100);
            generate计算结果Data(100);
//            generate转动惯量标定表Data(100);
//            generate转动惯量测试记录打印表Data(100);
//            generate转动惯量测试记录表Data(100);
//            generate转动惯量测试记录表oldData(100);
            
            log.info("成功生成所有测试数据");
        } catch (Exception e) {
            log.error("生成测试数据失败: {}", e.getMessage(), e);
        }
    }

    private void createAllTables() {
        try (Connection conn = AccessDatabaseUtil.getConnection(DB_PATH)) {
            // 创建标定参数表
            String sql = "CREATE TABLE IF NOT EXISTS 标定参数 (" +
                "编号 TEXT, " +
                "SD_传感器到原点距离 TEXT, " +
                "SD_标定等级 TEXT, " +
                "SD_平台重量 TEXT, " +
                "SD_平台P1系数 TEXT, " +
                "SD_平台P2系数 TEXT, " +
                "SD_平台P3系数 TEXT, " +
                "Plat_P3重量 TEXT, " +
                "Plat_PTotal重量 TEXT)";
            conn.createStatement().execute(sql);

            // 创建测试数据打印表
            sql = "CREATE TABLE IF NOT EXISTS 测试数据打印表 (" +
                "测试编号 TEXT, " +
                "产品型号 TEXT, " +
                "批次 TEXT, " +
                "产品名称 TEXT, " +
                "操作人员1 TEXT, " +
                "操作人员2 TEXT, " +
                "操作人员3 TEXT, " +
                "操作人员4 TEXT, " +
                "测试日期 TEXT, " +
                "V_Satellite_BalanceAngle TEXT, " +
                "V_Satellite_BalanceMoment TEXT)";
            conn.createStatement().execute(sql);

            // 创建测试数据表
            sql = "CREATE TABLE IF NOT EXISTS 测试数据表 (" +
                "测试编号 TEXT, " +
                "产品型号 TEXT, " +
                "批次 TEXT, " +
                "产品名称 TEXT, " +
                "操作人员1 TEXT, " +
                "操作人员2 TEXT, " +
                "操作人员3 TEXT, " +
                "操作人员4 TEXT, " +
                "测试日期 TEXT, " +
                "V_Satellite_BalanceAngle TEXT, " +
                "V_Satellite_BalanceMoment TEXT)";
            conn.createStatement().execute(sql);

            // 创建计算结果表 - 移除主键和索引
            sql = "CREATE TABLE IF NOT EXISTS 计算结果 (" +
                "测试编号 TEXT, " +
                "产品型号 TEXT, " +
                "测试日期 TEXT, " +
                "质心Xctest TEXT, " +
                "testXc TEXT)";
            conn.createStatement().execute(sql);

            // 创建转动惯量标定表
            sql = "CREATE TABLE IF NOT EXISTS 转动惯量标定表 (" +
                "编号 TEXT, " +
                "测量周期数 TEXT, " +
                "测量次数 TEXT, " +
                "PlatT1 TEXT, " +
                "标定日期 TEXT)";
            conn.createStatement().execute(sql);

            // 创建转动惯量测试记录打印表
            sql = "CREATE TABLE IF NOT EXISTS 转动惯量测试记录打印表 (" +
                "测试编号 TEXT, " +
                "产品型号 TEXT, " +
                "产品批次 TEXT, " +
                "产品名称 TEXT, " +
                "旋转角度bate TEXT)";
            conn.createStatement().execute(sql);

            // 创建转动惯量测试记录表
            sql = "CREATE TABLE IF NOT EXISTS 转动惯量测试记录表 (" +
                "测试编号 TEXT, " +
                "产品型号 TEXT, " +
                "产品批次 TEXT, " +
                "产品名称 TEXT, " +
                "旋转角度bate TEXT)";
            conn.createStatement().execute(sql);

            // 创建转动惯量测试记录表old
            sql = "CREATE TABLE IF NOT EXISTS 转动惯量测试记录表old (" +
                "测试编号 TEXT, " +
                "产品型号 TEXT, " +
                "产品批次 TEXT, " +
                "产品名称 TEXT, " +
                "ZSatI TEXT)";
            conn.createStatement().execute(sql);

            log.info("成功创建所有数据表");
        } catch (Exception e) {
            log.error("创建表失败: {}", e.getMessage(), e);
        }
    }

    private String generateRandomValue() {
        return String.format("%.4f", random.nextDouble() * 100);
    }

    private String generateRandomDate() {
        return LocalDateTime.now().minusDays(random.nextInt(365)).format(dateFormatter);
    }

    private String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }

    @SuppressWarnings("unused")
    private void generate标定参数Data(int count) {
        String sql = "INSERT INTO 标定参数 (编号, SD_传感器到原点距离, SD_标定等级, SD_平台重量) VALUES (?, ?, ?, ?)";
        try (Connection conn = AccessDatabaseUtil.getConnection(DB_PATH);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            for (int i = 0; i < count; i++) {
                pstmt.setString(1, "BDP" + String.format("%05d", i + 1));
                pstmt.setString(2, generateRandomValue());
                pstmt.setString(3, "A" + random.nextInt(5));
                pstmt.setString(4, generateRandomValue());
                pstmt.executeUpdate();
            }
            log.info("成功生成{}条标定参数数据", count);
        } catch (Exception e) {
            log.error("生成标定参数数据失败: {}", e.getMessage(), e);
        }
    }
    @SuppressWarnings("unused")
    private void generate测试数据打印表Data(int count) {
        String sql = "INSERT INTO 测试数据打印表 (测试编号, 产品型号, 批次, 产品名称, 操作人员1) VALUES (?, ?, ?, ?, ?)";
        try (Connection conn = AccessDatabaseUtil.getConnection(DB_PATH);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            for (int i = 0; i < count; i++) {
                pstmt.setString(1, "TEST" + String.format("%05d", i + 1));
                pstmt.setString(2, "MODEL-" + generateRandomString(5));
                pstmt.setString(3, "BATCH-" + generateRandomString(3));
                pstmt.setString(4, "产品-" + generateRandomString(4));
                pstmt.setString(5, "操作员-" + generateRandomString(2));
                pstmt.executeUpdate();
            }
            log.info("成功生成{}条测试数据打印表数据", count);
        } catch (Exception e) {
            log.error("生成测试数据打印表数据失败: {}", e.getMessage(), e);
        }
    }
    @SuppressWarnings("unused")
    private void generate测试数据表Data(int count) {
        String sql = "INSERT INTO 测试数据表 (测试编号, 产品型号, 批次, 产品名称, 操作人员1) VALUES (?, ?, ?, ?, ?)";
        try (Connection conn = AccessDatabaseUtil.getConnection(DB_PATH);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            for (int i = 0; i < count; i++) {
                String testId = "TEST" + String.format("%05d", i + 1);
                pstmt.setString(1, testId);
                pstmt.setString(2, "MODEL-" + generateRandomString(5));
                pstmt.setString(3, "BATCH-" + generateRandomString(3));
                pstmt.setString(4, "产品-" + generateRandomString(4));
                pstmt.setString(5, "操作员-" + generateRandomString(2));
                pstmt.executeUpdate();
            }
            log.info("成功生成{}条测试数据表数据", count);
        } catch (Exception e) {
            log.error("生成测试数据表数据失败: {}", e.getMessage(), e);
        }
    }

    private void generate计算结果Data(int count) {
        String sql = "INSERT INTO 计算结果 (测试编号, 产品型号, 测试日期, 质心Xctest, testXc) VALUES (?, ?, ?, ?, ?)";
        try (Connection conn = AccessDatabaseUtil.getConnection(DB_PATH);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            for (int i = 0; i < count; i++) {
                pstmt.setString(1, "TEST" + String.format("%05d", i + 1));
                pstmt.setString(2, "MODEL-" + generateRandomString(5));
                pstmt.setString(3, generateRandomDate());
                pstmt.setString(4, generateRandomValue());
                pstmt.setString(5, generateRandomValue());
                pstmt.executeUpdate();
            }
            log.info("成功生成{}条计算结果数据", count);
        } catch (Exception e) {
            log.error("生成计算结果数据失败: {}", e.getMessage(), e);
        }
    }
    @SuppressWarnings("unused")
    private void generate转动惯量标定表Data(int count) {
        String sql = "INSERT INTO 转动惯量标定表 (编号, 测量周期数, 测量次数, PlatT1, PlatT2, 标定日期) VALUES (?, ?, ?, ?, ?, ?)";
        try (Connection conn = AccessDatabaseUtil.getConnection(DB_PATH);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            for (int i = 0; i < count; i++) {
                pstmt.setString(1, "ZDL" + String.format("%05d", i + 1));
                pstmt.setString(2, String.valueOf(random.nextInt(10) + 1));
                pstmt.setString(3, String.valueOf(random.nextInt(5) + 1));
                pstmt.setString(4, generateRandomValue());
                pstmt.setString(5, generateRandomValue());
                pstmt.setString(6, generateRandomDate());
                pstmt.executeUpdate();
            }
            log.info("成功生成{}条转动惯量标定表数据", count);
        } catch (Exception e) {
            log.error("生成转动惯量标定表数据失败: {}", e.getMessage(), e);
        }
    }
    @SuppressWarnings("unused")
    private void generate转动惯量测试记录打印表Data(int count) {
        String sql = "INSERT INTO 转动惯量测试记录打印表 (测试编号, 产品型号, 产品批次, 产品名称, TestPeriodCount, TestTimes) VALUES (?, ?, ?, ?, ?, ?)";
        try (Connection conn = AccessDatabaseUtil.getConnection(DB_PATH);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            for (int i = 0; i < count; i++) {
                pstmt.setString(1, "TEST" + String.format("%05d", i + 1));
                pstmt.setString(2, "MODEL-" + generateRandomString(5));
                pstmt.setString(3, "BATCH-" + generateRandomString(3));
                pstmt.setString(4, "产品-" + generateRandomString(4));
                pstmt.setString(5, String.valueOf(random.nextInt(10) + 1));
                pstmt.setString(6, String.valueOf(random.nextInt(5) + 1));
                pstmt.executeUpdate();
            }
            log.info("成功生成{}条转动惯量测试记录打印表数据", count);
        } catch (Exception e) {
            log.error("生成转动惯量测试记录打印表数据失败: {}", e.getMessage(), e);
        }
    }
    @SuppressWarnings("unused")
    private void generate转动惯量测试记录表Data(int count) {
        String sql = "INSERT INTO 转动惯量测试记录表 (测试编号, 产品型号, 产品批次, 产品名称, TestPeriodCount, TestTimes) VALUES (?, ?, ?, ?, ?, ?)";
        try (Connection conn = AccessDatabaseUtil.getConnection(DB_PATH);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            for (int i = 0; i < count; i++) {
                pstmt.setString(1, "TEST" + String.format("%05d", i + 1));
                pstmt.setString(2, "MODEL-" + generateRandomString(5));
                pstmt.setString(3, "BATCH-" + generateRandomString(3));
                pstmt.setString(4, "产品-" + generateRandomString(4));
                pstmt.setString(5, String.valueOf(random.nextInt(10) + 1));
                pstmt.setString(6, String.valueOf(random.nextInt(5) + 1));
                pstmt.executeUpdate();
            }
            log.info("成功生成{}条转动惯量测试记录表数据", count);
        } catch (Exception e) {
            log.error("生成转动惯量测试记录表数据失败: {}", e.getMessage(), e);
        }
    }
    @SuppressWarnings("unused")
    private void generate转动惯量测试记录表oldData(int count) {
        String sql = "INSERT INTO 转动惯量测试记录表old (测试编号, 产品型号, 产品批次, 产品名称, TestPeriodCount, TestTimes) VALUES (?, ?, ?, ?, ?, ?)";
        try (Connection conn = AccessDatabaseUtil.getConnection(DB_PATH);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            for (int i = 0; i < count; i++) {
                pstmt.setString(1, "TEST" + String.format("%05d", i + 1));
                pstmt.setString(2, "MODEL-" + generateRandomString(5));
                pstmt.setString(3, "BATCH-" + generateRandomString(3));
                pstmt.setString(4, "产品-" + generateRandomString(4));
                pstmt.setString(5, String.valueOf(random.nextInt(10) + 1));
                pstmt.setString(6, String.valueOf(random.nextInt(5) + 1));
                pstmt.executeUpdate();
            }
            log.info("成功生成{}条转动惯量测试记录表old数据", count);
        } catch (Exception e) {
            log.error("生成转动惯量测试记录表old数据失败: {}", e.getMessage(), e);
        }
    }
}
