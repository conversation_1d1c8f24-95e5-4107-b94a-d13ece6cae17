package com.cirpoint.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.service.panorama.PanoramaService;
import com.cirpoint.util.FileDownloadUtil;
import java.io.File;
import java.io.IOException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 全景图热点编辑控制器
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@RestController
@RequestMapping("/panorama")
public class PanoramaController {

    private final PanoramaService panoramaService;

    @Autowired
    public PanoramaController(PanoramaService panoramaService) {
        this.panoramaService = panoramaService;
    }

    /**
     * 获取型号列表
     *
     * @return 型号列表
     */
    @GetMapping("/model/list")
    public ResponseEntity<JSONObject> getModelList() {
        try {
            JSONObject result = panoramaService.getModelList();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取型号列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取型号列表失败: " + e.getMessage());
            errorResult.set("data", new JSONArray());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 检查热点标题是否重复
     *
     * @param taskId 任务ID
     * @param title 热点标题
     * @param hotspotId 当前热点ID（编辑时排除自己）
     * @return 检查结果
     */
    @PostMapping("/hotspot/checkTitle")
    public ResponseEntity<JSONObject> checkHotspotTitle(
            @RequestParam("taskId") Long taskId,
            @RequestParam("title") String title,
            @RequestParam(value = "hotspotId", required = false) Long hotspotId) {

        try {
            JSONObject result = panoramaService.checkHotspotTitle(taskId, title, hotspotId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查热点标题失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "检查热点标题失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 创建新任务
     *
     * @param taskName    任务名称
     * @param modelId     型号ID
     * @param modelName   型号名称
     * @param description 任务描述
     * @param username    创建用户名
     * @return 创建结果
     */
    @PostMapping("/task/create")
    public ResponseEntity<JSONObject> createTask(
            @RequestParam("taskName") String taskName,
            @RequestParam(value = "modelId", required = false) String modelId,
            @RequestParam(value = "modelName", required = false) String modelName,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam("username") String username) {

        try {
            // 验证用户名参数
            if (username == null || username.trim().isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.set("success", false);
                errorResult.set("msg", "用户名不能为空");
                return ResponseEntity.ok(errorResult);
            }

            JSONObject result = panoramaService.createTask(taskName, modelId, modelName, description, username);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("创建任务失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "创建任务失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取任务列表
     *
     * @param username 用户名，用于过滤用户权限内的任务
     * @return 任务列表
     */
    @GetMapping("/task/list")
    public ResponseEntity<JSONObject> getTaskList(@RequestParam("username") String username) {
        try {
            // 验证用户名参数
            if (username == null || username.trim().isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.set("success", false);
                errorResult.set("msg", "用户名不能为空");
                return ResponseEntity.ok(errorResult);
            }

            JSONObject result = panoramaService.getTaskList(username);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取任务列表失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @GetMapping("/task/{taskId}")
    public ResponseEntity<JSONObject> getTaskDetail(@PathVariable("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.getTaskDetail(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务详情失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取任务详情失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取任务管理列表（分页）
     *
     * @param page     页码
     * @param limit    每页数量
     * @param username 用户名，用于过滤用户权限内的任务
     * @return 任务管理列表
     */
    @GetMapping("/task/management/list")
    public ResponseEntity<JSONObject> getTaskManagementList(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit,
            @RequestParam("username") String username) {
        try {
            // 验证用户名参数
            if (username == null || username.trim().isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.set("code", 1);
                errorResult.set("msg", "用户名不能为空");
                errorResult.set("count", 0);
                errorResult.set("data", new JSONArray());
                return ResponseEntity.ok(errorResult);
            }

            JSONObject result = panoramaService.getTaskManagementList(page, limit, username);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务管理列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("code", 1);
            errorResult.set("msg", "获取任务管理列表失败: " + e.getMessage());
            errorResult.set("count", 0);
            errorResult.set("data", new JSONArray());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 更新任务信息（仅任务名称和描述）
     *
     * @param taskId      任务ID
     * @param taskName    任务名称
     * @param description 任务描述
     * @return 更新结果
     */
    @PutMapping("/task/update")
    public ResponseEntity<JSONObject> updateTask(
            @RequestParam("taskId") Long taskId,
            @RequestParam("taskName") String taskName,
            @RequestParam(value = "description", required = false) String description) {
        try {
            JSONObject result = panoramaService.updateTask(taskId, taskName, description);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新任务信息失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "更新任务信息失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 删除任务（级联删除相关数据）
     *
     * @param taskId 任务ID
     * @return 删除结果
     */
    @DeleteMapping("/task/{taskId}")
    public ResponseEntity<JSONObject> deleteTask(@PathVariable("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.deleteTask(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除任务失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "删除任务失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取任务删除前的统计信息
     *
     * @param taskId 任务ID
     * @return 统计信息
     */
    @GetMapping("/task/{taskId}/delete-stats")
    public ResponseEntity<JSONObject> getTaskDeleteStats(@PathVariable("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.getTaskDeleteStats(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务删除统计信息失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 检查任务是否已存在热点数据
     *
     * @param taskId 任务ID
     * @return 检查结果
     */
    @GetMapping("/check/hotspots")
    public ResponseEntity<JSONObject> checkExistingHotspots(@RequestParam("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.checkExistingHotspots(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查热点数据失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "检查热点数据失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 清理任务的热点数据和文件信息
     *
     * @param taskId 任务ID
     * @return 清理结果
     */
    @PostMapping("/clear/data")
    public ResponseEntity<JSONObject> clearTaskData(@RequestParam("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.clearTaskData(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("清理任务数据失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "清理任务数据失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 上传全景图ZIP包
     *
     * @param taskId 任务ID
     * @param file   ZIP文件
     * @return 上传结果
     */
    @PostMapping("/upload/zip")
    public ResponseEntity<JSONObject> uploadPanoramaZip(
            @RequestParam("taskId") Long taskId,
            @RequestParam("file") MultipartFile file) {
        
        try {
            JSONObject result = panoramaService.uploadPanoramaZip(taskId, file);
            return ResponseEntity.ok(result);
        } catch (IOException e) {
            log.error("上传ZIP文件失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "上传ZIP文件失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 上传单机信息Excel文件
     *
     * @param taskId 任务ID
     * @param file   Excel文件
     * @return 上传结果
     */
    @PostMapping("/upload/excel")
    public ResponseEntity<JSONObject> uploadDeviceExcel(
            @RequestParam("taskId") Long taskId,
            @RequestParam("file") MultipartFile file) {
        
        try {
            JSONObject result = panoramaService.uploadDeviceExcel(taskId, file);
            return ResponseEntity.ok(result);
        } catch (IOException e) {
            log.error("上传Excel文件失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "上传Excel文件失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取任务下的设备列表
     *
     * @param taskId 任务ID
     * @param page   页码
     * @param limit  每页数量
     * @return 设备列表
     */
    @GetMapping("/device/list")
    public ResponseEntity<JSONObject> getDeviceList(
            @RequestParam("taskId") Long taskId,
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "limit", defaultValue = "15") int limit) {

        try {
            JSONObject result = panoramaService.getDeviceList(taskId, page, limit);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取设备列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("code", 1);
            errorResult.set("msg", "获取设备列表失败: " + e.getMessage());
            errorResult.set("count", 0);
            errorResult.set("data", new JSONArray());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 导出设备数据为Excel文件
     *
     * @param taskId 任务ID
     * @return Excel文件下载响应
     */
    @GetMapping("/device/export")
    public ResponseEntity<?> exportDeviceExcel(@RequestParam("taskId") Long taskId) {
        try {
            File excelFile = panoramaService.exportDeviceExcel(taskId);
            if (excelFile != null) {
                return FileDownloadUtil.fileResponseAndDelete(excelFile);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("导出Excel文件失败");
            }
        } catch (Exception e) {
            log.error("导出设备Excel失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("导出失败: " + e.getMessage());
        }
    }

    /**
     * 获取热点列表
     *
     * @param taskId 任务ID
     * @param panoramaId 全景节点ID（可选，用于多节点支持）
     * @param page   页码
     * @param limit  每页数量
     * @param titleKeyword 标题关键词（可选，用于筛选）
     * @param descriptionKeyword 描述关键词（可选，用于筛选）
     * @return 热点列表
     */
    @GetMapping("/hotspot/list")
    public ResponseEntity<JSONObject> getHotspotList(
            @RequestParam("taskId") Long taskId,
            @RequestParam(value = "panoramaId", required = false) String panoramaId,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit,
            @RequestParam(value = "titleKeyword", required = false) String titleKeyword,
            @RequestParam(value = "descriptionKeyword", required = false) String descriptionKeyword) {

        try {
            JSONObject result = panoramaService.getHotspotList(taskId, panoramaId, page, limit, titleKeyword, descriptionKeyword);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取热点列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取热点列表失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取当前全景节点ID
     *
     * @param taskId 任务ID
     * @return 当前节点信息
     */
    @GetMapping("/current-node")
    public ResponseEntity<JSONObject> getCurrentNode(@RequestParam("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.getCurrentNode(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取当前节点失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取当前节点失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 添加新热点
     *
     * @param taskId      任务ID
     * @param nodeId      节点ID
     * @param pan         水平角度
     * @param tilt        垂直角度
     * @param title       热点标题
     * @param description 热点描述
     * @param deviceId    关联单机ID
     * @return 添加结果
     */
    @PostMapping("/hotspot/add")
    public ResponseEntity<JSONObject> addHotspot(
            @RequestParam("taskId") Long taskId,
            @RequestParam("nodeId") String nodeId,
            @RequestParam("pan") String pan,
            @RequestParam("tilt") String tilt,
            @RequestParam("title") String title,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "deviceId", required = false) Long deviceId) {

        try {
            JSONObject result = panoramaService.addHotspot(taskId, nodeId, pan, tilt, title, description, deviceId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("添加热点失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "添加热点失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 更新热点信息
     *
     * @param hotspotId   热点ID
     * @param editedTitle       编辑后热点标题
     * @param editedDescription 编辑后热点描述
     * @param deviceId    关联单机ID
     * @return 更新结果
     */
    @PostMapping("/hotspot/update")
    public ResponseEntity<JSONObject> updateHotspot(
            @RequestParam("hotspotId") Long hotspotId,
            @RequestParam(value = "editedTitle", required = false) String editedTitle,
            @RequestParam(value = "editedDescription", required = false) String editedDescription,
            @RequestParam(value = "deviceId", required = false) Long deviceId) {

        try {
            JSONObject result = panoramaService.updateHotspot(hotspotId, editedTitle, editedDescription, deviceId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新热点信息失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "更新热点信息失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }



    /**
     * 热点定位
     *
     * @param hotspotId 热点ID
     * @return 定位信息
     */
    @PostMapping("/hotspot/locate")
    public ResponseEntity<JSONObject> locateHotspot(@RequestParam("hotspotId") Long hotspotId) {
        try {
            JSONObject result = panoramaService.locateHotspot(hotspotId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("热点定位失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "热点定位失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 删除热点
     *
     * @param hotspotId 热点ID
     * @return 删除结果
     */
    @DeleteMapping("/hotspot/{hotspotId}")
    public ResponseEntity<JSONObject> deleteHotspot(@PathVariable("hotspotId") Long hotspotId) {
        try {
            JSONObject result = panoramaService.deleteHotspot(hotspotId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除热点失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "删除热点失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 批量删除热点
     *
     * @param hotspotIds 热点ID数组
     * @return 删除结果
     */
    @DeleteMapping("/hotspot/batch")
    public ResponseEntity<JSONObject> batchDeleteHotspots(@RequestBody List<Long> hotspotIds) {
        try {
            if (hotspotIds == null || hotspotIds.isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.set("success", false);
                errorResult.set("msg", "热点ID列表不能为空");
                return ResponseEntity.ok(errorResult);
            }

            JSONObject result = panoramaService.batchDeleteHotspots(hotspotIds);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量删除热点失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "批量删除热点失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 根据热点信息查找热点记录
     *
     * @param taskId 任务ID
     * @param nodeId 节点ID
     * @param hotspotId 热点XML ID
     * @param title 热点标题
     * @param description 热点描述
     * @param skinid 热点皮肤ID
     * @return 热点记录
     */
    @PostMapping("/hotspot/findByInfo")
    public ResponseEntity<JSONObject> findHotspotByInfo(
            @RequestParam("taskId") Long taskId,
            @RequestParam(value = "nodeId", required = false) String nodeId,
            @RequestParam(value = "hotspotId", required = false) String hotspotId,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "skinid", required = false) String skinid) {
        try {
            JSONObject result = panoramaService.findHotspotByInfo(
                taskId, nodeId, hotspotId, title, description, skinid);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查找热点失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "查找热点失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 根据坐标查找热点记录（更可靠的匹配方式）
     *
     * @param taskId 任务ID
     * @param nodeId 节点ID
     * @param pan 水平角度
     * @param tilt 垂直角度
     * @return 热点记录
     */
    @PostMapping("/hotspot/findByCoordinates")
    public ResponseEntity<JSONObject> findHotspotByCoordinates(
            @RequestParam("taskId") Long taskId,
            @RequestParam(value = "nodeId", required = false) String nodeId,
            @RequestParam("pan") String pan,
            @RequestParam("tilt") String tilt) {
        try {
            JSONObject result = panoramaService.findHotspotByCoordinates(taskId, nodeId, pan, tilt);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("根据坐标查找热点失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "根据坐标查找热点失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }



    /**
     * 导出修改后的全景图包
     *
     * @param taskId 任务ID
     * @return ZIP文件下载响应
     */
    @PostMapping("/export")
    public ResponseEntity<?> exportPanorama(@RequestParam("taskId") Long taskId) {
        try {
            String zipFilePath = panoramaService.exportPanorama(taskId);
            if (zipFilePath != null) {
                return FileDownloadUtil.fileResponseAndDelete(new File(zipFilePath));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("导出全景图包失败");
            }
        } catch (Exception e) {
            log.error("导出全景图包失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("导出失败: " + e.getMessage());
        }
    }

    /**
     * 获取全景图预览路径
     *
     * @param taskId 任务ID
     * @return 预览路径
     */
    @GetMapping("/preview/path")
    public ResponseEntity<JSONObject> getPreviewPath(@RequestParam("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.getPreviewPath(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取预览路径失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取预览路径失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 重新导入指定任务的型号单机数据
     *
     * @param taskId 任务ID
     * @return 操作结果
     */
    @PostMapping("/task/{taskId}/refresh-devices")
    public ResponseEntity<JSONObject> refreshDevicesForTask(@PathVariable("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.refreshDeviceData(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("重新导入单机数据失败, taskId={}", taskId, e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "重新导入单机数据失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

}
