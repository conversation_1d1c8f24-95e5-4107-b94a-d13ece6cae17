// main-app.js - 主应用逻辑，整合其他模块

// 编辑器应用主体
const editorApp = (function () {
    // 初始化函数
    function init() {
        // 初始化Monaco编辑器
        window.editorManager.initMonacoEditor();

        // 初始化多路径管理器（先初始化多路径管理器，确保启用的路径已加载）
        window.multiPathManager.initMultiPathManager();

        // 初始化文件树（使用多路径管理器中的启用路径）
        window.treeManager.initZTree();

        // 创建标签页管理器 - 确保在编辑器加载完成后创建
        // 由于Monaco编辑器是异步加载的，我们需要在editor-manager.js中处理标签页管理器的创建

        // 初始化终端
        window.terminalManager.init();

        // 绑定事件
        bindEvents();

        // 初始化侧边栏页签
        window.uiUtils.switchSidebarTab('explorer');
    }

    // 绑定事件
    function bindEvents() {
        // 右键菜单事件 - 确保先隐藏菜单再执行对应操作
        $('#m_add_file').on('click', function() {
            window.treeManager.hideRMenu();
            window.treeManager.addFile();
        });

        $('#m_add_folder').on('click', function() {
            window.treeManager.hideRMenu();
            window.treeManager.addFolder();
        });

        $('#m_delete').on('click', function() {
            window.treeManager.hideRMenu();
            window.treeManager.deleteNode();
        });

        $('#m_rename').on('click', function() {
            window.treeManager.hideRMenu();
            window.treeManager.renameNode();
        });

        $('#m_copy_path').on('click', function() {
            window.treeManager.hideRMenu();
            window.treeManager.copyPath();
        });

        $('#m_download').on('click', function() {
            window.treeManager.hideRMenu();
            window.treeManager.downloadFile();
        });

        $('#m_search_in_folder').on('click', function() {
            window.treeManager.hideRMenu();
            window.treeManager.searchInFolder();
        });

        $('#m_export').on('click', function() {
            window.treeManager.hideRMenu();
            window.treeManager.exportFiles();
        });

        $('#m_import').on('click', function() {
            window.treeManager.hideRMenu();
            window.treeManager.importFiles();
        });

        $('#m_open_in_terminal').on('click', function() {
            window.treeManager.hideRMenu();
            window.treeManager.openInTerminal();
        });

        $('#m_properties').on('click', function() {
            window.treeManager.hideRMenu();
            window.treeManager.showFileProperties();
        });

        // 保存按钮点击事件
        $('#saveBtnContainer').off('click').on('click', function (e) {
            e.preventDefault();
            window.editorManager.saveFile();
        });

        // 标签页点击事件
        $('#tabList').on('click', '.tab-item', function () {
            const path = $(this).data('path');

            // 先保存当前页签的滚动位置
            if (window.tabManager.activeTab && window.tabManager.activeTab.model && window.editor) {
                window.tabManager.activeTab.viewState = window.editor.saveViewState();
            }

            window.tabManager.setActive(path, false);  // 不需要重新加载内容
        });

        // 关闭标签页
        $('#tabList').on('click', '.tab-close', function (e) {
            e.stopPropagation();
            const path = $(this).closest('.tab-item').data('path');
            window.tabManager.close(path);
        });

        // 添加标签页右键菜单事件
        window.tabManagerModule.bindTabRightMenuEvents();

        // 添加拖动调整宽度功能
        window.editorManager.initResizer();

        // 添加搜索相关事件
        window.searchManager.bindSearchEvents();

        // 侧边栏页签切换事件
        $('.sidebar-tab').on('click', function() {
            const target = $(this).data('target');
            window.uiUtils.switchSidebarTab(target);
        });

        // 终端切换按钮事件
        $('#terminalToggleBtn').on('click', function() {
            window.terminalManager.toggle();
        });

        // 添加终端相关的快捷键
        $(document).on('keydown', function(e) {
            // Ctrl+` 或 Ctrl+~ 切换终端
            if (e.ctrlKey && (e.key === '`' || e.key === '~')) {
                e.preventDefault();
                window.terminalManager.toggle();
            }
        });
    }

    // 刷新文件树
    function refreshFileTree() {
        window.treeManager.initZTree();
    }

    return {
        init: init,
        bindPasswordEvents: window.authManager.bindPasswordEvents,
        clearAll: window.uiUtils.clearAll,
        refreshFileTree: refreshFileTree
    };
})();

// 页面加载完成后初始化密码验证
$(document).ready(function () {
    console.log('页面加载完成，开始初始化应用...');

    // 检查Viewer.js是否正确加载
    if (typeof Viewer === 'undefined') {
        console.error('警告: Viewer.js未正确加载，图片预览功能将受限');
        // 尝试重新加载Viewer.js
        $.getScript('static/lib/viewerjs-main/dist/viewer.min.js')
            .done(function() {
                console.log('Viewer.js已动态加载成功');
            })
            .fail(function() {
                console.error('Viewer.js动态加载失败');
            });
    } else {
        console.log('Viewer.js已正确加载');
    }

    // 绑定密码验证事件
    editorApp.bindPasswordEvents();
    // 不要在这里直接调用init()，等待密码验证通过后再调用
});

// 导出全局变量
window.editorApp = editorApp;