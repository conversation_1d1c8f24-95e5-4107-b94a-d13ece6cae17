<div class="apiDetail">
<div>
	<h2><span>String</span><span class="path">treeNode.</span>parentTId</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.core</span> 核心 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>treeNode 节点的父节点唯一标识 tId。</p>
			<p class="highlight_red">1、v3.x 用 parentTId 替换了原先的 parentNode 属性，同时增加了 getParentNode 方法，以避免原先 parentNode 造成的 clone 死循环</p>
			<p class="highlight_red">2、初始化节点数据时，由 zTree 增加此属性，请勿提前赋值</p>
		</div>
	</div>
	<h3>String 格式说明</h3>
	<div class="desc">
	<p>zTree 内部生成的节点唯一标识，请参考 treeNode.tId 的说明</p>
	<p class="highlight_red">如果 treeNode 是根节点，则 parentTId = null</p>
	</div>
	<h3>treeNode 举例</h3>
	<h4>1. 查看当前被选中的节点的父节点 tId</h4>
	<pre xmlns=""><code>var treeObj = $.fn.zTree.getZTreeObj("tree");
var sNodes = treeObj.getSelectedNodes();
if (sNodes.length > 0) {
	var parentTId = sNodes[0].parentTId;
}
</code></pre>
</div>
</div>