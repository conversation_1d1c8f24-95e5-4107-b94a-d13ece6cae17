/**
 * 全景图热点编辑系统 - 文件上传模块
 * 
 * <AUTHOR>
 * @date 2025-06-10
 * @description 处理ZIP文件上传、上传前检查等功能
 * @requires panorama-core.js
 */

/**
 * 全景图编辑器文件上传模块
 */
var PanoramaUpload = (function() {
    'use strict';
    
    // ==================== 上传前检查 ====================
    
    /**
     * 检查是否已存在热点数据（上传前）
     * @param {Object} uploadObj 上传对象
     * @param {Object} uploadInstance 上传实例
     */
    function checkExistingHotspotsBeforeUpload(uploadObj, uploadInstance) {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) {
            layui.layer.msg('请先选择任务', { icon: 2 });
            return;
        }
        
        var $ = layui.$;
        $.get('/panorama/check/hotspots', { taskId: currentTaskId }, function (res) {
            if (res.success) {
                if (res.hasData) {
                    // 存在热点数据，显示确认对话框
                    layui.layer.confirm('当前任务已存在热点数据，重新上传将清除所有已编辑的热点信息，是否继续？', {
                        icon: 3,
                        title: '重新上传全景图',
                        btn: ['继续上传', '取消']
                    }, function (index) {
                        // 用户确认继续上传
                        layui.layer.close(index);
                        clearTaskDataAndUpload(uploadObj, uploadInstance);
                    }, function (index) {
                        // 用户取消上传
                        layui.layer.close(index);
                    });
                } else {
                    // 不存在热点数据，直接上传
                    proceedWithUpload(uploadObj, uploadInstance);
                }
            } else {
                layui.layer.msg('检查热点数据失败: ' + res.msg, { icon: 2 });
            }
        }).fail(function () {
            layui.layer.msg('网络请求失败', { icon: 2 });
        });
    }

    /**
     * 清理任务数据并上传
     * @param {Object} uploadObj 上传对象
     * @param {Object} uploadInstance 上传实例
     */
    function clearTaskDataAndUpload(uploadObj, uploadInstance) {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        if (!currentTaskId) return;
        
        layui.layer.load();
        var $ = layui.$;
        $.post('/panorama/clear/data', { taskId: currentTaskId }, function (res) {
            if (res.success) {
                // 清理成功，继续上传
                proceedWithUpload(uploadObj, uploadInstance);
            } else {
                layui.layer.closeAll('loading');
                layui.layer.msg('清理数据失败: ' + res.msg, { icon: 2 });
            }
        }).fail(function () {
            layui.layer.closeAll('loading');
            layui.layer.msg('网络请求失败', { icon: 2 });
        });
    }

    /**
     * 执行文件上传
     * @param {Object} uploadObj 上传对象
     * @param {Object} uploadInstance 上传实例
     */
    function proceedWithUpload(uploadObj, uploadInstance) {
        // 显示加载状态
        var loadingLayers = document.querySelectorAll('.layui-layer-loading');
        if (!loadingLayers.length) {
            layui.layer.load();
        }

        // 使用Layui upload实例的upload方法继续上传
        if (uploadInstance && typeof uploadInstance.upload === 'function') {
            // 临时修改upload配置，移除before回调以避免循环
            var originalBefore = uploadInstance.config.before;
            uploadInstance.config.before = function () {
                layui.layer.load(); // 显示加载状态
                return true; // 允许上传
            };

            // 重新触发上传
            uploadInstance.upload();

            // 恢复原始before回调
            setTimeout(function () {
                uploadInstance.config.before = originalBefore;
            }, 100);
        } else {
            layui.layer.closeAll('loading');
            layui.layer.msg('上传实例无效，请重新选择文件', { icon: 2 });
        }
    }
    
    // ==================== 上传状态管理 ====================
    
    /**
     * 更新上传状态显示
     * @param {string} type 上传类型（'zip' 或 'excel'）
     * @param {string} status 状态文本
     */
    function updateUploadStatus(type, status) {
        var statusElement;
        if (type === 'zip') {
            statusElement = document.getElementById('zipStatus');
        } else if (type === 'excel') {
            statusElement = document.getElementById('excelStatus');
        }
        
        if (statusElement) {
            statusElement.textContent = status;
        }
    }
    
    /**
     * 处理上传成功
     * @param {Object} res 响应数据
     * @param {string} type 上传类型
     */
    function handleUploadSuccess(res, type) {
        layui.layer.closeAll('loading');
        
        if (res.success) {
            var message = type === 'zip' ? 'ZIP文件上传成功' : 'Excel文件上传成功';
            layui.layer.msg(message, { icon: 1 });
            
            // 更新状态显示
            updateUploadStatus(type, '已上传');
            
            // 根据上传类型执行相应的后续操作
            if (type === 'zip') {
                // ZIP文件上传成功后的操作
                if (typeof PanoramaHotspot !== 'undefined' && PanoramaHotspot.loadHotspotData) {
                    PanoramaHotspot.loadHotspotData();
                }
                if (typeof PanoramaPreview !== 'undefined' && PanoramaPreview.loadPreview) {
                    PanoramaPreview.loadPreview();
                }
            } else if (type === 'excel') {
                // Excel文件上传成功后的操作
                if (typeof PanoramaDevice !== 'undefined' && PanoramaDevice.updateDeviceStatus) {
                    PanoramaDevice.updateDeviceStatus();
                }
            }
        } else {
            layui.layer.msg('上传失败: ' + res.msg, { icon: 2 });
        }
    }
    
    /**
     * 处理上传失败
     * @param {string} type 上传类型
     * @param {string} errorMsg 错误信息
     */
    function handleUploadError(type, errorMsg) {
        layui.layer.closeAll('loading');
        
        var message = errorMsg || '上传失败';
        layui.layer.msg(message, { icon: 2 });
    }
    
    // ==================== 上传进度管理 ====================
    
    /**
     * 显示上传进度
     * @param {number} percent 进度百分比
     * @param {string} type 上传类型
     */
    function showUploadProgress(percent, type) {
        var message = type === 'zip' ? '正在上传ZIP文件...' : '正在上传Excel文件...';
        
        // 如果支持进度显示，可以在这里实现
        if (percent < 100) {
            // 显示进度信息
            console.log(message + ' ' + percent + '%');
        }
    }
    
    /**
     * 隐藏上传进度
     */
    function hideUploadProgress() {
        // 隐藏进度显示
        layui.layer.closeAll('loading');
    }
    
    // ==================== 文件验证 ====================
    
    /**
     * 验证ZIP文件
     * @param {File} file 文件对象
     * @returns {boolean} 是否有效
     */
    function validateZipFile(file) {
        if (!file) {
            layui.layer.msg('请选择文件', { icon: 2 });
            return false;
        }
        
        // 检查文件扩展名
        var fileName = file.name.toLowerCase();
        if (!fileName.endsWith('.zip')) {
            layui.layer.msg('请选择ZIP格式的文件', { icon: 2 });
            return false;
        }
        
        // 检查文件大小（例如限制为100MB）
        var maxSize = 100 * 1024 * 1024; // 100MB
        if (file.size > maxSize) {
            layui.layer.msg('文件大小不能超过100MB', { icon: 2 });
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证Excel文件
     * @param {File} file 文件对象
     * @returns {boolean} 是否有效
     */
    function validateExcelFile(file) {
        if (!file) {
            layui.layer.msg('请选择文件', { icon: 2 });
            return false;
        }
        
        // 检查文件扩展名
        var fileName = file.name.toLowerCase();
        if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
            layui.layer.msg('请选择Excel格式的文件（.xlsx或.xls）', { icon: 2 });
            return false;
        }
        
        // 检查文件大小（例如限制为10MB）
        var maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            layui.layer.msg('文件大小不能超过10MB', { icon: 2 });
            return false;
        }
        
        return true;
    }
    
    // ==================== 上传配置 ====================
    
    /**
     * 获取上传配置
     * @param {string} type 上传类型
     * @returns {Object} 上传配置
     */
    function getUploadConfig(type) {
        var currentTaskId = PanoramaCore.getCurrentTaskId();
        
        var baseConfig = {
            data: {
                taskId: function () {
                    return currentTaskId;
                }
            },
            before: function (obj) {
                if (!currentTaskId) {
                    layui.layer.msg('请先选择任务', { icon: 2 });
                    return false;
                }
                return true;
            }
        };
        
        if (type === 'zip') {
            return Object.assign(baseConfig, {
                url: '/panorama/upload/zip',
                accept: 'file',
                exts: 'zip',
                done: function (res) {
                    handleUploadSuccess(res, 'zip');
                },
                error: function () {
                    handleUploadError('zip');
                }
            });
        } else if (type === 'excel') {
            return Object.assign(baseConfig, {
                url: '/panorama/upload/excel',
                accept: 'file',
                exts: 'xlsx|xls',
                done: function (res) {
                    handleUploadSuccess(res, 'excel');
                },
                error: function () {
                    handleUploadError('excel');
                }
            });
        }
        
        return baseConfig;
    }
    
    // ==================== 公共API ====================
    return {
        // 上传前检查
        checkExistingHotspotsBeforeUpload: checkExistingHotspotsBeforeUpload,
        clearTaskDataAndUpload: clearTaskDataAndUpload,
        proceedWithUpload: proceedWithUpload,
        
        // 上传状态管理
        updateUploadStatus: updateUploadStatus,
        handleUploadSuccess: handleUploadSuccess,
        handleUploadError: handleUploadError,
        
        // 上传进度管理
        showUploadProgress: showUploadProgress,
        hideUploadProgress: hideUploadProgress,
        
        // 文件验证
        validateZipFile: validateZipFile,
        validateExcelFile: validateExcelFile,
        
        // 上传配置
        getUploadConfig: getUploadConfig
    };
})();

// ==================== 向后兼容性支持 ====================
// 保持原有函数的全局访问方式

window.checkExistingHotspotsBeforeUpload = PanoramaUpload.checkExistingHotspotsBeforeUpload;
window.clearTaskDataAndUpload = PanoramaUpload.clearTaskDataAndUpload;
window.proceedWithUpload = PanoramaUpload.proceedWithUpload;
