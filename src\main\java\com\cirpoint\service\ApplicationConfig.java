package com.cirpoint.service;

import org.springframework.beans.factory.annotation.Value;

public class ApplicationConfig {

	@Value("${app.pdf-font-path}")
	protected String pdfFontPath;

	@Value("${excel.tpl.path}")
	protected String excelTplPath;

	@Value("${file.upload.path}")
	protected String fileUploadPath;

	@Value("${file.temp.path}")
	protected String tempPath;

	@Value("${push.temp.path}")
	protected String pushTempPath;

	@Value("${dl.sync.path}")
	protected String dlSyncPath;

	@Value("${rg.sync.path}")
	protected String rgSyncPath;

}
