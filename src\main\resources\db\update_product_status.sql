-- 更新TEST_PRODUCT_TREE表中product类型的STATUS_字段
DECLARE
  -- 定义状态数组
  TYPE status_array IS TABLE OF VARCHAR2(50);
  v_status_list status_array := status_array(
    '大纲编制', '大纲审查', '报告编制', '报告审查',
    '小批量报告编制', '小批量报告审查', '状态鉴定文件编制',
    '院内审查', '前置审查', '正式审查'
  );
BEGIN
  -- 遍历所有product类型的记录
  FOR product IN (SELECT ID_ FROM TEST_PRODUCT_TREE WHERE TYPE_ = 'product')
  LOOP
    -- 随机选择一个状态并更新
    UPDATE TEST_PRODUCT_TREE
    SET STATUS_ = 
      CASE CEIL(DBMS_RANDOM.VALUE(1, 10))
        WHEN 1 THEN '大纲编制'
        WHEN 2 THEN '大纲审查'
        WHEN 3 THEN '报告编制'
        WHEN 4 THEN '报告审查'
        WHEN 5 THEN '小批量报告编制'
        WHEN 6 THEN '小批量报告审查'
        WHEN 7 THEN '状态鉴定文件编制'
        WHEN 8 THEN '院内审查'
        WHEN 9 THEN '前置审查'
        WHEN 10 THEN '正式审查'
      END
    WHERE ID_ = product.ID_;
  END LOOP;
  
  COMMIT;
  
  -- 输出更新结果
  DBMS_OUTPUT.PUT_LINE('更新完成，共更新 ' || SQL%ROWCOUNT || ' 条记录');
END;
/
