<!DOCTYPE html>
<HTML>
<HEAD>
	<TITLE> ZTREE DEMO - getNodeByParam / getNodesByParam / getNodesByParamFuzzy</TITLE>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<link rel="stylesheet" href="http://maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css">
	<link rel="stylesheet" href="../../../css/demo.css" type="text/css">
	<link rel="stylesheet" href="../../../css/zTreeStyle/zTreeStyle.css" type="text/css">
	<script type="text/javascript" src="../../../js/jquery-1.4.4.min.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.core.js"></script>
	<!--  <script type="text/javascript" src="../../../js/jquery.ztree.excheck.js"></script>
		<script type="text/javascript" src="../../../js/jquery.ztree.exedit.js"></script>-->
	<style>
		.ztree li span {
			position: relative;
		}
		.ztree li > a.highlight_alt
		{
			color: #00A600;
			font-weight: bold;
		}
		.ztree li > a.highlight span.node_name:before,
		.ztree li > a.highlight_hiddennodes span.node_name:before,
		.ztree li > a.hiddennodes span.node_name:before 
		{
			font-family: FontAwesome;
			color: green;
			font-size: 14px;
			position: absolute;
			left: 100%;
			margin-left: 8px;
		}
		.ztree li > a.highlight span.node_name:before
		{
			content: '\f058';
		}
		.ztree li > a.hiddennodes span.node_name:before 
		{
			content: '\f00e';
		}
		.ztree li > a.highlight.hiddennodes span.node_name:before
		{
			content: '\f058  \f00e';
		}
	</style>
	<SCRIPT type="text/javascript">
		<!--
		var setting = {
			callback:
			{
				onCollapse: clickButton,
				onExpand: clickButton
			},
			data: {
				key: {
					title: "t"
				},
				simpleData: {
					enable: true
				}				
			},
			view: {
				fontCss: getFontCss,
				// nodeClasses: getNodeClasses
			}
		};

		var zNodes =[
			{ id:1, pId:0, name:"节点搜索演示 1", t:"id=1", open:true},
			{ id:11, pId:1, name:"关键字可以是名字", t:"id=11"},
			{ id:12, pId:1, name:"关键字可以是level", t:"id=12"},
			{ id:13, pId:1, name:"关键字可以是id", t:"id=13"},
			{ id:14, pId:1, name:"关键字可以是各种属性", t:"id=14"},
			{ id:2, pId:0, name:"节点搜索演示 2", t:"id=2", open:true},
			{ id:21, pId:2, name:"可以只搜索一个节点", t:"id=21"},
			{ id:22, pId:2, name:"可以搜索节点集合", t:"id=22"},
			{ id:23, pId:2, name:"搜我吧", t:"id=23"},
			{ id:3, pId:0, name:"节点搜索演示 3", t:"id=3", open:true },
			{ id:31, pId:3, name:"我的 id 是: 31", t:"id=31"},
			{ id:32, pId:31, name:"我的 id 是: 32", t:"id=32"},
			{ id:33, pId:32, name:"我的 id 是: 33", t:"id=33"}
		];

		function focusKey(e) {
			if (key.hasClass("empty")) {
				key.removeClass("empty");
			}
		}
		function blurKey(e) {
			if (key.get(0).value === "") {
				key.addClass("empty");
			}
		}
		var lastValue = "", nodeList = [], fontCss = {}, nodeClasses = [];
		function clickRadio(e) {
			lastValue = "";
			searchNode(e);
		}
		function clickButton(e, treeId, treeNode) {
			if ( $("#styleNodesByCSS").attr('checked') ) return;
			updateNodes(false, treeNode);
			updateNodes(true, treeNode)
		}
		function searchNode(e) {
			var zTree = $.fn.zTree.getZTreeObj("treeDemo");
			if (!$("#getNodesByFilter").attr("checked")) {
				var value = $.trim(key.get(0).value);
				var keyType = "";
				if ($("#name").attr("checked")) {
					keyType = "name";
				} else if ($("#level").attr("checked")) {
					keyType = "level";
					value = parseInt(value);
				} else if ($("#id").attr("checked")) {
					keyType = "id";
					value = parseInt(value);
				}
				if (key.hasClass("empty")) {
					value = "";
				}
				if (lastValue === value) return;
				lastValue = value;
				updateNodes(false);
				if (value === "") return;

				if ($("#getNodeByParam").attr("checked")) {
					var node = zTree.getNodeByParam(keyType, value);
					if (node === null) {
						nodeList = [];
					} else {
						nodeList = [node];
					}
				} else if ($("#getNodesByParam").attr("checked")) {
					nodeList = zTree.getNodesByParam(keyType, value);
				} else if ($("#getNodesByParamFuzzy").attr("checked")) {
					nodeList = zTree.getNodesByParamFuzzy(keyType, value);
				}
			} else {
				updateNodes(false);
				nodeList = zTree.getNodesByFilter(filter);
			}
			updateNodes(true);

		}
		function updateNodes(highlight, node = null) {
			var zTree = $.fn.zTree.getZTreeObj("treeDemo");
			var applyClasses = $("#styleNodesByClasses").attr('checked');
			var expanded = node && node.open;

			// If expanding a node then it MUST be a parent
			// in which case it cannot be hiding matched nodes
			if ( applyClasses && expanded )
			{
				node.hiddenNodes = false;
				zTree.updateNode(node);
			}

			for( var i=0, l=nodeList.length; i<l; i++ )
			{
				nodeList[i].highlight = highlight;
				nodeList[i].hiddenNodes = false;
				if ( applyClasses && highlight )
				{
					// Make parent nodes of matched nodes show the 
					// existence of hidden nodes if the parent is closed.
					var node = nodeList[i];
					while( true )
					{
						if ( ! node.parentTId ) break;
						var parentNode = zTree.getNodeByTId( node.parentTId );
						if ( parentNode.isParent && parentNode.open ) break;
						parentNode.hiddenNodes = true;
						zTree.updateNode( parentNode );
						node = parentNode;
					}
				}
				zTree.updateNode(nodeList[i]);
			}
		}
		function getFontCss(treeId, treeNode) {
			return $("#styleNodesByCSS").attr('checked') 
				? ((!!treeNode.highlight) ? {color:"#A60000", "font-weight":"bold"} : {color:"#333", "font-weight":"normal"})
				: {color:"#333", "font-weight":"normal"};
		}
		function getNodeClasses(treeId, treeNode) {
			var classes = $("#styleNodesByCSS").attr('checked') || ! ( !!treeNode.highlight || !!treeNode.hiddenNodes )
				? {remove: ['highlight','highlight_alt','hiddennodes','highlight_hiddennodes']}
				: ( !!treeNode.highlight
					? ( (!!treeNode.hiddenNodes) 
						? {add:['highlight','highlight_alt','hiddennodes']} 
						: {add:['highlight','highlight_alt']} 
						)
					: {add:['hiddennodes','highlight_alt']} 
					);
			return classes;
		}
		function filter(node) {
			return !node.isParent && node.isFirstNode;
		}

		function resetTree(e) {
			if ($("#styleNodesByCSS").attr('checked')) {
				delete setting.view.nodeClasses;
				setting.view.fontCss = getFontCss;
			} else {
				delete setting.view.fontCss;
				setting.view.nodeClasses = getNodeClasses;
			}
			initTree();
			clickRadio(e);
		}

		function initTree() {
			$.fn.zTree.init($("#treeDemo"), setting, zNodes);
		}

		var key;
		$(document).ready(function(){
			initTree();
			key = $("#key");
			key.bind("focus", focusKey)
			.bind("blur", blurKey)
			.bind("propertychange", searchNode)
			.bind("input", searchNode);
			$("#name").bind("change", clickRadio);
			$("#level").bind("change", clickRadio);
			$("#id").bind("change", clickRadio);
			$("#getNodeByParam").bind("change", clickRadio);
			$("#getNodesByParam").bind("change", clickRadio);
			$("#getNodesByParamFuzzy").bind("change", clickRadio);
			$("#getNodesByFilter").bind("change", clickRadio);
			$("#styleNodesByCSS").bind("change", resetTree);
			$("#styleNodesByClasses").bind("change", resetTree);
			// $(".ztree li > span.button").bind("click", clickButton);
		});
		//-->
	</SCRIPT>
</HEAD>

<BODY>
<h1>根据参数查找节点</h1>
<h6>[ 文件路径: core/searchNodes.html ]</h6>
<div class="content_wrap">
	<div class="zTreeDemoBackground left">
		<ul id="treeDemo" class="ztree"></ul>
	</div>
	<div class="right">
		<ul class="info">
			<li class="title"><h2>1、getNodeByParam / getNodesByParam / getNodesByParamFuzzy 方法操作说明</h2>
				<ul class="list">
				<li class="highlight_red">使用 zTreeObj.getNodeByParam / getNodesByParam / getNodesByParamFuzzy / getNodeByTId 方法，详细请参见 API 文档中的相关内容</li>
				<li><p>搜索试试看：<br/>
						属性值( value )：<input type="text" id="key" value="" class="empty" /><br/>
						属性( key )：<input type="radio" id="name" name="keyType" class="radio first" checked /><span>name (string)</span><br/>
						<input type="radio" id="level" name="keyType" class="radio" style="margin-left:68px;" /><span>level (number) ... 根节点 level = 0</span><br/>
						<input type="radio" id="id" name="keyType" class="radio" style="margin-left:68px;" /><span>id (number)</span><br/>
						方法：<input type="radio" id="getNodeByParam" name="funType" class="radio first" /><span>getNodeByParam</span><br/>
						<input type="radio" id="getNodesByParam" name="funType" class="radio" style="margin-left:36px;" /><span>getNodesByParam</span><br/>
						<input type="radio" id="getNodesByParamFuzzy" name="funType" class="radio" style="margin-left:36px;" checked /><span>getNodesByParamFuzzy (only string)</span><br/>
						<input type="radio" id="getNodesByFilter" name="funType" class="radio" style="margin-left:36px;" /><span>getNodesByFilter (参考本页源码中 function filter)</span><br/>
						样式：<input type="radio" id="styleNodesByCSS" name="styleType" class="radio first" checked /><span>styleNodesByCSS</span><br/>
						<input type="radio" id="styleNodesByClasses" name="styleType" class="radio" style="margin-left:36px;" /><span>styleNodesByClasses</span><br/>
					</p>
				</li>
				</ul>
			</li>
			<li class="title"><h2>2、setting 配置信息说明</h2>
				<ul class="list">
				<li>不需要对 setting 进行特殊设置</li>
				</ul>
			</li>
			<li class="title"><h2>3、treeNode 节点数据说明</h2>
				<ul class="list">
				<li class="highlight_red">请注意各个方法使用时保证传入查找的参数类型与设定要查找的属性的类型一致</li>
				</ul>
			</li>
		</ul>
	</div>
</div>
</BODY>
</HTML>