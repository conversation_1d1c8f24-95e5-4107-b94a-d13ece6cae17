package com.cirpoint.model;

import lombok.Data;

/**
 * 文件属性响应类
 */
@Data
public class FilePropertiesResponse {
    private String name;              // 文件名
    private String path;              // 文件路径
    private boolean directory;        // 是否为目录
    private long size;                // 文件大小（字节）
    private String readableSize;      // 可读文件大小
    private long creationTime;        // 创建时间
    private String creationTimeStr;   // 可读创建时间
    private long lastModifiedTime;    // 最后修改时间
    private String lastModifiedTimeStr; // 可读最后修改时间
    private int childCount;           // 子文件/文件夹数量（目录适用）
} 