package com.cirpoint.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 全景图文件访问控制器
 * 用于提供解压后的全景图文件访问
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@RestController
@RequestMapping("/file/preview")
public class PanoramaFileController {

    @Value("${file.upload.path:D:/DataPkgFile/}")
    private String fileUploadPath;

    /**
     * 访问全景图文件
     *
     * @param request HTTP请求
     * @return 文件资源
     */
    @GetMapping("/**")
    public ResponseEntity<Resource> getFile(HttpServletRequest request) {
        try {
            // 获取请求路径
            String requestPath = request.getRequestURI();
            String filePath = requestPath.replace("/file/preview", "");
            
            // 构建完整文件路径
            Path fullPath = Paths.get(fileUploadPath + filePath).normalize();
            File file = fullPath.toFile();
            
            // 检查文件是否存在
            if (!file.exists() || !file.isFile()) {
                return ResponseEntity.notFound().build();
            }
            
            // 安全检查：确保文件在允许的目录内
            if (!fullPath.startsWith(Paths.get(fileUploadPath).normalize())) {
                log.warn("尝试访问不安全的文件路径: {}", fullPath);
                return ResponseEntity.badRequest().build();
            }
            
            // 创建文件资源
            Resource resource = new FileSystemResource(file);
            
            // 确定文件类型
            String contentType = determineContentType(file.getName());
            
            // 构建响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(contentType));
            
            // 对于HTML文件，设置缓存控制
            if (contentType.equals("text/html")) {
                headers.setCacheControl("no-cache, no-store, must-revalidate");
                headers.setPragma("no-cache");
                headers.setExpires(0);
            }
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("访问文件失败: {}", request.getRequestURI(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 确定文件的Content-Type
     *
     * @param fileName 文件名
     * @return Content-Type
     */
    private String determineContentType(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        
        switch (extension) {
            case "html":
            case "htm":
                return "text/html";
            case "js":
                return "application/javascript";
            case "css":
                return "text/css";
            case "xml":
                return "application/xml";
            case "json":
                return "application/json";
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "svg":
                return "image/svg+xml";
            case "ico":
                return "image/x-icon";
            case "woff":
                return "font/woff";
            case "woff2":
                return "font/woff2";
            case "ttf":
                return "font/ttf";
            case "eot":
                return "application/vnd.ms-fontobject";
            default:
                return "application/octet-stream";
        }
    }
    
    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        
        return fileName.substring(lastDotIndex + 1);
    }
}
