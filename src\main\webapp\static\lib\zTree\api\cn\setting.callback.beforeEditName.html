<div class="apiDetail">
<div>
	<h2><span>Function(treeId, treeNode)</span><span class="path">setting.callback.</span>beforeEditName</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.exedit</span> 扩展 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>用于捕获节点编辑按钮的 click 事件，并且根据返回值确定是否允许进入名称编辑状态</p>
			<p class="highlight_red">此事件回调函数最主要是用于捕获编辑按钮的点击事件，然后触发自定义的编辑界面操作。</p>
			<p>默认值：null</p>
		</div>
	</div>
	<h3>Function 参数说明</h3>
	<div class="desc">
	<h4><b>treeId</b><span>String</span></h4>
	<p>对应 zTree 的 <b class="highlight_red">treeId</b>，便于用户操控</p>
	<h4 class="topLine"><b>treeNode</b><span>JSON</span></h4>
	<p>将要进入编辑名称状态的节点 JSON 数据对象</p>
	<h4 class="topLine"><b>返回值</b><span>Boolean</span></h4>
	<p>返回值是 true / false</p>
	<p class="highlight_red">如果返回 false，节点将无法进入 zTree 默认的编辑名称状态</p>
	</div>
	<h3>setting & function 举例</h3>
	<h4>1. 禁止修改父节点的名称</h4>
	<pre xmlns=""><code>function zTreeBeforeEditName(treeId, treeNode) {
	return !treeNode.isParent;
}
var setting = {
	edit: {
		enable: true
	},
	callback: {
		beforeEditName: zTreeBeforeEditName
	}
};
......</code></pre>
</div>
</div>