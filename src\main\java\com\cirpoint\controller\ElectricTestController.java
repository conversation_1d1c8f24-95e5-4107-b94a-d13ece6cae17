package com.cirpoint.controller;

import com.cirpoint.service.ElectricTestService;
import com.cirpoint.util.FileDownloadUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 电测试管理
 */
@RestController
@RequestMapping("/electric/test")
public class ElectricTestController {

	private final ElectricTestService electricTestService;

	@Autowired
	public ElectricTestController(ElectricTestService electricTestService) {
		this.electricTestService = electricTestService;
	}

	/**
	 * 导出测试事件
	 *
	 * @param query 查询条件
	 * @return Excel文件下载响应
	 */
	@PostMapping("/export/event")
	public ResponseEntity<?> exportTestEvent(@RequestParam("query") String query) {
		return FileDownloadUtil.fileResponseAndDelete(electricTestService.exportTestEvent(query));
	}

	/**
	 * 同步创建测试表格
	 *
	 * @param model    模型名称
	 * @param nodeCode 节点代码
	 * @return 创建结果
	 */
	@PostMapping("/sync/create/file")
	public ResponseEntity<?> syncCreateTable(
			@RequestParam(value = "model", required = false, defaultValue = "null") String model,
			@RequestParam(value = "nodeCode", required = false, defaultValue = "null") String nodeCode) {
		return ResponseEntity.ok(electricTestService.syncCreateTable(model, nodeCode));
	}

	/**
	 * 同步测试表格数据
	 *
	 * @return 同步结果
	 */
	@PostMapping("/sync/test/file")
	public ResponseEntity<?> syncTestFile() {
		return ResponseEntity.ok(electricTestService.syncTestFile());
	}

	/**
	 * 创建测试文件
	 *
	 * @return 创建结果
	 */
	@PostMapping("/create/file")
	public ResponseEntity<?> createTestFile() {
		electricTestService.createTestFile();
		return ResponseEntity.ok("创建测试文件成功");
	}

	/**
	 * 创建临时测试文件
	 *
	 * @return 创建结果
	 */
	@PostMapping("/create/temporary/file")
	public ResponseEntity<?> createTemporaryFile() {
		electricTestService.createTemporaryFile();
		return ResponseEntity.ok("创建临时测试文件成功");
	}
} 