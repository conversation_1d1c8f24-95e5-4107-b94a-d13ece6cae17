package com.cirpoint.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.ReflectionTestUtils;

@SpringBootTest
class CleanupServiceTest {

    @Autowired
    private CleanupService cleanupService;

    @BeforeEach
    void setUp() {
		ReflectionTestUtils.setField(cleanupService, "fileUploadPath", "D:/DataPkgFile/");
    }

    @Test
    void testCleanupExpiredData() {
        cleanupService.cleanupExpiredDataForTest();
    }


}