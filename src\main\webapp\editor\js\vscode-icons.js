/**
 * VSCode Icons 文件类型映射
 * 基于VSCode Icons插件的图标映射逻辑
 */

(function() {
    // 基本图标路径
    const ICON_BASE_PATH = 'css/vscode-icons/icons/';
    
    // 默认图标
    const DEFAULT_FILE_ICON = ICON_BASE_PATH + 'default_file.svg';
    const DEFAULT_FOLDER_ICON = ICON_BASE_PATH + 'default_folder.svg';
    const DEFAULT_FOLDER_OPEN_ICON = ICON_BASE_PATH + 'default_folder_opened.svg';
    
    // 文件扩展名映射
    const FILE_EXTENSION_ICONS = {
        // 常见文件类型
        'html': 'file_type_html.svg',
        'htm': 'file_type_html.svg',
        'css': 'file_type_css.svg',
        'scss': 'file_type_sass.svg',
        'less': 'file_type_less.svg',
        'js': 'file_type_js.svg',
        'jsx': 'file_type_react.svg',
        'ts': 'file_type_typescript.svg',
        'tsx': 'file_type_react_ts.svg',
        'json': 'file_type_json.svg',
        'xml': 'file_type_xml.svg',
        'svg': 'file_type_svg.svg',
        
        // 编程语言
        'java': 'file_type_java.svg',
        'py': 'file_type_python.svg',
        'c': 'file_type_c.svg',
        'cpp': 'file_type_cpp.svg',
        'h': 'file_type_c.svg',
        'hpp': 'file_type_cpp.svg',
        'cs': 'file_type_csharp.svg',
        'go': 'file_type_go.svg',
        'rb': 'file_type_ruby.svg',
        'php': 'file_type_php.svg',
        'pl': 'file_type_perl.svg',
        'scala': 'file_type_scala.svg',
        'swift': 'file_type_swift.svg',
        'kt': 'file_type_kotlin.svg',
        'rs': 'file_type_rust.svg',
        
        // 数据库
        'sql': 'file_type_sql.svg',
        'db': 'file_type_sql.svg',
        'sqlite': 'file_type_sqlite.svg',
        
        // 文档
        'md': 'file_type_markdown.svg',
        'txt': 'file_type_text.svg',
        'pdf': 'file_type_pdf.svg',
        'doc': 'file_type_word.svg',
        'docx': 'file_type_word.svg',
        'xls': 'file_type_excel.svg',
        'xlsx': 'file_type_excel.svg',
        'ppt': 'file_type_powerpoint.svg',
        'pptx': 'file_type_powerpoint.svg',
        
        // 图片
        'jpg': 'file_type_image.svg',
        'jpeg': 'file_type_image.svg',
        'png': 'file_type_image.svg',
        'gif': 'file_type_image.svg',
        'bmp': 'file_type_image.svg',
        'ico': 'file_type_image.svg',
        'webp': 'file_type_image.svg',
        
        // 音视频
        'mp3': 'file_type_audio.svg',
        'wav': 'file_type_audio.svg',
        'ogg': 'file_type_audio.svg',
        'mp4': 'file_type_video.svg',
        'avi': 'file_type_video.svg',
        'mov': 'file_type_video.svg',
        'webm': 'file_type_video.svg',
        
        // 配置文件
        'json': 'file_type_json.svg',
        'yaml': 'file_type_yaml.svg',
        'yml': 'file_type_yaml.svg',
        'toml': 'file_type_toml.svg',
        'ini': 'file_type_ini.svg',
        'conf': 'file_type_config.svg',
        'config': 'file_type_config.svg',
        
        // 压缩文件
        'zip': 'file_type_zip.svg',
        'rar': 'file_type_zip.svg',
        'tar': 'file_type_zip.svg',
        'gz': 'file_type_zip.svg',
        '7z': 'file_type_zip.svg',
        
        // 其他常见文件
        'log': 'file_type_log.svg',
        'bat': 'file_type_bat.svg',
        'sh': 'file_type_shell.svg',
        'ps1': 'file_type_powershell.svg',
        'csv': 'file_type_csv.svg',
        'jsp': 'file_type_jsp.svg'
    };
    
    // 特殊文件名映射
    const SPECIAL_FILENAME_ICONS = {
        'package.json': 'file_type_npm.svg',
        'package-lock.json': 'file_type_npm.svg',
        'npm-debug.log': 'file_type_npm.svg',
        'yarn.lock': 'file_type_yarn.svg',
        'dockerfile': 'file_type_docker.svg',
        '.gitignore': 'file_type_git.svg',
        '.gitattributes': 'file_type_git.svg',
        'license': 'file_type_license.svg',
        'license.md': 'file_type_license.svg',
        'license.txt': 'file_type_license.svg',
        'readme.md': 'file_type_readme.svg',
        'readme.txt': 'file_type_readme.svg',
        'makefile': 'file_type_makefile.svg',
        'gulpfile.js': 'file_type_gulp.svg',
        'gruntfile.js': 'file_type_grunt.svg',
        'webpack.config.js': 'file_type_webpack.svg'
    };
    
    // 文件夹名称映射
    const FOLDER_ICONS = {
        'src': 'folder_type_src.svg',
        'source': 'folder_type_src.svg',
        'sources': 'folder_type_src.svg',
        'dist': 'folder_type_dist.svg',
        'build': 'folder_type_build.svg',
        'public': 'folder_type_public.svg',
        'node_modules': 'folder_type_node.svg',
        'test': 'folder_type_test.svg',
        'tests': 'folder_type_test.svg',
        'docs': 'folder_type_docs.svg',
        'doc': 'folder_type_docs.svg',
        'images': 'folder_type_images.svg',
        'img': 'folder_type_images.svg',
        'assets': 'folder_type_assets.svg',
        'css': 'folder_type_css.svg',
        'js': 'folder_type_js.svg',
        'components': 'folder_type_component.svg',
        'config': 'folder_type_config.svg'
    };
    
    // 文件夹打开状态图标映射
    const FOLDER_OPEN_ICONS = {
        'src': 'folder_type_src_opened.svg',
        'source': 'folder_type_src_opened.svg',
        'sources': 'folder_type_src_opened.svg',
        'dist': 'folder_type_dist_opened.svg',
        'build': 'folder_type_build_opened.svg',
        'public': 'folder_type_public_opened.svg',
        'node_modules': 'folder_type_node_opened.svg',
        'test': 'folder_type_test_opened.svg',
        'tests': 'folder_type_test_opened.svg',
        'docs': 'folder_type_docs_opened.svg',
        'doc': 'folder_type_docs_opened.svg',
        'images': 'folder_type_images_opened.svg',
        'img': 'folder_type_images_opened.svg',
        'assets': 'folder_type_assets_opened.svg',
        'css': 'folder_type_css_opened.svg',
        'js': 'folder_type_js_opened.svg',
        'components': 'folder_type_component_opened.svg',
        'config': 'folder_type_config_opened.svg'
    };
    
    /**
     * 获取文件图标
     * @param {string} fileName - 文件名
     * @returns {string} - 图标路径
     */
    function getFileIcon(fileName) {
        // 检查是否是特殊文件名
        const lowerFileName = fileName.toLowerCase();
        if (SPECIAL_FILENAME_ICONS[lowerFileName]) {
            return ICON_BASE_PATH + SPECIAL_FILENAME_ICONS[lowerFileName];
        }
        
        // 获取文件扩展名
        const extension = fileName.split('.').pop().toLowerCase();
        if (FILE_EXTENSION_ICONS[extension]) {
            return ICON_BASE_PATH + FILE_EXTENSION_ICONS[extension];
        }
        
        // 返回默认图标
        return DEFAULT_FILE_ICON;
    }
    
    /**
     * 获取文件夹图标
     * @param {string} folderName - 文件夹名
     * @param {boolean} isOpen - 是否打开状态
     * @returns {string} - 图标路径
     */
    function getFolderIcon(folderName, isOpen) {
        const lowerFolderName = folderName.toLowerCase();
        
        if (isOpen) {
            if (FOLDER_OPEN_ICONS[lowerFolderName]) {
                return ICON_BASE_PATH + FOLDER_OPEN_ICONS[lowerFolderName];
            }
            return DEFAULT_FOLDER_OPEN_ICON;
        } else {
            if (FOLDER_ICONS[lowerFolderName]) {
                return ICON_BASE_PATH + FOLDER_ICONS[lowerFolderName];
            }
            return DEFAULT_FOLDER_ICON;
        }
    }
    
    /**
     * 应用图标到文件树
     */
    function applyIcons() {
        // 应用文件图标
        $('.ztree li a').each(function() {
            const title = $(this).attr('title');
            if (!title) return;
            
            const isFolder = $(this).find('span.button.ico_open, span.button.ico_close').length > 0;
            
            if (isFolder) {
                // 处理文件夹
                const folderName = title.split('/').pop();
                const isOpen = $(this).find('span.button.ico_open').length > 0;
                
                if (isOpen) {
                    $(this).find('span.button.ico_open').css('background-image', `url('${getFolderIcon(folderName, true)}')`);
                } else {
                    $(this).find('span.button.ico_close').css('background-image', `url('${getFolderIcon(folderName, false)}')`);
                }
            } else {
                // 处理文件
                const fileName = title.split('/').pop();
                $(this).find('span.button.ico_docu').css('background-image', `url('${getFileIcon(fileName)}')`);
            }
        });
    }
    
    // 在文档加载完成后应用图标
    $(document).ready(function() {
        // 初始应用
        applyIcons();
        
        // 监听树节点展开/折叠事件
        $('.ztree').on('click', 'li a', function() {
            // 延迟执行，确保树节点状态已更新
            setTimeout(applyIcons, 100);
        });
    });
})(); 