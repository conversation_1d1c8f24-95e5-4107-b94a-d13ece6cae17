package com.cirpoint.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cirpoint.util.CommonUtil;
import com.cirpoint.util.Util;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * AIT看板服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AitScreenService {

	/**
	 * 导出AIT看板数据为Excel
	 *
	 * @param treeId    型号ID
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @param fileType  文件类型
	 * @return 生成的Excel文件
	 * @throws Exception 异常
	 */
	public File exportExcel(String treeId, String startDate, String endDate, String fileType) throws Exception {
		// 1. 调用Thingworx接口获取数据
		JSONArray dataArray = queryListData(treeId, startDate, endDate, fileType);

		// 2. 准备表头和列宽
		JSONArray headers = getHeadersByFileType(fileType);
		JSONArray columnWidths = getColumnWidthsByFileType(fileType);

		// 3. 准备数据
		JSONArray data = new JSONArray();
		if (!dataArray.isEmpty()) {
			for (int i = 0; i < dataArray.size(); i++) {
				JSONObject rowData = dataArray.getJSONObject(i);
				JSONArray row = new JSONArray();

				// 根据文件类型填充不同的数据
				if (fileType.contains("现场临时处理单")) {
					// 现场临时处理单数据
					row.add(i + 1); // 序号
					row.add(rowData.getStr("MODEL_NAME", "")); // 型号
					row.add(rowData.getStr("LEFT_NAME", "")); // 阶段
					row.add(rowData.getStr("NAME", "")); // 文件名称
					row.add(rowData.getStr("OPT_CATEGORY", "")); // 类型
					row.add(rowData.getStr("OPT_DATE", "")); // 日期

					// 状态
					String status = rowData.getInt("IS_FINISHED", 0) == 1 ? "已完成" : "未完成";
					row.add(status);
				} else if (fileType.contains("现场问题处理单")) {
					// 现场问题处理单数据
					row.add(i + 1); // 序号
					row.add(rowData.getStr("modelName", "")); // 型号
					row.add(rowData.getStr("processName", "")); // 阶段
					row.add(rowData.getStr("FILE_NUMBER", "")); // 文件编号
					row.add(rowData.getStr("FILE_NAME", "")); // 文件名称

					// 根据问题处理单的不同子类型添加相应字段
					if (fileType.contains("全部")) {
						// 添加状态列
						String state = rowData.getStr("STATE", "-1");
						log.info(state);
						String statusText;
						switch (state) {
							case "0":
								statusText = "已提出";
								break;
							case "1":
								statusText = "已填写处理方案";
								break;
							case "2":
								statusText = "已填写实施工艺";
								break;
							case "3":
								statusText = "工艺负责人已签署";
								break;
							case "4":
								statusText = "产品负责人已签署";
								break;
							case "5":
								statusText = "已生效";
								break;
							case "6":
								statusText = "已完成";
								break;
							case "7":
								statusText = "已闭环";
								break;
							case "8":
								statusText = "已作废";
								break;
							case "9":
								statusText = "已填写处理结果";
								break;
							default:
								statusText = "未知状态";
								break;
						}
						row.add(statusText); // 状态
					} else if (fileType.contains("更改设计文件")) {
						// 添加设计更改单号
						row.add(rowData.getStr("DESIGN_CHANGE_NO", "")); // 设计更改单号

						// 添加设计文件完成状态列
						String designChangeNo = rowData.getStr("DESIGN_CHANGE_NO", "");
						String statusText = StrUtil.isNotBlank(designChangeNo) ? "已完成" : "未完成";
						row.add(statusText); // 完成状态
					} else if (fileType.contains("更改工艺文件")) {
						// 添加工艺更改单号
						row.add(rowData.getStr("PROCESS_CHANGE_NO", "")); // 工艺更改单号

						// 添加工艺文件完成状态列
						String processChangeNo = rowData.getStr("PROCESS_CHANGE_NO", "");
						String statusText = StrUtil.isNotBlank(processChangeNo) ? "已完成" : "未完成";
						row.add(statusText); // 完成状态
					}
				} else {
					// 其他文件类型数据
					row.add(i + 1); // 序号
					row.add(rowData.getStr("modelName", "")); // 型号
					row.add(rowData.getStr("processName", "")); // 阶段
					row.add(rowData.getStr("FILE_NUMBER", "")); // 文件编号
					row.add(rowData.getStr("FILE_NAME", "")); // 文件名称
					row.add(rowData.getStr("FILE_TYPE", "")); // 文件类型
				}

				data.add(row);
			}
		}

		// 4. 生成Excel文件
		String fileName = fileType.replace("|", "_");
		return CommonUtil.createExcelFile(fileName, headers, data, columnWidths, 18);
	}

	/**
	 * 导出产品交接单数据为Excel
	 *
	 * @param treeId     型号ID
	 * @param startDate  开始日期
	 * @param endDate    结束日期
	 * @param groupType  分组类型 (ISCERTIFICATE、ISLUOHAN、ISSUBMIT)
	 * @param seriesName 系列名称
	 * @param unit       单位名称 (可选)
	 * @return 生成的Excel文件
	 * @throws Exception 异常
	 */
	public File exportSubmitExcel(String treeId, String startDate, String endDate, String groupType, String seriesName, String unit) throws Exception {
		// 1. 调用Thingworx接口获取数据
		JSONArray dataArray = querySubmitListData(treeId, startDate, endDate, groupType, seriesName, unit);

		// 2. 准备表头和列宽
		JSONArray headers = getSubmitHeadersByGroupType(groupType);
		JSONArray columnWidths = getSubmitColumnWidthsByGroupType(groupType);

		// 3. 准备数据
		JSONArray data = new JSONArray();
		if (dataArray != null && !dataArray.isEmpty()) {
			for (int i = 0; i < dataArray.size(); i++) {
				JSONObject rowData = dataArray.getJSONObject(i);
				JSONArray row = new JSONArray();

				// 添加公共字段
				row.add(i + 1); // 序号
				row.add(rowData.getStr("modelName", "")); // 型号
				row.add(rowData.getStr("PRODUCTNAME", "")); // 产品名称
				row.add(rowData.getStr("PRODUCTCODE", "")); // 产品代号
				row.add(rowData.getStr("BATCHCODE", "")); // 批次号
				row.add(rowData.getStr("SUBMITUNIT", "")); // 提交单位

				// 根据分组类型添加特定字段
				if ("ISCERTIFICATE".equals(groupType)) {
					// 交付验收情况
					row.add(rowData.getStr("ISCERTIFICATE", ""));
				} else if ("ISLUOHAN".equals(groupType)) {
					// 落焊情况和落焊时机
					row.add(rowData.getStr("ISLUOHAN", ""));
					row.add(rowData.getStr("LUOHANPHASE", ""));
				} else if ("ISSUBMIT".equals(groupType)) {
					// 交付情况、产品证明书、产品履历书和其他证明
					row.add(rowData.getStr("ISSUBMIT", ""));
					row.add(rowData.getStr("CERTIFICATENUMBER", ""));
					row.add(rowData.getStr("RESUMENUMBER", ""));
					row.add(rowData.getStr("OTHER_CERTIFICATE1", ""));
					row.add(rowData.getStr("OTHER_CERTIFICATE2", ""));
				}

				data.add(row);
			}
		}

		// 4. 生成Excel文件
		String groupTypeDesc = "";
		switch (groupType) {
			case "ISCERTIFICATE":
				groupTypeDesc = "交付验收情况";
				break;
			case "ISLUOHAN":
				groupTypeDesc = "落焊单机";
				break;
			case "ISSUBMIT":
				groupTypeDesc = "单机证明材料交付";
				break;
			default:
				groupTypeDesc = groupType;
				break;
		}

		String fileName = "产品交接单_" + groupTypeDesc;
		if (StrUtil.isNotBlank(seriesName)) {
			fileName += "_" + seriesName;
		}
		if (StrUtil.isNotBlank(unit)) {
			fileName += "_" + unit;
		}

		return CommonUtil.createExcelFile(fileName, headers, data, columnWidths, 18);
	}

	/**
	 * 导出现场问题处理单数据为Excel（包含设计和工艺两个sheet页）
	 *
	 * @param treeId    型号ID
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @param status    状态筛选 (all-全部, finished-已完成, unfinished-未完成)
	 * @return 生成的Excel文件
	 * @throws Exception 异常
	 */
	public File exportProblemExcel(String treeId, String startDate, String endDate, String status) throws Exception {
		// 创建包含多个Sheet的Excel文件所需的数据结构
		List<JSONArray> headersList = new ArrayList<>();
		List<JSONArray> dataList = new ArrayList<>();
		List<JSONArray> columnWidthsList = new ArrayList<>();
		List<String> sheetNames = new ArrayList<>();

		// 1. 处理第一个sheet - 更改设计文件
		JSONArray designData = prepareSheetData(treeId, startDate, endDate, "现场问题处理单|更改设计文件", status);
		if (designData != null) {
			JSONArray designHeaders = getHeadersByFileType("现场问题处理单|更改设计文件");
			JSONArray designColumnWidths = getColumnWidthsByFileType("现场问题处理单|更改设计文件");

			headersList.add(designHeaders);
			dataList.add(designData);
			columnWidthsList.add(designColumnWidths);
			sheetNames.add("更改设计文件");
		}

		// 2. 处理第二个sheet - 更改工艺文件
		JSONArray techData = prepareSheetData(treeId, startDate, endDate, "现场问题处理单|更改工艺文件", status);
		if (techData != null) {
			JSONArray techHeaders = getHeadersByFileType("现场问题处理单|更改工艺文件");
			JSONArray techColumnWidths = getColumnWidthsByFileType("现场问题处理单|更改工艺文件");

			headersList.add(techHeaders);
			dataList.add(techData);
			columnWidthsList.add(techColumnWidths);
			sheetNames.add("更改工艺文件");
		}

		// 3. 生成多sheet的Excel文件
		String statusText = "all".equals(status) ? "全部" : ("finished".equals(status) ? "已完成" : "未完成");
		String fileName = "现场问题处理单_" + statusText;

		return CommonUtil.createMultiSheetExcelFile(fileName, headersList, dataList, columnWidthsList, sheetNames, 18);
	}

	/**
	 * 准备单个sheet页的数据
	 *
	 * @param treeId    型号ID
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @param fileType  文件类型
	 * @param status    状态筛选
	 * @return 数据数组
	 * @throws Exception 异常
	 */
	private JSONArray prepareSheetData(String treeId, String startDate, String endDate, String fileType, String status) throws Exception {
		// 1. 调用Thingworx接口获取数据
		JSONArray rawDataArray = queryListData(treeId, startDate, endDate, fileType);

		// 如果没有数据，返回空
		if (rawDataArray == null || rawDataArray.isEmpty()) {
			return new JSONArray();
		}

		// 2. 根据状态筛选数据
		JSONArray filteredArray = new JSONArray();
		if (!"all".equals(status)) {
			// 筛选符合状态条件的数据
			for (int i = 0; i < rawDataArray.size(); i++) {
				JSONObject rowData = rawDataArray.getJSONObject(i);
				boolean isFinished = false;

				// 根据文件类型判断完成状态的字段
				if (fileType.contains("更改设计文件")) {
					String designChangeNo = rowData.getStr("DESIGN_CHANGE_NO", "");
					isFinished = StrUtil.isNotBlank(designChangeNo);
				} else if (fileType.contains("更改工艺文件")) {
					String processChangeNo = rowData.getStr("PROCESS_CHANGE_NO", "");
					isFinished = StrUtil.isNotBlank(processChangeNo);
				}

				// 根据状态参数和完成状态进行筛选
				if (("finished".equals(status) && isFinished) || ("unfinished".equals(status) && !isFinished)) {
					filteredArray.add(rowData);
				}
			}
		} else {
			// 全部状态，直接使用所有数据
			filteredArray = rawDataArray;
		}

		// 3. 准备数据
		JSONArray data = new JSONArray();
		for (int i = 0; i < filteredArray.size(); i++) {
			JSONObject rowData = filteredArray.getJSONObject(i);
			JSONArray row = new JSONArray();

			// 添加通用字段
			row.add(i + 1); // 序号
			row.add(rowData.getStr("modelName", "")); // 型号
			row.add(rowData.getStr("processName", "")); // 阶段
			row.add(rowData.getStr("FILE_NUMBER", "")); // 文件编号
			row.add(rowData.getStr("FILE_NAME", "")); // 文件名称

			// 根据文件类型添加特定字段
			if (fileType.contains("更改设计文件")) {
				// 添加设计更改单号
				row.add(rowData.getStr("DESIGN_CHANGE_NO", "")); // 设计更改单号

				// 添加设计文件完成状态列
				String designChangeNo = rowData.getStr("DESIGN_CHANGE_NO", "");
				String statusText = StrUtil.isNotBlank(designChangeNo) ? "已完成" : "未完成";
				row.add(statusText); // 完成状态
			} else if (fileType.contains("更改工艺文件")) {
				// 添加工艺更改单号
				row.add(rowData.getStr("PROCESS_CHANGE_NO", "")); // 工艺更改单号

				// 添加工艺文件完成状态列
				String processChangeNo = rowData.getStr("PROCESS_CHANGE_NO", "");
				String statusText = StrUtil.isNotBlank(processChangeNo) ? "已完成" : "未完成";
				row.add(statusText); // 完成状态
			}

			data.add(row);
		}

		return data;
	}

	/**
	 * 根据文件类型获取表头
	 *
	 * @param fileType 文件类型
	 * @return 表头数组
	 */
	private JSONArray getHeadersByFileType(String fileType) {
		List<String> headerList = new ArrayList<>();

		// 公共表头
		headerList.add("序号");
		headerList.add("型号");
		headerList.add("阶段");

		// 根据不同文件类型添加特定表头
		if (fileType.contains("现场临时处理单")) {
			headerList.add("文件名称");
			headerList.add("类型");
			headerList.add("日期");
			headerList.add("状态");
		} else if (fileType.contains("现场问题处理单")) {
			headerList.add("文件编号");
			headerList.add("文件名称");

			// 根据问题处理单的子类型添加不同的表头
			if (fileType.contains("全部")) {
				headerList.add("状态");
			} else if (fileType.contains("更改设计文件")) {
				headerList.add("设计更改单号");
				headerList.add("完成状态");
			} else if (fileType.contains("更改工艺文件")) {
				headerList.add("工艺更改单号");
				headerList.add("完成状态");
			}
		} else {
			headerList.add("文件编号");
			headerList.add("文件名称");
			headerList.add("文件类型");
		}

		return JSONUtil.parseArray(headerList);
	}

	/**
	 * 根据分组类型获取表头
	 *
	 * @param groupType 分组类型
	 * @return 表头数组
	 */
	private JSONArray getSubmitHeadersByGroupType(String groupType) {
		List<String> headerList = new ArrayList<>();

		// 公共表头
		headerList.add("序号");
		headerList.add("型号");
		headerList.add("产品名称");
		headerList.add("产品代号");
		headerList.add("批次号");
		headerList.add("提交单位");

		// 根据不同分组类型添加特定表头
		if ("ISCERTIFICATE".equals(groupType)) {
			headerList.add("交付验收情况");
		} else if ("ISLUOHAN".equals(groupType)) {
			headerList.add("落焊情况");
			headerList.add("落焊时机");
		} else if ("ISSUBMIT".equals(groupType)) {
			headerList.add("交付情况");
			headerList.add("产品证明书");
			headerList.add("产品履历书");
			headerList.add("其他证明1");
			headerList.add("其他证明2");
		}

		return JSONUtil.parseArray(headerList);
	}

	/**
	 * 根据文件类型获取列宽
	 *
	 * @param fileType 文件类型
	 * @return 列宽数组
	 */
	private JSONArray getColumnWidthsByFileType(String fileType) {
		List<Integer> widthList = new ArrayList<>();

		// 公共列宽
		widthList.add(8);  // 序号
		widthList.add(15); // 型号
		widthList.add(25); // 阶段

		// 根据不同文件类型添加特定列宽
		if (fileType.contains("现场临时处理单")) {
			widthList.add(30); // 文件名称
			widthList.add(20); // 类型
			widthList.add(15); // 日期
			widthList.add(10); // 状态
		} else if (fileType.contains("现场问题处理单")) {
			widthList.add(20); // 文件编号
			widthList.add(30); // 文件名称

			// 根据问题处理单的子类型添加不同的列宽
			if (fileType.contains("全部")) {
				widthList.add(18); // 状态宽度
			} else if (fileType.contains("更改设计文件")) {
				widthList.add(18); // 设计更改单号宽度
				widthList.add(10); // 完成状态宽度
			} else if (fileType.contains("更改工艺文件")) {
				widthList.add(18); // 工艺更改单号宽度
				widthList.add(10); // 完成状态宽度
			}
		} else {
			widthList.add(30); // 文件编号
			widthList.add(30); // 文件名称
			widthList.add(15); // 文件类型
		}

		return JSONUtil.parseArray(widthList);
	}

	/**
	 * 根据分组类型获取列宽
	 *
	 * @param groupType 分组类型
	 * @return 列宽数组
	 */
	private JSONArray getSubmitColumnWidthsByGroupType(String groupType) {
		List<Integer> widthList = new ArrayList<>();

		// 公共列宽
		widthList.add(8);  // 序号
		widthList.add(15); // 型号
		widthList.add(25); // 产品名称
		widthList.add(20); // 产品代号
		widthList.add(15); // 批次号
		widthList.add(15); // 提交单位

		// 根据不同分组类型添加特定列宽
		if ("ISCERTIFICATE".equals(groupType)) {
			widthList.add(15); // 交付验收情况
		} else if ("ISLUOHAN".equals(groupType)) {
			widthList.add(12); // 落焊情况
			widthList.add(12); // 落焊时机
		} else if ("ISSUBMIT".equals(groupType)) {
			widthList.add(10); // 交付情况
			widthList.add(12); // 产品证明书
			widthList.add(12); // 产品履历书
			widthList.add(10); // 其他证明1
			widthList.add(10); // 其他证明2
		}

		return JSONUtil.parseArray(widthList);
	}

	/**
	 * 查询列表数据
	 *
	 * @param treeId    型号ID
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @param fileType  文件类型
	 * @return 数据列表
	 * @throws Exception 异常
	 */
	private JSONArray queryListData(String treeId, String startDate, String endDate, String fileType) throws Exception {
		try {
			// 构建请求参数
			JSONObject params = JSONUtil.createObj()
					.set("treeId", treeId)
					.set("startDate", startDate)
					.set("endDate", endDate)
					.set("fileType", fileType)
					.set("page", 1)
					.set("limit", 0); // 0表示不限制

			// 调用Thingworx接口
			JSONObject result = Util.postTwxForObject("Thing.Fn.AitScreen", "QueryList", params);

			// 判断是否成功
			if (result.containsKey("code") && result.getInt("code") == 0) {
				// 获取数据列表
				JSONArray dataArray = result.getJSONArray("data");
				if (dataArray != null && !dataArray.isEmpty()) {
					return dataArray;
				}
			} else {
				throw new Exception(result.getStr("msg"));
			}

			return new JSONArray();
		} catch (Exception e) {
			log.error("查询AIT看板数据失败", e);
			throw e;
		}
	}

	/**
	 * 查询提交列表数据
	 *
	 * @param treeId     型号ID
	 * @param startDate  开始日期
	 * @param endDate    结束日期
	 * @param groupType  分组类型
	 * @param seriesName 系列名称
	 * @param unit       单位名称 (可选)
	 * @return 数据列表
	 * @throws Exception 异常
	 */
	private JSONArray querySubmitListData(String treeId, String startDate, String endDate, String groupType, String seriesName, String unit) throws Exception {
		try {
			// 构建请求参数
			JSONObject params = JSONUtil.createObj()
					.set("treeId", treeId)
					.set("startDate", startDate)
					.set("endDate", endDate)
					.set("groupType", groupType)
					.set("seriesName", seriesName)
					.set("unit", unit)
					.set("page", 1)
					.set("limit", 0); // 0表示不限制

			// 调用Thingworx接口
			JSONObject result = Util.postTwxForObject("Thing.Fn.AitScreen", "QuerySubmitList", params);

			// 判断是否成功
			if (result.containsKey("code") && result.getInt("code") == 0) {
				// 获取数据列表
				JSONArray dataArray = result.getJSONArray("data");
				if (dataArray != null && !dataArray.isEmpty()) {
					return dataArray;
				}
			}

			return new JSONArray();
		} catch (Exception e) {
			log.error("查询产品交接单数据失败", e);
			throw e;
		}
	}

	/**
	 * 导出现场临时处理单数据为Excel（Sheet页名称根据类型动态确定）
	 *
	 * @param treeId    型号ID
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @param status    状态筛选 (all-全部, finished-已完成, unfinished-未完成)
	 * @return 生成的Excel文件
	 * @throws Exception 异常
	 */
	public File exportTempExcel(String treeId, String startDate, String endDate, String status) throws Exception {
		// 1. 获取现场临时处理单数据
		JSONArray tempData = queryListData(treeId, startDate, endDate, "现场临时处理单");

		// 如果没有数据，返回空Excel
		if (tempData == null || tempData.isEmpty()) {
			List<JSONArray> headersList = new ArrayList<>();
			List<JSONArray> dataList = new ArrayList<>();
			List<JSONArray> columnWidthsList = new ArrayList<>();
			List<String> sheetNames = new ArrayList<>();

			// 添加一个空的sheet页
			JSONArray headers = getHeadersByFileType("现场临时处理单");
			JSONArray columnWidths = getColumnWidthsByFileType("现场临时处理单");

			headersList.add(headers);
			dataList.add(new JSONArray());
			columnWidthsList.add(columnWidths);
			sheetNames.add("现场临时处理单");

			String statusText = "all".equals(status) ? "全部" : ("finished".equals(status) ? "已完成" : "未完成");
			String fileName = "现场临时处理单_" + statusText;

			return CommonUtil.createMultiSheetExcelFile(fileName, headersList, dataList, columnWidthsList, sheetNames, 18);
		}

		// 2. 根据OPT_CATEGORY字段分类数据
		Map<String, JSONArray> typeDataMap = new HashMap<>();

		// 根据状态筛选并按类型分组
		for (int i = 0; i < tempData.size(); i++) {
			JSONObject rowData = tempData.getJSONObject(i);
			boolean isFinished = rowData.getInt("IS_FINISHED", 0) == 1;

			// 根据状态参数筛选数据
			if ("all".equals(status) ||
					("finished".equals(status) && isFinished) ||
					("unfinished".equals(status) && !isFinished)) {

				// 获取类型
				String category = rowData.getStr("OPT_CATEGORY", "其他");

				// 初始化该类型的数组
				if (!typeDataMap.containsKey(category)) {
					typeDataMap.put(category, new JSONArray());
				}

				// 添加到对应类型的数组中
				typeDataMap.get(category).add(rowData);
			}
		}

		// 3. 准备多sheet页数据
		List<JSONArray> headersList = new ArrayList<>();
		List<JSONArray> dataList = new ArrayList<>();
		List<JSONArray> columnWidthsList = new ArrayList<>();
		List<String> sheetNames = new ArrayList<>();

		// 获取所有类型并排序
		List<String> categories = new ArrayList<>(typeDataMap.keySet());
		Collections.sort(categories);

		// 按类型处理数据
		for (String category : categories) {
			JSONArray categoryData = typeDataMap.get(category);
			if (categoryData != null && !categoryData.isEmpty()) {
				// 获取表头和列宽
				JSONArray headers = getHeadersByFileType("现场临时处理单");
				JSONArray columnWidths = getColumnWidthsByFileType("现场临时处理单");

				// 准备该类型的数据
				JSONArray formattedData = new JSONArray();
				for (int i = 0; i < categoryData.size(); i++) {
					JSONObject rowData = categoryData.getJSONObject(i);
					JSONArray row = new JSONArray();

					// 添加通用字段
					row.add(i + 1); // 序号
					row.add(rowData.getStr("MODEL_NAME", "")); // 型号
					row.add(rowData.getStr("LEFT_NAME", "")); // 阶段
					row.add(rowData.getStr("NAME", "")); // 文件名称
					row.add(rowData.getStr("OPT_CATEGORY", "")); // 类型
					row.add(rowData.getStr("OPT_DATE", "")); // 日期

					// 状态
					String statusText = rowData.getInt("IS_FINISHED", 0) == 1 ? "已完成" : "未完成";
					row.add(statusText);

					formattedData.add(row);
				}

				// 添加到对应的列表中
				headersList.add(headers);
				dataList.add(formattedData);
				columnWidthsList.add(columnWidths);
				sheetNames.add(category); // 使用类型作为sheet名称
			}
		}

		// 如果没有满足条件的数据，添加一个空的sheet页
		if (headersList.isEmpty()) {
			JSONArray headers = getHeadersByFileType("现场临时处理单");
			JSONArray columnWidths = getColumnWidthsByFileType("现场临时处理单");

			headersList.add(headers);
			dataList.add(new JSONArray());
			columnWidthsList.add(columnWidths);
			sheetNames.add("现场临时处理单");
		}

		// 4. 生成Excel文件
		String statusText = "all".equals(status) ? "全部" : ("finished".equals(status) ? "已完成" : "未完成");
		String fileName = "现场临时处理单_" + statusText;

		return CommonUtil.createMultiSheetExcelFile(fileName, headersList, dataList, columnWidthsList, sheetNames, 18);
	}

	/**
	 * 导出技术状态更改单数据为Excel
	 *
	 * @param treeId    型号ID
	 * @param username  用户名
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @param status    状态筛选 (all-全部, finished-已完成, unfinished-未完成)
	 * @param situation 情况筛选 (all-全部, 或具体情况值)
	 * @param queryType 查询类型 (main-仅主表, branch-主表+分支表, all-默认)
	 * @return 生成的Excel文件
	 * @throws Exception 异常
	 */
	public File exportChangeOrderExcel(String treeId, String username, String startDate, String endDate, String status, String situation, String queryType) throws Exception {
		// 1. 准备查询参数
		JSONObject params = new JSONObject();
		params.set("treeId", treeId);
		params.set("username", username);
		params.set("startDate", startDate);
		params.set("endDate", endDate);
		params.set("status", status);
		params.set("situation", situation);
		params.set("queryType", queryType);
		params.set("isAllData", true);
		params.set("page", 1);
		params.set("limit", 10000); // 设置一个较大的值，确保获取所有数据

		// 2. 调用Thingworx接口获取数据
		JSONObject result = Util.postTwxForObject("Thing.Fn.AitScreen", "QueryChangeOrderList", params);

		// 3. 检查查询结果
		if (!result.getInt("code", -1).equals(0)) {
			throw new Exception("查询技术状态更改单数据失败：" + result.getStr("msg", "未知错误"));
		}

		// 4. 获取数据列表
		JSONArray dataArray = result.getJSONArray("data");
		if (dataArray == null) {
			dataArray = new JSONArray();
		}

		// 5. 根据查询类型准备表头和列宽
		JSONArray headers = new JSONArray();
		JSONArray columnWidths = new JSONArray();

		if ("main".equals(queryType)) {
			// 主表模式：配置27个业务字段的表头和列宽
			// 基本信息区域（8个字段）
			headers.add("序号");
			headers.add("单据编号");
			headers.add("型号");
			headers.add("研制阶段");
			headers.add("产品代号");
			headers.add("批次号及数量");
			headers.add("更改单号");
			headers.add("更改项目");

			// 人员信息区域（6个字段）
			headers.add("制单人");
			headers.add("制单日期");
			headers.add("设计师");
			headers.add("质量跟踪确认人员");
			headers.add("签署人");
			headers.add("签署日期");

			// 更改信息区域（6个字段）
			headers.add("更改前编号");
			headers.add("更改前名称");
			headers.add("更改前版本");
			headers.add("更改后编号");
			headers.add("更改后名称");
			headers.add("更改后版本");

			// 文件信息区域（4个字段）
			headers.add("更改单发放包编号");
			headers.add("更改单发放包名称");
			headers.add("PDM文件链接");
			headers.add("来自单位");

			// 系统信息区域（3个字段）
			headers.add("单据状态");
			headers.add("是否结束");
			headers.add("状态");

			// 设置列宽 - 基本信息区域
			columnWidths.add(8);  // 序号
			columnWidths.add(25); // 单据编号
			columnWidths.add(15); // 型号
			columnWidths.add(15); // 研制阶段
			columnWidths.add(20); // 产品代号
			columnWidths.add(20); // 批次号及数量
			columnWidths.add(25); // 更改单号
			columnWidths.add(30); // 更改项目

			// 设置列宽 - 人员信息区域
			columnWidths.add(15); // 制单人
			columnWidths.add(15); // 制单日期
			columnWidths.add(15); // 设计师
			columnWidths.add(20); // 质量跟踪确认人员
			columnWidths.add(15); // 签署人
			columnWidths.add(15); // 签署日期

			// 设置列宽 - 更改信息区域
			columnWidths.add(25); // 更改前编号
			columnWidths.add(30); // 更改前名称
			columnWidths.add(15); // 更改前版本
			columnWidths.add(25); // 更改后编号
			columnWidths.add(30); // 更改后名称
			columnWidths.add(15); // 更改后版本

			// 设置列宽 - 文件信息区域
			columnWidths.add(35); // 更改单发放包编号
			columnWidths.add(25); // 更改单发放包名称
			columnWidths.add(40); // PDM文件链接
			columnWidths.add(20); // 来自单位

			// 设置列宽 - 系统信息区域
			columnWidths.add(15); // 单据状态
			columnWidths.add(12); // 是否结束
			columnWidths.add(12); // 状态
		} else {
			// 分支表模式：配置65个业务字段的表头和列宽（27个主表+38个分支表）
			// 主表字段添加"（主）"标识以区分数据来源
			// 基本信息区域（8个字段）
			headers.add("序号");
			headers.add("单据编号（主）");
			headers.add("型号（主）");
			headers.add("研制阶段（主）");
			headers.add("产品代号（主）");
			headers.add("批次号及数量（主）");
			headers.add("更改单号（主）");
			headers.add("更改项目（主）");

			// 人员信息区域（6个字段）
			headers.add("制单人（主）");
			headers.add("制单日期（主）");
			headers.add("设计师（主）");
			headers.add("质量跟踪确认人员（主）");
			headers.add("签署人（主）");
			headers.add("签署日期（主）");

			// 更改信息区域（6个字段）
			headers.add("更改前编号（主）");
			headers.add("更改前名称（主）");
			headers.add("更改前版本（主）");
			headers.add("更改后编号（主）");
			headers.add("更改后名称（主）");
			headers.add("更改后版本（主）");

			// 文件信息区域（4个字段）
			headers.add("更改单发放包编号（主）");
			headers.add("更改单发放包名称（主）");
			headers.add("PDM文件链接（主）");
			headers.add("来自单位（主）");

			// 系统信息区域（3个字段）
			headers.add("单据状态（主）");
			headers.add("是否结束（主）");
			headers.add("状态（主）");

			// 分支表字段（38个）- 移除系统字段
			// 分支基本信息区域（8个字段）
			headers.add("分支");
			headers.add("业务部门");
			headers.add("具体情况");
			headers.add("工艺人员");
			headers.add("工艺师");
			headers.add("检验人员");
			headers.add("是否结束");
			headers.add("其它说明1");

			// 文件更改信息区域（6个字段）
			headers.add("生产文件更改前编号");
			headers.add("生产文件更改前名称");
			headers.add("生产文件更改前版本");
			headers.add("生产文件更改后编号");
			headers.add("生产文件更改后名称");
			headers.add("生产文件更改后版本");

			// 产品更改信息区域（6个字段）
			headers.add("主6产品在线更改数量");
			headers.add("主6产品在线更改结论");
			headers.add("主6产品库房更改数量");
			headers.add("主6产品库房更改结论");
			headers.add("主6产品已交更改数量");
			headers.add("主6产品已交更改结论");

			// 元器件信息区域（3个字段）
			headers.add("元器件（更改前）");
			headers.add("元器件（更改后）");
			headers.add("其它说明2");

			// 确认信息区域（15个字段）
			headers.add("工艺确认日期");
			headers.add("检验确认日期");
			headers.add("业务部门确认");
			headers.add("业务部门签字");
			headers.add("业务部门签字日期");
			headers.add("质量师确认");
			headers.add("质量师签字");
			headers.add("质量师签字日期");
			headers.add("工艺移交人员");
			headers.add("工艺移交人员ID");
			headers.add("检验移交人员");
			headers.add("检验移交人员ID");
			headers.add("工艺师ID");
			headers.add("检验师ID");
			headers.add("业务部门ID");

			// 系统信息区域（0个字段）
			// 移除绑定ID、创建时间、更新时间字段

			// 设置列宽 - 主表字段
			// 基本信息区域
			columnWidths.add(8);  // 序号
			columnWidths.add(25); // 单据编号（主）
			columnWidths.add(15); // 型号（主）
			columnWidths.add(15); // 研制阶段（主）
			columnWidths.add(20); // 产品代号（主）
			columnWidths.add(20); // 批次号及数量（主）
			columnWidths.add(25); // 更改单号（主）
			columnWidths.add(30); // 更改项目（主）

			// 人员信息区域
			columnWidths.add(15); // 制单人（主）
			columnWidths.add(15); // 制单日期（主）
			columnWidths.add(15); // 设计师（主）
			columnWidths.add(20); // 质量跟踪确认人员（主）
			columnWidths.add(15); // 签署人（主）
			columnWidths.add(15); // 签署日期（主）

			// 更改信息区域
			columnWidths.add(25); // 更改前编号（主）
			columnWidths.add(30); // 更改前名称（主）
			columnWidths.add(15); // 更改前版本（主）
			columnWidths.add(25); // 更改后编号（主）
			columnWidths.add(30); // 更改后名称（主）
			columnWidths.add(15); // 更改后版本（主）

			// 文件信息区域
			columnWidths.add(35); // 更改单发放包编号（主）
			columnWidths.add(25); // 更改单发放包名称（主）
			columnWidths.add(40); // PDM文件链接（主）
			columnWidths.add(20); // 来自单位（主）

			// 系统信息区域
			columnWidths.add(15); // 单据状态（主）
			columnWidths.add(12); // 是否结束（主）
			columnWidths.add(12); // 状态（主）

			// 设置列宽 - 分支表字段
			// 分支基本信息区域
			columnWidths.add(15); // 分支
			columnWidths.add(20); // 业务部门
			columnWidths.add(25); // 具体情况
			columnWidths.add(15); // 工艺人员
			columnWidths.add(15); // 工艺师
			columnWidths.add(15); // 检验人员
			columnWidths.add(12); // 是否结束
			columnWidths.add(30); // 其它说明1

			// 文件更改信息区域
			columnWidths.add(25); // 生产文件更改前编号
			columnWidths.add(30); // 生产文件更改前名称
			columnWidths.add(15); // 生产文件更改前版本
			columnWidths.add(25); // 生产文件更改后编号
			columnWidths.add(30); // 生产文件更改后名称
			columnWidths.add(15); // 生产文件更改后版本

			// 产品更改信息区域
			columnWidths.add(20); // 主6产品在线更改数量
			columnWidths.add(25); // 主6产品在线更改结论
			columnWidths.add(20); // 主6产品库房更改数量
			columnWidths.add(25); // 主6产品库房更改结论
			columnWidths.add(20); // 主6产品已交更改数量
			columnWidths.add(25); // 主6产品已交更改结论

			// 元器件信息区域
			columnWidths.add(25); // 元器件（更改前）
			columnWidths.add(25); // 元器件（更改后）
			columnWidths.add(30); // 其它说明2

			// 确认信息区域
			columnWidths.add(15); // 工艺确认日期
			columnWidths.add(15); // 检验确认日期
			columnWidths.add(25); // 业务部门确认
			columnWidths.add(15); // 业务部门签字
			columnWidths.add(15); // 业务部门签字日期
			columnWidths.add(25); // 质量师确认
			columnWidths.add(15); // 质量师签字
			columnWidths.add(15); // 质量师签字日期
			columnWidths.add(15); // 工艺移交人员
			columnWidths.add(20); // 工艺移交人员ID
			columnWidths.add(15); // 检验移交人员
			columnWidths.add(20); // 检验移交人员ID
			columnWidths.add(20); // 工艺师ID
			columnWidths.add(20); // 检验师ID
			columnWidths.add(20); // 业务部门ID

			// 系统信息区域
			// 移除绑定ID、创建时间、更新时间的列宽设置
		}

		// 6. 根据查询类型准备数据
		JSONArray data = new JSONArray();
		for (int i = 0; i < dataArray.size(); i++) {
			JSONObject rowData = dataArray.getJSONObject(i);
			JSONArray row = new JSONArray();

			if ("main".equals(queryType)) {
				// 主表模式：提取27个业务字段数据
				// 基本信息区域
				row.add(i + 1); // 序号
				row.add(rowData.getStr("BILLNO", "")); // 单据编号
				row.add(rowData.getStr("XH", "")); // 型号
				row.add(rowData.getStr("YZJD", "")); // 研制阶段
				row.add(rowData.getStr("CPDH", "")); // 产品代号
				row.add(rowData.getStr("PCH_SL", "")); // 批次号及数量
				row.add(rowData.getStr("GGDH", "")); // 更改单号
				row.add(rowData.getStr("GGXM", "")); // 更改项目

				// 人员信息区域（6个字段）
				row.add(rowData.getStr("ZDR", "")); // 制单人
				row.add(rowData.getStr("ZDRQ", "")); // 制单日期
				row.add(rowData.getStr("SJSMC", "")); // 设计师
				row.add(rowData.getStr("ZLGZQRRY", "")); // 质量跟踪确认人员
				row.add(rowData.getStr("QSR", "")); // 签署人
				row.add(rowData.getStr("QSRQ", "")); // 签署日期

				// 更改信息区域（6个字段）
				row.add(rowData.getStr("SRWJ_GGQ", "")); // 更改前编号
				row.add(rowData.getStr("SRWJ_GGQ_MC", "")); // 更改前名称
				row.add(rowData.getStr("SRWJ_GGQ_BB", "")); // 更改前版本
				row.add(rowData.getStr("SRWJ_GGH", "")); // 更改后编号
				row.add(rowData.getStr("SRWJ_GGH_MC", "")); // 更改后名称
				row.add(rowData.getStr("SRWJ_GGH_BB", "")); // 更改后版本

				// 文件信息区域（4个字段）
				row.add(rowData.getStr("GGDFFBBH", "")); // 更改单发放包编号
				row.add(rowData.getStr("GGDFFBMC", "")); // 更改单发放包名称
				row.add(rowData.getStr("PDMFILELINK", "")); // PDM文件链接
				row.add(rowData.getStr("LZDW", "")); // 来自单位

				// 系统信息区域（3个字段）
				row.add(rowData.getStr("VBILLSTATUS", "")); // 单据状态
				row.add(rowData.getStr("ISEND", "")); // 是否结束
				row.add(rowData.getStr("STATUS", "")); // 状态
			} else {
				// 分支表模式：提取65个业务字段数据（27个主表+38个分支表）
				// 主表字段数据提取
				// 基本信息区域（8个字段）
				row.add(i + 1); // 序号
				row.add(rowData.getStr("BILLNO", "")); // 单据编号（主）
				row.add(rowData.getStr("XH", "")); // 型号（主）
				row.add(rowData.getStr("YZJD", "")); // 研制阶段（主）
				row.add(rowData.getStr("CPDH", "")); // 产品代号（主）
				row.add(rowData.getStr("PCH_SL", "")); // 批次号及数量（主）
				row.add(rowData.getStr("GGDH", "")); // 更改单号（主）
				row.add(rowData.getStr("GGXM", "")); // 更改项目（主）

				// 人员信息区域（6个字段）
				row.add(rowData.getStr("ZDR", "")); // 制单人（主）
				row.add(rowData.getStr("ZDRQ", "")); // 制单日期（主）
				row.add(rowData.getStr("SJSMC", "")); // 设计师（主）
				row.add(rowData.getStr("ZLGZQRRY", "")); // 质量跟踪确认人员（主）
				row.add(rowData.getStr("QSR", "")); // 签署人（主）
				row.add(rowData.getStr("QSRQ", "")); // 签署日期（主）

				// 更改信息区域（6个字段）
				row.add(rowData.getStr("SRWJ_GGQ", "")); // 更改前编号（主）
				row.add(rowData.getStr("SRWJ_GGQ_MC", "")); // 更改前名称（主）
				row.add(rowData.getStr("SRWJ_GGQ_BB", "")); // 更改前版本（主）
				row.add(rowData.getStr("SRWJ_GGH", "")); // 更改后编号（主）
				row.add(rowData.getStr("SRWJ_GGH_MC", "")); // 更改后名称（主）
				row.add(rowData.getStr("SRWJ_GGH_BB", "")); // 更改后版本（主）

				// 文件信息区域（4个字段）
				row.add(rowData.getStr("GGDFFBBH", "")); // 更改单发放包编号（主）
				row.add(rowData.getStr("GGDFFBMC", "")); // 更改单发放包名称（主）
				row.add(rowData.getStr("PDMFILELINK", "")); // PDM文件链接（主）
				row.add(rowData.getStr("LZDW", "")); // 来自单位（主）

				// 系统信息区域（3个字段）
				row.add(rowData.getStr("VBILLSTATUS", "")); // 单据状态（主）
				row.add(rowData.getStr("ISEND", "")); // 是否结束（主）
				row.add(rowData.getStr("STATUS", "")); // 状态（主）

				// 分支表字段（38个）- 移除系统字段
				// 分支基本信息区域（8个字段）
				row.add(rowData.getStr("FZ", "")); // 分支
				row.add(rowData.getStr("YWBM", "")); // 业务部门
				row.add(rowData.getStr("JTQK", "")); // 具体情况
				row.add(rowData.getStr("GYRY", "")); // 工艺人员
				row.add(rowData.getStr("GYS", "")); // 工艺师
				row.add(rowData.getStr("JYY", "")); // 检验人员
				row.add(rowData.getStr("B_ISEND", "")); // 是否结束（分支表）
				row.add(rowData.getStr("QTSM1", "")); // 其它说明1

				// 文件更改信息区域（6个字段）
				row.add(rowData.getStr("SCWJ_GGQ", "")); // 生产文件更改前编号
				row.add(rowData.getStr("SCWJ_GGQ_MC", "")); // 生产文件更改前名称
				row.add(rowData.getStr("SCWJ_GGQ_BB", "")); // 生产文件更改前版本
				row.add(rowData.getStr("SCWJ_GGH", "")); // 生产文件更改后编号
				row.add(rowData.getStr("SCWJ_GGH_MC", "")); // 生产文件更改后名称
				row.add(rowData.getStr("SCWJ_GGH_BB", "")); // 生产文件更改后版本

				// 产品更改信息区域（6个字段）
				row.add(rowData.getStr("ZFCP_ZX_GGSL", "")); // 主6产品在线更改数量
				row.add(rowData.getStr("ZFCP_ZX_GGJL", "")); // 主6产品在线更改结论
				row.add(rowData.getStr("ZFCP_KF_GGSL", "")); // 主6产品库房更改数量
				row.add(rowData.getStr("ZFCP_KF_GGJL", "")); // 主6产品库房更改结论
				row.add(rowData.getStr("ZFCP_YJ_GGSL", "")); // 主6产品已交更改数量
				row.add(rowData.getStr("ZFCP_YJ_GGJL", "")); // 主6产品已交更改结论

				// 元器件信息区域（3个字段）
				row.add(rowData.getStr("YQJ_GGQ", "")); // 元器件（更改前）
				row.add(rowData.getStr("YQJ_GGH", "")); // 元器件（更改后）
				row.add(rowData.getStr("QTSM2", "")); // 其它说明2

				// 确认信息区域（15个字段）
				row.add(rowData.getStr("RQ1", "")); // 工艺确认日期
				row.add(rowData.getStr("RQ2", "")); // 检验确认日期
				row.add(rowData.getStr("YYBMQR", "")); // 业务部门确认
				row.add(rowData.getStr("QZ3", "")); // 业务部门签字
				row.add(rowData.getStr("RQ3", "")); // 业务部门签字日期
				row.add(rowData.getStr("ZLSQR", "")); // 质量师确认
				row.add(rowData.getStr("QZ4", "")); // 质量师签字
				row.add(rowData.getStr("RQ4", "")); // 质量师签字日期
				row.add(rowData.getStr("YJRY_GY", "")); // 工艺移交人员
				row.add(rowData.getStr("YJRYID_GY", "")); // 工艺移交人员ID
				row.add(rowData.getStr("YJRY_JY", "")); // 检验移交人员
				row.add(rowData.getStr("YJRYID_JY", "")); // 检验移交人员ID
				row.add(rowData.getStr("GYSID", "")); // 工艺师ID
				row.add(rowData.getStr("JYSID", "")); // 检验师ID
				row.add(rowData.getStr("YWBMID", "")); // 业务部门ID

				// 系统信息区域（0个字段）
				// 移除绑定ID、创建时间、更新时间字段
			}

			data.add(row);
		}

		// 7. 生成Excel文件
		String statusText = "all".equals(status) ? "全部" : ("finished".equals(status) ? "已完成" : "未完成");
		String situationText = "all".equals(situation) ? "全部情况" : situation;
		String queryTypeText = "main".equals(queryType) ? "总数量" : "分支详情";
		String fileName = "技术状态更改单_" + queryTypeText + "_" + statusText + "_" + situationText;

		return CommonUtil.createExcelFile(fileName, headers, data, columnWidths, 18);
	}

	/**
	 * 导出不合格品审理单数据为Excel
	 *
	 * @param treeId        型号ID
	 * @param startDate     开始日期
	 * @param endDate       结束日期
	 * @param status        状态筛选 (all-全部, finished-已完成, unfinished-未完成)
	 * @param severityLevel 严重程度筛选 (all-全部, 一级, 二级, 三级)
	 * @return 生成的Excel文件
	 * @throws Exception 异常
	 */
	public File exportNonconformityExcel(String treeId, String username, String startDate, String endDate, String status, String severityLevel) throws Exception {
		// 1. 准备查询参数
		JSONObject params = new JSONObject();
		params.set("treeId", treeId);
		params.set("username", username);
		params.set("startDate", startDate);
		params.set("endDate", endDate);
		params.set("status", status);
		params.set("severityLevel", severityLevel);
		params.set("page", 1);
		params.set("limit", 10000); // 设置一个较大的值，确保获取所有数据
		params.set("isAllData", true);

		// 2. 调用Thingworx接口获取数据
		JSONObject result = Util.postTwxForObject("Thing.Fn.AitScreen", "QueryNonconformityList", params);

		// 3. 检查查询结果
		if (result.getInt("code", -1) != 0) {
			throw new Exception("查询不合格品审理单数据失败：" + result.getStr("msg", "未知错误"));
		}

		// 4. 获取数据列表
		JSONArray dataArray = result.getJSONArray("data");
		if (dataArray == null) {
			dataArray = new JSONArray();
		}

		// 5. 准备Excel表头和列宽配置
		JSONArray headers = new JSONArray();
		JSONArray columnWidths = new JSONArray();

		// 基本信息区域（15个字段）
		headers.add("序号");
		headers.add("单据编号");
		headers.add("型号");
		headers.add("研制阶段");
		headers.add("产品（或零部组件）名称");
		headers.add("产品编号");
		headers.add("批次号");
		headers.add("产品图号（代号）");
		headers.add("工序号");
		headers.add("发现地点");
		headers.add("不合格品数量");
		headers.add("责任部门");
		headers.add("操作者");
		headers.add("送检数");
		headers.add("发现日期");

		// 审理信息区域（10个字段）
		headers.add("审理日期");
		headers.add("审理地点");
		headers.add("不合格品情况描述");
		headers.add("质量检验人员");
		headers.add("制单日期");
		headers.add("不合格品严重程度");
		headers.add("严重程度确认人");
		headers.add("严重程度确认日期");
		headers.add("原因分类");
		headers.add("原因分析");

		// 审理意见区域（15个字段）
		headers.add("不合格品审理意见");
		headers.add("纠正");
		headers.add("纠正措施");
		headers.add("不合格品审理组长");
		headers.add("审理人员意见");
		headers.add("审理人员签字");
		headers.add("审理人员签字日期");
		headers.add("提交型号总体审查");
		headers.add("提交院不合格品审理委员会审查");
		headers.add("提交军代表审查");
		headers.add("审理组长签字");
		headers.add("审理组长签字日期");
		headers.add("型号总体意见");
		headers.add("型号总体签字");
		headers.add("型号总体签字日期");

		// 委员会及军代表意见区域（10个字段）
		headers.add("院不合格品审理委员会意见");
		headers.add("委员会签字");
		headers.add("委员会签字日期");
		headers.add("军代表意见");
		headers.add("军代表签字");
		headers.add("军代表签字日期");
		headers.add("不合格品处置落实情况");
		headers.add("纠正措施落实情况");
		headers.add("型号和专业质量师签字");
		headers.add("质量师签字日期");

		// 系统信息区域（7个字段）
		headers.add("上传审查意见");
		headers.add("单据状态");
		headers.add("是否结束");
		headers.add("状态");
		headers.add("创建日期");
		headers.add("发起部门");
		headers.add("填报人ID");

		// 设置列宽 - 基本信息区域
		columnWidths.add(8);  // 序号
		columnWidths.add(25); // 单据编号
		columnWidths.add(15); // 型号
		columnWidths.add(15); // 研制阶段
		columnWidths.add(30); // 产品（或零部组件）名称
		columnWidths.add(20); // 产品编号
		columnWidths.add(15); // 批次号
		columnWidths.add(25); // 产品图号（代号）
		columnWidths.add(15); // 工序号
		columnWidths.add(20); // 发现地点
		columnWidths.add(15); // 不合格品数量
		columnWidths.add(20); // 责任部门
		columnWidths.add(15); // 操作者
		columnWidths.add(12); // 送检数
		columnWidths.add(15); // 发现日期

		// 设置列宽 - 审理信息区域
		columnWidths.add(15); // 审理日期
		columnWidths.add(20); // 审理地点
		columnWidths.add(40); // 不合格品情况描述
		columnWidths.add(15); // 质量检验人员
		columnWidths.add(15); // 制单日期
		columnWidths.add(15); // 不合格品严重程度
		columnWidths.add(15); // 严重程度确认人
		columnWidths.add(15); // 严重程度确认日期
		columnWidths.add(30); // 原因分类
		columnWidths.add(40); // 原因分析

		// 设置列宽 - 审理意见区域
		columnWidths.add(40); // 不合格品审理意见
		columnWidths.add(30); // 纠正
		columnWidths.add(40); // 纠正措施
		columnWidths.add(20); // 不合格品审理组长
		columnWidths.add(30); // 审理人员意见
		columnWidths.add(15); // 审理人员签字
		columnWidths.add(15); // 审理人员签字日期
		columnWidths.add(20); // 提交型号总体审查
		columnWidths.add(30); // 提交院不合格品审理委员会审查
		columnWidths.add(20); // 提交军代表审查
		columnWidths.add(15); // 审理组长签字
		columnWidths.add(15); // 审理组长签字日期
		columnWidths.add(30); // 型号总体意见
		columnWidths.add(15); // 型号总体签字
		columnWidths.add(15); // 型号总体签字日期

		// 设置列宽 - 委员会及军代表意见区域
		columnWidths.add(35); // 院不合格品审理委员会意见
		columnWidths.add(15); // 委员会签字
		columnWidths.add(15); // 委员会签字日期
		columnWidths.add(30); // 军代表意见
		columnWidths.add(15); // 军代表签字
		columnWidths.add(15); // 军代表签字日期
		columnWidths.add(35); // 不合格品处置落实情况
		columnWidths.add(35); // 纠正措施落实情况
		columnWidths.add(20); // 型号和专业质量师签字
		columnWidths.add(15); // 质量师签字日期

		// 设置列宽 - 系统信息区域
		columnWidths.add(30); // 上传审查意见
		columnWidths.add(15); // 单据状态
		columnWidths.add(12); // 是否结束
		columnWidths.add(12); // 状态
		columnWidths.add(15); // 创建日期
		columnWidths.add(20); // 发起部门
		columnWidths.add(15); // 填报人ID

		// 6. 准备Excel数据内容
		JSONArray data = new JSONArray();
		for (int i = 0; i < dataArray.size(); i++) {
			JSONObject rowData = dataArray.getJSONObject(i);
			JSONArray row = new JSONArray();

			// 基本信息区域（15个字段）
			row.add(i + 1); // 序号
			row.add(rowData.getStr("BILLNO", "")); // 单据编号
			row.add(rowData.getStr("XH", "")); // 型号
			row.add(rowData.getStr("YZJD", "")); // 研制阶段
			row.add(rowData.getStr("CP", "")); // 产品（或零部组件）名称
			row.add(rowData.getStr("CPBH", "")); // 产品编号
			row.add(rowData.getStr("PCH", "")); // 批次号
			row.add(rowData.getStr("CPTH", "")); // 产品图号（代号）
			row.add(rowData.getStr("GXH", "")); // 工序号
			row.add(rowData.getStr("FXDD", "")); // 发现地点
			row.add(rowData.getStr("BHGSL", "")); // 不合格品数量
			row.add(rowData.getStr("ZRBM", "")); // 责任部门
			row.add(rowData.getStr("CZZ", "")); // 操作者
			row.add(rowData.getStr("SJS", "")); // 送检数
			row.add(rowData.getStr("FXRQ", "")); // 发现日期

			// 审理信息区域（10个字段）
			row.add(rowData.getStr("SLRQ", "")); // 审理日期
			row.add(rowData.getStr("SLDD", "")); // 审理地点
			row.add(rowData.getStr("BHGPQKMS", "")); // 不合格品情况描述
			row.add(rowData.getStr("BZRY", "")); // 质量检验人员
			row.add(rowData.getStr("ZDRQ", "")); // 制单日期
			row.add(rowData.getStr("BHGPYZCD", "")); // 不合格品严重程度
			row.add(rowData.getStr("QZ1", "")); // 严重程度确认人
			row.add(rowData.getStr("RQ1", "")); // 严重程度确认日期
			row.add(rowData.getStr("YYFL", "")); // 原因分类
			row.add(rowData.getStr("YYFX", "")); // 原因分析

			// 审理意见区域（15个字段）
			row.add(rowData.getStr("BHGPSLYJ", "")); // 不合格品审理意见
			row.add(rowData.getStr("JZ", "")); // 纠正
			row.add(rowData.getStr("JZCS", "")); // 纠正措施
			row.add(rowData.getStr("BHGPSLZZ", "")); // 不合格品审理组长
			row.add(rowData.getStr("YJ2", "")); // 审理人员意见
			row.add(rowData.getStr("QZ2", "")); // 审理人员签字
			row.add(rowData.getStr("RQ2", "")); // 审理人员签字日期
			row.add(rowData.getStr("TJXHZTSC", "")); // 提交型号总体审查
			row.add(rowData.getStr("TJYBHGPWWH", "")); // 提交院不合格品审理委员会审查
			row.add(rowData.getStr("TJJDBSC", "")); // 提交军代表审查
			row.add(rowData.getStr("QZ3", "")); // 审理组长签字
			row.add(rowData.getStr("RQ3", "")); // 审理组长签字日期
			row.add(rowData.getStr("YJ3", "")); // 型号总体意见
			row.add(rowData.getStr("QZ4", "")); // 型号总体签字
			row.add(rowData.getStr("RQ4", "")); // 型号总体签字日期

			// 委员会及军代表意见区域（10个字段）
			row.add(rowData.getStr("YJ4", "")); // 院不合格品审理委员会意见
			row.add(rowData.getStr("QZ5", "")); // 委员会签字
			row.add(rowData.getStr("RQ5", "")); // 委员会签字日期
			row.add(rowData.getStr("YJ5", "")); // 军代表意见
			row.add(rowData.getStr("QZ6", "")); // 军代表签字
			row.add(rowData.getStr("RQ6", "")); // 军代表签字日期
			row.add(rowData.getStr("BHGPCZLSQK", "")); // 不合格品处置落实情况
			row.add(rowData.getStr("JZCSLSQK", "")); // 纠正措施落实情况
			row.add(rowData.getStr("QZ7", "")); // 型号和专业质量师签字
			row.add(rowData.getStr("RQ7", "")); // 质量师签字日期

			// 系统信息区域（7个字段）
			row.add(rowData.getStr("SCSCYJ", "")); // 上传审查意见
			row.add(rowData.getStr("VBILLSTATUS", "")); // 单据状态
			row.add(rowData.getStr("ISEND", "")); // 是否结束
			row.add(rowData.getStr("STATUS_TEXT", "")); // 状态
			row.add(rowData.getStr("CREATE_DATE", "")); // 创建日期
			row.add(rowData.getStr("BZBM", "")); // 发起部门
			row.add(rowData.getStr("BZRYID", "")); // 填报人ID

			data.add(row);
		}

		// 7. 生成Excel文件
		String statusText = "all".equals(status) ? "全部" : ("finished".equals(status) ? "已完成" : "未完成");
		String severityText = "all".equals(severityLevel) ? "全部严重程度" : severityLevel;
		String fileName = "不合格品审理单_" + statusText + "_" + severityText;

		return CommonUtil.createExcelFile(fileName, headers, data, columnWidths, 18);
	}

	/**
	 * 导出汇总统计数据为Excel
	 *
	 * @param treeId    型号ID
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @param groupType 分组类型 (ISSUBMIT、ISLUOHAN、ISCERTIFICATE)
	 * @return 生成的Excel文件
	 * @throws Exception 异常
	 */
	public File exportSummaryExcel(String treeId, String startDate, String endDate, String groupType) throws Exception {
		try {
			// 1. 调用Thingworx接口获取统计数据
			JSONObject summaryData = querySummaryData(treeId, startDate, endDate, groupType);

			// 2. 准备表头和列宽
			JSONArray headers = getSummaryHeadersByGroupType(groupType);
			JSONArray columnWidths = getSummaryColumnWidthsByGroupType(groupType);

			// 3. 处理数据
			JSONArray data = new JSONArray();
			if (summaryData != null && summaryData.getBool("success", false)) {
				JSONObject dataObj = summaryData.getJSONObject("data");
				if (dataObj != null) {
					JSONArray names = dataObj.getJSONArray("names");
					JSONObject counts = dataObj.getJSONObject("counts");

					// 添加调试日志
					log.info("导出汇总数据 - groupType: {}, counts keys: {}", groupType, counts.keySet());
					log.info("导出汇总数据 - names: {}", names);
					log.info("导出汇总数据 - counts: {}", counts);

					if (names != null && counts != null) {
						for (int i = 0; i < names.size(); i++) {
							String nameWithId = names.getStr(i);
							if (StrUtil.isBlank(nameWithId) || nameWithId.trim().isEmpty()) {
								continue; // 跳过空行
							}

							// 解析型号名称（去掉ID部分）
							String modelName = nameWithId.contains("~~~") ?
								nameWithId.substring(0, nameWithId.indexOf("~~~")) : nameWithId;

							JSONArray row = new JSONArray();
							row.add(i + 1); // 序号
							row.add(modelName); // 型号

							// 根据groupType添加对应的统计列
							if ("ISCERTIFICATE".equals(groupType)) {
								// 未交付装星单机汇总: 已交付、未交付
								JSONArray deliveredCounts = counts.getJSONArray("已交付");
								JSONArray undeliveredCounts = counts.getJSONArray("未交付");

								log.info("处理第{}行数据，型号: {}", i, modelName);
								log.info("已交付数组: {}, 未交付数组: {}", deliveredCounts, undeliveredCounts);

								int delivered = (deliveredCounts != null && i < deliveredCounts.size()) ?
									deliveredCounts.getInt(i) : 0;
								int undelivered = (undeliveredCounts != null && i < undeliveredCounts.size()) ?
									undeliveredCounts.getInt(i) : 0;

								log.info("已交付: {}, 未交付: {}", delivered, undelivered);

								row.add(delivered);   // 已交付
								row.add(undelivered); // 未交付

							} else if ("ISLUOHAN".equals(groupType)) {
								// 落焊单机汇总: 不需落焊、已落焊、需要但未落焊
								JSONArray noNeedCounts = counts.getJSONArray("不需落焊");
								JSONArray finishedCounts = counts.getJSONArray("已落焊");
								JSONArray unfinishedCounts = counts.getJSONArray("需要但未落焊");

								int noNeed = (noNeedCounts != null && i < noNeedCounts.size()) ?
									noNeedCounts.getInt(i) : 0;
								int finished = (finishedCounts != null && i < finishedCounts.size()) ?
									finishedCounts.getInt(i) : 0;
								int unfinished = (unfinishedCounts != null && i < unfinishedCounts.size()) ?
									unfinishedCounts.getInt(i) : 0;

								row.add(noNeed);     // 不需落焊
								row.add(finished);   // 已落焊
								row.add(unfinished); // 需要但未落焊

							} else if ("ISSUBMIT".equals(groupType)) {
								// 单机证明材料交付汇总: 已提交、未提交
								JSONArray submittedCounts = counts.getJSONArray("已提交");
								JSONArray unsubmittedCounts = counts.getJSONArray("未提交");

								int submitted = (submittedCounts != null && i < submittedCounts.size()) ?
									submittedCounts.getInt(i) : 0;
								int unsubmitted = (unsubmittedCounts != null && i < unsubmittedCounts.size()) ?
									unsubmittedCounts.getInt(i) : 0;

								row.add(submitted);   // 已提交
								row.add(unsubmitted); // 未提交
							}

							data.add(row);
						}
					}
				}
			}

			// 4. 生成Excel文件
			String fileName = getSummaryFileNameByGroupType(groupType);
			return CommonUtil.createExcelFile(fileName, headers, data, columnWidths, 18);

		} catch (Exception e) {
			log.error("导出汇总统计数据失败", e);
			throw e;
		}
	}

	/**
	 * 查询汇总统计数据
	 *
	 * @param treeId    型号ID
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @param groupType 分组类型
	 * @return 统计数据
	 * @throws Exception 异常
	 */
	private JSONObject querySummaryData(String treeId, String startDate, String endDate, String groupType) throws Exception {
		try {
			// 准备参数
			JSONObject params = new JSONObject();
			params.set("treeId", Double.parseDouble(treeId));
			params.set("startDate", startDate);
			params.set("endDate", endDate);
			params.set("username", ""); // 空用户名，获取所有数据
			params.set("groupType", groupType);

			// 调用Thingworx接口
			JSONObject result = Util.postTwxForObject("Thing.Fn.AitScreen", "QueryModelSubmitCount", params);

			// 添加调试日志
			log.info("ThingWorx返回的原始数据: {}", result);

			return result;
		} catch (Exception e) {
			log.error("查询汇总统计数据失败", e);
			throw e;
		}
	}

	/**
	 * 根据分组类型获取表头
	 *
	 * @param groupType 分组类型
	 * @return 表头数组
	 */
	private JSONArray getSummaryHeadersByGroupType(String groupType) {
		JSONArray headers = new JSONArray();
		headers.add("序号");
		headers.add("型号");

		switch (groupType) {
			case "ISCERTIFICATE":
				headers.add("已交付");
				headers.add("未交付");
				break;
			case "ISLUOHAN":
				headers.add("不需落焊");
				headers.add("已落焊");
				headers.add("需要但未落焊");
				break;
			case "ISSUBMIT":
				headers.add("已提交");
				headers.add("未提交");
				break;
			default:
				headers.add("数据");
				break;
		}

		return headers;
	}

	/**
	 * 根据分组类型获取列宽
	 *
	 * @param groupType 分组类型
	 * @return 列宽数组
	 */
	private JSONArray getSummaryColumnWidthsByGroupType(String groupType) {
		JSONArray columnWidths = new JSONArray();
		columnWidths.add(8);  // 序号
		columnWidths.add(20); // 型号

		switch (groupType) {
			case "ISCERTIFICATE":
				columnWidths.add(25); // 已交付
				columnWidths.add(25); // 未交付
				break;
			case "ISLUOHAN":
				columnWidths.add(25); // 不需落焊
				columnWidths.add(25); // 已落焊
				columnWidths.add(30); // 需要但未落焊
				break;
			case "ISSUBMIT":
				columnWidths.add(25); // 已提交
				columnWidths.add(25); // 未提交
				break;
			default:
				columnWidths.add(20); // 数据
				break;
		}

		return columnWidths;
	}

	/**
	 * 根据分组类型获取文件名
	 *
	 * @param groupType 分组类型
	 * @return 文件名
	 */
	private String getSummaryFileNameByGroupType(String groupType) {
		switch (groupType) {
			case "ISCERTIFICATE":
				return "未交付装星单机汇总";
			case "ISLUOHAN":
				return "落焊单机汇总";
			case "ISSUBMIT":
				return "单机证明材料交付汇总";
			default:
				return "汇总统计数据";
		}
	}
}
