package com.cirpoint.util;

import com.cirpoint.model.ReportTreeNode;
import java.util.ArrayList;
import java.util.List;

public class ReportTreeUtil {
	/**
	 * 两层循环实现建树
	 *
	 * @param treeNodes
	 *            传入的树节点列表
	 */
	public static <T extends ReportTreeNode> List<T> build(List<T> treeNodes, Object root) {
		List<T> trees = new ArrayList<>();
		for (T treeNode : treeNodes) {
			if (root.equals(treeNode.getPid())) {
				trees.add(treeNode);
			}
			for (T it : treeNodes) {
				if (it.getPid().equals(treeNode.getId())) {
					if (treeNode.getChildren() == null) {
						treeNode.setChildren(new ArrayList<>());
					}
					treeNode.add(it);
				}
			}
		}
		return trees;
	}

}
