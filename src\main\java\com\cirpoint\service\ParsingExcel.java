package com.cirpoint.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.util.Util;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;
import lombok.Getter;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import static com.cirpoint.util.Util.getCellValue;

public class ParsingExcel {
	private static final Logger log = Logger.getLogger(ParsingExcel.class.getName());
	private static final int START_ROW = 3;
	private static final int HEADER_ROW = 2;

	@Getter
	public enum ExcelType {
		ELECTRONIC_COMPONENTS("insert_electronic_components", 23, START_ROW),
		CABLE_INSULATION("insert_cable_insulation_test", 12, START_ROW),
		HEATING_ELEMENT("insert_heating_element_reinspection", 9, HEADER_ROW),
		HEATING_CIRCUIT("insert_heating_circuit_test", 11, HEADER_ROW);

		private final String insertMethod;
		private final int columnCount;
		private final int startRow;

		ExcelType(String insertMethod, int columnCount, int startRow) {
			this.insertMethod = insertMethod;
			this.columnCount = columnCount;
			this.startRow = startRow;
		}

	}


	public static int parseExcel(String filePath, String resultId, ExcelType excelType) {
		File file = new File(filePath);
		if (!file.exists()) {
			log.warning("File not found: " + filePath);
			return 0;
		}

		try (FileInputStream fis = new FileInputStream(file);
			 XSSFWorkbook workbook = new XSSFWorkbook(fis)) {

			XSSFSheet sheet = workbook.getSheetAt(0);
			if (sheet == null) {
				throw new IllegalArgumentException("Excel sheet not found");
			}

			JSONArray jsonArray = processRows(sheet, resultId, excelType);
			return sendToThingworx(jsonArray, excelType.getInsertMethod());

		} catch (IOException e) {
			log.severe("Error parsing Excel file: " + filePath + ", " + e.getMessage());
			return 0;
		}
	}

	private static JSONArray processRows(XSSFSheet sheet, String resultId, ExcelType excelType) {
		JSONArray jsonArray = new JSONArray();
		int lastRowNum = sheet.getLastRowNum();
		Map<Integer, String> fieldMappings = getFieldMappings(excelType);

		for (int i = excelType.getStartRow(); i <= lastRowNum; i++) {
			XSSFRow row = sheet.getRow(i);
			if (row == null) continue;

			JSONObject jsonObject = new JSONObject();
			jsonObject.set("resultId", resultId);

			for (int j = 0; j < excelType.getColumnCount(); j++) {
				XSSFCell cell = row.getCell(j);
				String value = getCellValue(cell);
				String fieldName = fieldMappings.get(j);
				if (fieldName != null) {
					jsonObject.set(fieldName, value);
				}
			}

			jsonArray.add(jsonObject);
		}
		return jsonArray;
	}


	private static int sendToThingworx(JSONArray jsonArray, String insertMethod) {
		try {
			JSONObject requestJson = new JSONObject();
			requestJson.set("jsons", jsonArray.toString());

			JSONArray response = Util.postTwx("Thing.Fn.ExcelImport", insertMethod, requestJson);

			return response.getJSONObject(0).getInt("result");
		} catch (Exception e) {
			log.severe("Error sending data to Thingworx: " + e.getMessage());
			return 0;
		}
	}

	private static Map<Integer, String> getFieldMappings(ExcelType type) {
		Map<Integer, String> mappings = new HashMap<>();

		switch (type) {
			case ELECTRONIC_COMPONENTS:
				mappings.put(0, "serial_number");
				mappings.put(1, "model");
				mappings.put(2, "lib_ref");
				mappings.put(3, "model_specification");
				mappings.put(4, "batch_number");
				mappings.put(5, "manufacturer");
				mappings.put(6, "quality_grade");
				mappings.put(7, "installed_capacity");
				mappings.put(8, "single_aircraft");
				mappings.put(9, "screening_center");
				mappings.put(10, "screening_quantity");
				mappings.put(11, "screening_qualified_quantity");
				mappings.put(12, "unqualified_number");
				mappings.put(13, "DPA_situation");
				mappings.put(14, "import_situation");
				mappings.put(15, "external_devices");
				mappings.put(16, "approver");
				mappings.put(17, "extended_type");
				mappings.put(18, "reexamination_conclusion");
				mappings.put(19, "drawing_description_model");
				mappings.put(20, "actual_model");
				mappings.put(21, "test_report_no");
				mappings.put(22, "internal_certificate");
				break;

			case CABLE_INSULATION:
				mappings.put(0, "serial_number");
				mappings.put(1, "model");
				mappings.put(2, "cable_number");
				mappings.put(3, "starting_connector");
				mappings.put(4, "starting_contact");
				mappings.put(5, "terminal_connector");
				mappings.put(6, "terminal_contact");
				mappings.put(7, "theoretical_value");
				mappings.put(8, "test_value");
				mappings.put(9, "insulation");
				mappings.put(10, "pressurization");
				mappings.put(11, "conclusion");
				break;

			case HEATING_ELEMENT:
				mappings.put(0, "serial_number");
				mappings.put(1, "model");
				mappings.put(2, "heating_plate_name");
				mappings.put(3, "heating_plate_number");
				mappings.put(4, "resistance");
				mappings.put(5, "insulation");
				mappings.put(6, "photo_number");
				mappings.put(7, "remark1");
				mappings.put(8, "remark2");
				break;

			case HEATING_CIRCUIT:
				mappings.put(0, "serial_number");
				mappings.put(1, "model");
				mappings.put(2, "connector_no");
				mappings.put(3, "start_node_number");
				mappings.put(4, "end_node_number");
				mappings.put(5, "heating_circuit_name");
				mappings.put(6, "resistance_after_pasting");
				mappings.put(7, "insulation_after_pasting");
				mappings.put(8, "remark1");
				mappings.put(9, "remark2");
				mappings.put(10, "remark3");
				break;
		}

		return mappings;
	}
}