package com.cirpoint.model.worktime;

import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 迟到早退统计结果数据模型
 * 用于表示员工迟到早退统计结果，支持明细和汇总两种用途
 */
@Data
public class LateEarlyResult {
    
    /**
     * 序号（从1开始）
     */
    private Integer sequence;
    
    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * 工号（9位员工编号）
     */
    private String employeeId;
    
    /**
     * 工作日期（明细表使用）
     */
    private LocalDate workDate;
    
    /**
     * 当日净工时（小时，保留2位小数）
     */
    private Double dailyHours;
    
    /**
     * 出勤天数（汇总表使用）
     */
    private Integer attendanceDays;
    
    /**
     * 平均工时（小时，保留2位小数，汇总表使用）
     */
    private Double averageHours;
    
    /**
     * 上午迟到次数（当日或累计）
     */
    private Integer morningLateCount;
    
    /**
     * 上午早退次数（当日或累计）
     */
    private Integer morningEarlyCount;
    
    /**
     * 下午迟到次数（当日或累计）
     */
    private Integer afternoonLateCount;
    
    /**
     * 下午早退次数（当日或累计）
     */
    private Integer afternoonEarlyCount;
    
    /**
     * 上午第一次进入时间（明细表使用）
     */
    private LocalDateTime morningFirstEntry;
    
    /**
     * 上午最后一次出时间（明细表使用）
     */
    private LocalDateTime morningLastExit;
    
    /**
     * 下午第一次进入时间（明细表使用）
     */
    private LocalDateTime afternoonFirstEntry;
    
    /**
     * 下午最后一次出时间（明细表使用）
     */
    private LocalDateTime afternoonLastExit;

    /**
     * 厂房标识（用于区分不同厂房）
     */
    private String location;

    /**
     * 是否参与迟到早退统计（仅910/920厂房参与）
     */
    private Boolean participateInLateEarlyStats;

    /**
     * 总工时（用于工时汇总表）
     */
    private Double totalHours;
    
    /**
     * 默认构造函数
     */
    public LateEarlyResult() {
        this.morningLateCount = 0;
        this.morningEarlyCount = 0;
        this.afternoonLateCount = 0;
        this.afternoonEarlyCount = 0;
        this.dailyHours = 0.0;
        this.attendanceDays = 0;
        this.averageHours = 0.0;
        this.totalHours = 0.0;
        this.participateInLateEarlyStats = false;
    }
    
    /**
     * 明细记录构造函数
     * @param employeeName 员工姓名
     * @param workDate 工作日期
     * @param dailyHours 当日净工时
     */
    public LateEarlyResult(String employeeName, LocalDate workDate, Double dailyHours) {
        this();
        this.employeeName = employeeName;
        this.workDate = workDate;
        this.dailyHours = dailyHours;
        this.attendanceDays = 1;
    }

    /**
     * 明细记录构造函数（包含工号）
     * @param employeeName 员工姓名
     * @param employeeId 工号
     * @param workDate 工作日期
     * @param dailyHours 当日净工时
     */
    public LateEarlyResult(String employeeName, String employeeId, LocalDate workDate, Double dailyHours) {
        this(employeeName, workDate, dailyHours);
        this.employeeId = employeeId;
    }
    
    /**
     * 汇总记录构造函数
     * @param employeeName 员工姓名
     * @param attendanceDays 出勤天数
     * @param averageHours 平均工时
     */
    public LateEarlyResult(String employeeName, Integer attendanceDays, Double averageHours) {
        this();
        this.employeeName = employeeName;
        this.attendanceDays = attendanceDays;
        this.averageHours = averageHours;
    }

    /**
     * 汇总记录构造函数（包含工号）
     * @param employeeName 员工姓名
     * @param employeeId 工号
     * @param attendanceDays 出勤天数
     * @param averageHours 平均工时
     */
    public LateEarlyResult(String employeeName, String employeeId, Integer attendanceDays, Double averageHours) {
        this(employeeName, attendanceDays, averageHours);
        this.employeeId = employeeId;
    }

    /**
     * 工时统计记录构造函数（用于纯工时统计）
     * @param employeeName 员工姓名
     * @param workDate 工作日期
     * @param dailyHours 当日净工时
     * @param location 厂房位置
     */
    public LateEarlyResult(String employeeName, LocalDate workDate, Double dailyHours, String location) {
        this(employeeName, workDate, dailyHours);
        this.location = location;
        this.participateInLateEarlyStats = "910厂房".equals(location) || "920厂房".equals(location);
    }

    /**
     * 工时统计记录构造函数（包含工号，用于纯工时统计）
     * @param employeeName 员工姓名
     * @param employeeId 工号
     * @param workDate 工作日期
     * @param dailyHours 当日净工时
     * @param location 厂房位置
     */
    public LateEarlyResult(String employeeName, String employeeId, LocalDate workDate, Double dailyHours, String location) {
        this(employeeName, employeeId, workDate, dailyHours);
        this.location = location;
        this.participateInLateEarlyStats = "910厂房".equals(location) || "920厂房".equals(location);
    }

    /**
     * 综合汇总记录构造函数
     * @param employeeName 员工姓名
     * @param attendanceDays 出勤天数
     * @param totalHours 总工时
     * @param averageHours 平均工时
     */
    public LateEarlyResult(String employeeName, Integer attendanceDays, Double totalHours, Double averageHours) {
        this(employeeName, attendanceDays, averageHours);
        this.totalHours = totalHours;
    }

    /**
     * 综合汇总记录构造函数（包含工号）
     * @param employeeName 员工姓名
     * @param employeeId 工号
     * @param attendanceDays 出勤天数
     * @param totalHours 总工时
     * @param averageHours 平均工时
     */
    public LateEarlyResult(String employeeName, String employeeId, Integer attendanceDays, Double totalHours, Double averageHours) {
        this(employeeName, employeeId, attendanceDays, averageHours);
        this.totalHours = totalHours;
    }
    
    /**
     * 获取工作日期字符串（YYYY-MM-DD格式）
     * @return 日期字符串
     */
    public String getWorkDateString() {
        return workDate != null ? workDate.toString() : "";
    }
    
    /**
     * 获取格式化的当日净工时（保留2位小数）
     * @return 格式化的工时字符串
     */
    public String getFormattedDailyHours() {
        return dailyHours != null ? String.format("%.2f", dailyHours) : "0.00";
    }
    
    /**
     * 获取格式化的平均工时（保留2位小数）
     * @return 格式化的平均工时字符串
     */
    public String getFormattedAverageHours() {
        return averageHours != null ? String.format("%.2f", averageHours) : "0.00";
    }

    /**
     * 获取格式化的总工时（保留2位小数）
     * @return 格式化的总工时字符串
     */
    public String getFormattedTotalHours() {
        return totalHours != null ? String.format("%.2f", totalHours) : "0.00";
    }
    
    /**
     * 获取格式化的时间字符串
     * @param dateTime 时间对象
     * @return 格式化的时间字符串 HH:mm:ss
     */
    public String getFormattedTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
    }
    
    /**
     * 获取上午第一次进入时间字符串
     * @return 格式化的时间字符串
     */
    public String getMorningFirstEntryString() {
        return getFormattedTime(morningFirstEntry);
    }
    
    /**
     * 获取上午最后一次出时间字符串
     * @return 格式化的时间字符串
     */
    public String getMorningLastExitString() {
        return getFormattedTime(morningLastExit);
    }
    
    /**
     * 获取下午第一次进入时间字符串
     * @return 格式化的时间字符串
     */
    public String getAfternoonFirstEntryString() {
        return getFormattedTime(afternoonFirstEntry);
    }
    
    /**
     * 获取下午最后一次出时间字符串
     * @return 格式化的时间字符串
     */
    public String getAfternoonLastExitString() {
        return getFormattedTime(afternoonLastExit);
    }
    
    /**
     * 验证结果数据的有效性
     * @return 验证结果
     */
    public boolean isValid() {
        // 基础字段验证
        if (employeeName == null || employeeName.trim().isEmpty()) {
            return false;
        }

        // 日期或出勤天数至少有一个
        if (workDate == null && (attendanceDays == null || attendanceDays <= 0)) {
            return false;
        }

        // 工时验证
        if (dailyHours == null || dailyHours < 0) {
            return false;
        }

        // 迟到早退次数验证
        if (morningLateCount == null || morningLateCount < 0 ||
            morningEarlyCount == null || morningEarlyCount < 0 ||
            afternoonLateCount == null || afternoonLateCount < 0 ||
            afternoonEarlyCount == null || afternoonEarlyCount < 0) {
            return false;
        }

        // 汇总记录的额外验证
        if (isSummaryRecord()) {
            if (totalHours != null && totalHours < 0) {
                return false;
            }
            if (averageHours != null && averageHours < 0) {
                return false;
            }
        }

        return true;
    }
    
    /**
     * 获取员工复合标识符
     * 格式：员工姓名+工号（如果工号存在）
     * @return 员工复合标识符
     */
    public String getEmployeeCompositeKey() {
        if (employeeName == null) {
            return "";
        }
        if (employeeId != null && !employeeId.trim().isEmpty()) {
            return employeeName + "+" + employeeId;
        } else {
            return employeeName;
        }
    }

    /**
     * 获取用于拼音排序的键值
     * 格式：员工复合标识符 + 日期（如果有）
     * @return 排序键
     */
    public String getSortKey() {
        if (workDate != null) {
            return getEmployeeCompositeKey() + "|" + getWorkDateString();
        } else {
            return getEmployeeCompositeKey();
        }
    }
    
    /**
     * 判断是否为汇总记录
     * @return true表示汇总记录，false表示明细记录
     */
    public boolean isSummaryRecord() {
        return workDate == null && attendanceDays != null && attendanceDays > 0;
    }

    /**
     * 判断是否参与迟到早退统计
     * @return true表示参与迟到早退统计，false表示不参与
     */
    public boolean isParticipateInLateEarlyStats() {
        return participateInLateEarlyStats != null && participateInLateEarlyStats;
    }

    /**
     * 判断是否为910/920厂房
     * @return true表示是910/920厂房，false表示其他厂房
     */
    public boolean isFactory910Or920() {
        return "910厂房".equals(location) || "920厂房".equals(location) || "910/920厂房".equals(location);
    }
    
    /**
     * 累加迟到早退次数（用于汇总计算）
     * @param other 另一个结果对象
     */
    public void addCounts(LateEarlyResult other) {
        if (other != null) {
            this.morningLateCount += other.morningLateCount;
            this.morningEarlyCount += other.morningEarlyCount;
            this.afternoonLateCount += other.afternoonLateCount;
            this.afternoonEarlyCount += other.afternoonEarlyCount;
        }
    }
    
    @Override
    public String toString() {
        String employeeInfo = employeeId != null ?
            String.format("姓名='%s', 工号='%s'", employeeName, employeeId) :
            String.format("姓名='%s'", employeeName);

        if (isSummaryRecord()) {
            return String.format("LateEarlyResult{序号=%d, %s, 出勤天数=%d, 平均时长=%.2f, " +
                    "上午迟到=%d, 上午早退=%d, 下午迟到=%d, 下午早退=%d}",
                    sequence, employeeInfo, attendanceDays, averageHours,
                    morningLateCount, morningEarlyCount, afternoonLateCount, afternoonEarlyCount);
        } else {
            return String.format("LateEarlyResult{序号=%d, %s, 日期=%s, 净时长=%.2f, " +
                    "上午迟到=%d, 上午早退=%d, 下午迟到=%d, 下午早退=%d}",
                    sequence, employeeInfo, workDate, dailyHours,
                    morningLateCount, morningEarlyCount, afternoonLateCount, afternoonEarlyCount);
        }
    }
}
