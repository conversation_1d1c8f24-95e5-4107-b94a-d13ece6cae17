package com.cirpoint.controller;

import com.cirpoint.config.LogViewerConfig;
import com.cirpoint.model.LogEntry;
import com.cirpoint.model.LogFile;
import com.cirpoint.model.PageResult;
import com.cirpoint.service.LogFileService;
import com.cirpoint.service.LogReaderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日志查看器控制器
 * 提供日志查看相关的所有API端点
 */
@RestController
@RequestMapping("/api/logs")
public class LogViewerController {
    
    private static final Logger logger = LoggerFactory.getLogger(LogViewerController.class);
    
    @Autowired
    private LogReaderService logReaderService;
    
    @Autowired
    private LogFileService logFileService;
    
    @Autowired
    private LogViewerConfig config;
    
    /**
     * 日志查看器页面入口
     */
    @GetMapping("/viewer")
    public String logViewerPage() {
        if (!config.isEnabled()) {
            return "<!DOCTYPE html><html><body><h1>日志查看器已禁用</h1></body></html>";
        }
        
        // 重定向到静态页面
        return "redirect:/tools/log.html";
    }
    
    /**
     * SSE实时日志推送端点
     */
    @GetMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamLogs() {
        if (!config.isEnabled()) {
            logger.warn("Log viewer is disabled, rejecting SSE connection");
            return null;
        }
        
        SseEmitter emitter = new SseEmitter(config.getSseTimeoutMs());
        
        boolean added = logReaderService.addSseClient(emitter);
        if (!added) {
            logger.warn("Failed to add SSE client, maximum connections reached");
            emitter.completeWithError(new RuntimeException("Maximum connections reached"));
            return emitter;
        }
        
        logger.info("New SSE connection established");
        return emitter;
    }
    
    /**
     * 获取日志文件列表
     */
    @GetMapping("/files")
    public ResponseEntity<List<LogFile>> getLogFiles() {
        try {
            List<LogFile> logFiles = logFileService.getAllLogFiles();
            return ResponseEntity.ok(logFiles);
        } catch (Exception e) {
            logger.error("Error getting log files", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 分页获取历史日志
     */
    @GetMapping("/history")
    public ResponseEntity<PageResult<LogEntry>> getHistoryLogs(
            @RequestParam String fileName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "200") int size) {
        
        try {
            // 参数验证
            if (page < 0) {
                return ResponseEntity.badRequest().build();
            }
            
            PageResult<LogEntry> result = logFileService.readLogFileWithPagination(fileName, page, size);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("Error reading history logs for file: " + fileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 搜索日志内容
     */
    @GetMapping("/search")
    public ResponseEntity<PageResult<LogEntry>> searchLogs(
            @RequestParam String fileName,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String level,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "200") int size) {
        
        try {
            // 参数验证
            if (page < 0) {
                return ResponseEntity.badRequest().build();
            }
            
            PageResult<LogEntry> result = logFileService.searchInLogFile(fileName, keyword, level, page, size);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("Error searching logs in file: " + fileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 下载日志文件
     */
    @GetMapping("/download")
    public ResponseEntity<Resource> downloadLogFile(@RequestParam("file") String fileName) {
        try {
            Resource resource = logFileService.getLogFileResource(fileName);
            if (resource == null || !resource.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, "text/plain; charset=UTF-8");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("Error downloading log file: " + fileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取日志文件统计信息
     */
    @GetMapping("/stats/{fileName}")
    public ResponseEntity<Map<String, Object>> getLogFileStats(@PathVariable String fileName) {
        try {
            Map<String, Object> stats = logFileService.getLogFileStats(fileName);
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("Error getting stats for log file: " + fileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取可用的日志级别
     */
    @GetMapping("/levels")
    public ResponseEntity<List<String>> getAvailableLogLevels() {
        try {
            List<String> levels = logFileService.getAvailableLogLevels();
            return ResponseEntity.ok(levels);
        } catch (Exception e) {
            logger.error("Error getting log levels", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取系统状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getSystemStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("enabled", config.isEnabled());
            status.put("connectedClients", logReaderService.getConnectedClientCount());
            status.put("maxClients", config.getMaxSseClients());
            status.put("currentReadPosition", logReaderService.getCurrentReadPosition());
            status.put("currentFileSize", logReaderService.getCurrentFileSize());
            status.put("recentLogsCacheSize", logReaderService.getRecentLogsCacheSize());
            status.put("pushIntervalMs", config.getPushIntervalMs());
            
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            logger.error("Error getting system status", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 检查文件是否可访问
     */
    @GetMapping("/check/{fileName}")
    public ResponseEntity<Map<String, Object>> checkFileAccess(@PathVariable String fileName) {
        try {
            boolean accessible = logFileService.isFileAccessible(fileName);
            Map<String, Object> result = new HashMap<>();
            result.put("fileName", fileName);
            result.put("accessible", accessible);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("Error checking file access: " + fileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 手动清理无效的SSE连接
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupConnections() {
        try {
            int beforeCount = logReaderService.getConnectedClientCount();
            logReaderService.cleanupInvalidConnections();
            int afterCount = logReaderService.getConnectedClientCount();
            
            Map<String, Object> result = new HashMap<>();
            result.put("beforeCount", beforeCount);
            result.put("afterCount", afterCount);
            result.put("cleaned", beforeCount - afterCount);
            
            logger.info("Manual cleanup completed: {} connections cleaned", beforeCount - afterCount);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("Error during manual cleanup", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 诊断端点 - 检查日志查看器状态
     */
    @GetMapping("/diagnose")
    public ResponseEntity<Map<String, Object>> diagnoseSystem() {
        Map<String, Object> diagnosis = new HashMap<>();
        
        try {
            // 基本配置信息
            diagnosis.put("enabled", config.isEnabled());
            diagnosis.put("logPath", config.getCurrentLogFilePath());
            diagnosis.put("archivedPath", config.getArchivedLogPath());
            
            // 文件系统检查
            File logFile = new File(config.getCurrentLogFilePath());
            File logDir = logFile.getParentFile();
            File archiveDir = new File(config.getArchivedLogPath());
            
            Map<String, Object> fileSystem = new HashMap<>();
            fileSystem.put("logDirExists", logDir.exists());
            fileSystem.put("logFileExists", logFile.exists());
            fileSystem.put("archiveDirExists", archiveDir.exists());
            fileSystem.put("logFileSize", logFile.exists() ? logFile.length() : 0);
            fileSystem.put("logFileReadable", logFile.exists() ? logFile.canRead() : false);
            diagnosis.put("fileSystem", fileSystem);
            
            // 服务状态
            Map<String, Object> services = new HashMap<>();
            services.put("connectedClients", logReaderService.getConnectedClientCount());
            services.put("currentReadPosition", logReaderService.getCurrentReadPosition());
            services.put("currentFileSize", logReaderService.getCurrentFileSize());
            services.put("recentLogsCacheSize", logReaderService.getRecentLogsCacheSize());
            diagnosis.put("services", services);
            
            // 获取最近的几行日志进行测试
            try {
                List<LogFile> files = logFileService.getAllLogFiles();
                diagnosis.put("availableFiles", files.size());
                
                if (!files.isEmpty()) {
                    // 读取当前日志文件的最后几行
                    PageResult<LogEntry> recentLogs = logFileService.readLogFileWithPagination(
                        files.get(0).getFileName(), 0, 5);
                    diagnosis.put("sampleLogsCount", recentLogs.getContent().size());
                    diagnosis.put("sampleLogs", recentLogs.getContent());
                }
            } catch (Exception e) {
                diagnosis.put("logReadError", e.getMessage());
            }
            
            diagnosis.put("timestamp", System.currentTimeMillis());
            diagnosis.put("status", "OK");
            
        } catch (Exception e) {
            diagnosis.put("error", e.getMessage());
            diagnosis.put("status", "ERROR");
            logger.error("Diagnosis failed", e);
        }
        
        return ResponseEntity.ok(diagnosis);
    }
    
    /**
     * 全局异常处理
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Map<String, String>> handleIllegalArgument(IllegalArgumentException e) {
        Map<String, String> error = new HashMap<>();
        error.put("error", "Invalid parameter");
        error.put("message", e.getMessage());
        return ResponseEntity.badRequest().body(error);
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, String>> handleGenericException(Exception e) {
        Map<String, String> error = new HashMap<>();
        error.put("error", "Internal server error");
        error.put("message", "An unexpected error occurred");
        logger.error("Unexpected error in LogViewerController", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }
} 