/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* 容器布局 */
.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 100;
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.online {
    background-color: #4CAF50;
    box-shadow: 0 0 5px #4CAF50;
}

.status-indicator.offline {
    background-color: #f44336;
}

.status-indicator.connecting {
    background-color: #ff9800;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 侧边栏 */
.sidebar {
    width: 320px;
    background: white;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.panel {
    border-bottom: 1px solid #e0e0e0;
    padding: 1rem;
}

.panel h3 {
    margin-bottom: 1rem;
    color: #555;
    font-size: 1rem;
    font-weight: 600;
}

/* 文件列表 */
.file-list-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.file-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: #fafafa;
}

.file-item {
    padding: 0.5rem;
    cursor: pointer;
    border-bottom: 1px solid #e0e0e0;
    transition: background-color 0.2s;
}

.file-item:hover {
    background-color: #e3f2fd;
}

.file-item.active {
    background-color: #2196f3;
    color: white;
}

.file-item:last-child {
    border-bottom: none;
}

.file-name {
    font-weight: 500;
    display: block;
}

.file-info {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.2rem;
}

.file-item.active .file-info {
    color: rgba(255,255,255,0.8);
}

/* 搜索表单 */
.search-form {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* 系统状态 */
.status-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-item {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
}

.status-item span:first-child {
    color: #666;
}

.status-item span:last-child {
    font-weight: 500;
}

/* 内容区域 */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    overflow: hidden;
}

/* 标签页 */
.tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.tab-button {
    padding: 1rem 2rem;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
    transition: all 0.2s;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background-color: #e9ecef;
}

.tab-button.active {
    color: #2196f3;
    border-bottom-color: #2196f3;
    background-color: white;
}

.tab-content {
    flex: 1;
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.tab-content.active {
    display: flex;
}

/* 日志控制栏 */
.log-controls, .history-controls {
    padding: 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.file-selector, .pagination-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.log-count {
    margin-left: auto;
    font-size: 0.9rem;
    color: #666;
}

/* 日志容器 */
.log-container {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.log-content {
    height: 100%;
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
    background: #1e1e1e;
    color: #d4d4d4;
}

.log-placeholder {
    text-align: center;
    color: #666;
    font-style: italic;
    margin-top: 2rem;
}

/* 日志条目样式 */
.log-entry {
    margin-bottom: 0;
    padding: 0.1rem 0.3rem;
    border-radius: 3px;
    white-space: pre-wrap;
    word-break: break-all;
    transition: background-color 0.2s;
    position: relative;
    line-height: 1.3;
    padding-left: 0;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    border-left: 3px solid #000000
}

/* 行号显示 */
.log-entry[data-line-number]::before {
    content: attr(data-line-number);
    font-family: 'Courier New', monospace;
    font-size: 0.8em;
    cursor: pointer;
    height: 100%;
    box-sizing: border-box;
    align-items: flex-start;
    justify-content: flex-end;
    position: static;
    display: inline-block;
    width: 6em;
    min-width: 3em;
    text-align: right;
    padding: 0 0.5rem 0 0;
    margin-right: 0;
    background: transparent;
    border-right: 1px solid #e0e0e0;
    color: #999;
    line-height: inherit;
    user-select: none;
    flex-shrink: 0;
}

.log-entry[data-line-number]:hover::before {
    background: #e9ecef;
    color: #666;
}

.log-entry .log-content {
    display: block;
    width: 100%;
    line-height: 1.3;
}

.log-entry:hover {
    background-color: rgba(255,255,255,0.05);
}

.log-entry.new {
    animation: highlight 1s ease-out;
}

@keyframes highlight {
    0% { background-color: rgba(76, 175, 80, 0.3); }
    100% { background-color: transparent; }
}

/* 日志级别颜色 */
.log-entry.ERROR {
    color: #f44336;
    border-left: 3px solid #f44336;
}

.log-entry.WARN {
    color: #ff9800;
    border-left: 3px solid #ff9800;
}

.log-entry.INFO {
    color: #2196f3;
    border-left: 3px solid #2196f3;
}

.log-entry.DEBUG {
    color: #4caf50;
    border-left: 3px solid #4caf50;
}

.log-entry.TRACE {
    color: #9e9e9e;
    border-left: 3px solid #9e9e9e;
}

/* 分页控件 */
.pagination {
    padding: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.pagination-button {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s;
}

.pagination-button:hover:not(:disabled) {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-button.active {
    background-color: #2196f3;
    color: white;
    border-color: #2196f3;
}

.pagination-info {
    margin: 0 1rem;
    font-size: 0.9rem;
    color: #666;
}

/* 表单控件 */
.form-input, .form-select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.2s;
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: #2196f3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background-color: #2196f3;
    color: white;
}

.btn-primary:hover {
    background-color: #1976d2;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-small {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2196f3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #666;
}

/* 提示框 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 4px;
    color: white;
    display: flex;
    align-items: center;
    gap: 1rem;
    z-index: 1001;
    animation: slideIn 0.3s ease-out;
}

.toast.error {
    background-color: #f44336;
}

.toast.success {
    background-color: #4caf50;
}

.toast-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 1rem;
    color: #666;
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 280px;
    }
    
    .header {
        padding: 0.5rem 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .header h1 {
        font-size: 1.2rem;
    }
    
    .log-controls, .history-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
    
    .log-count {
        margin-left: 0;
    }
}

@media (max-width: 480px) {
    .sidebar {
        width: 100%;
        position: absolute;
        top: 0;
        left: -100%;
        height: 100%;
        z-index: 200;
        transition: left 0.3s ease;
    }
    
    .sidebar.open {
        left: 0;
    }
    
    .main-content {
        flex-direction: column;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 新日志指示器样式 */
.new-logs-indicator {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: #007bff;
    color: white;
    border-radius: 20px;
    padding: 8px 16px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: none;
    font-family: inherit;
    display: flex;
    align-items: center;
    gap: 4px;
}

.new-logs-indicator:hover {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.new-logs-indicator.hidden {
    opacity: 0;
    transform: translateY(10px);
    pointer-events: none;
}

.new-logs-indicator.show {
    opacity: 1;
    transform: translateY(0);
    animation: slideUp 0.3s ease-out;
}

.new-logs-count {
    font-weight: bold;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 2px 8px;
    margin-right: 4px;
    font-size: 0.8rem;
}

.new-logs-text {
    font-size: 0.85rem;
}

.new-logs-action {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-left: 4px;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .new-logs-indicator {
        bottom: 10px;
        right: 10px;
        padding: 6px 12px;
        font-size: 0.8rem;
    }
    
    .new-logs-count {
        padding: 1px 6px;
        font-size: 0.7rem;
    }
    
    .new-logs-text,
    .new-logs-action {
        font-size: 0.75rem;
    }
}