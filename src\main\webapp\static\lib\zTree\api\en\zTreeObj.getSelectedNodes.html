<div class="apiDetail">
<div>
	<h2><span>Function()</span><span class="path">zTreeObj.</span>getSelectedNodes</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Get the JSON data objects collection of the selected nodes in zTree.</p>
			<p class="highlight_red">Please use zTree object to executing the method.</p>
		</div>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>Return </b><span>Array(JSON)</span></h4>
	<p>The JSON data objects collection of the selected nodes.</p>
	</div>
	<h3>Examples of function</h3>
	<h4>1. get the selected nodes</h4>
	<pre xmlns=""><code>var treeObj = $.fn.zTree.getZTreeObj("tree");
var nodes = treeObj.getSelectedNodes();
</code></pre>
</div>
</div>