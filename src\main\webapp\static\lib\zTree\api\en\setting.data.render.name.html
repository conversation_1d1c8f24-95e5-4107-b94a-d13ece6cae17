<div class="apiDetail">
  <div>
    <h2>
      <span>Function(name, treeNode)</span
      ><span class="path">setting.data.render.</span>name
    </h2>
    <h3>
      Overview<span class="h3_info"
        >[ depends on <span class="highlight_green">jquery.ztree.core</span> js
        ]</span
      >
    </h3>
    <div class="desc">
      <p></p>
      <div class="longdesc">
        <p>Used to dynamically render node name using custom methods</p>
        <p>Default: null</p>
        <p class="highlight_red">v3.5.45+</p>
      </div>
    </div>
    <h3>Function Parameter Descriptions</h3>
    <div class="desc">
      <h4><b>name</b><span>String</span></h4>
      <p>Node name obtained by setting.key.name</p>
      <h4 class="topLine"><b>treeNode</b><span>JSON</span></h4>
      <p>JSON data object of the target node</p>
      <p class="highlight_red">When will render the root, treeNode = null</p>
      <h4 class="topLine"><b>Return</b><span>Bo<PERSON>an</span></h4>
      <p>Return the name of the node you need to display</p>
    </div>
    <h3>Examples of setting & function</h3>
    <pre xmlns=""><code>
	var setting = {
		data: {
			render: function(name,treeNode){
				return name + treeNode.id;
			}
		}
	};
	......</code></pre>
  </div>
</div>
