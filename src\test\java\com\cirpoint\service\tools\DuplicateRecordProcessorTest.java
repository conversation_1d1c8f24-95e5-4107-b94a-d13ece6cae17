package com.cirpoint.service.tools;

import com.cirpoint.constant.WorkTimeConstants;
import com.cirpoint.model.worktime.AttendanceRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 门禁数据去重处理器测试类
 */
public class DuplicateRecordProcessorTest {

    private DuplicateRecordProcessor processor;
    private DateTimeFormatter formatter;

    @BeforeEach
    public void setUp() {
        processor = new DuplicateRecordProcessor();
        formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    }

    @Test
    @DisplayName("测试空记录列表")
    public void testEmptyRecordList() {
        List<AttendanceRecord> records = new ArrayList<>();
        List<AttendanceRecord> result = processor.removeDuplicateRecords(records);
        
        assertTrue(result.isEmpty(), "空记录列表应该返回空结果");
    }

    @Test
    @DisplayName("测试单条记录")
    public void testSingleRecord() {
        List<AttendanceRecord> records = new ArrayList<>();
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:00:00"));
        
        List<AttendanceRecord> result = processor.removeDuplicateRecords(records);
        
        assertEquals(1, result.size(), "单条记录应该保持不变");
        assertEquals("张三", result.get(0).getEmployeeName());
    }

    @Test
    @DisplayName("测试2分钟内重复刷卡去重")
    public void testDuplicateWithinTimeWindow() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 张三在910厂房2分钟内多次刷入卡
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:10"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:30"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:11:00"));
        
        List<AttendanceRecord> result = processor.removeDuplicateRecords(records);
        
        assertEquals(1, result.size(), "2分钟内的重复记录应该只保留1条");
        assertEquals("2025-06-26T08:10:10", result.get(0).getEventTime().toString(), "应该保留最早的记录");
    }

    @Test
    @DisplayName("测试超过2分钟的记录不去重")
    public void testRecordsBeyondTimeWindow() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 张三在910厂房超过2分钟的刷卡记录
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:00:00"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:03:00")); // 3分钟后
        
        List<AttendanceRecord> result = processor.removeDuplicateRecords(records);
        
        assertEquals(2, result.size(), "超过2分钟的记录应该都保留");
    }

    @Test
    @DisplayName("测试不同员工的记录不互相影响")
    public void testDifferentEmployees() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 张三和李四同时在910厂房刷卡
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:10"));
        records.add(createRecord("李四", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:20"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:30"));
        
        List<AttendanceRecord> result = processor.removeDuplicateRecords(records);
        
        assertEquals(2, result.size(), "不同员工的记录应该分别处理");
        
        // 验证结果包含两个不同的员工
        boolean hasZhangSan = result.stream().anyMatch(r -> "张三".equals(r.getEmployeeName()));
        boolean hasLiSi = result.stream().anyMatch(r -> "李四".equals(r.getEmployeeName()));
        assertTrue(hasZhangSan && hasLiSi, "结果应该包含两个不同员工的记录");
    }

    @Test
    @DisplayName("测试不同场所的记录不互相影响")
    public void testDifferentLocations() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 张三在不同场所刷卡
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:10"));
        records.add(createRecord("张三", "920厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:20"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:30"));
        
        List<AttendanceRecord> result = processor.removeDuplicateRecords(records);
        
        assertEquals(2, result.size(), "不同场所的记录应该分别处理");
        
        // 验证结果包含两个不同的场所
        boolean has910 = result.stream().anyMatch(r -> "910厂房".equals(r.getLocation()));
        boolean has920 = result.stream().anyMatch(r -> "920厂房".equals(r.getLocation()));
        assertTrue(has910 && has920, "结果应该包含两个不同场所的记录");
    }

    @Test
    @DisplayName("测试不同方向的记录不互相影响")
    public void testDifferentDirections() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 张三在910厂房进出刷卡
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:10"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-26 08:10:20"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:10:30"));
        
        List<AttendanceRecord> result = processor.removeDuplicateRecords(records);
        
        assertEquals(2, result.size(), "不同方向的记录应该分别处理");
        
        // 验证结果包含进和出两种方向
        boolean hasIn = result.stream().anyMatch(r -> WorkTimeConstants.DIRECTION_IN.equals(r.getDirection()));
        boolean hasOut = result.stream().anyMatch(r -> WorkTimeConstants.DIRECTION_OUT.equals(r.getDirection()));
        assertTrue(hasIn && hasOut, "结果应该包含进出两种方向的记录");
    }

    @Test
    @DisplayName("测试复杂场景的去重处理")
    public void testComplexDeduplicationScenario() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 复杂场景：多个员工、多个场所、多个时间点
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:00:00"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:00:30")); // 重复，应删除
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-26 12:00:00"));
        records.add(createRecord("李四", "920厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:30:00"));
        records.add(createRecord("李四", "920厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:30:45")); // 重复，应删除
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 13:00:00")); // 不重复
        
        List<AttendanceRecord> result = processor.removeDuplicateRecords(records);
        
        assertEquals(4, result.size(), "复杂场景应该正确去重");
        
        // 验证去重统计信息
        String stats = processor.getDeduplicationStats(records, result);
        assertTrue(stats.contains("原始记录: 6"), "统计信息应该正确");
        assertTrue(stats.contains("去重后记录: 4"), "统计信息应该正确");
        assertTrue(stats.contains("移除重复记录: 2"), "统计信息应该正确");
    }

    @Test
    @DisplayName("测试记录时间排序")
    public void testRecordTimeOrdering() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 添加乱序的记录
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 10:00:00"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-26 08:00:00"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 09:00:00"));
        
        List<AttendanceRecord> result = processor.removeDuplicateRecords(records);
        
        assertEquals(3, result.size(), "所有记录都应该保留");
        
        // 验证结果按时间排序
        for (int i = 1; i < result.size(); i++) {
            assertTrue(result.get(i-1).getEventTime().isBefore(result.get(i).getEventTime()) ||
                      result.get(i-1).getEventTime().equals(result.get(i).getEventTime()),
                      "结果应该按时间升序排列");
        }
    }

    /**
     * 创建测试用的打卡记录
     */
    private AttendanceRecord createRecord(String employeeName, String location, String direction, String eventTimeStr) {
        AttendanceRecord record = new AttendanceRecord();
        record.setEmployeeName(employeeName);
        record.setLocation(location);
        record.setDirection(direction);
        record.setEventTime(LocalDateTime.parse(eventTimeStr, formatter));
        
        // 根据location设置accessPoint
        if ("910厂房".equals(location)) {
            record.setAccessPoint("910" + direction);
        } else if ("920厂房".equals(location)) {
            record.setAccessPoint("920" + direction);
        } else if ("二号楼".equals(location)) {
            record.setAccessPoint("二号楼" + direction);
        }
        
        return record;
    }
}
