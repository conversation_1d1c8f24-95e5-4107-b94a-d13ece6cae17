// tab-manager.js - 标签页管理相关功能

// 标签页管理对象
window.createTabManager = function(editor) {
    return {
        tabs: [],
        activeTab: null,
        add: function (node) {
            // 注意：重复检查已经移到onClick和搜索结果点击事件中处理
            // 这里直接添加新标签页
            const newTab = {
                path: node.path,
                name: node.name,
                model: null,
                viewState: null,
                modified: false
            };

            this.tabs.push(newTab);
            this.renderTabs();
            this.loadTabContent(newTab);  // 只在这里加载一次
            this.activeTab = newTab;      // 直接设置活动标签页
            this.syncTreeSelection();      // 同步树选择状态
        },
        // 新增方法：关闭与指定路径相关的标签页
        closeRelatedTabs: function(nodePath, isFolder) {
            if (isFolder) {
                // 如果是文件夹，关闭所有以此路径开头的标签页
                var pathWithSlash = nodePath.endsWith('/') ? nodePath : nodePath + '/';
                var tabsToClose = [];

                // 收集需要关闭的标签页
                this.tabs.forEach(function(tab) {
                    if (tab.path === nodePath || tab.path.startsWith(pathWithSlash)) {
                        // 销毁viewer实例
                        if (tab.isImage && tab.viewer) {
                            tab.viewer.destroy();
                            tab.viewer = null;
                        }
                        tabsToClose.push(tab.path);
                    }
                });

                // 关闭收集到的标签页
                tabsToClose.forEach(tabPath => this.close(tabPath));
            } else {
                // 如果是文件节点，关闭相应的标签页
                const tab = this.tabs.find(t => t.path === nodePath);
                if (tab && tab.isImage && tab.viewer) {
                    tab.viewer.destroy();
                    tab.viewer = null;
                }
                this.close(nodePath);
            }
        },
        setActive: function (path, reload = false) {
            const previousTab = this.activeTab;

            // 保存当前页签视图状态，确保滚动位置的保存
            if (previousTab && previousTab.model && window.editor) {
                previousTab.viewState = window.editor.saveViewState();
            }

            this.activeTab = this.tabs.find(t => t.path === path);

            // 如果找不到对应标签页，直接返回
            if (!this.activeTab) {
                console.error('找不到标签页:', path);
                return;
            }

            // 渲染标签页
            this.renderTabs();

            // 确保标签页可见（滚动到可见区域）
            setTimeout(() => {
                const $activeTab = $(`.tab-item[data-path="${path}"]`);
                if ($activeTab.length) {
                    // 获取标签页容器
                    const $container = $('.tabs-container');
                    // 计算滚动位置
                    const scrollLeft = $activeTab.position().left + $container.scrollLeft() - $container.width()/2 + $activeTab.width()/2;
                    // 平滑滚动到标签页
                    $container.animate({scrollLeft: Math.max(0, scrollLeft)}, 200);

                    // 确保标签页高亮
                    $('.tab-item').removeClass('active');
                    $activeTab.addClass('active');
                }
            }, 50);

            // 同步树选择状态
            this.syncTreeSelection();

            if (this.activeTab) {
                // 处理图片标签页
                if (this.activeTab.isImage) {
                    // 图片标签页始终重新加载内容，确保显示正确的图片
                    this.loadTabContent(this.activeTab);
                    return;
                }

                // 处理文本标签页
                // 如果之前显示的是图片，需要隐藏图片预览，显示编辑器
                $('#imagePreview').hide();
                $('#editor').show();

                if (this.activeTab.model) {
                    window.editor.setModel(this.activeTab.model);
                    if (this.activeTab.viewState) {
                        window.editor.restoreViewState(this.activeTab.viewState);
                    }
                    window.editor.focus();
                } else if (reload) {
                    this.loadTabContent(this.activeTab);
                }
            }
        },
        close: function (path) {
            const index = this.tabs.findIndex(t => t.path === path);
            if (index > -1) {
                const closingTab = this.tabs[index];

                // 如果关闭的是图片标签页，销毁viewer实例并移除图片预览
                if (closingTab.isImage && path === this.activeTab?.path) {
                    if (closingTab.viewer) {
                        try {
                            closingTab.viewer.destroy();
                        } catch (error) {
                            console.error('销毁Viewer实例时出错:', error);
                        }
                        closingTab.viewer = null;
                    }
                    $('#imagePreview').remove();
                }

                // 如果不是图片标签页，保存编辑器状态
                if (!closingTab.isImage && closingTab.model) {
                    closingTab.viewState = window.editor.saveViewState();
                }

                this.tabs.splice(index, 1);
                this.renderTabs();

                if (path === this.activeTab?.path) {
                    this.activeTab = this.tabs.length > 0 ? this.tabs[0] : null;
                    if (this.activeTab) {
                        this.loadTabContent(this.activeTab);
                    } else {
                        // 没有标签页了，显示编辑器并清空内容
                        $('#imagePreview').remove();
                        $('#editor').show();
                        window.editor.setValue('');
                    }
                }
            }
        },
        // 关闭其他标签页
        closeOthers: function (path) {
            // 保存当前标签页
            const currentTab = this.tabs.find(t => t.path === path);
            if (!currentTab) return;

            // 销毁其他标签页的viewer实例
            this.tabs.forEach(tab => {
                if (tab.path !== path && tab.isImage && tab.viewer) {
                    try {
                        tab.viewer.destroy();
                    } catch (error) {
                        console.error('销毁Viewer实例时出错:', error);
                    }
                    tab.viewer = null;
                }
            });

            // 关闭其他标签页
            this.tabs = this.tabs.filter(t => t.path === path);
            this.activeTab = currentTab;
            this.renderTabs();

            // 确保显示正确的内容
            if (currentTab.isImage) {
                // 如果是图片标签页，确保显示图片预览
                if (!$('#imagePreview').length) {
                    this.loadTabContent(currentTab);
                } else {
                    $('#editor').hide();
                    $('#imagePreview').show();
                }
            } else {
                // 如果是文本标签页，确保显示编辑器
                $('#imagePreview').remove();
                $('#editor').show();

                if (currentTab.model) {
                    window.editor.setModel(currentTab.model);
                    if (currentTab.viewState) {
                        window.editor.restoreViewState(currentTab.viewState);
                    }
                    window.editor.focus();
                }
            }
        },
        // 关闭右侧标签页
        closeRight: function (path) {
            const index = this.tabs.findIndex(t => t.path === path);
            if (index === -1) return;

            // 保存当前标签页的状态
            if (this.activeTab) {
                const activeIndex = this.tabs.findIndex(t => t.path === this.activeTab.path);
                if (activeIndex > index) {
                    // 如果活动标签页在右侧，则将当前标签页设为活动
                    this.activeTab = this.tabs[index];
                }
            }

            // 销毁右侧标签页的viewer实例
            for (let i = index + 1; i < this.tabs.length; i++) {
                const tab = this.tabs[i];
                if (tab.isImage && tab.viewer) {
                    try {
                        tab.viewer.destroy();
                    } catch (error) {
                        console.error('销毁Viewer实例时出错:', error);
                    }
                    tab.viewer = null;
                }
            }

            // 移除右侧标签页
            this.tabs = this.tabs.filter((t, i) => i <= index);
            this.renderTabs();

            // 确保显示正确的内容
            if (this.activeTab) {
                if (this.activeTab.isImage) {
                    // 如果是图片标签页，确保显示图片预览
                    if (!$('#imagePreview').length) {
                        this.loadTabContent(this.activeTab);
                    } else {
                        $('#editor').hide();
                        $('#imagePreview').show();
                    }
                } else {
                    // 如果是文本标签页，确保显示编辑器
                    $('#imagePreview').remove();
                    $('#editor').show();

                    if (this.activeTab.model) {
                        window.editor.setModel(this.activeTab.model);
                        if (this.activeTab.viewState) {
                            window.editor.restoreViewState(this.activeTab.viewState);
                        }
                        window.editor.focus();
                    }
                }
            }
        },
        // 关闭左侧标签页
        closeLeft: function (path) {
            const index = this.tabs.findIndex(t => t.path === path);
            if (index === -1) return;

            // 保存当前标签页的状态
            if (this.activeTab) {
                const activeIndex = this.tabs.findIndex(t => t.path === this.activeTab.path);
                if (activeIndex < index) {
                    // 如果活动标签页在左侧，则将当前标签页设为活动
                    this.activeTab = this.tabs[index];
                }
            }

            // 销毁左侧标签页的viewer实例
            for (let i = 0; i < index; i++) {
                const tab = this.tabs[i];
                if (tab.isImage && tab.viewer) {
                    try {
                        tab.viewer.destroy();
                    } catch (error) {
                        console.error('销毁Viewer实例时出错:', error);
                    }
                    tab.viewer = null;
                }
            }

            // 移除左侧标签页
            this.tabs = this.tabs.filter((t, i) => i >= index);
            this.renderTabs();

            // 确保显示正确的内容
            if (this.activeTab) {
                if (this.activeTab.isImage) {
                    // 如果是图片标签页，确保显示图片预览
                    if (!$('#imagePreview').length) {
                        this.loadTabContent(this.activeTab);
                    } else {
                        $('#editor').hide();
                        $('#imagePreview').show();
                    }
                } else {
                    // 如果是文本标签页，确保显示编辑器
                    $('#imagePreview').remove();
                    $('#editor').show();

                    if (this.activeTab.model) {
                        window.editor.setModel(this.activeTab.model);
                        if (this.activeTab.viewState) {
                            window.editor.restoreViewState(this.activeTab.viewState);
                        }
                        window.editor.focus();
                    }
                }
            }
        },
        // 关闭所有标签页
        closeAll: function () {
            // 关闭前销毁所有viewer实例
            this.tabs.forEach(tab => {
                if (tab.isImage && tab.viewer) {
                    try {
                        tab.viewer.destroy();
                    } catch (error) {
                        console.error('销毁Viewer实例时出错:', error);
                    }
                    tab.viewer = null;
                }
            });

            this.tabs = [];
            this.activeTab = null;
            this.renderTabs();

            // 移除图片预览，显示编辑器
            $('#imagePreview').remove();
            $('#editor').show();
            window.editor.setValue('');
        },
        renderTabs: function () {
            const $tabList = $('#tabList').empty();
            this.tabs.forEach(tab => {
                const isActive = tab.path === this.activeTab?.path;
                const isImage = tab.isImage === true;
                const $tab = $(`
                    <li class="tab-item ${isActive ? 'active' : ''}" data-path="${tab.path}" data-is-image="${isImage}">
                        <span class="tab-title">${tab.name}</span>
                        <span class="tab-close">&times;</span>
                    </li>
                `);
                $tabList.append($tab);
            });

            // 初始化拖拽排序功能
            this.initTabSortable();
        },

        // 初始化标签页拖拽排序功能
        initTabSortable: function() {
            $('#tabList').sortable({
                axis: 'x',                // 只允许水平方向拖动
                containment: 'parent',    // 限制在父容器内拖动
                tolerance: 'pointer',     // 使用鼠标指针确定放置位置
                cursor: 'move',           // 拖动时的鼠标样式
                opacity: 0.7,             // 拖动时的透明度
                stop: (event, ui) => {
                    // 拖拽结束后重新排序tabs数组
                    const newTabs = [];
                    $('#tabList li').each((index, element) => {
                        const path = $(element).data('path');
                        const tab = this.tabs.find(t => t.path === path);
                        if (tab) {
                            newTabs.push(tab);
                        }
                    });

                    // 更新tabs数组
                    this.tabs = newTabs;

                    // 确保激活的标签页状态正确
                    if (this.activeTab) {
                        const activeTab = this.tabs.find(t => t.path === this.activeTab.path);
                        if (activeTab) {
                            this.activeTab = activeTab;
                        }
                    }
                }
            }).disableSelection(); // 防止文本被选中
        },
        loadTabContent: function (tab) {
            $.ajax({
                url: '/editor/content',
                method: 'GET',
                data: { path: tab.path },
                success: (res) => {
                    if (!res.success) {
                        // 创建只读的提示信息模型
                        if (!tab.model) {
                            tab.model = monaco.editor.createModel(res.msg, 'plaintext');
                        }
                        // 标记这个标签页是提示信息
                        tab.isMessage = true;
                        window.editor.setModel(tab.model);
                        window.editor.updateOptions({ readOnly: true });
                        return;
                    }

                    // 处理图片文件
                    if (res.isImage) {
                        // 标记这个标签页是图片
                        tab.isImage = true;

                        // 清理旧的viewer实例
                        if (tab.viewer) {
                            try {
                                tab.viewer.destroy();
                            } catch (error) {
                                console.error('销毁旧的Viewer实例时出错:', error);
                            }
                            tab.viewer = null;
                        }

                        // 如果编辑器容器中已有图片预览，先移除
                        $('#imagePreview').remove();

                        // 创建图片预览元素
                        const timestamp = new Date().getTime();
                        const imageUrl = `/editor/image?path=${encodeURIComponent(tab.path)}&t=${timestamp}`;
                        const $imagePreview = $(`
                            <div id="imagePreview" class="image-preview">
                                <div class="viewer-container">
                                    <img src="${imageUrl}" alt="${tab.name}" />
                                </div>
                            </div>
                        `);

                        // 隐藏编辑器，显示图片预览
                        $('#editor').hide();
                        $('#editor').after($imagePreview);

                        // 初始化viewerjs
                        const imageElement = $imagePreview.find('img')[0];
                        try {
                            if (typeof Viewer !== 'undefined') {
                                tab.viewer = new Viewer(imageElement, {
                                    inline: true,              // 嵌入式显示
                                    navbar: false,             // 不显示底部缩略图导航
                                    title: false,              // 不显示标题
                                    toolbar: {                 // 自定义工具栏
                                        zoomIn: 1,
                                        zoomOut: 1,
                                        oneToOne: 1,
                                        reset: 1,
                                        prev: 0,
                                        play: 0,
                                        next: 0,
                                        rotateLeft: 1,
                                        rotateRight: 1,
                                        flipHorizontal: 1,
                                        flipVertical: 1,
                                    },
                                    viewed() {
                                        // 图片加载完成后的回调
                                        console.log('图片使用viewerjs加载完成');
                                    }
                                });
                                console.log('Viewer.js实例创建成功');
                            } else {
                                console.error('Viewer对象未定义，可能是viewerjs库未正确加载');
                            }
                        } catch (error) {
                            console.error('初始化Viewer.js时出错:', error);
                        }

                        // 同步树选择状态
                        this.syncTreeSelection();
                        return;
                    }

                    // 处理文本文件
                    tab.isImage = false;

                    // 如果之前显示的是图片，需要隐藏图片预览，显示编辑器
                    $('#imagePreview').remove();
                    $('#editor').show();

                    const ext = tab.path.split('.').pop();
                    const language = window.treeManager.getLanguageByExt(ext);

                    if (!tab.model) {
                        tab.model = monaco.editor.createModel(res.data, language);
                    }
                    // 清除提示信息标记
                    tab.isMessage = false;

                    window.editor.setModel(tab.model);
                    monaco.editor.setModelLanguage(tab.model, language);

                    // 根据是否是提示信息设置只读状态
                    window.editor.updateOptions({ readOnly: tab.isMessage });

                    if (tab.viewState) {
                        window.editor.restoreViewState(tab.viewState);
                    }
                    window.editor.focus();
                    this.syncTreeSelection();
                }
            });
        },
        syncTreeSelection: function () {
            if (!this.activeTab) return;

            // 查找对应的树节点
            const node = window.zTreeObj.getNodeByParam("path", this.activeTab.path);
            if (node) {
                // 先展开父节点
                const parentNode = node.getParentNode();
                if (parentNode) {
                    window.zTreeObj.expandNode(parentNode, true, false, true);
                }

                // 选中节点并滚动到可见区域
                window.zTreeObj.selectNode(node);

                // 确保节点在可见区域
                const $node = $(`#${node.tId}_a`);
                if ($node.length) {
                    const $container = $('.file-explorer-content');
                    const nodeTop = $node.offset().top;
                    const containerTop = $container.offset().top;
                    const containerHeight = $container.height();

                    // 如果节点不在可见区域，滚动到节点
                    if (nodeTop < containerTop || nodeTop > containerTop + containerHeight) {
                        $container.animate({
                            scrollTop: $container.scrollTop() + nodeTop - containerTop - containerHeight/2
                        }, 200);
                    }
                }
            }
        },
        saveCurrentTab: function() {
            if (!this.activeTab || this.activeTab.isMessage) return;

            // 保存前先获取当前视图状态
            if (window.editor) {
                this.activeTab.viewState = window.editor.saveViewState();
            }

            window.editorManager.saveFile();
        },
        // 查找标签页索引
        findTabIndex: function(tabPath) {
            return this.tabs.findIndex(t => t.path === tabPath);
        },

        // 查找标签页
        findTab: function(tabPath) {
            return this.tabs.find(t => t.path === tabPath);
        },

        closeTab: function(tabPath) {
            const tabIndex = this.findTabIndex(tabPath);
            if (tabIndex === -1) return;

            const tab = this.tabs[tabIndex];

            // 如果关闭的是当前活动标签页，需要保存视图状态
            if (this.activeTab && this.activeTab.path === tabPath && window.editor) {
                tab.viewState = window.editor.saveViewState();
            }
        },
        activateTab: function(tabPath) {
            const tab = this.findTab(tabPath);
            if (!tab) return;

            // 保存当前标签页的视图状态
            if (this.activeTab && window.editor) {
                this.activeTab.viewState = window.editor.saveViewState();
            }
        },

        // 关闭与指定路径相关的所有标签页
        closePathRelatedTabs: function(basePath) {
            // 确保路径以斜杠结尾，便于前缀匹配
            const pathPrefix = basePath.endsWith('/') ? basePath : basePath + '/';

            // 收集需要关闭的标签页
            const tabsToClose = [];

            this.tabs.forEach(tab => {
                // 关闭完全匹配的标签页或以该路径为前缀的标签页
                if (tab.path === basePath || tab.path.startsWith(pathPrefix)) {
                    // 如果是图片标签页，销毁viewer实例
                    if (tab.isImage && tab.viewer) {
                        try {
                            tab.viewer.destroy();
                        } catch (error) {
                            console.error('销毁Viewer实例时出错:', error);
                        }
                        tab.viewer = null;
                    }
                    tabsToClose.push(tab.path);
                }
            });

            // 关闭收集到的标签页
            tabsToClose.forEach(path => this.close(path));

            // 如果没有标签页了，清空编辑器
            if (this.tabs.length === 0) {
                $('#imagePreview').remove();
                $('#editor').show();
                window.editor.setValue('');
            }
        }
    };
};

// 显示标签页右键菜单
function showTabRightMenu(e, tabPath) {
    // 创建或获取菜单元素
    let tabRightMenu = $("#tabRightMenu");
    if (tabRightMenu.length === 0) {
        // 如果菜单不存在，创建它
        $("body").append(`
            <div id="tabRightMenu" class="right-menu layui-hide">
                <ul>
                    <li id="tab_close"><i class="layui-icon layui-icon-close"></i> 关闭</li>
                    <li id="tab_close_others"><i class="layui-icon layui-icon-close"></i> 关闭其他</li>
                    <li id="tab_close_right"><i class="layui-icon layui-icon-right"></i> 关闭右侧</li>
                    <li id="tab_close_left"><i class="layui-icon layui-icon-left"></i> 关闭左侧</li>
                    <li id="tab_close_all"><i class="layui-icon layui-icon-close-fill"></i> 全部关闭</li>
                </ul>
            </div>
        `);
        tabRightMenu = $("#tabRightMenu");
        bindTabRightMenuEvents();
    }

    const x = e.pageX || e.clientX;
    const y = e.pageY || e.clientY;

    // 设置当前右键点击的标签页路径
    tabRightMenu.data("path", tabPath);

    // 显示右键菜单
    tabRightMenu.removeClass("layui-hide")
        .css({
            "position": "fixed",
            "top": y + "px",
            "left": x + "px",
            "display": "block",
            "z-index": "9999"
        });

    // 阻止默认右键菜单
    e.preventDefault();
    e.stopPropagation();

    // 调试日志
    console.log("显示标签页右键菜单", tabPath, x, y);
}

// 隐藏标签页右键菜单
function hideTabRightMenu() {
    $("#tabRightMenu").addClass("layui-hide").css("display", "none");
}

// 绑定标签页右键菜单事件
function bindTabRightMenuEvents() {
    // 标签页右键点击事件
    $(document).off("contextmenu", "#tabList .tab-item").on("contextmenu", "#tabList .tab-item", function(e) {
        const path = $(this).data("path");
        showTabRightMenu(e, path);
        return false; // 阻止默认右键菜单
    });

    // 关闭当前标签页
    $(document).off("click", "#tab_close").on("click", "#tab_close", function() {
        const path = $("#tabRightMenu").data("path");
        if (path) {
            window.tabManager.close(path);
        }
        hideTabRightMenu();
    });

    // 关闭其他标签页
    $(document).off("click", "#tab_close_others").on("click", "#tab_close_others", function() {
        const path = $("#tabRightMenu").data("path");
        if (path) {
            window.tabManager.closeOthers(path);
        }
        hideTabRightMenu();
    });

    // 关闭右侧标签页
    $(document).off("click", "#tab_close_right").on("click", "#tab_close_right", function() {
        const path = $("#tabRightMenu").data("path");
        if (path) {
            window.tabManager.closeRight(path);
        }
        hideTabRightMenu();
    });

    // 关闭左侧标签页
    $(document).off("click", "#tab_close_left").on("click", "#tab_close_left", function() {
        const path = $("#tabRightMenu").data("path");
        if (path) {
            window.tabManager.closeLeft(path);
        }
        hideTabRightMenu();
    });

    // 关闭所有标签页
    $(document).off("click", "#tab_close_all").on("click", "#tab_close_all", function() {
        window.tabManager.closeAll();
        hideTabRightMenu();
    });

    // 点击其他区域隐藏菜单
    $(document).off("click.tabRightMenu").on("click.tabRightMenu", function(e) {
        if (!$(e.target).closest("#tabRightMenu").length) {
            hideTabRightMenu();
        }
    });

    // 调试日志
    console.log("标签页右键菜单事件绑定完成");
}

// 导出函数
window.tabManagerModule = {
    createTabManager: createTabManager,
    showTabRightMenu: showTabRightMenu,
    hideTabRightMenu: hideTabRightMenu,
    bindTabRightMenuEvents: bindTabRightMenuEvents
};