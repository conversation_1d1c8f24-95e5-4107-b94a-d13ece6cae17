package com.cirpoint.monitor;

import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import java.io.File;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.GlobalMemory;
import oshi.hardware.HardwareAbstractionLayer;

import javax.annotation.PostConstruct;

/**
 * 系统资源监控
 * 监控CPU、内存、磁盘等系统资源的使用情况
 */
@Slf4j
@Component
public class SystemMonitor {

    @Autowired
    private MeterRegistry registry;

    private final SystemInfo systemInfo = new SystemInfo();
    private final HardwareAbstractionLayer hardware = systemInfo.getHardware();
    private final MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
    private long[] prevTicks;

    @PostConstruct
    public void init() {
        // 初始化CPU使用率计算
        CentralProcessor processor = hardware.getProcessor();
        prevTicks = processor.getSystemCpuLoadTicks();

        // 注册系统指标
        registerSystemMetrics();
        
        log.info("系统监控初始化完成");
    }

    /**
     * 注册系统监控指标
     */
    private void registerSystemMetrics() {
        // CPU使用率
        Gauge.builder("system.cpu.usage", this::getCpuUsage)
                .description("CPU使用率")
                .register(registry);

        // 系统内存使用率
        Gauge.builder("system.memory.usage", this::getSystemMemoryUsage)
                .description("系统内存使用率")
                .register(registry);

        // JVM堆内存使用率
        Gauge.builder("jvm.heap.usage", this::getHeapMemoryUsage)
                .description("JVM堆内存使用率")
                .register(registry);

        // 系统磁盘使用率
        Gauge.builder("system.disk.usage", this::getDiskUsage)
                .description("系统磁盘使用率")
                .register(registry);
    }

    /**
     * 获取CPU使用率
     */
    public double getCpuUsage() {
        CentralProcessor processor = hardware.getProcessor();
        long[] newTicks = processor.getSystemCpuLoadTicks();
        double cpuUsage = processor.getSystemCpuLoadBetweenTicks(prevTicks);
        prevTicks = newTicks;
        return cpuUsage * 100.0;
    }

    /**
     * 获取系统内存使用率
     */
    public double getSystemMemoryUsage() {
        GlobalMemory memory = hardware.getMemory();
        long total = memory.getTotal();
        long available = memory.getAvailable();
        return ((double) (total - available) / total) * 100.0;
    }

    /**
     * 获取JVM堆内存使用率
     */
    public double getHeapMemoryUsage() {
        MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
        return ((double) heapMemoryUsage.getUsed() / heapMemoryUsage.getMax()) * 100.0;
    }

    /**
     * 获取磁盘使用率
     */
    public double getDiskUsage() {
        File file = new File("/");
        long total = file.getTotalSpace();
        long free = file.getFreeSpace();
        return ((double) (total - free) / total) * 100.0;
    }

    /**
     * 定时记录系统资源使用情况
     */
    @Scheduled(fixedRate = 600000) // 每分钟执行一次
    public void logSystemMetrics() {
        log.info("系统资源使用情况：");
        log.info("CPU使用率: {}%", String.format("%.2f", getCpuUsage()));
        log.info("系统内存使用率: {}%", String.format("%.2f", getSystemMemoryUsage()));
        log.info("JVM堆内存使用率: {}%", String.format("%.2f", getHeapMemoryUsage()));
        log.info("磁盘使用率: {}%", String.format("%.2f", getDiskUsage()));
    }
}
