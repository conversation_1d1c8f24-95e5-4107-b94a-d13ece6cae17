package com.cirpoint.service.tools;

import com.cirpoint.constant.WorkTimeConstants;
import com.cirpoint.model.worktime.AttendanceRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工时系统集成测试
 * 验证去重处理器与跨日验证器的集成效果
 */
public class WorkTimeIntegrationTest {

    private DuplicateRecordProcessor duplicateProcessor;
    private CrossDayValidator crossDayValidator;
    private DateTimeFormatter formatter;

    @BeforeEach
    public void setUp() {
        duplicateProcessor = new DuplicateRecordProcessor();
        crossDayValidator = new CrossDayValidator();
        formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    }

    @Test
    @DisplayName("测试去重处理与跨日验证的集成流程")
    public void testIntegratedDeduplicationAndCrossDayValidation() {
        List<AttendanceRecord> originalRecords = new ArrayList<>();
        
        // 构造测试数据：包含重复记录和跨日场景
        // 张三：正常跨日工作，但有重复刷卡
        originalRecords.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-25 22:00:00"));
        originalRecords.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-25 22:00:30")); // 重复，应被去除
        originalRecords.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-26 06:00:00"));
        
        // 李四：有悬挂的出场记录，且有重复刷卡
        originalRecords.add(createRecord("李四", "920厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-26 08:00:00")); // 悬挂出场
        originalRecords.add(createRecord("李四", "920厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-26 08:00:45")); // 重复，应被去除
        originalRecords.add(createRecord("李四", "920厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 09:00:00"));
        originalRecords.add(createRecord("李四", "920厂房", WorkTimeConstants.DIRECTION_OUT, "2025-06-26 17:00:00"));
        
        // 第一步：去重处理
        List<AttendanceRecord> deduplicatedRecords = duplicateProcessor.removeDuplicateRecords(originalRecords);
        
        // 验证去重效果
        assertEquals(5, deduplicatedRecords.size(), "应该去除2条重复记录");
        
        // 验证张三的重复记录被正确去除
        long zhangSanInCount = deduplicatedRecords.stream()
                .filter(r -> "张三".equals(r.getEmployeeName()) && WorkTimeConstants.DIRECTION_IN.equals(r.getDirection()))
                .count();
        assertEquals(1, zhangSanInCount, "张三的重复入场记录应该被去除");
        
        // 验证李四的重复记录被正确去除
        long liSiOutCount = deduplicatedRecords.stream()
                .filter(r -> "李四".equals(r.getEmployeeName()) && WorkTimeConstants.DIRECTION_OUT.equals(r.getDirection()))
                .count();
        assertEquals(2, liSiOutCount, "李四应该有2条出场记录（1条悬挂+1条正常）");
        
        // 第二步：跨日验证
        Set<AttendanceRecord> invalidRecords = crossDayValidator.validateCrossDayRecords(deduplicatedRecords);
        
        // 验证跨日验证效果
        assertEquals(1, invalidRecords.size(), "应该识别出1条悬挂记录");
        
        // 验证悬挂记录是李四的第一条出场记录
        AttendanceRecord invalidRecord = invalidRecords.iterator().next();
        assertEquals("李四", invalidRecord.getEmployeeName());
        assertEquals(WorkTimeConstants.DIRECTION_OUT, invalidRecord.getDirection());
        assertEquals(LocalDateTime.of(2025, 6, 26, 8, 0, 0), invalidRecord.getEventTime());
        
        // 第三步：过滤有效记录
        List<AttendanceRecord> validRecords = deduplicatedRecords.stream()
                .filter(record -> !invalidRecords.contains(record))
                .collect(Collectors.toList());
        
        // 验证最终有效记录
        assertEquals(4, validRecords.size(), "最终应该有4条有效记录");
        
        // 验证张三的跨日工作记录完整保留
        long zhangSanRecords = validRecords.stream()
                .filter(r -> "张三".equals(r.getEmployeeName()))
                .count();
        assertEquals(2, zhangSanRecords, "张三应该有2条有效记录（1入1出）");
        
        // 验证李四的正常工作记录保留
        long liSiValidRecords = validRecords.stream()
                .filter(r -> "李四".equals(r.getEmployeeName()))
                .count();
        assertEquals(2, liSiValidRecords, "李四应该有2条有效记录（去除悬挂记录后）");
    }

    @Test
    @DisplayName("测试大量重复记录的处理性能")
    public void testPerformanceWithManyDuplicates() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 生成大量重复记录
        for (int i = 0; i < 1000; i++) {
            records.add(createRecord("员工" + (i % 10), "910厂房", WorkTimeConstants.DIRECTION_IN, 
                    String.format("2025-06-26 08:%02d:%02d", i / 60, i % 60)));
        }
        
        long startTime = System.currentTimeMillis();
        
        // 执行去重处理
        List<AttendanceRecord> deduplicatedRecords = duplicateProcessor.removeDuplicateRecords(records);
        
        long endTime = System.currentTimeMillis();
        long processingTime = endTime - startTime;
        
        // 验证处理结果
        assertTrue(deduplicatedRecords.size() < records.size(), "应该去除一些重复记录");
        assertTrue(processingTime < 5000, "处理1000条记录应该在5秒内完成，实际用时: " + processingTime + "ms");
        
        System.out.println("性能测试结果：");
        System.out.println("原始记录数: " + records.size());
        System.out.println("去重后记录数: " + deduplicatedRecords.size());
        System.out.println("处理时间: " + processingTime + "ms");
    }

    @Test
    @DisplayName("测试边界情况：恰好120秒的时间间隔")
    public void testBoundaryTimeWindow() {
        List<AttendanceRecord> records = new ArrayList<>();
        
        // 添加恰好120秒间隔的记录
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:00:00"));
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:02:00")); // 恰好120秒
        records.add(createRecord("张三", "910厂房", WorkTimeConstants.DIRECTION_IN, "2025-06-26 08:02:01")); // 121秒，应保留
        
        List<AttendanceRecord> result = duplicateProcessor.removeDuplicateRecords(records);
        
        assertEquals(2, result.size(), "恰好120秒的记录应该被去重，121秒的应该保留");
        
        // 验证保留的记录时间
        assertEquals(LocalDateTime.of(2025, 6, 26, 8, 0, 0), result.get(0).getEventTime());
        assertEquals(LocalDateTime.of(2025, 6, 26, 8, 2, 1), result.get(1).getEventTime());
    }

    /**
     * 创建测试用的打卡记录
     */
    private AttendanceRecord createRecord(String employeeName, String location, String direction, String eventTimeStr) {
        AttendanceRecord record = new AttendanceRecord();
        record.setEmployeeName(employeeName);
        record.setLocation(location);
        record.setDirection(direction);
        record.setEventTime(LocalDateTime.parse(eventTimeStr, formatter));
        
        // 根据location设置accessPoint
        if ("910厂房".equals(location)) {
            record.setAccessPoint("910" + direction);
        } else if ("920厂房".equals(location)) {
            record.setAccessPoint("920" + direction);
        } else if ("二号楼".equals(location)) {
            record.setAccessPoint("二号楼" + direction);
        }
        
        return record;
    }
}
