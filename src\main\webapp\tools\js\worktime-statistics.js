/**
 * 员工工时统计工具 JavaScript 交互逻辑
 */

// 开发者控制台提示
console.log('%c员工工时统计工具 - 开发者功能', 'color: #1e9fff; font-size: 14px; font-weight: bold;');
console.log('%c可用的控制台命令:', 'color: #67c23a; font-weight: bold;');
console.log('showToolSection() - 显示工具操作区域');
console.log('hideToolSection() - 隐藏工具操作区域');
console.log('%c注意：工具操作区域仅供开发和测试使用', 'color: #f56c6c;');

layui.use(['layer', 'element'], function(){
    var layer = layui.layer;
    var element = layui.element;
    var $ = layui.jquery;
    var $uploadDragArea = $('#uploadDragArea');
    var $fileInput = $('#fileInput');
    var $selectFileBtn = $('#selectFileBtn');
    var $processBtn = $('#processBtn');
    var $fileInfo = $('#fileInfo');
    var $fileName = $('#fileName');
    var $fileSize = $('#fileSize');
    var $progressArea = $('#progressArea');
    var $resultCard = $('#resultCard');
    var $downloadBtn = $('#downloadBtn');
    var $quickStats = $('#quickStats');
    var $generateTestBtn = $('#generateTestBtn');
    var $toolSection = $('#toolSection');
    var $uploadSection = $('#uploadSection');
    
    // 全局变量
    var selectedFile = null;
    var isProcessing = false;
    var downloadUrl = null;
    
    // 初始化
    init();
    
    function init() {
        bindEvents();
        setupDragAndDrop();
    }
    
    /**
     * 绑定事件
     */
    function bindEvents() {
        // 选择文件按钮
        $selectFileBtn.on('click', function() {
            $fileInput.click();
        });
        
        // 文件选择事件
        $fileInput.on('change', function() {
            var files = this.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });
        
        // 开始处理按钮
        $processBtn.on('click', function() {
            if (selectedFile && !isProcessing) {
                processFile();
            } else if (!selectedFile) {
                layer.msg('请先选择要处理的Excel文件', {icon: 2});
            }
        });
        
        // 下载按钮
        $downloadBtn.on('click', function() {
            if (downloadUrl) {
                downloadFile(downloadUrl);
            }
        });
        
        // 生成测试数据按钮
        $generateTestBtn.on('click', function() {
            showTestDataDialog();
        });
        
        // 拖拽区域点击事件
        $uploadDragArea.on('click', function() {
            $fileInput.click();
        });
    }
    
    /**
     * 设置拖拽上传
     */
    function setupDragAndDrop() {
        // 阻止默认拖拽行为
        $(document).on('dragover drop', function(e) {
            e.preventDefault();
        });
        
        // 拖拽进入
        $uploadDragArea.on('dragenter dragover', function(e) {
            e.preventDefault();
            $(this).addClass('dragover');
        });
        
        // 拖拽离开
        $uploadDragArea.on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
        });
        
        // 文件放置
        $uploadDragArea.on('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
            
            var files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });
    }
    
    /**
     * 处理文件选择
     */
    function handleFileSelect(file) {
        // 验证文件类型
        if (!file.name.toLowerCase().endsWith('.xlsx')) {
            layer.msg('请选择Excel格式的文件（.xlsx）', {icon: 2});
            return;
        }
        
        // 验证文件大小（100MB）
        if (file.size > 100 * 1024 * 1024) {
            layer.msg('文件大小不能超过100MB', {icon: 2});
            return;
        }
        
        selectedFile = file;
        
        // 显示文件信息
        $fileName.text(file.name);
        $fileSize.text(formatFileSize(file.size));
        $fileInfo.show();
        
        // 启用处理按钮
        $processBtn.prop('disabled', false).removeClass('layui-btn-disabled');
        
        // 高亮上传区域
        $uploadDragArea.addClass('success-highlight');
        
        layer.msg('文件选择成功', {icon: 1});
    }
    
    /**
     * 处理文件
     */
    function processFile() {
        if (!selectedFile) {
            layer.msg('请先选择文件', {icon: 2});
            return;
        }
        
        isProcessing = true;
        
        // 禁用按钮
        $processBtn.prop('disabled', true).addClass('layui-btn-disabled');
        $selectFileBtn.prop('disabled', true).addClass('layui-btn-disabled');
        
        // 显示进度条
        $progressArea.show();
        element.progress('demo', '30%');
        
        // 创建FormData
        var formData = new FormData();
        formData.append('file', selectedFile);
        
        // 显示加载层
        var loadingIndex = layer.load(1, {
            content: '正在处理文件，请稍候...',
            shade: [0.3, '#000']
        });
        
        // 发送请求
        $.ajax({
            url: '/api/worktime/upload',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            timeout: 300000, // 5分钟超时
            success: function(response) {
                layer.close(loadingIndex);
                handleProcessResult(response);
            },
            error: function() {
                layer.close(loadingIndex);
                handleProcessError();
            }
        });
    }
    
    /**
     * 处理处理结果
     */
    function handleProcessResult(response) {
        isProcessing = false;
        
        // 恢复按钮状态
        $processBtn.prop('disabled', false).removeClass('layui-btn-disabled');
        $selectFileBtn.prop('disabled', false).removeClass('layui-btn-disabled');
        
        // 隐藏进度条
        $progressArea.hide();
        
        if (response.success) {
            // 处理成功
            downloadUrl = response.downloadUrl;
            
            // 显示结果区域
            $resultCard.show();
            
            // 显示快速统计（如果有数据）
            if (response.stats) {
                updateQuickStats(response.stats);
                $quickStats.show();
            }
            
            layer.msg('工时统计处理完成', {icon: 1});
            
            // 滚动到结果区域
            $('html, body').animate({
                scrollTop: $resultCard.offset().top - 100
            }, 500);
            
        } else {
            // 处理失败
            layer.alert(response.message || '处理失败，请重试', {
                icon: 2,
                title: '处理失败'
            });
            
            // 错误高亮
            $uploadDragArea.removeClass('success-highlight').addClass('error-highlight');
            setTimeout(function() {
                $uploadDragArea.removeClass('error-highlight');
            }, 3000);
        }
    }
    
    /**
     * 处理处理错误
     */
    function handleProcessError() {
        isProcessing = false;
        
        // 恢复按钮状态
        $processBtn.prop('disabled', false).removeClass('layui-btn-disabled');
        $selectFileBtn.prop('disabled', false).removeClass('layui-btn-disabled');
        
        // 隐藏进度条
        $progressArea.hide();
        
        layer.alert('网络请求失败，请检查网络连接后重试', {
            icon: 2,
            title: '请求失败'
        });
    }
    
    /**
     * 更新快速统计
     */
    function updateQuickStats(stats) {
        $('#recordCount').text(stats.recordCount || 0);
        $('#employeeCount').text(stats.employeeCount || 0);
        $('#dayCount').text(stats.dayCount || 0);
    }
    
    /**
     * 下载文件
     */
    function downloadFile(url) {
        var link = document.createElement('a');
        link.href = url;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        layer.msg('开始下载文件', {icon: 1});
    }
    
    /**
     * 显示测试数据对话框
     */
    function showTestDataDialog() {
        layer.open({
            type: 1,
            title: '生成测试数据',
            content: `
                <div style="padding: 20px;">
                    <form class="layui-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label">员工数量</label>
                            <div class="layui-input-block">
                                <input type="number" id="testEmployeeCount" value="5" min="1" max="50" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">天数</label>
                            <div class="layui-input-block">
                                <input type="number" id="testDayCount" value="7" min="1" max="30" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button type="button" class="layui-btn" onclick="generateTestData()">生成并下载</button>
                                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            `,
            area: ['400px', '300px'],
            shadeClose: true
        });
    }
    
    /**
     * 显示工具操作区域（开发者功能）
     * 在控制台中调用：showToolSection()
     */
    window.showToolSection = function() {
        $toolSection.show();
        $uploadSection.removeClass('layui-col-md12').addClass('layui-col-md8');
    };

    /**
     * 隐藏工具操作区域
     * 在控制台中调用：hideToolSection()
     */
    window.hideToolSection = function() {
        $toolSection.hide();
        $uploadSection.removeClass('layui-col-md8').addClass('layui-col-md12');
    };

    /**
     * 生成测试数据
     */
    window.generateTestData = function() {
        var employeeCount = $('#testEmployeeCount').val() || 5;
        var dayCount = $('#testDayCount').val() || 7;

        var url = '/api/worktime/generate-test-data?employeeCount=' + employeeCount + '&days=' + dayCount;

        layer.closeAll();
        layer.msg('正在生成测试数据...', {icon: 16, time: 2000});

        // 直接下载
        window.open(url, '_blank');
    };
    

    
    /**
     * 格式化文件大小
     */
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});
