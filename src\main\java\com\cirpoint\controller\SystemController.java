package com.cirpoint.controller;

import com.cirpoint.model.Result;
import com.cirpoint.service.SystemService;
import com.cirpoint.util.FileDownloadUtil;
import java.io.File;
import java.io.IOException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;


@RestController
@RequestMapping("/system")
public class SystemController {

	private final SystemService systemService;

	@Autowired
	public SystemController(SystemService systemService) {
		this.systemService = systemService;
	}

	/**
	 * 获取客户端IP地址
	 *
	 * @param request HTTP请求对象
	 * @return 包含IP地址的响应
	 */
	@GetMapping("/get/ip")
	public ResponseEntity<?> getClientIp(HttpServletRequest request) {
		return Result.ok(systemService.getClientIp(request));
	}

	/**
	 * 导入用户数据
	 *
	 * @param file 上传的Excel文件
	 * @return 导入结果
	 */
	@PostMapping("/import/user")
	public ResponseEntity<?> importUsers(@RequestParam("uploadFile") MultipartFile file) throws IOException {
		return ResponseEntity.ok(systemService.importUsers(file));
	}

	/**
	 * 打开或下载文件
	 *
	 * @param fileName 文件名
	 * @param filePath 文件路径
	 * @return 文件内容响应
	 */
	@RequestMapping(value = "/open/file", method = {RequestMethod.GET, RequestMethod.POST})
	public ResponseEntity<?> openFile(
			@RequestParam String fileName,
			@RequestParam String filePath) {
		return systemService.getFileForOpen(fileName, filePath);
	}

	/**
	 * 导出日志Excel
	 *
	 * @param content     日志内容
	 * @param stime       开始时间
	 * @param etime       结束时间
	 * @param operation   操作类型
	 * @param result      操作结果
	 * @param username    用户名
	 * @param currentUser 当前用户
	 * @return Excel文件
	 */
	@PostMapping("/export/log")
	public ResponseEntity<?> exportLogExcel(
			@RequestParam(required = false) String content,
			@RequestParam(required = false) String stime,
			@RequestParam(required = false) String etime,
			@RequestParam(required = false) String operation,
			@RequestParam(required = false) String result,
			@RequestParam(required = false) String username,
			@RequestParam(required = false) String currentUser) {
		File excelFile = systemService.exportLogExcel(content, stime, etime, operation, result, username, currentUser);
		return FileDownloadUtil.fileResponseAndDelete(excelFile);
	}

	/**
	 * 导出确认表日志Excel
	 *
	 * @param query 查询参数
	 * @return Excel文件下载响应
	 */
	@PostMapping("/export/confirm/log")
	public ResponseEntity<?> exportConfirmLog(@RequestParam String query) {
		File excelFile = systemService.exportConfirmLogExcel(query);
		return FileDownloadUtil.fileResponseAndDelete(excelFile);
	}

	/**
	 * 导出用户Excel
	 *
	 * @return Excel文件
	 */
	@PostMapping("/export/user")
	public ResponseEntity<?> exportUserExcel() {
		File excelFile = systemService.exportUserExcel();
		return FileDownloadUtil.fileResponseAndDelete(excelFile);
	}

	/**
	 * 导出用户导入模板
	 *
	 * @return Excel模板文件
	 */
	@PostMapping("/export/user/tpl")
	public ResponseEntity<?> exportUserTemplate() {
		File excelFile = systemService.exportUserTemplate();
		return FileDownloadUtil.fileResponseAndDelete(excelFile);
	}
}
