package com.cirpoint.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class EditorSecurityConfig {
    
    @Getter
	@Value("${editor.security.password:}")
    private String editorPassword;
    
    @Value("${spring.profiles.active:dev}")
    private String activeProfile;
    
    public boolean isProductionEnvironment() {
        return "prod".equals(activeProfile);
    }

}
