// tree-manager.js - 文件树管理相关功能

// 根据文件扩展名获取编程语言
function getLanguageByExt(ext) {
    var languageMap = {
        'js': 'javascript',
        'ts': 'typescript',
        'html': 'html',
        'htm': 'html',
        'css': 'css',
        'less': 'less',
        'scss': 'scss',
        'sass': 'scss',
        'json': 'json',
        'java': 'java',
        'py': 'python',
        'rb': 'ruby',
        'php': 'php',
        'sql': 'sql',
        'md': 'markdown',
        'txt': 'plaintext',
        'yml': 'yaml',
        'yaml': 'yaml',
        'sh': 'shell',
        'bat': 'bat',
        'log': 'log',
        'xml': 'xml',
        "properties": "properties"
    };
    return languageMap[ext] || 'plaintext';
}

var treeSetting = {
    view: {
        dblClickExpand: false, //双击节点时，是否自动展开父节点的标识
        showLine: true, //是否显示节点之间的连线
        selectedMulti: false, //设置是否允许同时选中多个节点,
        showTitle: true
    },
    async: {
        enable: true,
        url: "/editor/tree",
        type: "post",
        autoParam: ["path"],
        contentType: "application/json;charset=utf-8",
        dataType: 'json',
        dataFilter: function (treeId, parentNode, responseData) {
            return ajaxDataFilter(responseData);
        }
    },
    check: {
        enable: false
    },
    data: {
        simpleData: { //简单数据模式
            enable: true,
            idKey: "id",
            pIdKey: "pid",
            rootPId: "-1"
        },
        key: {
            isParent: "parent",
            name: "name"
        }
    },
    callback: {
        onClick: onClick,
        onRightClick: onRightClick
    }
};

// 点击节点时的回调
function onClick(event, treeId, treeNode) {
    if (!treeNode.parent) {

        // 确保节点被选中
        window.zTreeObj.selectNode(treeNode);

        // 使用绝对路径
        const absolutePath = treeNode.absolutePath || treeNode.path;

        // 检查文件是否已经打开
        const existingTab = window.tabManager.tabs.find(t => t.path === absolutePath);
        if (existingTab) {
            // 如果文件已经打开，直接激活对应标签页
            window.tabManager.setActive(absolutePath, false);
            return;
        }

        // 创建一个新的节点对象，使用绝对路径
        const nodeWithAbsolutePath = {
            ...treeNode,
            path: absolutePath
        };

        // 如果文件未打开，添加新标签页
        window.tabManager.add(nodeWithAbsolutePath);

        // 确保标签页高亮
        setTimeout(() => {
            const $activeTab = $(`.tab-item[data-path="${absolutePath}"]`);
            if ($activeTab.length) {
                $('.tab-item').removeClass('active');
                $activeTab.addClass('active');
            }
        }, 50);
    }
}

// 右键菜单点击事件
function onRightClick(event, treeId, treeNode) {
    if (!treeNode) return;

    // 确保zTreeObj已初始化
    if (!window.zTreeObj) {
        console.error('zTree实例未初始化');
        return;
    }

    window.zTreeObj.selectNode(treeNode); // 现在可以安全调用
    showRMenu(event.clientX, event.clientY);

    // 检查是否为根节点
    const isRootNode = treeNode.pid === "-1";
    // 判断是否为文件夹，使用parent属性
    const isFolder = treeNode.parent === true;

    // 控制菜单项显示
    if (isRootNode) {
        // 根节点只显示部分菜单项
        $("#m_add_file, #m_add_folder").toggle(true);
        $("#m_delete, #m_rename").toggle(false);
        $("#m_download").toggle(false);
        $("#m_copy_path").toggle(false);
        $("#m_export").toggle(true);
        $("#m_import").toggle(true);
        $("#m_open_in_terminal").toggle(false);
        $("#m_properties").toggle(true);
        $("#m_search_in_folder").toggle(true);
        $("#m_copy_abs_path").toggle(false);
    } else {
        // 非根节点的菜单项显示逻辑
        $("#m_add_file, #m_add_folder").toggle(isFolder);
        $("#m_delete, #m_rename").toggle(true);
        $("#m_download").toggle(!isFolder); // 只对文件显示下载选项
        $("#m_export").toggle(isFolder); // 导出选项只在文件夹上显示
        $("#m_import").toggle(isFolder); // 导入选项只在文件夹上显示
        $("#m_open_in_terminal").toggle(isFolder); // "在终端中打开"选项只在文件夹上显示
        $("#m_properties").toggle(true); // 属性选项始终显示
        // "在文件夹中查找"只在文件夹节点上显示
        $("#m_search_in_folder").toggle(isFolder);
        $("#m_copy_abs_path").toggle(true);
    }
}

$(document).on('click', '#m_copy_abs_path', function() {
    var node = window.zTreeObj.getSelectedNodes()[0];
    if (!node) {
        layer.msg('未选中节点', {icon: 0});
        return;
    }
    // 绝对路径字段假设为absolutePath，没有则用path字段
    var absPath = node.absolutePath || node.path;
    var textarea = document.createElement('textarea');
    textarea.value = absPath;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);
    layer.msg('绝对路径已复制到剪贴板');
});

// 显示右键菜单
function showRMenu(x, y) {
    const $rMenu = $("#rMenu");

    // 先显示菜单但设为透明，以便获取其真实尺寸
    $rMenu.removeClass('layui-hide').css('opacity', '0');

    const winHeight = $(window).height();
    const winWidth = $(window).width();
    const menuHeight = $rMenu.outerHeight();
    const menuWidth = $rMenu.outerWidth();

    // 检查下方是否有足够空间
    const isBottomOverflow = (y + menuHeight > winHeight);

    // 如果下方空间不足，则在上方显示菜单
    if (isBottomOverflow) {
        // 将菜单显示在鼠标上方
        y = y - menuHeight;
        // 如果上方也没有足够空间，则尽量靠近屏幕顶部
        if (y < 0) y = 10;
    }

    // 防止菜单水平方向溢出窗口
    x = x + menuWidth > winWidth ? winWidth - menuWidth - 10 : x;

    // 如果菜单左边界小于0，则靠近左侧边缘显示
    if (x < 0) x = 10;

    // 设置菜单位置并显示
    $rMenu.css({
        "top": y + "px",
        "left": x + "px",
        "position": "fixed", // 修复滚动时位置偏移问题
        "opacity": "1" // 恢复透明度
    });

    // 点击页面任意位置隐藏菜单
    $(document).one("click", function () {
        hideRMenu();
    });
}

// 隐藏右键菜单
function hideRMenu() {
    $("#rMenu").addClass('layui-hide');
}


/**
 * 操作完节点之后重新加载节点并选中指定节点
 * @param {string} refreshId 需要刷新的节点ID
 * @param {string} selId 需要选中的节点ID，如果为空则选中刷新的节点
 */
function refurshTreeAndSelectNode(refreshId, selId) {
    // 如果未指定选中节点ID，则使用刷新节点ID
    selId = selId || refreshId;

    // 获取需要刷新的节点
    const refreshTreeNode = window.zTreeObj.getNodeByParam("id", refreshId, null);

    // 确保节点是父节点
    if (!refreshTreeNode.parent) {
        refreshTreeNode.parent = true;
        window.zTreeObj.updateNode(refreshTreeNode);
    }

    // 重新异步加载子节点并在加载完成后选中指定节点
    window.zTreeObj.reAsyncChildNodes(refreshTreeNode, 'refresh', false, function () {
        // 展开刷新的节点
        window.zTreeObj.expandNode(refreshTreeNode, true, false, true);

        // 获取并选中指定节点
        const newSelNode = window.zTreeObj.getNodeByParam("id", selId, null);
        window.zTreeObj.selectNode(newSelNode, false, true);

        // 触发节点点击事件
        onClick(null, window.zTreeObj.setting.treeId, newSelNode);
    });
}

/**
 * 依据path定位节点
 * @param {*} path
 */
function locateTreeByPath(path) {
    // 解析路径，获取文件名和文件夹路径
    const parts = path.split('/');

    // 从多路径管理中获取已启用的路径
    $.ajax({
        url: '/multiPath/enabled',
        type: 'get',
        async: false, // 使用同步请求确保在继续之前获取到路径
        success: function(enabledPaths) {
            if (!enabledPaths || enabledPaths.length === 0) {
                console.error('没有已启用的路径');
                layer.msg('没有已启用的路径，无法定位文件', {icon: 2});
                return;
            }

            // 找到包含目标路径的已启用路径
            let matchingRootPath = null;
            for (let i = 0; i < enabledPaths.length; i++) {
                // 将路径中的反斜杠替换为正斜杠
                const rootPath = enabledPaths[i].path.replace(/\\/g, '/');
                if (path.startsWith(rootPath)) {
                    matchingRootPath = rootPath;
                    break;
                }
            }

            if (!matchingRootPath) {
                console.error('找不到匹配的根路径');
                layer.msg('找不到匹配的根路径，无法定位文件', {icon: 2});
                return;
            }

            // 构建节点路径数组，从匹配的根路径开始
            var nodePaths = [matchingRootPath];

            // 如果路径比根路径长，添加子路径
            if (path.length > matchingRootPath.length) {
                // 依次获取每一层的节点的path
                for (let i = 0; i < parts.length; i++) {
                    const nodePath = parts.slice(0, i + 1).join('/');
                    // 只添加完整路径（避免添加部分路径）
                    if (nodePath.length >= matchingRootPath.length) {
                        nodePaths.push(nodePath);
                    }
                }
            }

            // 去重，确保路径唯一
            nodePaths = [...new Set(nodePaths)];

            // 递归定位节点
            function recursionTree(index) {
                var nodePath = nodePaths[index];
                if (nodePath !== undefined) {
                    var thisNode = window.zTreeObj.getNodeByParam("path", nodePath, null);
                    if (!thisNode) {
                        // 如果找不到节点，可能是因为路径格式不匹配，尝试使用绝对路径查找
                        thisNode = window.zTreeObj.getNodeByParam("absolutePath", nodePath, null);
                    }

                    if (!thisNode) {
                        console.error('找不到节点:', nodePath);
                        return;
                    }

                    if (index == nodePaths.length - 1) {
                        window.zTreeObj.selectNode(thisNode);
                        onClick(null, window.zTreeObj.setting.treeId, thisNode);
                    } else {
                        //如果这个节点是父节点的话
                        if (thisNode.parent) {
                            //判断是否是展开的状态
                            if (thisNode.open) {
                                var newIndex = index + 1;
                                recursionTree(newIndex);
                            } else {
                                //如果没有展开的话需要请求该节点下的子数据
                                window.zTreeObj.reAsyncChildNodes(thisNode, "refresh", false, function () {
                                    //展开之后再判断下一层级的节点
                                    var newIndex = index + 1;
                                    recursionTree(newIndex);
                                });
                            }
                        }
                    }
                }
            }

            // 开始递归定位
            recursionTree(0);
        },
        error: function() {
            console.error('获取已启用路径失败');
            layer.msg('获取已启用路径失败，无法定位文件', {icon: 2});
        }
    });
}

// 新建文件
function addFile() {
    var parentNode = window.zTreeObj.getSelectedNodes()[0];
    layer.prompt({ title: '请输入文件名' }, function (value, index) {
        createNode(value, 'file', parentNode, index);
    });
}

// 新建文件夹
function addFolder() {
    var parentNode = window.zTreeObj.getSelectedNodes()[0];
    layer.prompt({ title: '请输入文件夹名' }, function (value, index) {
        createNode(value, 'folder', parentNode, index);
    });
}

// 公共创建节点函数
function createNode(name, type, parentNode, index) {
    // 使用绝对路径
    const absolutePath = parentNode.absolutePath || parentNode.path;

    $.ajax({
        url: '/editor/create',
        method: 'POST',
        data: {
            path: absolutePath,
            name: name,
            type: type
        },
        success: function (data) {
            layer.close(index);
            // 定位并选择新创建的节点
            refurshTreeAndSelectNode(parentNode.id, data.uuid);
        },
        error: function (xhr) {
            // 检查是否为409冲突（文件或文件夹已存在）
            if (xhr.status === 409) {
                layer.msg((type === 'file' ? '创建文件' : '创建文件夹') + '失败：' + xhr.responseText, { icon: 0 });
            } else {
                layer.msg((type === 'file' ? '创建文件' : '创建文件夹') + '失败：' + (xhr.responseText || '未知错误'), { icon: 2 });
            }
        }
    });
}

// 删除节点
function deleteNode() {
    var node = window.zTreeObj.getSelectedNodes()[0];

    // 使用绝对路径
    const absolutePath = node.absolutePath || node.path;

    layer.confirm('确定要删除吗？', function (index) {
        $.ajax({
            url: '/editor/delete',
            method: 'POST',
            data: {
                path: absolutePath
            },
            success: function () {
                layer.close(index);

                // 检查并关闭对应的标签页
                if (window.tabManager) {
                    // 使用tabManager的closeRelatedTabs方法关闭相关标签页
                    window.tabManager.closeRelatedTabs(absolutePath, node.parent === true);
                }

                refurshTreeAndSelectNode(node.getParentNode().id);
            },
            error: function (xhr) {
                layer.msg('删除失败：' + xhr.responseText);
            }
        });
    });
}

// 下载文件
function downloadFile() {
    var treeNode = window.zTreeObj.getSelectedNodes()[0];

    // 使用绝对路径
    const absolutePath = treeNode.absolutePath || treeNode.path;

    // 创建一个临时的a标签用于下载
    var link = document.createElement('a');
    link.href = '/editor/download?path=' + encodeURIComponent(absolutePath);
    link.download = treeNode.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 导出文件或文件夹
function exportFiles() {
    var nodes = window.zTreeObj.getSelectedNodes();
    if (!nodes || nodes.length === 0) return;

    // 只支持单选或多选（批量导出）
    var paths = [];
    var fileName = '';
    if (nodes.length === 1) {
        // 使用绝对路径
        paths.push(nodes[0].absolutePath || nodes[0].path);
        fileName = nodes[0].name;
    } else {
        for (var i = 0; i < nodes.length; i++) {
            // 使用绝对路径
            paths.push(nodes[i].absolutePath || nodes[i].path);
        }
        fileName = '批量导出';
    }
    // 过滤非法字符
    fileName = fileName.replace(/[\\/:*?"<>|]/g, '_');

    // 构建下载URL，增加fileName参数
    var downloadUrl = 'editor/export?paths=' + encodeURIComponent(paths.join(',')) + '&fileName=' + encodeURIComponent(fileName);

    // 创建临时链接并点击下载
    var link = document.createElement('a');
    link.href = downloadUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 导入文件
function importFiles() {
    const treeNode = window.zTreeObj.getSelectedNodes()[0];
    if (!treeNode) return;

    // 先确保对话框可见
    $('#importDialog').removeClass('layui-hide');

    // 打开导入对话框
    const importLayer = layer.open({
        type: 1,
        title: '导入文件',
        content: $('#importDialog'),
        area: ['580px', '430px'],
        maxWidth: '100%',
        shadeClose: false,
        anim: 1, // 弹出动画
        success: function () {
            // 添加悬停效果
            $('#importUpload').hover(
                function () {
                    $(this).css({
                        'border-color': '#009688',
                        'background-color': '#f0f9ff',
                        'box-shadow': '0 4px 12px rgba(0,0,0,0.1)'
                    });
                },
                function () {
                    $(this).css({
                        'border-color': '#1E9FFF',
                        'background-color': '#f9f9f9',
                        'box-shadow': '0 2px 8px rgba(0,0,0,0.05)'
                    });
                }
            );

            // 初始化上传组件
            layui.use('upload', function () {
                const upload = layui.upload;

                upload.render({
                    elem: '#importUpload',
                    url: 'editor/import',
                    accept: 'file',
                    multiple: true, // 开启多文件选择
                    data: {
                        targetPath: treeNode.absolutePath || treeNode.path
                    },
                    before: function () {
                        layer.load(2); // 使用更好看的加载动画
                    },
                    done: function (res) {
                        layer.closeAll('loading');
                        if (res.success) {
                            refurshTreeAndSelectNode(treeNode.id);
                        } else {
                            layer.msg('导入失败: ' + (res.message || '未知错误'), { icon: 2, time: 2000 });
                        }
                    },
                    error: function () {
                        layer.closeAll('loading');
                        layer.msg('上传出错', { icon: 2, time: 2000 });
                    }
                });
            });

            // 绑定取消按钮
            $('#importCancel').off('click').on('click', function () {
                layer.close(importLayer);
            });

            // 绑定导入按钮
            $('#importSubmit').off('click').on('click', function () {
                // 触发上传控件的点击事件
                $('#importUpload').click();
            });
        },
        end: function () {
            $('#importDialog').addClass('layui-hide');
        }
    });
}

// 重命名节点
function renameNode() {
    const node = window.zTreeObj.getSelectedNodes()[0];
    const oldName = node.name;
    const parentNode = node.getParentNode();

    // 使用绝对路径
    const absolutePath = node.absolutePath || node.path;
    console.log("重命名节点，节点绝对路径:", absolutePath, "节点ID:", node.id, "是否文件夹:", node.parent);

    // 创建重命名对话框
    layer.prompt({
        title: '请输入新名称',
        value: oldName,
        formType: 0,  // 文本输入框类型
        success: function (layero, index) {
            // 聚焦输入框并选中文本
            const $input = $(layero).find('.layui-layer-input');
            $input.focus().select();
        }
    }, function (value, index) {
        // 如果值没有变化，直接关闭对话框，无需执行重命名
        if (value === oldName) {
            layer.close(index);
            return;
        }
        // 执行重命名AJAX请求
        $.ajax({
            url: '/editor/rename',
            method: 'POST',
            data: {
                oldPath: absolutePath,
                newName: value
            },
            success: function (data) {
                layer.close(index);

                // 检查并关闭对应的标签页
                if (window.tabManager) {
                    // 使用tabManager的closeRelatedTabs方法关闭相关标签页
                    window.tabManager.closeRelatedTabs(absolutePath, node.parent === true);
                }

                refurshTreeAndSelectNode(parentNode.id, data.uuid);
            },
            error: function (xhr) {
                // 检查是否为409冲突（同名文件已存在）
                if (xhr.status === 409) {
                    layer.msg('重命名失败：' + xhr.responseText, { icon: 0 });
                } else {
                    layer.msg('重命名失败：' + (xhr.responseText || '未知错误'), { icon: 2 });
                }
            }
        });
    });
}

// 复制路径
function copyPath() {
    const node = window.zTreeObj.getSelectedNodes()[0];

    // 使用绝对路径
    const absolutePath = node.absolutePath || node.path;

    const textarea = document.createElement('textarea');
    textarea.value = absolutePath;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);
    layer.msg('路径已复制到剪贴板');
}



// 处理异步加载的数据
function ajaxDataFilter(responseData) {
    if (responseData) {
        // 对节点进行排序：文件夹在前，文件在后，同类型按名称排序
        responseData.sort(function (a, b) {
            // 如果一个是文件夹一个是文件，文件夹排在前面
            if (a.parent !== b.parent) {
                return a.parent ? -1 : 1;
            }
            // 如果都是文件夹或都是文件，按名称排序
            return a.name.localeCompare(b.name, 'zh-CN');
        });
    }
    return responseData;
}

// 初始化文件树
function initZTree() {
    $.ajax({
        url: "/editor/tree?path=",
        type: "get",
        contentType: "application/json;charset=utf-8",
        dataType: 'json',
        success: function (data) {
            data = ajaxDataFilter(data);
            window.zTreeObj = $.fn.zTree.init($("#fileTree"), treeSetting, data);
        },
        error: function (error) {
            console.error("初始化文件树失败:", error);
        }
    });
}

// 在文件夹中查找
function searchInFolder() {
    const node = window.zTreeObj.getSelectedNodes()[0];
    if (!node) {
        console.error('未选中节点');
        return;
    }

    console.log("在文件夹中查找:", node);

    // 确保是文件夹节点
    if (!node.parent) {
        console.error('选中的不是文件夹节点');
        return;
    }

    // 切换到搜索页签
    window.uiUtils.switchSidebarTab('search');

    // 使用绝对路径
    const absolutePath = node.absolutePath || node.path;

    // 设置搜索范围提示
    $('#searchScopeInfo').text(`搜索范围: ${node.name}`).show();

    // 显示清除按钮
    $('#clearSearchScope').show();

    // 存储当前搜索文件夹路径（使用绝对路径）
    window.currentSearchFolder = absolutePath;

    // 聚焦搜索框
    setTimeout(function () {
        $('#searchInput').focus();
    }, 100);
}

// 在终端中打开文件夹
function openInTerminal() {
    const nodes = window.zTreeObj.getSelectedNodes();
    if (nodes.length === 0) return;

    const node = nodes[0];
    if (!node.parent) return;

    // 使用绝对路径
    const absolutePath = node.absolutePath || node.path;

    // 如果终端未打开，先打开终端
    if (!$('#terminalContainer').is(':visible')) {
        window.terminalManager.toggle(true);
        // 等待终端初始化完成后执行cd命令
        setTimeout(() => {
            window.terminalManager.execute('cd ' + absolutePath);
        }, 100);
    } else {
        // 终端已经打开，直接执行cd命令
        window.terminalManager.execute('cd ' + absolutePath);
    }
}

// 显示文件属性
function showFileProperties() {
    var treeNode = window.zTreeObj.getSelectedNodes()[0];
    if (!treeNode) return;

    // 使用绝对路径
    var absolutePath = treeNode.absolutePath || treeNode.path;
    console.log("获取文件属性，节点绝对路径:", absolutePath, "节点ID:", treeNode.id);

    // 显示加载层
    var loadingIndex = layer.load(2);

    // 请求文件属性数据
    $.ajax({
        url: '/editor/properties',
        method: 'GET',
        data: {
            path: absolutePath
        },
        success: function (data) {
            // 关闭加载层
            layer.close(loadingIndex);

            if (!data) {
                layer.msg('获取文件属性失败');
                return;
            }

            console.log("文件属性数据:", data);

            // 构建HTML内容，使用新的样式结构
            var html = '<div class="file-properties-container">' +
                '<div class="file-properties-header">' +
                '<i class="layui-icon ' + (data.directory ? 'layui-icon-folder' : 'layui-icon-file') + '"></i>' +
                '<span>' + data.name + '</span>' +
                '</div>' +
                '<div class="file-properties-content">' +
                '<table class="file-properties-table">' +
                '<tbody>' +
                '<tr><th>类型</th><td>' + (data.directory ? '文件夹' : (data.name.split('.').pop().toUpperCase() + ' 文件')) + '</td></tr>' +
                '<tr><th>大小</th><td>' + data.readableSize + '</td></tr>';

            // 如果是目录，添加子项目数量
            if (data.directory) {
                html += '<tr><th>包含项目</th><td>' + data.childCount + ' 个项目</td></tr>';
            }

            html += '<tr><th>位置</th><td class="file-path">' + data.path + '</td></tr>' +
                '<tr><th>创建时间</th><td>' + (data.creationTimeStr || '未知') + '</td></tr>' +
                '<tr><th>修改时间</th><td>' + (data.lastModifiedTimeStr || '未知') + '</td></tr>' +
                '</tbody>' +
                '</table>' +
                '</div>' +
                '</div>';

            // 添加内联样式
            var style = '<style>' +
                '.file-properties-container { padding: 0; background-color: #252526; color: #d4d4d4; }' +
                '.file-properties-header { display: flex; align-items: center; padding: 15px; border-bottom: 1px solid #3c3c3c; background-color: #333; }' +
                '.file-properties-header i { font-size: 24px; margin-right: 10px; color: #e6e6e6; }' +
                '.file-properties-header span { font-size: 16px; font-weight: bold; color: #fff; word-break: break-all; }' +
                '.file-properties-content { padding: 0; }' +
                '.file-properties-table { width: 100%; border-collapse: collapse; table-layout: fixed; }' +
                '.file-properties-table th { width: 30%; text-align: left; padding: 12px 15px; font-weight: bold; border-bottom: 1px solid #3c3c3c; background-color: #2d2d2d; color: #e6e6e6; }' +
                '.file-properties-table td { width: 70%; padding: 12px 15px; border-bottom: 1px solid #3c3c3c; word-break: break-all; }' +
                '.file-properties-table tr:last-child th, .file-properties-table tr:last-child td { border-bottom: none; }' +
                '.file-properties-table tr:hover th, .file-properties-table tr:hover td { background-color: #2a2a2a; }' +
                '.file-path { word-break: break-all; }' +
                '</style>';

            // 打开属性对话框
            layer.open({
                type: 1,
                title: '文件属性',
                area: ['450px', 'auto'],
                content: style + html,
                shadeClose: true,
                closeBtn: 1,
                anim: 1,
                skin: 'layui-layer-lan' // 使用蓝色主题
            });
        },
        error: function (xhr, status, error) {
            // 关闭加载层
            layer.close(loadingIndex);
            console.error("获取文件属性失败:", error);
            layer.msg('获取文件属性失败: ' + error);
        }
    });
}

// 导出对象
window.treeManager = {
    initZTree: initZTree,
    getLanguageByExt: getLanguageByExt,
    deleteNode: deleteNode,
    addFile: addFile,
    addFolder: addFolder,
    renameNode: renameNode,
    copyPath: copyPath,
    downloadFile: downloadFile,
    searchInFolder: searchInFolder,
    openInTerminal: openInTerminal,
    hideRMenu: hideRMenu,
    showFileProperties: showFileProperties,
    exportFiles: exportFiles,
    importFiles: importFiles,
    onClick: onClick,
    refurshTreeAndSelectNode: refurshTreeAndSelectNode,
    locateTreeByPath: locateTreeByPath
};
