!function(fe){var Ee={event:{DRAG:"ztree_drag",DROP:"ztree_drop",RENAME:"ztree_rename",DRAGMOVE:"ztree_dragmove"},id:{EDIT:"_edit",INPUT:"_input",REMOVE:"_remove"},move:{TYPE_INNER:"inner",TYPE_PREV:"prev",TYPE_NEXT:"next"},node:{CURSELECTED_EDIT:"curSelectedNode_Edit",TMPTARGET_TREE:"tmpTargetzTree",TMPTARGET_NODE:"tmpTargetNode"}},s={onHoverOverNode:function(e,t){var o=Re.getSetting(e.data.treeId),d=Re.getRoot(o);d.curHoverNode!=t&&s.onHoverOutNode(e),d.curHoverNode=t,be.addHoverDom(o,t)},onHoverOutNode:function(e,t){var o=Re.getSetting(e.data.treeId),d=Re.getRoot(o);d.curHoverNode&&!Re.isSelectedNode(o,d.curHoverNode)&&(be.removeTreeDom(o,d.curHoverNode),d.curHoverNode=null)},onMousedownNode:function(e,t){var o,d,Z=Re.getSetting(e.data.treeId),$=Re.getRoot(Z),J=Re.getRoots();if(2==e.button||!Z.edit.enable||!Z.edit.drag.isCopy&&!Z.edit.drag.isMove)return!0;var r=e.target,n=Re.getRoot(Z).curSelectedList,ee=[];if(Re.isSelectedNode(Z,t))for(o=0,d=n.length;o<d;o++){if(n[o].editNameFlag&&Ie.eqs(r.tagName,"input")&&null!==r.getAttribute("treeNode"+he.id.INPUT))return!0;if(ee.push(n[o]),ee[0].parentTId!==n[o].parentTId){ee=[t];break}}else ee=[t];be.editNodeBlur=!0,be.cancelCurEditNode(Z);var te,oe,de,re,ne,ae=fe(Z.treeObj.get(0).ownerDocument),ie=fe(Z.treeObj.get(0).ownerDocument.body),le=!1,se=Z,l=Z,ce=null,Ne=null,ue=null,ve=he.move.TYPE_INNER,ge=e.clientX,me=e.clientY,pe=(new Date).getTime();function s(e){if(0==$.dragFlag&&Math.abs(ge-e.clientX)<Z.edit.drag.minMoveSize&&Math.abs(me-e.clientY)<Z.edit.drag.minMoveSize)return!0;var t,o,d,r,n;if(ie.css("cursor","pointer"),0==$.dragFlag){if(0==Ie.apply(Z.callback.beforeDrag,[Z.treeId,ee],!0))return Te(e),!0;for(t=0,o=ee.length;t<o;t++)0==t&&($.dragNodeShowBefore=[]),d=ee[t],Re.nodeIsParent(Z,d)&&d.open?(be.expandCollapseNode(Z,d,!d.open),$.dragNodeShowBefore[d.tId]=!0):$.dragNodeShowBefore[d.tId]=!1;$.dragFlag=1,J.showHoverDom=!1,Ie.showIfameMask(Z,!0);var a=!0,i=-1;if(1<ee.length){var l=ee[0].parentTId?Re.nodeChildren(Z,ee[0].getParentNode()):Re.getNodes(Z);for(n=[],t=0,o=l.length;t<o;t++)if(void 0!==$.dragNodeShowBefore[l[t].tId]&&(a&&-1<i&&i+1!==t&&(a=!1),n.push(l[t]),i=t),ee.length===n.length){ee=n;break}}for(a&&(re=ee[0].getPreNode(),ne=ee[ee.length-1].getNextNode()),te=Pe("<ul class='zTreeDragUL'></ul>",Z),t=0,o=ee.length;t<o;t++)(d=ee[t]).editNameFlag=!1,be.selectNode(Z,d,0<t),be.removeTreeDom(Z,d),t>Z.edit.drag.maxShowNodeNum-1||((r=Pe("<li id='"+d.tId+"_tmp'></li>",Z)).append(Pe(d,he.id.A,Z).clone()),r.css("padding","0"),r.children("#"+d.tId+he.id.A).removeClass(he.node.CURSELECTED),te.append(r),t==Z.edit.drag.maxShowNodeNum-1&&(r=Pe("<li id='"+d.tId+"_moretmp'><a>  ...  </a></li>",Z),te.append(r)));te.attr("id",ee[0].tId+he.id.UL+"_tmp"),te.addClass(Z.treeObj.attr("class")),te.appendTo(ie),(oe=Pe("<span class='tmpzTreeMove_arrow'></span>",Z)).attr("id","zTreeMove_arrow_tmp"),oe.appendTo(ie),Z.treeObj.trigger(he.event.DRAG,[e,Z.treeId,ee])}if(1==$.dragFlag){if(de&&oe.attr("id")==e.target.id&&ue&&e.clientX+ae.scrollLeft()+2>fe("#"+ue+he.id.A,de).offset().left){var s=fe("#"+ue+he.id.A,de);e.target=0<s.length?s.get(0):e.target}else de&&(de.removeClass(he.node.TMPTARGET_TREE),ue&&fe("#"+ue+he.id.A,de).removeClass(he.node.TMPTARGET_NODE+"_"+he.move.TYPE_PREV).removeClass(he.node.TMPTARGET_NODE+"_"+Ee.move.TYPE_NEXT).removeClass(he.node.TMPTARGET_NODE+"_"+Ee.move.TYPE_INNER));ue=de=null,le=!1,se=Z;var c=Re.getSettings();for(var N in c)c[N].treeId&&c[N].edit.enable&&c[N].treeId!=Z.treeId&&(e.target.id==c[N].treeId||0<fe(e.target).parents("#"+c[N].treeId).length)&&(le=!0,se=c[N]);var u=ae.scrollTop(),v=ae.scrollLeft(),g=se.treeObj.offset(),m=se.treeObj.get(0).scrollHeight,p=se.treeObj.get(0).scrollWidth,T=e.clientY+u-g.top,f=se.treeObj.height()+g.top-e.clientY-u,E=e.clientX+v-g.left,I=se.treeObj.width()+g.left-e.clientX-v,h=T<Z.edit.drag.borderMax&&T>Z.edit.drag.borderMin,b=f<Z.edit.drag.borderMax&&f>Z.edit.drag.borderMin,R=E<Z.edit.drag.borderMax&&E>Z.edit.drag.borderMin,P=I<Z.edit.drag.borderMax&&I>Z.edit.drag.borderMin,C=T>Z.edit.drag.borderMin&&f>Z.edit.drag.borderMin&&E>Z.edit.drag.borderMin&&I>Z.edit.drag.borderMin,w=h&&se.treeObj.scrollTop()<=0,M=b&&se.treeObj.scrollTop()+se.treeObj.height()+10>=m,_=R&&se.treeObj.scrollLeft()<=0,O=P&&se.treeObj.scrollLeft()+se.treeObj.width()+10>=p;if(e.target&&Ie.isChildOrSelf(e.target,se.treeId)){for(var D=e.target;D&&D.tagName&&!Ie.eqs(D.tagName,"li")&&D.id!=se.treeId;)D=D.parentNode;var y=!0;for(t=0,o=ee.length;t<o;t++){if(d=ee[t],D.id===d.tId){y=!1;break}if(0<Pe(d,Z).find("#"+D.id).length){y=!1;break}}y&&e.target&&Ie.isChildOrSelf(e.target,D.id+he.id.A)&&(de=fe(D),ue=D.id)}d=ee[0],C&&Ie.isChildOrSelf(e.target,se.treeId)&&(!de&&(e.target.id==se.treeId||w||M||_||O)&&(le||!le&&d.parentTId)&&(de=se.treeObj),h?se.treeObj.scrollTop(se.treeObj.scrollTop()-10):b&&se.treeObj.scrollTop(se.treeObj.scrollTop()+10),R?se.treeObj.scrollLeft(se.treeObj.scrollLeft()-10):P&&se.treeObj.scrollLeft(se.treeObj.scrollLeft()+10),de&&de!=se.treeObj&&de.offset().left<se.treeObj.offset().left&&se.treeObj.scrollLeft(se.treeObj.scrollLeft()+de.offset().left-se.treeObj.offset().left)),te.css({top:e.clientY+u+3+"px",left:e.clientX+v+3+"px"});var k=0,S=0;if(de&&de.attr("id")!=se.treeId){var L=null==ue?null:Re.getNodeCache(se,ue),A=(e.ctrlKey||e.metaKey)&&Z.edit.drag.isMove&&Z.edit.drag.isCopy||!Z.edit.drag.isMove&&Z.edit.drag.isCopy,Y=!(!re||ue!==re.tId),x=!(!ne||ue!==ne.tId),j=d.parentTId&&d.parentTId==ue,z=(A||!x)&&Ie.apply(se.edit.drag.prev,[se.treeId,ee,L],!!se.edit.drag.prev),B=(A||!Y)&&Ie.apply(se.edit.drag.next,[se.treeId,ee,L],!!se.edit.drag.next),H=(A||!j)&&!(se.data.keep.leaf&&!Re.nodeIsParent(Z,L))&&Ie.apply(se.edit.drag.inner,[se.treeId,ee,L],!!se.edit.drag.inner);function F(){de=null,ue="",ve=he.move.TYPE_INNER,oe.css({display:"none"}),window.zTreeMoveTimer&&(clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null)}if(z||B||H){var V=fe("#"+ue+he.id.A,de),U=L.isLastNode?null:fe("#"+L.getNextNode().tId+he.id.A,de.next()),G=V.offset().top,X=V.offset().left,q=z?H?.25:B?.5:1:-1,W=B?H?.75:z?.5:0:-1,K=(e.clientY+u-G)/V.height();if((1==q||K<=q&&-.2<=K)&&z?(k=1-oe.width(),S=G-oe.height()/2,ve=he.move.TYPE_PREV):(0==W||W<=K&&K<=1.2)&&B?(k=1-oe.width(),S=null==U||Re.nodeIsParent(Z,L)&&L.open?G+V.height()-oe.height()/2:U.offset().top-oe.height()/2,ve=he.move.TYPE_NEXT):H?(k=5-oe.width(),S=G,ve=he.move.TYPE_INNER):F(),de&&(oe.css({display:"block",top:S+"px",left:X+k+"px"}),V.addClass(he.node.TMPTARGET_NODE+"_"+ve),ce==ue&&Ne==ve||(pe=(new Date).getTime()),L&&Re.nodeIsParent(Z,L)&&ve==he.move.TYPE_INNER)){var Q=!0;window.zTreeMoveTimer&&window.zTreeMoveTargetNodeTId!==L.tId?(clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null):window.zTreeMoveTimer&&window.zTreeMoveTargetNodeTId===L.tId&&(Q=!1),Q&&(window.zTreeMoveTimer=setTimeout(function(){ve==he.move.TYPE_INNER&&L&&Re.nodeIsParent(Z,L)&&!L.open&&(new Date).getTime()-pe>se.edit.drag.autoOpenTime&&Ie.apply(se.callback.beforeDragOpen,[se.treeId,L],!0)&&(be.switchNode(se,L),se.edit.drag.autoExpandTrigger&&se.treeObj.trigger(he.event.EXPAND,[se.treeId,L]))},se.edit.drag.autoOpenTime+50),window.zTreeMoveTargetNodeTId=L.tId)}}else F()}else ve=he.move.TYPE_INNER,de&&Ie.apply(se.edit.drag.inner,[se.treeId,ee,null],!!se.edit.drag.inner)?de.addClass(he.node.TMPTARGET_TREE):de=null,oe.css({display:"none"}),window.zTreeMoveTimer&&(clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null);ce=ue,Ne=ve,Z.treeObj.trigger(he.event.DRAGMOVE,[e,Z.treeId,ee])}return!1}function Te(d){if(window.zTreeMoveTimer&&(clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null),Ne=ce=null,ae.unbind("mousemove",s),ae.unbind("mouseup",Te),ae.unbind("selectstart",c),ie.css("cursor",""),de&&(de.removeClass(he.node.TMPTARGET_TREE),ue&&fe("#"+ue+he.id.A,de).removeClass(he.node.TMPTARGET_NODE+"_"+he.move.TYPE_PREV).removeClass(he.node.TMPTARGET_NODE+"_"+Ee.move.TYPE_NEXT).removeClass(he.node.TMPTARGET_NODE+"_"+Ee.move.TYPE_INNER)),Ie.showIfameMask(Z,!1),J.showHoverDom=!0,0!=$.dragFlag){var e,t,o;for(e=$.dragFlag=0,t=ee.length;e<t;e++)o=ee[e],Re.nodeIsParent(Z,o)&&$.dragNodeShowBefore[o.tId]&&!o.open&&(be.expandCollapseNode(Z,o,!o.open),delete $.dragNodeShowBefore[o.tId]);te&&te.remove(),oe&&oe.remove();var r=(d.ctrlKey||d.metaKey)&&Z.edit.drag.isMove&&Z.edit.drag.isCopy||!Z.edit.drag.isMove&&Z.edit.drag.isCopy;if(!r&&de&&ue&&ee[0].parentTId&&ue==ee[0].parentTId&&ve==he.move.TYPE_INNER&&(de=null),de){var n=null==ue?null:Re.getNodeCache(se,ue);if(0==Ie.apply(Z.callback.beforeDrop,[se.treeId,ee,n,ve,r],!0))return void be.selectNodes(l,ee);var a=r?Ie.clone(ee):ee;function i(){if(le){if(!r)for(var e=0,t=ee.length;e<t;e++)be.removeNode(Z,ee[e]);ve==he.move.TYPE_INNER?be.addNodes(se,n,-1,a):be.addNodes(se,n.getParentNode(),ve==he.move.TYPE_PREV?n.getIndex():n.getIndex()+1,a)}else if(r&&ve==he.move.TYPE_INNER)be.addNodes(se,n,-1,a);else if(r)be.addNodes(se,n.getParentNode(),ve==he.move.TYPE_PREV?n.getIndex():n.getIndex()+1,a);else if(ve!=he.move.TYPE_NEXT)for(e=0,t=a.length;e<t;e++)be.moveNode(se,n,a[e],ve,!1);else for(e=-1,t=a.length-1;e<t;t--)be.moveNode(se,n,a[t],ve,!1);be.selectNodes(se,a);var o=Pe(a[0],Z).get(0);be.scrollIntoView(Z,o),Z.treeObj.trigger(he.event.DROP,[d,se.treeId,a,n,ve,r])}ve==he.move.TYPE_INNER&&Ie.canAsync(se,n)?be.asyncNode(se,n,!1,i):i()}else be.selectNodes(l,ee),Z.treeObj.trigger(he.event.DROP,[d,Z.treeId,ee,null,null,null])}}function c(){return!1}return Ie.uCanDo(Z)&&ae.bind("mousemove",s),ae.bind("mouseup",Te),ae.bind("selectstart",c),!0}},e={tools:{getAbs:function(e){var t=e.getBoundingClientRect(),o=document.body.scrollTop+document.documentElement.scrollTop,d=document.body.scrollLeft+document.documentElement.scrollLeft;return[t.left+d,t.top+o]},inputFocus:function(e){e.get(0)&&(e.focus(),Ie.setCursorPosition(e.get(0),e.val().length))},inputSelect:function(e){e.get(0)&&(e.focus(),e.select())},setCursorPosition:function(e,t){if(e.setSelectionRange)e.focus(),e.setSelectionRange(t,t);else if(e.createTextRange){var o=e.createTextRange();o.collapse(!0),o.moveEnd("character",t),o.moveStart("character",t),o.select()}},showIfameMask:function(e,t){for(var o=Re.getRoot(e);0<o.dragMaskList.length;)o.dragMaskList[0].remove(),o.dragMaskList.shift();if(t)for(var d=Pe("iframe",e),r=0,n=d.length;r<n;r++){var a=d.get(r),i=Ie.getAbs(a),l=Pe("<div id='zTreeMask_"+r+"' class='zTreeMask' style='top:"+i[1]+"px; left:"+i[0]+"px; width:"+a.offsetWidth+"px; height:"+a.offsetHeight+"px;'></div>",e);l.appendTo(Pe("body",e)),o.dragMaskList.push(l)}}},view:{addEditBtn:function(e,t){if(!(t.editNameFlag||0<Pe(t,he.id.EDIT,e).length)&&Ie.apply(e.edit.showRenameBtn,[e.treeId,t],e.edit.showRenameBtn)){var o=Pe(t,he.id.A,e),d="<span class='"+he.className.BUTTON+" edit' id='"+t.tId+he.id.EDIT+"' title='"+Ie.apply(e.edit.renameTitle,[e.treeId,t],e.edit.renameTitle)+"' treeNode"+he.id.EDIT+" style='display:none;'></span>";o.append(d),Pe(t,he.id.EDIT,e).bind("click",function(){return Ie.uCanDo(e)&&0!=Ie.apply(e.callback.beforeEditName,[e.treeId,t],!0)&&be.editNode(e,t),!1}).show()}},addRemoveBtn:function(e,t){if(!(t.editNameFlag||0<Pe(t,he.id.REMOVE,e).length)&&Ie.apply(e.edit.showRemoveBtn,[e.treeId,t],e.edit.showRemoveBtn)){var o=Pe(t,he.id.A,e),d="<span class='"+he.className.BUTTON+" remove' id='"+t.tId+he.id.REMOVE+"' title='"+Ie.apply(e.edit.removeTitle,[e.treeId,t],e.edit.removeTitle)+"' treeNode"+he.id.REMOVE+" style='display:none;'></span>";o.append(d),Pe(t,he.id.REMOVE,e).bind("click",function(){return Ie.uCanDo(e)&&0!=Ie.apply(e.callback.beforeRemove,[e.treeId,t],!0)&&(be.removeNode(e,t),e.treeObj.trigger(he.event.REMOVE,[e.treeId,t])),!1}).bind("mousedown",function(e){return!0}).show()}},addHoverDom:function(e,t){Re.getRoots().showHoverDom&&(t.isHover=!0,e.edit.enable&&(be.addEditBtn(e,t),be.addRemoveBtn(e,t)),Ie.apply(e.view.addHoverDom,[e.treeId,t]))},cancelCurEditNode:function(e,t,o){var d=Re.getRoot(e),r=d.curEditNode;if(r){var n=d.curEditInput,a=t||(o?Re.nodeName(e,r):n.val());if(!1===Ie.apply(e.callback.beforeRename,[e.treeId,r,a,o],!0))return!1;Re.nodeName(e,r,a),Pe(r,he.id.A,e).removeClass(he.node.CURSELECTED_EDIT),n.unbind(),be.setNodeName(e,r),r.editNameFlag=!1,d.curEditNode=null,d.curEditInput=null,be.selectNode(e,r,!1),e.treeObj.trigger(he.event.RENAME,[e.treeId,r,o])}return d.noSelection=!0},editNode:function(t,e){var o=Re.getRoot(t);if(be.editNodeBlur=!1,Re.isSelectedNode(t,e)&&o.curEditNode==e&&e.editNameFlag)setTimeout(function(){Ie.inputFocus(o.curEditInput)},0);else{e.editNameFlag=!0,be.removeTreeDom(t,e),be.cancelCurEditNode(t),be.selectNode(t,e,!1),Pe(e,he.id.SPAN,t).html("<input type=text class='rename' id='"+e.tId+he.id.INPUT+"' treeNode"+he.id.INPUT+" >");var d=Pe(e,he.id.INPUT,t);d.attr("value",Re.nodeName(t,e)),t.edit.editNameSelectAll?Ie.inputSelect(d):Ie.inputFocus(d),d.bind("blur",function(e){be.editNodeBlur||be.cancelCurEditNode(t)}).bind("keydown",function(e){"13"==e.keyCode?(be.editNodeBlur=!0,be.cancelCurEditNode(t)):"27"==e.keyCode&&be.cancelCurEditNode(t,null,!0)}).bind("click",function(e){return!1}).bind("dblclick",function(e){return!1}),Pe(e,he.id.A,t).addClass(he.node.CURSELECTED_EDIT),o.curEditInput=d,o.noSelection=!1,o.curEditNode=e}},moveNode:function(e,t,o,d,r,n){var a=Re.getRoot(e);if(t!=o&&(!e.data.keep.leaf||!t||Re.nodeIsParent(e,t)||d!=he.move.TYPE_INNER)){var i=o.parentTId?o.getParentNode():a,l=null===t||t==a;l&&null===t&&(t=a),l&&(d=he.move.TYPE_INNER);var s,c,N=t.parentTId?t.getParentNode():a;if(d!=he.move.TYPE_PREV&&d!=he.move.TYPE_NEXT&&(d=he.move.TYPE_INNER),d==he.move.TYPE_INNER&&(l?o.parentTId=null:(Re.nodeIsParent(e,t)||(Re.nodeIsParent(e,t,!0),t.open=!!t.open,be.setNodeLineIcos(e,t)),o.parentTId=t.tId)),l)c=s=e.treeObj;else{if(n||d!=he.move.TYPE_INNER?n||be.expandCollapseNode(e,t.getParentNode(),!0,!1):be.expandCollapseNode(e,t,!0,!1),s=Pe(t,e),c=Pe(t,he.id.UL,e),s.get(0)&&!c.get(0)){var u=[];be.makeUlHtml(e,t,u,""),s.append(u.join(""))}c=Pe(t,he.id.UL,e)}var v=Pe(o,e);v.get(0)?s.get(0)||v.remove():v=be.appendNodes(e,o.level,[o],null,-1,!1,!0).join(""),c.get(0)&&d==he.move.TYPE_INNER?c.append(v):s.get(0)&&d==he.move.TYPE_PREV?s.before(v):s.get(0)&&d==he.move.TYPE_NEXT&&s.after(v);var g,m,p=-1,T=0,f=null,E=null,I=o.level,h=Re.nodeChildren(e,i),b=Re.nodeChildren(e,N),R=Re.nodeChildren(e,t);if(o.isFirstNode)p=0,1<h.length&&((f=h[1]).isFirstNode=!0);else if(o.isLastNode)(f=h[(p=h.length-1)-1]).isLastNode=!0;else for(g=0,m=h.length;g<m;g++)if(h[g].tId==o.tId){p=g;break}if(0<=p&&h.splice(p,1),d!=he.move.TYPE_INNER)for(g=0,m=b.length;g<m;g++)b[g].tId==t.tId&&(T=g);if(d==he.move.TYPE_INNER?(0<(R=R||Re.nodeChildren(e,t,[])).length&&((E=R[R.length-1]).isLastNode=!1),R.splice(R.length,0,o),o.isLastNode=!0,o.isFirstNode=1==R.length):t.isFirstNode&&d==he.move.TYPE_PREV?(b.splice(T,0,o),(E=t).isFirstNode=!1,o.parentTId=t.parentTId,o.isFirstNode=!0,o.isLastNode=!1):t.isLastNode&&d==he.move.TYPE_NEXT?(b.splice(T+1,0,o),(E=t).isLastNode=!1,o.parentTId=t.parentTId,o.isFirstNode=!1,o.isLastNode=!0):(d==he.move.TYPE_PREV?b.splice(T,0,o):b.splice(T+1,0,o),o.parentTId=t.parentTId,o.isFirstNode=!1,o.isLastNode=!1),Re.fixPIdKeyValue(e,o),Re.setSonNodeLevel(e,o.getParentNode(),o),be.setNodeLineIcos(e,o),be.repairNodeLevelClass(e,o,I),!e.data.keep.parent&&h.length<1){Re.nodeIsParent(e,i,!1),i.open=!1;var P=Pe(i,he.id.UL,e),C=Pe(i,he.id.SWITCH,e),w=Pe(i,he.id.ICON,e);be.replaceSwitchClass(i,C,he.folder.DOCU),be.replaceIcoClass(i,w,he.folder.DOCU),P.css("display","none")}else f&&be.setNodeLineIcos(e,f);E&&be.setNodeLineIcos(e,E),e.check&&e.check.enable&&be.repairChkClass&&(be.repairChkClass(e,i),be.repairParentChkClassWithSelf(e,i),i!=o.parent&&be.repairParentChkClassWithSelf(e,o)),n||be.expandCollapseParentNode(e,o.getParentNode(),!0,r)}},removeEditBtn:function(e,t){Pe(t,he.id.EDIT,e).unbind().remove()},removeRemoveBtn:function(e,t){Pe(t,he.id.REMOVE,e).unbind().remove()},removeTreeDom:function(e,t){t.isHover=!1,be.removeEditBtn(e,t),be.removeRemoveBtn(e,t),Ie.apply(e.view.removeHoverDom,[e.treeId,t])},repairNodeLevelClass:function(e,t,o){if(o!==t.level){var d=Pe(t,e),r=Pe(t,he.id.A,e),n=Pe(t,he.id.UL,e),a=he.className.LEVEL+o,i=he.className.LEVEL+t.level;d.removeClass(a),d.addClass(i),r.removeClass(a),r.addClass(i),n.removeClass(a),n.addClass(i)}},selectNodes:function(e,t){for(var o=0,d=t.length;o<d;o++)be.selectNode(e,t[o],0<o)}},event:{},data:{setSonNodeLevel:function(e,t,o){if(o){var d=Re.nodeChildren(e,o),r=o.level;if(o.level=t?t.level+1:0,be.repairNodeLevelClass(e,o,r),d)for(var n=0,a=d.length;n<a;n++)d[n]&&Re.setSonNodeLevel(e,o,d[n])}}}};fe.extend(!0,fe.fn.zTree.consts,Ee),fe.extend(!0,fe.fn.zTree._z,e);var t=fe.fn.zTree,Ie=t._z.tools,he=t.consts,be=t._z.view,Re=t._z.data,Pe=(t._z.event,Ie.$);Re.exSetting({edit:{enable:!1,editNameSelectAll:!1,showRemoveBtn:!0,showRenameBtn:!0,removeTitle:"remove",renameTitle:"rename",drag:{autoExpandTrigger:!1,isCopy:!0,isMove:!0,prev:!0,next:!0,inner:!0,minMoveSize:5,borderMax:10,borderMin:-5,maxShowNodeNum:5,autoOpenTime:500}},view:{addHoverDom:null,removeHoverDom:null},callback:{beforeDrag:null,beforeDragOpen:null,beforeDrop:null,beforeEditName:null,beforeRename:null,onDrag:null,onDragMove:null,onDrop:null,onRename:null}}),Re.addInitBind(function(i){var e=i.treeObj,t=he.event;e.bind(t.RENAME,function(e,t,o,d){Ie.apply(i.callback.onRename,[e,t,o,d])}),e.bind(t.DRAG,function(e,t,o,d){Ie.apply(i.callback.onDrag,[t,o,d])}),e.bind(t.DRAGMOVE,function(e,t,o,d){Ie.apply(i.callback.onDragMove,[t,o,d])}),e.bind(t.DROP,function(e,t,o,d,r,n,a){Ie.apply(i.callback.onDrop,[t,o,d,r,n,a])})}),Re.addInitUnBind(function(e){var t=e.treeObj,o=he.event;t.unbind(o.RENAME),t.unbind(o.DRAG),t.unbind(o.DRAGMOVE),t.unbind(o.DROP)}),Re.addInitCache(function(e){}),Re.addInitNode(function(e,t,o,d,r,n,a){o&&(o.isHover=!1,o.editNameFlag=!1)}),Re.addInitProxy(function(e){var t=e.target,o=Re.getSetting(e.data.treeId),d=e.relatedTarget,r="",n=null,a="",i=null,l=null;if(Ie.eqs(e.type,"mouseover")?(l=Ie.getMDom(o,t,[{tagName:"a",attrName:"treeNode"+he.id.A}]))&&(r=Ie.getNodeMainDom(l).id,a="hoverOverNode"):Ie.eqs(e.type,"mouseout")?(l=Ie.getMDom(o,d,[{tagName:"a",attrName:"treeNode"+he.id.A}]))||(r="remove",a="hoverOutNode"):Ie.eqs(e.type,"mousedown")&&(l=Ie.getMDom(o,t,[{tagName:"a",attrName:"treeNode"+he.id.A}]))&&(r=Ie.getNodeMainDom(l).id,a="mousedownNode"),0<r.length)switch(n=Re.getNodeCache(o,r),a){case"mousedownNode":i=s.onMousedownNode;break;case"hoverOverNode":i=s.onHoverOverNode;break;case"hoverOutNode":i=s.onHoverOutNode}return{stop:!1,node:n,nodeEventType:a,nodeEventCallback:i,treeEventType:"",treeEventCallback:null}}),Re.addInitRoot(function(e){var t=Re.getRoot(e),o=Re.getRoots();t.curEditNode=null,t.curEditInput=null,t.curHoverNode=null,t.dragFlag=0,t.dragNodeShowBefore=[],t.dragMaskList=new Array,o.showHoverDom=!0}),Re.addZTreeTools(function(l,e){e.cancelEditName=function(e){Re.getRoot(this.setting).curEditNode&&be.cancelCurEditNode(this.setting,e||null,!0)},e.copyNode=function(e,t,o,d){if(!t)return null;var r=Re.nodeIsParent(l,e);if(e&&!r&&this.setting.data.keep.leaf&&o===he.move.TYPE_INNER)return null;var n=this,a=Ie.clone(t);if(e||(e=null,o=he.move.TYPE_INNER),o==he.move.TYPE_INNER){function i(){be.addNodes(n.setting,e,-1,[a],d)}Ie.canAsync(this.setting,e)?be.asyncNode(this.setting,e,d,i):i()}else be.addNodes(this.setting,e.parentNode,-1,[a],d),be.moveNode(this.setting,e,a,o,!1,d);return a},e.editName=function(e){e&&e.tId&&e===Re.getNodeCache(this.setting,e.tId)&&(e.parentTId&&be.expandCollapseParentNode(this.setting,e.getParentNode(),!0),be.editNode(this.setting,e))},e.moveNode=function(e,t,o,d){if(!t)return t;var r=Re.nodeIsParent(l,e);if(e&&!r&&this.setting.data.keep.leaf&&o===he.move.TYPE_INNER)return null;if(e&&(t.parentTId==e.tId&&o==he.move.TYPE_INNER||0<Pe(t,this.setting).find("#"+e.tId).length))return null;e=e||null;var n=this;function a(){be.moveNode(n.setting,e,t,o,!1,d)}return Ie.canAsync(this.setting,e)&&o===he.move.TYPE_INNER?be.asyncNode(this.setting,e,d,a):a(),t},e.setEditable=function(e){return this.setting.edit.enable=e,this.refresh()}});var n=be.cancelPreSelectedNode;be.cancelPreSelectedNode=function(e,t){for(var o=Re.getRoot(e).curSelectedList,d=0,r=o.length;d<r&&(t&&t!==o[d]||(be.removeTreeDom(e,o[d]),!t));d++);n&&n.apply(be,arguments)};var a=be.createNodes;be.createNodes=function(e,t,o,d,r){a&&a.apply(be,arguments),o&&be.repairParentChkClassWithSelf&&be.repairParentChkClassWithSelf(e,d)};var o=be.makeNodeUrl;be.makeNodeUrl=function(e,t){return e.edit.enable?null:o.apply(be,arguments)};var d=be.removeNode;be.removeNode=function(e,t){var o=Re.getRoot(e);o.curEditNode===t&&(o.curEditNode=null),d&&d.apply(be,arguments)};var r=be.selectNode;be.selectNode=function(e,t,o){var d=Re.getRoot(e);return(!Re.isSelectedNode(e,t)||d.curEditNode!=t||!t.editNameFlag)&&(r&&r.apply(be,arguments),be.addHoverDom(e,t),!0)};var i=Ie.uCanDo;Ie.uCanDo=function(e,t){var o=Re.getRoot(e);return!(!t||!(Ie.eqs(t.type,"mouseover")||Ie.eqs(t.type,"mouseout")||Ie.eqs(t.type,"mousedown")||Ie.eqs(t.type,"mouseup")))||(o.curEditNode&&(be.editNodeBlur=!1,o.curEditInput.focus()),!o.curEditNode&&(!i||i.apply(be,arguments)))}}(jQuery);