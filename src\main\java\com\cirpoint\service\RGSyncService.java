package com.cirpoint.service;

import javax.imageio.ImageIO;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.util.Util;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * &#064;date  2025年3月4日15:59:19
 * &#064;description 处理热管照片同步业务的service
 **/
@Slf4j
@Service
public class RGSyncService extends ApplicationConfig {

	/**
	 * 生成模拟测试文件
	 * 文件命名规则：日期-序号-[型号](编号1,编号2,编号3,)-其他信息.BMP
	 * 示例：20230101-000001-[RG_01](01,02,03)-测试.BMP
	 *
	 * @return 生成的文件数量
	 */
	public int generateMockFiles() {
		// 确保目标目录存在
		File targetDir = new File(rgSyncPath);
		if (!targetDir.exists()) {
			targetDir.mkdirs();
		}

		// 获取网络图片URL列表
		List<String> imageUrls = fetchImageUrls();

		// 生成文件计数器
		AtomicInteger counter = new AtomicInteger(0);

		// 生成100个文件
		for (int i = 0; i < 100; i++) {
			try {
				// 构建文件名
				String fileName = generateFileName();

				// 获取图片URL（循环使用列表中的URL）
				String imageUrl = imageUrls.get(i % imageUrls.size());

				// 下载并保存图片为BMP格式
				boolean success = downloadAndSaveImage(imageUrl, new File(targetDir, fileName));

				if (success) {
					counter.incrementAndGet();
					log.info("成功生成模拟文件: {}", fileName);
				}
			} catch (Exception e) {
				log.error("生成模拟文件失败", e);
			}
		}

		log.info("共生成{}个模拟文件", counter.get());
		return counter.get();
	}

	/**
	 * 生成符合规则的文件名
	 * 格式：日期-序号-[型号](编号1,编号2,编号3,)-其他信息.BMP
	 *
	 * @return 生成的文件名
	 */
	private String generateFileName() {
		// 1. 生成随机日期 (yyyyMMdd格式)
		LocalDate randomDate = LocalDate.now().minusDays(RandomUtil.randomInt(0, 365));
		String dateStr = randomDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

		// 2. 生成6位序号
		String serialNumber = String.format("%06d", RandomUtil.randomInt(1, 999999));

		// 3. 生成型号 (RG_01 ~ RG_10)
		String modelNumber = String.format("RG_%02d", RandomUtil.randomInt(1, 10));

		// 4. 生成1-3个编号 (01-99)
		int codeCount = RandomUtil.randomInt(1, 4); // 1到3个编号
		List<String> codes = new ArrayList<>();
		for (int i = 0; i < codeCount; i++) {
			codes.add(String.format("%02d", RandomUtil.randomInt(1, 99)));
		}
		String codeList = String.join(",", codes);

		// 5. 生成随机描述 (1-6个汉字)
		String[] chineseChars = {"测试", "样品", "热管", "照片", "图像", "文件", "产品", "型号", "规格", "检验", "质检", "合格"};
		int descLength = RandomUtil.randomInt(1, 4); // 1-3个词，最多6个汉字
		StringBuilder desc = new StringBuilder();
		for (int i = 0; i < descLength; i++) {
			desc.append(chineseChars[RandomUtil.randomInt(0, chineseChars.length - 1)]);
		}

		// 组合文件名
		return String.format("%s-%s-[%s](%s)-%s.BMP",
				dateStr, serialNumber, modelNumber, codeList, desc.toString());
	}

	/**
	 * 从互联网获取图片URL列表
	 *
	 * @return 图片URL列表
	 */
	private List<String> fetchImageUrls() {
		List<String> imageUrls = new ArrayList<>();

		try {
			// 使用unsplash作为图片源
			Document doc = Jsoup.connect("https://unsplash.com/s/photos/technology").get();
			Elements images = doc.select("img[srcset]");

			for (Element img : images) {
				String srcset = img.attr("srcset");
				if (StrUtil.isNotBlank(srcset)) {
					// 从srcset中提取第一个URL
					String[] parts = srcset.split(",");
					if (parts.length > 0) {
						String[] urlParts = parts[0].trim().split(" ");
						if (urlParts.length > 0) {
							imageUrls.add(urlParts[0]);
						}
					}
				}
			}

			// 如果没有找到图片，使用备用图片源
			if (imageUrls.isEmpty()) {
				// 添加一些备用的图片URL
				imageUrls.add("https://picsum.photos/800/600");
				imageUrls.add("https://picsum.photos/800/600?random=1");
				imageUrls.add("https://picsum.photos/800/600?random=2");
				imageUrls.add("https://picsum.photos/800/600?random=3");
				imageUrls.add("https://picsum.photos/800/600?random=4");
			}
		} catch (Exception e) {
			log.error("获取网络图片失败，使用备用图片源", e);
			// 添加备用图片源
			imageUrls.add("https://picsum.photos/800/600");
			imageUrls.add("https://picsum.photos/800/600?random=1");
			imageUrls.add("https://picsum.photos/800/600?random=2");
			imageUrls.add("https://picsum.photos/800/600?random=3");
			imageUrls.add("https://picsum.photos/800/600?random=4");
		}

		log.info("获取到{}个图片URL", imageUrls.size());
		return imageUrls;
	}

	/**
	 * 下载图片并保存为BMP格式
	 *
	 * @param imageUrl   图片URL
	 * @param targetFile 目标文件
	 * @return 是否成功
	 */
	private boolean downloadAndSaveImage(String imageUrl, File targetFile) {
		try {
			// 下载图片
			URL url = new URL(imageUrl);
			BufferedImage image = ImageIO.read(url);

			if (image == null) {
				log.error("无法读取图片: {}", imageUrl);
				return false;
			}

			// 保存为BMP格式
			return ImageIO.write(image, "BMP", targetFile);
		} catch (IOException e) {
			log.error("下载或保存图片失败: {}", imageUrl, e);
			return false;
		}
	}

	/**
	 * 从文件名中提取型号和编号信息
	 * 文件名格式：日期-序号-[型号](编号1,编号2,编号3,)-其他信息.BMP
	 * 示例：20230101-000001-[RG_01](01,02,03)-测试.BMP
	 *
	 * @param fileName 文件名
	 * @return 包含型号和编号信息的JSONObject，key为"model"和"codes"
	 */
	public JSONObject extractModelAndCodes(String fileName) {
		JSONObject result = new JSONObject();

		try {
			// 提取型号的正则表达式
			Pattern modelPattern = Pattern.compile("\\[(.*?)]");
			Matcher modelMatcher = modelPattern.matcher(fileName);

			if (modelMatcher.find()) {
				String model = modelMatcher.group(1);
				result.set("model", model);
				log.debug("从文件 {} 中提取到型号: {}", fileName, model);
			} else {
				log.warn("无法从文件 {} 中提取型号", fileName);
				result.set("model", "");
			}

			// 提取编号列表的正则表达式
			Pattern codesPattern = Pattern.compile("\\((.*?)\\)");
			Matcher codesMatcher = codesPattern.matcher(fileName);

			if (codesMatcher.find()) {
				String codesStr = codesMatcher.group(1);
				List<String> codes = Arrays.asList(codesStr.split(","));
				result.set("codes", codes);
				log.debug("从文件 {} 中提取到编号列表: {}", fileName, codes);
			} else {
				log.warn("无法从文件 {} 中提取编号列表", fileName);
				result.set("codes", new ArrayList<String>());
			}
		} catch (Exception e) {
			log.error("从文件名提取信息时发生错误: {}", fileName, e);
			result.set("model", "");
			result.set("codes", new ArrayList<String>());
		}

		return result;
	}

	@SuppressWarnings("null")
	public JSONArray collectRgPhoto(String processTreeId) {
		JSONArray resultFiles = new JSONArray();
		try {
			// 确保路径编码正确
			Path dirPath = Paths.get(rgSyncPath);

			//查询所有的热管型号
			JSONArray allRgCode = Util.postTwxForObject("Thing.Fn.RG", "QueryRgCode",
					new JSONObject().set("processTreeId", processTreeId)).getJSONArray("data");

			if (allRgCode.isEmpty()) {
				log.warn("未查询到热管型号");
				return resultFiles;
			}

			// 获取目录下所有文件
			File dir = dirPath.toFile();
			if (!dir.exists()) {
				log.warn("目录不存在: {}", dirPath);
				return resultFiles;
			}
			if (!dir.isDirectory()) {
				log.warn("路径不是目录: {}", dirPath);
				return resultFiles;
			}

			File[] files = dir.listFiles();
			if (files == null) {
				log.warn("目录为空: {}", dirPath);
				return resultFiles;
			}
			log.info("扫描目录 {} 中的文件，共发现 {} 个文件", dirPath, files.length);


			// 遍历处理文件
			for (File file : files) {
				String decodedFileName = file.getName();
				log.info("开始处理文件: {}", decodedFileName);

				// 检查文件格式
				String fileExt = FileNameUtil.extName(decodedFileName).toLowerCase();
				if (!"bmp".equals(fileExt)) {
					log.info("跳过不支持的文件格式: {}, 文件: {}", fileExt, decodedFileName);
					continue;
				}

				// 获取热管型号
				JSONObject modelAndCodes = extractModelAndCodes(decodedFileName);
				String rgCode = modelAndCodes.getStr("model");
				JSONArray codes = modelAndCodes.getJSONArray("codes");

				// 检查rg型号是否在allRgCode中
				boolean found = false;
				JSONObject matchedRg = null;
				for (int i = 0; i < allRgCode.size(); i++) {
					JSONObject dlObj = allRgCode.getJSONObject(i);
					if (rgCode.equals(dlObj.getStr("RG_CODE"))) {
						found = true;
						matchedRg = dlObj;
						break;
					}
				}
				if (!found) {
					log.warn("未找到匹配的热管型号记录: {}", rgCode);
					continue;
				}

				//复制文件到文件仓库
				// 创建月份目录
				String month = new SimpleDateFormat("yyyy-MM").format(new Date());
				String monthPath = fileUploadPath + File.separator + month;
				FileUtil.mkdir(monthPath);
				// 生成唯一文件名
				String uuid = UUID.randomUUID().toString();
				String filePath = monthPath + File.separator + uuid;

				FileUtil.copy(file, new File(filePath), true);

				// 构建结果对象
				JSONObject fileInfo = new JSONObject();
				fileInfo.set("absolutePath", file.getAbsolutePath());
				fileInfo.set("filePath", "//" + month + "//" + uuid);
				fileInfo.set("fileName", FileNameUtil.mainName(file));
				fileInfo.set("fileFormat", fileExt);
				fileInfo.set("treeId", matchedRg.getStr("TREEID"));
				fileInfo.set("rgCode", matchedRg.getStr("RG_CODE"));
				fileInfo.set("codes", codes);
				resultFiles.add(fileInfo);
				log.info("成功处理文件: {}, 信息: {}", decodedFileName, fileInfo);
			}
			log.info("热管照片收集完成，共处理 {} 个有效文件: {}", resultFiles.size(), resultFiles);
		} catch (Exception e) {
			log.error("热管照片收集时发生错误", e);
		}
		return resultFiles;
	}

}
