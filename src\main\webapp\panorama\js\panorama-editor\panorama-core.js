/**
 * 全景图热点编辑系统 - 核心模块
 * 
 * <AUTHOR>
 * @date 2025-06-10
 * @description 提供核心配置、全局变量管理和工具函数
 */

/**
 * 全景图编辑器核心模块
 * 使用IIFE模式确保ES5兼容性和命名空间隔离
 */
var PanoramaCore = (function() {
    'use strict';
    
    // ==================== 全局变量定义 ====================
    var globalVars = {
        currentTaskId: null,
        hotspotTable: null,
        deviceList: [],
        currentNodeId: null
    };
    
    // ==================== 配置常量 ====================
    var CONFIG = {
        // 表格配置
        TABLE: {
            DEFAULT_LIMIT: 25,
            LIMITS: [25, 50, 100],
            HEIGHT: 'full-45'
        },
        
        // 任务状态
        TASK_STATUS: {
            CREATING: 0,
            COMPLETED: 1,
            EXPORTED: 2
        },
        
        // 界面尺寸限制
        UI: {
            MIN_LEFT_WIDTH: 300,
            MIN_RIGHT_WIDTH: 300,
            MAX_LEFT_WIDTH_RATIO: 0.6
        }
    };
    
    // ==================== 工具函数 ====================
    
    /**
     * 获取状态文本
     * @param {number} status 状态码
     * @returns {string} 状态文本
     */
    function getStatusText(status) {
        switch (status) {
            case CONFIG.TASK_STATUS.CREATING: 
                return '创建中';
            case CONFIG.TASK_STATUS.COMPLETED: 
                return '已完成';
            case CONFIG.TASK_STATUS.EXPORTED: 
                return '已导出';
            default: 
                return '未知';
        }
    }
    
    /**
     * 格式化时间戳
     * @param {number|string} timestamp 时间戳
     * @returns {string} 格式化后的时间字符串
     */
    function formatTimestamp(timestamp) {
        if (!timestamp) return '-';

        // 如果是13位时间戳，直接使用；如果是10位，需要乘以1000
        var time = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;
        var date = new Date(time);

        if (isNaN(date.getTime())) return '-';

        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        var hours = String(date.getHours()).padStart(2, '0');
        var minutes = String(date.getMinutes()).padStart(2, '0');
        var seconds = String(date.getSeconds()).padStart(2, '0');

        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
    }
    
    /**
     * 获取当前用户名
     * @returns {string} 用户名
     */
    function getCurrentUsername() {
        return sessionStorage.getItem('username') || 'adm';
    }
    
    /**
     * 检查用户是否已登录
     * @returns {boolean} 是否已登录
     */
    function isUserLoggedIn() {
        var username = getCurrentUsername();
        return username && username !== 'adm'; // 'adm'是开发阶段的默认值
    }
    
    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    function generateUniqueId() {
        return 'panorama_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * 安全的JSON解析
     * @param {string} jsonString JSON字符串
     * @param {*} defaultValue 默认值
     * @returns {*} 解析结果或默认值
     */
    function safeJsonParse(jsonString, defaultValue) {
        try {
            return JSON.parse(jsonString);
        } catch (e) {
            return defaultValue || null;
        }
    }
    
    /**
     * 防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} wait 等待时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    function debounce(func, wait) {
        var timeout;
        return function() {
            var context = this;
            var args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                func.apply(context, args);
            }, wait);
        };
    }
    
    /**
     * 节流函数
     * @param {Function} func 要节流的函数
     * @param {number} limit 限制时间（毫秒）
     * @returns {Function} 节流后的函数
     */
    function throttle(func, limit) {
        var inThrottle;
        return function() {
            var args = arguments;
            var context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(function() {
                    inThrottle = false;
                }, limit);
            }
        };
    }
    
    // ==================== 全局变量访问器 ====================
    
    /**
     * 获取当前任务ID
     * @returns {string|null} 当前任务ID
     */
    function getCurrentTaskId() {
        return globalVars.currentTaskId;
    }
    
    /**
     * 设置当前任务ID
     * @param {string|null} taskId 任务ID
     */
    function setCurrentTaskId(taskId) {
        globalVars.currentTaskId = taskId;
    }
    
    /**
     * 获取热点表格实例
     * @returns {Object|null} 热点表格实例
     */
    function getHotspotTable() {
        return globalVars.hotspotTable;
    }
    
    /**
     * 设置热点表格实例
     * @param {Object} tableInstance 表格实例
     */
    function setHotspotTable(tableInstance) {
        globalVars.hotspotTable = tableInstance;
    }
    
    /**
     * 获取设备列表
     * @returns {Array} 设备列表
     */
    function getDeviceList() {
        return globalVars.deviceList || [];
    }
    
    /**
     * 设置设备列表
     * @param {Array} devices 设备列表
     */
    function setDeviceList(devices) {
        globalVars.deviceList = devices || [];
    }
    
    /**
     * 获取当前节点ID
     * @returns {string|null} 当前节点ID
     */
    function getCurrentNodeId() {
        return globalVars.currentNodeId;
    }
    
    /**
     * 设置当前节点ID
     * @param {string|null} nodeId 节点ID
     */
    function setCurrentNodeId(nodeId) {
        globalVars.currentNodeId = nodeId;
    }
    
    // ==================== 公共API ====================
    return {
        // 配置常量
        CONFIG: CONFIG,
        
        // 工具函数
        getStatusText: getStatusText,
        formatTimestamp: formatTimestamp,
        getCurrentUsername: getCurrentUsername,
        isUserLoggedIn: isUserLoggedIn,
        generateUniqueId: generateUniqueId,
        safeJsonParse: safeJsonParse,
        debounce: debounce,
        throttle: throttle,
        
        // 全局变量访问器
        getCurrentTaskId: getCurrentTaskId,
        setCurrentTaskId: setCurrentTaskId,
        getHotspotTable: getHotspotTable,
        setHotspotTable: setHotspotTable,
        getDeviceList: getDeviceList,
        setDeviceList: setDeviceList,
        getCurrentNodeId: getCurrentNodeId,
        setCurrentNodeId: setCurrentNodeId
    };
})();

// ==================== 向后兼容性支持 ====================
// 保持原有的全局变量访问方式，确保现有代码不受影响

// 全局变量的getter/setter，保持向后兼容
Object.defineProperty(window, 'currentTaskId', {
    get: function() { return PanoramaCore.getCurrentTaskId(); },
    set: function(value) { PanoramaCore.setCurrentTaskId(value); }
});

Object.defineProperty(window, 'hotspotTable', {
    get: function() { return PanoramaCore.getHotspotTable(); },
    set: function(value) { PanoramaCore.setHotspotTable(value); }
});

Object.defineProperty(window, 'deviceList', {
    get: function() { return PanoramaCore.getDeviceList(); },
    set: function(value) { PanoramaCore.setDeviceList(value); }
});

Object.defineProperty(window, 'currentNodeId', {
    get: function() { return PanoramaCore.getCurrentNodeId(); },
    set: function(value) { PanoramaCore.setCurrentNodeId(value); }
});

// 工具函数的全局访问，保持向后兼容
window.getStatusText = PanoramaCore.getStatusText;
window.formatTimestamp = PanoramaCore.formatTimestamp;
