package com.cirpoint.controller;

import com.cirpoint.service.AitScreenService;
import com.cirpoint.util.FileDownloadUtil;
import java.io.File;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * AIT看板导出控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/aitScreen")
public class AitScreenController {

    @Autowired
    private AitScreenService aitScreenService;

    /**
     * 导出AIT看板数据为Excel
     *
     * @param treeId    型号ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param fileType  文件类型
     * @return Excel文件响应
     */
    @PostMapping("/exportExcel")
    public ResponseEntity<?> exportExcel(
            @RequestParam(value = "treeId", required = false, defaultValue = "-1") String treeId,
            @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
            @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
            @RequestParam(value = "fileType", required = false, defaultValue = "") String fileType) {

        try {
            log.info("开始导出AIT看板数据，treeId={}, startDate={}, endDate={}, fileType={}", treeId, startDate, endDate, fileType);

            // 调用服务导出Excel
            File excelFile = aitScreenService.exportExcel(treeId, startDate, endDate, fileType);

            log.info("导出AIT看板数据成功");
            return FileDownloadUtil.fileResponseAndDelete(excelFile);
        } catch (Exception e) {
            log.error("导出AIT看板数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("导出失败，原因：" + e.getMessage());
        }
    }

    /**
     * 导出产品交接单数据为Excel
     *
     * @param treeId     型号ID
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param groupType  分组类型 (ISCERTIFICATE、ISLUOHAN、ISSUBMIT)
     * @param seriesName 系列名称
     * @param unit       单位名称 (可选)
     * @return Excel文件响应
     */
    @PostMapping("/exportSubmitExcel")
    public ResponseEntity<?> exportSubmitExcel(
            @RequestParam(value = "treeId", required = false, defaultValue = "-1") String treeId,
            @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
            @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
            @RequestParam(value = "groupType", required = false, defaultValue = "") String groupType,
            @RequestParam(value = "seriesName", required = false, defaultValue = "") String seriesName,
            @RequestParam(value = "unit", required = false, defaultValue = "") String unit) {

        try {
            log.info("开始导出产品交接单数据，treeId={}, startDate={}, endDate={}, groupType={}, seriesName={}, unit={}",
                    treeId, startDate, endDate, groupType, seriesName, unit);

            // 调用服务导出Excel
            File excelFile = aitScreenService.exportSubmitExcel(treeId, startDate, endDate, groupType, seriesName, unit);

            log.info("导出产品交接单数据成功");
            return FileDownloadUtil.fileResponseAndDelete(excelFile);
        } catch (Exception e) {
            log.error("导出产品交接单数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("导出失败，原因：" + e.getMessage());
        }
    }

    /**
     * 导出现场问题处理单数据为Excel
     *
     * @param treeId    型号ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param status    状态筛选 (all-全部, finished-已完成, unfinished-未完成)
     * @return Excel文件响应
     */
    @PostMapping("/exportProblemExcel")
    public ResponseEntity<?> exportProblemExcel(
            @RequestParam(value = "treeId", required = false, defaultValue = "-1") String treeId,
            @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
            @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
            @RequestParam(value = "status", required = false, defaultValue = "all") String status) {

        try {
            log.info("开始导出现场问题处理单数据，treeId={}, startDate={}, endDate={}, status={}",
                    treeId, startDate, endDate, status);

            // 调用服务导出Excel
            File excelFile = aitScreenService.exportProblemExcel(treeId, startDate, endDate, status);

            log.info("导出现场问题处理单数据成功");
            return FileDownloadUtil.fileResponseAndDelete(excelFile);
        } catch (Exception e) {
            log.error("导出现场问题处理单数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("导出失败，原因：" + e.getMessage());
        }
    }

    /**
     * 导出现场临时处理单数据为Excel
     *
     * @param treeId    型号ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param status    状态筛选 (all-全部, finished-已完成, unfinished-未完成)
     * @return Excel文件响应
     */
    @PostMapping("/exportTempExcel")
    public ResponseEntity<?> exportTempExcel(
            @RequestParam(value = "treeId", required = false, defaultValue = "-1") String treeId,
            @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
            @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
            @RequestParam(value = "status", required = false, defaultValue = "all") String status) {

        try {
            log.info("开始导出现场临时处理单数据，treeId={}, startDate={}, endDate={}, status={}",
                    treeId, startDate, endDate, status);

            // 调用服务导出Excel
            File excelFile = aitScreenService.exportTempExcel(treeId, startDate, endDate, status);

            log.info("导出现场临时处理单数据成功");
            return FileDownloadUtil.fileResponseAndDelete(excelFile);
        } catch (Exception e) {
            log.error("导出现场临时处理单数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("导出失败，原因：" + e.getMessage());
        }
    }

    /**
     * 导出技术状态更改单数据为Excel
     *
     * @param treeId    型号ID
     * @param username  用户名
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param status    状态筛选 (all-全部, finished-已完成, unfinished-未完成)
     * @param situation 情况筛选 (all-全部, 或具体情况值)
     * @param queryType 查询类型 (main-仅主表, branch-主表+分支表, all-默认)
     * @return Excel文件响应
     */
    @PostMapping("/exportChangeOrderExcel")
    public ResponseEntity<?> exportChangeOrderExcel(
            @RequestParam(value = "treeId", required = false, defaultValue = "-1") String treeId,
            @RequestParam(value = "username", required = false, defaultValue = "") String username,
            @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
            @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
            @RequestParam(value = "status", required = false, defaultValue = "all") String status,
            @RequestParam(value = "situation", required = false, defaultValue = "all") String situation,
            @RequestParam(value = "queryType", required = false, defaultValue = "all") String queryType) {

        try {
            log.info("开始导出技术状态更改单数据，treeId={}, username={}, startDate={}, endDate={}, status={}, situation={}, queryType={}",
                    treeId, username, startDate, endDate, status, situation, queryType);

            // 调用服务导出Excel
            File excelFile = aitScreenService.exportChangeOrderExcel(treeId, username, startDate, endDate, status, situation, queryType);

            log.info("导出技术状态更改单数据成功");
            return FileDownloadUtil.fileResponseAndDelete(excelFile);
        } catch (Exception e) {
            log.error("导出技术状态更改单数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("导出失败，原因：" + e.getMessage());
        }
    }

    /**
     * 导出不合格品审理单数据为Excel
     *
     * @param treeId        型号ID
     * @param username      用户名
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @param status        状态筛选 (all-全部, finished-已完成, unfinished-未完成)
     * @param severityLevel 严重程度筛选 (all-全部, 一级, 二级, 三级)
     * @return Excel文件响应
     */
    @PostMapping("/exportNonconformityExcel")
    public ResponseEntity<?> exportNonconformityExcel(
            @RequestParam(value = "treeId", required = false, defaultValue = "-1") String treeId,
            @RequestParam(value = "username", required = false, defaultValue = "") String username,
            @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
            @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
            @RequestParam(value = "status", required = false, defaultValue = "all") String status,
            @RequestParam(value = "severityLevel", required = false, defaultValue = "all") String severityLevel) {

        try {
            log.info("开始导出不合格品审理单数据，treeId={}, username={}, startDate={}, endDate={}, status={}, severityLevel={}",
                    treeId, username, startDate, endDate, status, severityLevel);

            // 调用服务导出Excel
            File excelFile = aitScreenService.exportNonconformityExcel(treeId, username, startDate, endDate, status, severityLevel);

            log.info("导出不合格品审理单数据成功");
            return FileDownloadUtil.fileResponseAndDelete(excelFile);
        } catch (Exception e) {
            log.error("导出不合格品审理单数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("导出失败，原因：" + e.getMessage());
        }
    }

    /**
     * 导出汇总统计数据为Excel
     *
     * @param treeId    型号ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param groupType 分组类型 (ISSUBMIT、ISLUOHAN、ISCERTIFICATE)
     * @return Excel文件响应
     */
    @PostMapping("/exportSummaryExcel")
    public ResponseEntity<?> exportSummaryExcel(
            @RequestParam(value = "treeId", required = false, defaultValue = "-1") String treeId,
            @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
            @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
            @RequestParam(value = "groupType", required = true) String groupType) {

        try {
            log.info("开始导出汇总统计数据，treeId={}, startDate={}, endDate={}, groupType={}",
                    treeId, startDate, endDate, groupType);

            // 调用服务导出Excel
            File excelFile = aitScreenService.exportSummaryExcel(treeId, startDate, endDate, groupType);

            log.info("导出汇总统计数据成功");
            return FileDownloadUtil.fileResponseAndDelete(excelFile);
        } catch (Exception e) {
            log.error("导出汇总统计数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("导出失败，原因：" + e.getMessage());
        }
    }
}
