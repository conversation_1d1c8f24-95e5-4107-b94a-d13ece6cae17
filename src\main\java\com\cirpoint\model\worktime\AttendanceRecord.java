package com.cirpoint.model.worktime;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 门禁打卡记录数据模型
 * 用于表示从Excel文件中读取的员工打卡数据
 */
@Data
public class AttendanceRecord {
    
    /**
     * 人员姓名
     */
    private String employeeName;
    
    /**
     * 工号（9位员工编号）
     */
    private String employeeId;
    
    /**
     * 所属组织（部门信息）
     */
    private String department;
    
    /**
     * 门禁点（打卡位置描述）
     */
    private String accessPoint;
    
    /**
     * 控制器（硬件设备ID）
     */
    private String controller;
    
    /**
     * 出/入方向（必须为"出"或"入"）
     */
    private String direction;
    
    /**
     * 事件时间（打卡时间戳）
     */
    private LocalDateTime eventTime;
    
    /**
     * 场所（根据门禁点计算得出）
     * 910厂房、920厂房、二号楼
     */
    private String location;
    
    /**
     * 根据门禁点判定场所
     * @param accessPoint 门禁点描述
     * @return 场所名称
     */
    public static String determineLocation(String accessPoint) {
        if (accessPoint == null) {
            return "未知场所";
        }
        
        String point = accessPoint.toLowerCase();
        if (point.contains("910")) {
            return "910厂房";
        } else if (point.contains("920")) {
            return "920厂房";
        } else if (point.contains("二号楼")) {
            return "二号楼";
        } else {
            return "其他场所";
        }
    }
    
    /**
     * 设置门禁点并自动计算场所
     * @param accessPoint 门禁点描述
     */
    public void setAccessPoint(String accessPoint) {
        this.accessPoint = accessPoint;
        this.location = determineLocation(accessPoint);
    }
    
    /**
     * 验证记录数据的有效性
     * @return 验证结果
     */
    public boolean isValid() {
        return employeeName != null && !employeeName.trim().isEmpty()
                && accessPoint != null && !accessPoint.trim().isEmpty()
                && direction != null && ("出".equals(direction) || "入".equals(direction))
                && eventTime != null;
    }
    
    /**
     * 获取打卡日期（用于分组）
     * @return 日期字符串 YYYY-MM-DD
     */
    public String getDateString() {
        if (eventTime == null) {
            return null;
        }
        return eventTime.toLocalDate().toString();
    }
    
    /**
     * 获取员工复合标识符
     * @return 员工复合标识符（姓名+工号）
     */
    public String getEmployeeCompositeKey() {
        if (employeeName == null) {
            return "";
        }
        if (employeeId != null && !employeeId.trim().isEmpty()) {
            return employeeName + "+" + employeeId;
        } else {
            return employeeName;
        }
    }

    /**
     * 获取分组键（员工复合标识符 + 场所 + 日期）
     * @return 分组键
     */
    public String getGroupKey() {
        return getEmployeeCompositeKey() + "|" + location + "|" + getDateString();
    }
    
    @Override
    public String toString() {
        return String.format("AttendanceRecord{姓名='%s', 场所='%s', 方向='%s', 时间=%s}", 
                employeeName, location, direction, eventTime);
    }
}
