package com.cirpoint.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.task.QualityTestSyncTask;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 质测数据同步控制器
 */
@Slf4j
@RestController
@RequestMapping("/quality/test")
public class QualityTestController {

    @Autowired
    private QualityTestSyncTask qualityTestSyncTask;

    /**
     * 手动执行质测数据同步任务
     * 
     * @return 同步结果
     */
    @PostMapping("/sync")
    public ResponseEntity<String> syncQualityTestData() {
        log.info("手动触发质测数据同步");
        
        JSONObject result = new JSONObject();
        result.set("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        try {
            // 调用定时任务的方法，并设置收集日志
            List<String> logs = qualityTestSyncTask.syncQualityTestData(true);
            
            // 添加日志到结果
            JSONArray logsArray = new JSONArray();
            if (logs != null) {
				logsArray.addAll(logs);
            }
            
            result.set("success", true);
            result.set("message", "质测数据同步任务已执行");
            result.set("logs", logsArray);
            
            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            log.error("手动执行质测数据同步失败", e);
            
            result.set("success", false);
            result.set("message", "质测数据同步执行失败: " + e.getMessage());
            
            // 如果有部分日志，也添加到结果中
            JSONArray logsArray = new JSONArray();
            try {
                List<String> logs = qualityTestSyncTask.syncQualityTestData(false);
                if (logs != null) {
					logsArray.addAll(logs);
                }
                result.set("logs", logsArray);
            } catch (Exception ex) {
                // 忽略获取日志的异常
                log.warn("获取部分执行日志失败", ex);
            }
            
            return ResponseEntity.ok(result.toString());
        }
    }
} 