package com.cirpoint.util.ws.client;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URL;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.apache.axis.encoding.XMLType;
import org.apache.axis.message.SOAPHeaderElement;
import org.apache.commons.codec.binary.Hex;

import javax.xml.namespace.QName;
import javax.xml.rpc.ParameterMode;

@Setter
@Getter
public class UploadFileAxisClientEx {


	private int fileSize;    //每个文件大小
	private String wsUrl;    //WS的地址
	private String method;    //WS的方法名
	private String namespaceURL;
	private String auth;    //调用凭证
	private String dataPackage;//采集数据转换器

	public static void main(String[] args) throws Exception {
		UploadFileAxisClientEx client = new UploadFileAxisClientEx();
		client.setWsUrl("http://***********:8085/avfms/ws/collect?wsdl");
		client.setFileSize(52428800);
		client.setNamespaceURL("http://service.collect.avfms.bjsasc.com");
		client.setAuth("avfms");
		client.setDataPackage("avfms");
		client.setMethod("UploadDIMonitorFile");

		String p = "C:\\TestOut\\1678871214198\\card-111-1678871278199.zip";
		client.upload(new File(p));
	}

	private SOAPHeaderElement createAuth(String namespaceURL, String auth) throws Exception {
		SOAPHeaderElement header = new SOAPHeaderElement(namespaceURL, "authrity");
		header.setPrefix("");
		header.addChildElement("username").addTextNode(auth);
		header.addChildElement("password").addTextNode("");
		return header;
	}

	public String callByAxis(String url, String namespaceURL, String methodName, String value, String auth) throws Exception {
		Service service = new Service();
		Call call = (Call) service.createCall();
		call.setTargetEndpointAddress(new URL(url));
		call.addParameter("title", XMLType.XSD_STRING, ParameterMode.IN);
		call.addHeader(createAuth(namespaceURL, auth));
		call.setReturnType(XMLType.XSD_STRING);
		return (String) call.invoke(new Object[]{value});
	}

	public static void uploadFileByAxisClient(File zipFile) throws Exception {
		UploadFileAxisClientEx client = new UploadFileAxisClientEx();
		client.upload(zipFile);
	}

	public void upload(File zipFile) throws Exception {
		this.splitFile(zipFile);
	}

	public String getMD5(File file) {
		FileInputStream fileInputStream = null;
		try {
			MessageDigest MD5 = MessageDigest.getInstance("MD5");
			fileInputStream = new FileInputStream(file);
			byte[] buffer = new byte[81920];
			int lenght = 0;
			while ((lenght = fileInputStream.read(buffer)) != -1) {
				MD5.update(buffer, 0, lenght);
			}
			fileInputStream.close();
			fileInputStream = null;
			return new String(Hex.encodeHex(MD5.digest()));
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (fileInputStream != null) {
					fileInputStream.close();
					fileInputStream = null;
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	private List<String> splitFile(File file) throws Exception {
		List<String> result = new ArrayList<>();
		int size = this.getFileSize();
		String md5 = getMD5(file);
		try (FileInputStream fis = new FileInputStream(file)) {
			byte[] byteArray = new byte[size];
			int len = 0;
			int i = 0;
			while ((len = fis.read(byteArray)) != -1) {

				//最后一块
				if (len < size) {
					//截取长度len的字节
					byte[] byteArray2 = new byte[len];
					System.arraycopy(byteArray, 0, byteArray2, 0, len);
					this.upload(this.getDataPackage(), file.getName(), "", byteArray2, 1L * i * size, true, md5);
				} else {
					this.upload(this.getDataPackage(), file.getName(), "", byteArray, 1L * i * size, false, md5);
				}
				i++;
			}
		}
		return result;
	}

	private void upload(String dataPackage, String fileName, String originalFileDir, byte[] buffer, long offset, boolean isLastBolck,
						String fileMD5) throws Exception {
		Object[] paramters = new Object[]{dataPackage, fileName, "", buffer, offset, isLastBolck, fileMD5};
		callDangAn(paramters);

	}

	private void callDangAn(Object[] paramters) throws Exception {
		Service service = new Service();
		Call call = (Call) service.createCall();
		call.setTargetEndpointAddress(new URL(this.getWsUrl()));
		call.setOperationName(new QName(this.getNamespaceURL(), this.getMethod()));
		call.addHeader(createAuth(this.getNamespaceURL(), this.getAuth()));

		call.addParameter("dataPackage", XMLType.XSD_STRING, ParameterMode.IN);
		call.addParameter("fileName", XMLType.XSD_STRING, ParameterMode.IN);
		call.addParameter("originalFileDir", XMLType.XSD_STRING, ParameterMode.IN);
		call.addParameter("buffer", XMLType.XSD_BASE64, ParameterMode.IN);
		call.addParameter("offset", XMLType.XSD_LONG, ParameterMode.IN);
		call.addParameter("isLastBolck", XMLType.XSD_BOOLEAN, ParameterMode.IN);
		call.addParameter("fileMD5", XMLType.XSD_STRING, ParameterMode.IN);

		call.setReturnType(XMLType.XSD_STRING);
		call.setEncodingStyle("utf-8");
		call.invoke(paramters);
	}
}
