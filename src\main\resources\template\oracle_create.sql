CREATE TABLE tx_Calibration_Parameters
(
    ID                             VARCHAR2(255), -- 编号
    Sensor_Distance_To_Origin      VARCHAR2(255), -- SD_传感器到原点距离
    Calibration_Level              VARCHAR2(255), -- SD_标定等级
    Platform_Weight                VARCHAR2(255), -- SD_平台重量
    Platform_P1_Coefficient        VARCHAR2(255), -- SD_平台P1系数
    Platform_P2_Coefficient        VARCHAR2(255), -- SD_平台P2系数
    Platform_P3_Coefficient        VARCHAR2(255), -- SD_平台P3系数
    Weight_1_Weight                VARCHAR2(255), -- SD_砝码1重量
    Weight_1_P1_Coefficient        VARCHAR2(255), -- SD_砝码1P1系数
    Weight_1_P2_Coefficient        VARCHAR2(255), -- SD_砝码1P2系数
    Weight_1_P3_Coefficient        VARCHAR2(255), -- SD_砝码1P3系数
    Weight_2_Weight                VARCHAR2(255), -- SD_砝码2重量
    Weight_2_P1_Coefficient        VARCHAR2(255), -- SD_砝码2P1系数
    Weight_2_P2_Coefficient        VARCHAR2(255), -- SD_砝码2P2系数
    Weight_2_P3_Coefficient        VARCHAR2(255), -- SD_砝码2P3系数
    Weight_3_Weight                VARCHAR2(255), -- SD_砝码3重量
    Weight_3_P1_Coefficient        VARCHAR2(255), -- SD_砝码3P1系数
    Weight_3_P2_Coefficient        VARCHAR2(255), -- SD_砝码3P2系数
    Weight_3_P3_Coefficient        VARCHAR2(255), -- SD_砝码3P3系数
    Weight_4_Weight                VARCHAR2(255), -- SD_砝码4重量
    Weight_4_P1_Coefficient        VARCHAR2(255), -- SD_砝码4P1系数
    Weight_4_P2_Coefficient        VARCHAR2(255), -- SD_砝码4P2系数
    Weight_4_P3_Coefficient        VARCHAR2(255), -- SD_砝码4P3系数
    Weight_5_Weight                VARCHAR2(255), -- SD_砝码5重量
    Weight_5_P1_Coefficient        VARCHAR2(255), -- SD_砝码5P1系数
    Weight_5_P2_Coefficient        VARCHAR2(255), -- SD_砝码5P2系数
    Weight_5_P3_Coefficient        VARCHAR2(255), -- SD_砝码5P3系数
    Weight_6_Weight                VARCHAR2(255), -- SD_砝码6重量
    Weight_6_P1_Coefficient        VARCHAR2(255), -- SD_砝码6P1系数
    Weight_6_P2_Coefficient        VARCHAR2(255), -- SD_砝码6P2系数
    Weight_6_P3_Coefficient        VARCHAR2(255), -- SD_砝码6P3系数
    Weight_7_Weight                VARCHAR2(255), -- SD_砝码7重量
    Weight_7_P1_Coefficient        VARCHAR2(255), -- SD_砝码7P1系数
    Weight_7_P2_Coefficient        VARCHAR2(255), -- SD_砝码7P2系数
    Weight_7_P3_Coefficient        VARCHAR2(255), -- SD_砝码7P3系数
    Weight_8_Weight                VARCHAR2(255), -- SD_砝码8重量
    Weight_8_P1_Coefficient        VARCHAR2(255), -- SD_砝码8P1系数
    Weight_8_P2_Coefficient        VARCHAR2(255), -- SD_砝码8P2系数
    Weight_8_P3_Coefficient        VARCHAR2(255), -- SD_砝码8P3系数
    Weight_9_Weight                VARCHAR2(255), -- SD_砝码9重量
    Weight_9_P1_Coefficient        VARCHAR2(255), -- SD_砝码9P1系数
    Weight_9_P2_Coefficient        VARCHAR2(255), -- SD_砝码9P2系数
    Weight_9_P3_Coefficient        VARCHAR2(255), -- SD_砝码9P3系数
    Weight_10_Weight               VARCHAR2(255), -- SD_砝码10重量
    Weight_10_P1_Coefficient       VARCHAR2(255), -- SD_砝码10P1系数
    Weight_10_P2_Coefficient       VARCHAR2(255), -- SD_砝码10P2系数
    Weight_10_P3_Coefficient       VARCHAR2(255), -- SD_砝码10P3系数
    Platform_P1_Code_Value         VARCHAR2(255), -- SD_平台P1码值
    Platform_P2_Code_Value         VARCHAR2(255), -- SD_平台P2码值
    Platform_P3_Code_Value         VARCHAR2(255), -- SD_平台P3码值
    Weight_1_P1_Code_Value         VARCHAR2(255), -- SD_砝码1P1码值
    Weight_1_P2_Code_Value         VARCHAR2(255), -- SD_砝码1P2码值
    Weight_1_P3_Code_Value         VARCHAR2(255), -- SD_砝码1P3码值
    Weight_2_P1_Code_Value         VARCHAR2(255), -- SD_砝码2P1码值
    Weight_2_P2_Code_Value         VARCHAR2(255), -- SD_砝码2P2码值
    Weight_2_P3_Code_Value         VARCHAR2(255), -- SD_砝码2P3码值
    Weight_3_P1_Code_Value         VARCHAR2(255), -- SD_砝码3P1码值
    Weight_3_P2_Code_Value         VARCHAR2(255), -- SD_砝码3P2码值
    Weight_3_P3_Code_Value         VARCHAR2(255), -- SD_砝码3P3码值
    Weight_4_P1_Code_Value         VARCHAR2(255), -- SD_砝码4P1码值
    Weight_4_P2_Code_Value         VARCHAR2(255), -- SD_砝码4P2码值
    Weight_4_P3_Code_Value         VARCHAR2(255), -- SD_砝码4P3码值
    Weight_5_P1_Code_Value         VARCHAR2(255), -- SD_砝码5P1码值
    Weight_5_P2_Code_Value         VARCHAR2(255), -- SD_砝码5P2码值
    Weight_5_P3_Code_Value         VARCHAR2(255), -- SD_砝码5P3码值
    Weight_6_P1_Code_Value         VARCHAR2(255), -- SD_砝码6P1码值
    Weight_6_P2_Code_Value         VARCHAR2(255), -- SD_砝码6P2码值
    Weight_6_P3_Code_Value         VARCHAR2(255), -- SD_砝码6P3码值
    Weight_7_P1_Code_Value         VARCHAR2(255), -- SD_砝码7P1码值
    Weight_7_P2_Code_Value         VARCHAR2(255), -- SD_砝码7P2码值
    Weight_7_P3_Code_Value         VARCHAR2(255), -- SD_砝码7P3码值
    Weight_8_P1_Code_Value         VARCHAR2(255), -- SD_砝码8P1码值
    Weight_8_P2_Code_Value         VARCHAR2(255), -- SD_砝码8P2码值
    Weight_8_P3_Code_Value         VARCHAR2(255), -- SD_砝码8P3码值
    Weight_9_P1_Code_Value         VARCHAR2(255), -- SD_砝码9P1码值
    Weight_9_P2_Code_Value         VARCHAR2(255), -- SD_砝码9P2码值
    Weight_9_P3_Code_Value         VARCHAR2(255), -- SD_砝码9P3码值
    Weight_10_P1_Code_Value        VARCHAR2(255), -- SD_砝码10P1码值
    Weight_10_P2_Code_Value        VARCHAR2(255), -- SD_砝码10P2码值
    Weight_10_P3_Code_Value        VARCHAR2(255), -- SD_砝码10P3码值
    Calibration_Date               VARCHAR2(255), -- SD_标定日期
    Eight_Point_Calibration_Result VARCHAR2(255), -- MD_八点标定结论
    Eight_Point_Calibration_Date   VARCHAR2(255), -- MD_八点标定日期
    Mass_Correction_Result         VARCHAR2(255), -- MD_质量校正结论
    Mass_Correction_Date           VARCHAR2(255), -- MD_质量校正日期
    Plat_P1_Weight                 VARCHAR2(255), -- Plat_P1重量
    Plat_P2_Weight                 VARCHAR2(255), -- Plat_P2重量
    Plat_P3_Weight                 VARCHAR2(255), -- Plat_P3重量
    Plat_Total_Weight              VARCHAR2(255)  -- Plat_PTotal重量
);

CREATE TABLE tx_Test_Data_Print
(
    Test_ID                    VARCHAR2(255), -- 测试编号
    Product_Model             VARCHAR2(255), -- 产品型号
    Batch_Number             VARCHAR2(255), -- 批次
    Product_Name             VARCHAR2(255), -- 产品名称
    Operator1                VARCHAR2(255), -- 操作人员1
    Operator2                VARCHAR2(255), -- 操作人员2
    Operator3                VARCHAR2(255), -- 操作人员3
    Operator4                VARCHAR2(255), -- 操作人员4
    Test_Date                VARCHAR2(255), -- 测试日期
    HPlatP1Weight            VARCHAR2(255),
    HPlatP2Weight            VARCHAR2(255),
    HPlatP3Weight            VARCHAR2(255),
    HPlatPTotalWeight        VARCHAR2(255),
    H_Clamp_CoorZero_Y0      VARCHAR2(255),
    H_Clamp_CoorZero_Z0      VARCHAR2(255),
    H_Clamp_P1               VARCHAR2(255),
    H_Clamp_P2               VARCHAR2(255),
    H_Clamp_P3               VARCHAR2(255),
    H_Clamp_G                VARCHAR2(255),
    H_Clamp_Yc               VARCHAR2(255),
    H_Clamp_Zc               VARCHAR2(255),
    H_Clamp_R                VARCHAR2(255),
    H_Clamp_Alfa             VARCHAR2(255),
    H_Clamp_My               VARCHAR2(255),
    H_Clamp_Mz               VARCHAR2(255),
    H_Clamp_BalanceAngle     VARCHAR2(255),
    H_Clamp_BalanceMoment    VARCHAR2(255),
    H_Satellite_CoorZero_Y0  VARCHAR2(255),
    H_Satellite_CoorZero_Z0  VARCHAR2(255),
    H_Satellite_P1           VARCHAR2(255),
    H_Satellite_P2           VARCHAR2(255),
    H_Satellite_P3           VARCHAR2(255),
    H_Satellite_G            VARCHAR2(255),
    H_Satellite_Yc           VARCHAR2(255),
    H_Satellite_Zc           VARCHAR2(255),
    H_Satellite_R            VARCHAR2(255),
    H_Satellite_Alfa         VARCHAR2(255),
    H_Satellite_My           VARCHAR2(255),
    H_Satellite_Mz           VARCHAR2(255),
    H_Satellite_BalanceAngle VARCHAR2(255),
    H_Satellite_BalanceMoment VARCHAR2(255),
    VPlatP1Weight            VARCHAR2(255),
    VPlatP2Weight            VARCHAR2(255),
    VPlatP3Weight            VARCHAR2(255),
    VPlatPTotalWeight        VARCHAR2(255),
    V_Clamp_CoorZero_X0      VARCHAR2(255),
    V_Clamp_P1               VARCHAR2(255),
    V_Clamp_P2               VARCHAR2(255),
    V_Clamp_P3               VARCHAR2(255),
    V_Clamp_G                VARCHAR2(255),
    V_Clamp_Xc               VARCHAR2(255),
    V_Clamp_Zc               VARCHAR2(255),
    V_Clamp_R                VARCHAR2(255),
    V_Clamp_Alfa             VARCHAR2(255),
    V_Clamp_Mx               VARCHAR2(255),
    V_Clamp_Mz               VARCHAR2(255),
    V_Clamp_BalanceAngle     VARCHAR2(255),
    V_Clamp_BalanceMoment    VARCHAR2(255),
    V_Satellite_CoorZero_X0  VARCHAR2(255),
    V_Satellite_P1           VARCHAR2(255),
    V_Satellite_P2           VARCHAR2(255),
    V_Satellite_P3           VARCHAR2(255),
    V_Satellite_G            VARCHAR2(255),
    V_Satellite_Xc           VARCHAR2(255),
    V_Satellite_Zc           VARCHAR2(255),
    V_Satellite_R            VARCHAR2(255),
    V_Satellite_Alfa         VARCHAR2(255),
    V_Satellite_Mx           VARCHAR2(255),
    V_Satellite_Mz           VARCHAR2(255),
    V_Satellite_BalanceAngle VARCHAR2(255),
    V_Satellite_BalanceMoment VARCHAR2(255)
);

CREATE TABLE tx_Test_Data
(
    Test_ID                    VARCHAR2(255), -- 测试编号
    Product_Model             VARCHAR2(255), -- 产品型号
    Batch_Number             VARCHAR2(255), -- 批次
    Product_Name             VARCHAR2(255), -- 产品名称
    Operator1                VARCHAR2(255), -- 操作人员1
    Operator2                VARCHAR2(255), -- 操作人员2
    Operator3                VARCHAR2(255), -- 操作人员3
    Operator4                VARCHAR2(255), -- 操作人员4
    Test_Date                VARCHAR2(255), -- 测试日期
    HPlatP1Weight            VARCHAR2(255),
    HPlatP2Weight            VARCHAR2(255),
    HPlatP3Weight            VARCHAR2(255),
    HPlatPTotalWeight        VARCHAR2(255),
    H_Clamp_CoorZero_Y0      VARCHAR2(255),
    H_Clamp_CoorZero_Z0      VARCHAR2(255),
    H_Clamp_P1               VARCHAR2(255),
    H_Clamp_P2               VARCHAR2(255),
    H_Clamp_P3               VARCHAR2(255),
    H_Clamp_G                VARCHAR2(255),
    H_Clamp_Yc               VARCHAR2(255),
    H_Clamp_Zc               VARCHAR2(255),
    H_Clamp_R                VARCHAR2(255),
    H_Clamp_Alfa             VARCHAR2(255),
    H_Clamp_My               VARCHAR2(255),
    H_Clamp_Mz               VARCHAR2(255),
    H_Clamp_BalanceAngle     VARCHAR2(255),
    H_Clamp_BalanceMoment    VARCHAR2(255),
    H_Satellite_CoorZero_Y0  VARCHAR2(255),
    H_Satellite_CoorZero_Z0  VARCHAR2(255),
    H_Satellite_P1           VARCHAR2(255),
    H_Satellite_P2           VARCHAR2(255),
    H_Satellite_P3           VARCHAR2(255),
    H_Satellite_G            VARCHAR2(255),
    H_Satellite_Yc           VARCHAR2(255),
    H_Satellite_Zc           VARCHAR2(255),
    H_Satellite_R            VARCHAR2(255),
    H_Satellite_Alfa         VARCHAR2(255),
    H_Satellite_My           VARCHAR2(255),
    H_Satellite_Mz           VARCHAR2(255),
    H_Satellite_BalanceAngle VARCHAR2(255),
    H_Satellite_BalanceMoment VARCHAR2(255),
    VPlatP1Weight            VARCHAR2(255),
    VPlatP2Weight            VARCHAR2(255),
    VPlatP3Weight            VARCHAR2(255),
    VPlatPTotalWeight        VARCHAR2(255),
    V_Clamp_CoorZero_X0      VARCHAR2(255),
    V_Clamp_P1               VARCHAR2(255),
    V_Clamp_P2               VARCHAR2(255),
    V_Clamp_P3               VARCHAR2(255),
    V_Clamp_G                VARCHAR2(255),
    V_Clamp_Xc               VARCHAR2(255),
    V_Clamp_Zc               VARCHAR2(255),
    V_Clamp_R                VARCHAR2(255),
    V_Clamp_Alfa             VARCHAR2(255),
    V_Clamp_Mx               VARCHAR2(255),
    V_Clamp_Mz               VARCHAR2(255),
    V_Clamp_BalanceAngle     VARCHAR2(255),
    V_Clamp_BalanceMoment    VARCHAR2(255),
    V_Satellite_CoorZero_X0  VARCHAR2(255),
    V_Satellite_P1           VARCHAR2(255),
    V_Satellite_P2           VARCHAR2(255),
    V_Satellite_P3           VARCHAR2(255),
    V_Satellite_G            VARCHAR2(255),
    V_Satellite_Xc           VARCHAR2(255),
    V_Satellite_Zc           VARCHAR2(255),
    V_Satellite_R            VARCHAR2(255),
    V_Satellite_Alfa         VARCHAR2(255),
    V_Satellite_Mx           VARCHAR2(255),
    V_Satellite_Mz           VARCHAR2(255),
    V_Satellite_BalanceAngle VARCHAR2(255),
    V_Satellite_BalanceMoment VARCHAR2(255)
);

CREATE TABLE tx_Calculation_Result
(
    Test_ID                    VARCHAR2(255) NOT NULL, -- 测试编号
    Product_Model             VARCHAR2(255), -- 产品型号
    Test_Date                VARCHAR2(255), -- 测试日期
    Xctest                   VARCHAR2(255), -- 质心Xctest
    Yctest                   VARCHAR2(255), -- 质心Yctest
    Zctest                   VARCHAR2(255), -- 质心Zctest
    Xcsat                    VARCHAR2(255), -- 质心Xcsat
    Ycsat                    VARCHAR2(255), -- 质心Ycsat
    Zcsat                    VARCHAR2(255), -- 质心Zcsat
    Ixtest                   VARCHAR2(255), -- 转动惯量Ixtest
    Iytest                   VARCHAR2(255), -- 转动惯量Iytest
    Iztest                   VARCHAR2(255), -- 转动惯量Iztest
    Ixytest                  VARCHAR2(255), -- 惯性积Ixytest
    Ixztest                  VARCHAR2(255), -- 惯性积Ixztest
    Iyztest                  VARCHAR2(255), -- 惯性积Iyztest
    IxWcent                  VARCHAR2(255), -- 转动惯量IxWcent
    IyWcent                  VARCHAR2(255), -- 转动惯量IyWcent
    IzWcent                  VARCHAR2(255), -- 转动惯量IzWcent
    IxyWcent                 VARCHAR2(255), -- 惯性积IxyWcent
    IxzWcent                 VARCHAR2(255), -- 惯性积IxzWcent
    IyzWcent                 VARCHAR2(255), -- 惯性积IyzWcent
    Xc                       VARCHAR2(255), -- 质心Xc
    IxBottom                 VARCHAR2(255), -- 转动惯量IxBottom
    IyBottom                 VARCHAR2(255), -- 转动惯量IyBottom
    IzBottom                 VARCHAR2(255), -- 转动惯量IzBottom
    IxyBottom                VARCHAR2(255), -- 惯性积IxyBottom
    IxzBottom                VARCHAR2(255), -- 惯性积IxzBottom
    IyzBottom                VARCHAR2(255), -- 惯性积IyzBottom
    RotatAngle              VARCHAR2(255), -- 倾倒角度RotatAngle
    Weight                   VARCHAR2(255), -- 卫星质量Weight
    testXc                   VARCHAR2(255), -- testXc
    CONSTRAINT pk_calculation_result PRIMARY KEY (Test_ID)
);

CREATE TABLE tx_Moment_Calibration
(
    ID                  VARCHAR2(255), -- 编号
    Period_Count        VARCHAR2(255), -- 测量周期数
    Test_Times         VARCHAR2(255), -- 测量次数
    PlatT1             VARCHAR2(255), -- PlatT1
    PlatT2             VARCHAR2(255), -- PlatT2
    PlatT3             VARCHAR2(255), -- PlatT3
    PlatT4             VARCHAR2(255), -- PlatT4
    PlatT5             VARCHAR2(255), -- PlatT5
    PlatTa             VARCHAR2(255), -- PlatTa
    Standard1MI        VARCHAR2(255), -- Standard1MI
    Standard1T1        VARCHAR2(255), -- Standard1T1
    Standard1T2        VARCHAR2(255), -- Standard1T2
    Standard1T3        VARCHAR2(255), -- Standard1T3
    Standard1T4        VARCHAR2(255), -- Standard1T4
    Standard1T5        VARCHAR2(255), -- Standard1T5
    Standard1Ta        VARCHAR2(255), -- Standard1Ta
    Standard1C         VARCHAR2(255), -- Standard1C
    Standard2MI        VARCHAR2(255), -- Standard2MI
    Standard2T1        VARCHAR2(255), -- Standard2T1
    Standard2T2        VARCHAR2(255), -- Standard2T2
    Standard2T3        VARCHAR2(255), -- Standard2T3
    Standard2T4        VARCHAR2(255), -- Standard2T4
    Standard2T5        VARCHAR2(255), -- Standard2T5
    Standard2Ta        VARCHAR2(255), -- Standard2Ta
    Standard2C         VARCHAR2(255), -- Standard2C
    Standard3MI        VARCHAR2(255), -- Standard3MI
    Standard3T1        VARCHAR2(255), -- Standard3T1
    Standard3T2        VARCHAR2(255), -- Standard3T2
    Standard3T3        VARCHAR2(255), -- Standard3T3
    Standard3T4        VARCHAR2(255), -- Standard3T4
    Standard3T5        VARCHAR2(255), -- Standard3T5
    Standard3Ta        VARCHAR2(255), -- Standard3Ta
    Standard3C         VARCHAR2(255), -- Standard3C
    Calibration_Date   VARCHAR2(255)  -- 标定日期
);

CREATE TABLE tx_Moment_Test_Record_Print
(
    Test_ID                    VARCHAR2(255), -- 测试编号
    Product_Model             VARCHAR2(255), -- 产品型号
    Batch_Number             VARCHAR2(255), -- 产品批次
    Product_Name             VARCHAR2(255), -- 产品名称
    Operator1                VARCHAR2(255), -- 操作人员1
    Operator2                VARCHAR2(255), -- 操作人员2
    Operator3                VARCHAR2(255), -- 操作人员3
    Test_Date                VARCHAR2(255), -- 测试日期
    Test_Period_Count        VARCHAR2(255), -- TestPeriodCount
    Test_Times              VARCHAR2(255), -- TestTimes
    Plat_TAV                VARCHAR2(255), -- PlatTAV
    Clamp_TAV_X             VARCHAR2(255), -- ClampTAV-X
    J0_KValue_X             VARCHAR2(255), -- J0-KValue-X
    Clmp_Sat_I_X            VARCHAR2(255), -- ClmpSatI-X
    Clamp_And_Sat_TAV_X     VARCHAR2(255), -- ClampAndSatTAV-X
    JD_KValue_X             VARCHAR2(255), -- JD-KValue-X
    Clamp_And_Sat_Sat_I_X   VARCHAR2(255), -- ClampAndSatSatI-X
    Sat_I_X                 VARCHAR2(255), -- SatI-X
    Clamp_TAV_Y             VARCHAR2(255), -- ClampTAV-Y
    J0_KValue_Y             VARCHAR2(255), -- J0-KValue-Y
    Clmp_Sat_I_Y            VARCHAR2(255), -- ClmpSatI-Y
    Clamp_And_Sat_TAV_Y     VARCHAR2(255), -- ClampAndSatTAV-Y
    JD_KValue_Y             VARCHAR2(255), -- JD-KValue-Y
    Clamp_And_Sat_Sat_I_Y   VARCHAR2(255), -- ClampAndSatSatI-Y
    Sat_I_Y                 VARCHAR2(255), -- SatI-Y
    Clamp_TAV_Z             VARCHAR2(255), -- ClampTAV-Z
    J0_KValue_Z             VARCHAR2(255), -- J0-KValue-Z
    Clmp_Sat_I_Z            VARCHAR2(255), -- ClmpSatI-Z
    Clamp_And_Sat_TAV_Z     VARCHAR2(255), -- ClampAndSatTAV-Z
    JD_KValue_Z             VARCHAR2(255), -- JD-KValue-Z
    Clamp_And_Sat_Sat_I_Z   VARCHAR2(255), -- ClampAndSatSatI-Z
    Sat_I_Z                 VARCHAR2(255), -- SatI-Z
    Work_Number             VARCHAR2(255), -- Work_Number
    Clamp_TAV_Statu1        VARCHAR2(255), -- ClampTAV-Statu1
    J0_KValue_Statu1        VARCHAR2(255), -- J0-KValue-Statu1
    Clmp_Sat_I_Statu1       VARCHAR2(255), -- ClmpSatI-Statu1
    Clamp_And_Sat_TAV_Statu1 VARCHAR2(255), -- ClampAndSatTAV-Statu1
    JD_KValue_Statu1        VARCHAR2(255), -- JD-KValue-Statu1
    Clamp_And_Sat_Sat_I_Statu1 VARCHAR2(255), -- ClampAndSatSatI-Statu1
    Sat_I_Statu1            VARCHAR2(255), -- SatI-Statu1
    Clamp_TAV_Statu2        VARCHAR2(255), -- ClampTAV-Statu2
    J0_KValue_Statu2        VARCHAR2(255), -- J0-KValue-Statu2
    Clmp_Sat_I_Statu2       VARCHAR2(255), -- ClmpSatI-Statu2
    Clamp_And_Sat_TAV_Statu2 VARCHAR2(255), -- ClampAndSatTAV-Statu2
    JD_KValue_Statu2        VARCHAR2(255), -- JD-KValue-Statu2
    Clamp_And_Sat_Sat_I_Statu2 VARCHAR2(255), -- ClampAndSatSatI-Statu2
    Sat_I_Statu2            VARCHAR2(255), -- SatI-Statu2
    Clamp_TAV_Statu3        VARCHAR2(255), -- ClampTAV-Statu3
    J0_KValue_Statu3        VARCHAR2(255), -- J0-KValue-Statu3
    Clmp_Sat_I_Statu3       VARCHAR2(255), -- ClmpSatI-Statu3
    Clamp_And_Sat_TAV_Statu3 VARCHAR2(255), -- ClampAndSatTAV-Statu3
    JD_KValue_Statu3        VARCHAR2(255), -- JD-KValue-Statu3
    Clamp_And_Sat_Sat_I_Statu3 VARCHAR2(255), -- ClampAndSatSatI-Statu3
    Sat_I_Statu3            VARCHAR2(255), -- SatI-Statu3
    Clamp_TAV_Statu4        VARCHAR2(255), -- ClampTAV-Statu4
    J0_KValue_Statu4        VARCHAR2(255), -- J0-KValue-Statu4
    Clmp_Sat_I_Statu4       VARCHAR2(255), -- ClmpSatI-Statu4
    Clamp_And_Sat_TAV_Statu4 VARCHAR2(255), -- ClampAndSatTAV-Statu4
    JD_KValue_Statu4        VARCHAR2(255), -- JD-KValue-Statu4
    Clamp_And_Sat_Sat_I_Statu4 VARCHAR2(255), -- ClampAndSatSatI-Statu4
    Sat_I_Statu4            VARCHAR2(255), -- SatI-Statu4
    Clamp_TAV_Statu5        VARCHAR2(255), -- ClampTAV-Statu5
    J0_KValue_Statu5        VARCHAR2(255), -- J0-KValue-Statu5
    Clmp_Sat_I_Statu5       VARCHAR2(255), -- ClmpSatI-Statu5
    Clamp_And_Sat_TAV_Statu5 VARCHAR2(255), -- ClampAndSatTAV-Statu5
    JD_KValue_Statu5        VARCHAR2(255), -- JD-KValue-Statu5
    Clamp_And_Sat_Sat_I_Statu5 VARCHAR2(255), -- ClampAndSatSatI-Statu5
    Sat_I_Statu5            VARCHAR2(255), -- SatI-Statu5
    Sat_Angle               VARCHAR2(255), -- Sat-Angle
    Clamp_TAV_Statu6        VARCHAR2(255), -- ClampTAV-Statu6
    J0_KValue_Statu6        VARCHAR2(255), -- J0-KValue-Statu6
    Clmp_Sat_I_Statu6       VARCHAR2(255), -- ClmpSatI-Statu6
    Clamp_And_Sat_TAV_Statu6 VARCHAR2(255), -- ClampAndSatTAV-Statu6
    JD_KValue_Statu6        VARCHAR2(255), -- JD-KValue-Statu6
    Clamp_And_Sat_Sat_I_Statu6 VARCHAR2(255), -- ClampAndSatSatI-Statu6
    Sat_I_Statu6            VARCHAR2(255), -- SatI-Statu6
    Rotate_Angle_Beta       VARCHAR2(255)  -- 旋转角度bate
);

CREATE TABLE tx_Moment_Test_Record
(
    Test_ID                    VARCHAR2(255), -- 测试编号
    Product_Model             VARCHAR2(255), -- 产品型号
    Batch_Number             VARCHAR2(255), -- 产品批次
    Product_Name             VARCHAR2(255), -- 产品名称
    Operator1                VARCHAR2(255), -- 操作人员1
    Operator2                VARCHAR2(255), -- 操作人员2
    Operator3                VARCHAR2(255), -- 操作人员3
    Test_Date                VARCHAR2(255), -- 测试日期
    Test_Period_Count        VARCHAR2(255), -- TestPeriodCount
    Test_Times              VARCHAR2(255), -- TestTimes
    Plat_TAV                VARCHAR2(255), -- PlatTAV
    Clamp_TAV_X             VARCHAR2(255), -- ClampTAV-X
    J0_KValue_X             VARCHAR2(255), -- J0-KValue-X
    Clmp_Sat_I_X            VARCHAR2(255), -- ClmpSatI-X
    Clamp_And_Sat_TAV_X     VARCHAR2(255), -- ClampAndSatTAV-X
    JD_KValue_X             VARCHAR2(255), -- JD-KValue-X
    Clamp_And_Sat_Sat_I_X   VARCHAR2(255), -- ClampAndSatSatI-X
    Sat_I_X                 VARCHAR2(255), -- SatI-X
    Clamp_TAV_Y             VARCHAR2(255), -- ClampTAV-Y
    J0_KValue_Y             VARCHAR2(255), -- J0-KValue-Y
    Clmp_Sat_I_Y            VARCHAR2(255), -- ClmpSatI-Y
    Clamp_And_Sat_TAV_Y     VARCHAR2(255), -- ClampAndSatTAV-Y
    JD_KValue_Y             VARCHAR2(255), -- JD-KValue-Y
    Clamp_And_Sat_Sat_I_Y   VARCHAR2(255), -- ClampAndSatSatI-Y
    Sat_I_Y                 VARCHAR2(255), -- SatI-Y
    Clamp_TAV_Z             VARCHAR2(255), -- ClampTAV-Z
    J0_KValue_Z             VARCHAR2(255), -- J0-KValue-Z
    Clmp_Sat_I_Z            VARCHAR2(255), -- ClmpSatI-Z
    Clamp_And_Sat_TAV_Z     VARCHAR2(255), -- ClampAndSatTAV-Z
    JD_KValue_Z             VARCHAR2(255), -- JD-KValue-Z
    Clamp_And_Sat_Sat_I_Z   VARCHAR2(255), -- ClampAndSatSatI-Z
    Sat_I_Z                 VARCHAR2(255), -- SatI-Z
    Work_Number             VARCHAR2(255), -- Work_Number
    Clamp_TAV_Statu1        VARCHAR2(255), -- ClampTAV-Statu1
    J0_KValue_Statu1        VARCHAR2(255), -- J0-KValue-Statu1
    Clmp_Sat_I_Statu1       VARCHAR2(255), -- ClmpSatI-Statu1
    Clamp_And_Sat_TAV_Statu1 VARCHAR2(255), -- ClampAndSatTAV-Statu1
    JD_KValue_Statu1        VARCHAR2(255), -- JD-KValue-Statu1
    Clamp_And_Sat_Sat_I_Statu1 VARCHAR2(255), -- ClampAndSatSatI-Statu1
    Sat_I_Statu1            VARCHAR2(255), -- SatI-Statu1
    Clamp_TAV_Statu2        VARCHAR2(255), -- ClampTAV-Statu2
    J0_KValue_Statu2        VARCHAR2(255), -- J0-KValue-Statu2
    Clmp_Sat_I_Statu2       VARCHAR2(255), -- ClmpSatI-Statu2
    Clamp_And_Sat_TAV_Statu2 VARCHAR2(255), -- ClampAndSatTAV-Statu2
    JD_KValue_Statu2        VARCHAR2(255), -- JD-KValue-Statu2
    Clamp_And_Sat_Sat_I_Statu2 VARCHAR2(255), -- ClampAndSatSatI-Statu2
    Sat_I_Statu2            VARCHAR2(255), -- SatI-Statu2
    Clamp_TAV_Statu3        VARCHAR2(255), -- ClampTAV-Statu3
    J0_KValue_Statu3        VARCHAR2(255), -- J0-KValue-Statu3
    Clmp_Sat_I_Statu3       VARCHAR2(255), -- ClmpSatI-Statu3
    Clamp_And_Sat_TAV_Statu3 VARCHAR2(255), -- ClampAndSatTAV-Statu3
    JD_KValue_Statu3        VARCHAR2(255), -- JD-KValue-Statu3
    Clamp_And_Sat_Sat_I_Statu3 VARCHAR2(255), -- ClampAndSatSatI-Statu3
    Sat_I_Statu3            VARCHAR2(255), -- SatI-Statu3
    Clamp_TAV_Statu4        VARCHAR2(255), -- ClampTAV-Statu4
    J0_KValue_Statu4        VARCHAR2(255), -- J0-KValue-Statu4
    Clmp_Sat_I_Statu4       VARCHAR2(255), -- ClmpSatI-Statu4
    Clamp_And_Sat_TAV_Statu4 VARCHAR2(255), -- ClampAndSatTAV-Statu4
    JD_KValue_Statu4        VARCHAR2(255), -- JD-KValue-Statu4
    Clamp_And_Sat_Sat_I_Statu4 VARCHAR2(255), -- ClampAndSatSatI-Statu4
    Sat_I_Statu4            VARCHAR2(255), -- SatI-Statu4
    Clamp_TAV_Statu5        VARCHAR2(255), -- ClampTAV-Statu5
    J0_KValue_Statu5        VARCHAR2(255), -- J0-KValue-Statu5
    Clmp_Sat_I_Statu5       VARCHAR2(255), -- ClmpSatI-Statu5
    Clamp_And_Sat_TAV_Statu5 VARCHAR2(255), -- ClampAndSatTAV-Statu5
    JD_KValue_Statu5        VARCHAR2(255), -- JD-KValue-Statu5
    Clamp_And_Sat_Sat_I_Statu5 VARCHAR2(255), -- ClampAndSatSatI-Statu5
    Sat_I_Statu5            VARCHAR2(255), -- SatI-Statu5
    Sat_Angle               VARCHAR2(255), -- Sat-Angle
    Clamp_TAV_Statu6        VARCHAR2(255), -- ClampTAV-Statu6
    J0_KValue_Statu6        VARCHAR2(255), -- J0-KValue-Statu6
    Clmp_Sat_I_Statu6       VARCHAR2(255), -- ClmpSatI-Statu6
    Clamp_And_Sat_TAV_Statu6 VARCHAR2(255), -- ClampAndSatTAV-Statu6
    JD_KValue_Statu6        VARCHAR2(255), -- JD-KValue-Statu6
    Clamp_And_Sat_Sat_I_Statu6 VARCHAR2(255), -- ClampAndSatSatI-Statu6
    Sat_I_Statu6            VARCHAR2(255), -- SatI-Statu6
    Rotate_Angle_Beta       VARCHAR2(255)  -- 旋转角度bate
);

CREATE TABLE tx_Moment_Test_Record_Old
(
    Test_ID                    VARCHAR2(255), -- 测试编号
    Product_Model             VARCHAR2(255), -- 产品型号
    Batch_Number             VARCHAR2(255), -- 产品批次
    Product_Name             VARCHAR2(255), -- 产品名称
    Operator1                VARCHAR2(255), -- 操作人员1
    Operator2                VARCHAR2(255), -- 操作人员2
    Operator3                VARCHAR2(255), -- 操作人员3
    Test_Date                VARCHAR2(255), -- 测试日期
    Test_Period_Count        VARCHAR2(255), -- TestPeriodCount
    Test_Times              VARCHAR2(255), -- TestTimes
    X_Clamp_T1              VARCHAR2(255), -- XClampT1
    X_Clamp_T2              VARCHAR2(255), -- XClampT2
    X_Clamp_T3              VARCHAR2(255), -- XClampT3
    X_Clamp_T4              VARCHAR2(255), -- XClampT4
    X_Clamp_T5              VARCHAR2(255), -- XClampT5
    X_Clamp_TAV             VARCHAR2(255), -- XClampTAV
    X_Clamp_C               VARCHAR2(255), -- XClampC
    X_Clamp_I               VARCHAR2(255), -- XClampI
    X_Clamp_And_Sat_T1      VARCHAR2(255), -- XClampAndSatT1
    X_Clamp_And_Sat_T2      VARCHAR2(255), -- XClampAndSatT2
    X_Clamp_And_Sat_T3      VARCHAR2(255), -- XClampAndSatT3
    X_Clamp_And_Sat_T4      VARCHAR2(255), -- XClampAndSatT4
    X_Clamp_And_Sat_T5      VARCHAR2(255), -- XClampAndSatT5
    X_Clamp_And_Sat_TAV     VARCHAR2(255), -- XClampAndSatTAV
    X_Clamp_And_Sat_C       VARCHAR2(255), -- XClampAndSatC
    X_Clamp_And_Sat_I       VARCHAR2(255), -- XClampAndSatI
    X_Sat_I                 VARCHAR2(255), -- XSatI
    Y_Clamp_T1              VARCHAR2(255), -- YClampT1
    Y_Clamp_T2              VARCHAR2(255), -- YClampT2
    Y_Clamp_T3              VARCHAR2(255), -- YClampT3
    Y_Clamp_T4              VARCHAR2(255), -- YClampT4
    Y_Clamp_T5              VARCHAR2(255), -- YClampT5
    Y_Clamp_TAV             VARCHAR2(255), -- YClampTAV
    Y_Clamp_C               VARCHAR2(255), -- YClampC
    Y_Clamp_I               VARCHAR2(255), -- YClampI
    Y_Clamp_And_Sat_T1      VARCHAR2(255), -- YClampAndSatT1
    Y_Clamp_And_Sat_T2      VARCHAR2(255), -- YClampAndSatT2
    Y_Clamp_And_Sat_T3      VARCHAR2(255), -- YClampAndSatT3
    Y_Clamp_And_Sat_T4      VARCHAR2(255), -- YClampAndSatT4
    Y_Clamp_And_Sat_T5      VARCHAR2(255), -- YClampAndSatT5
    Y_Clamp_And_Sat_TAV     VARCHAR2(255), -- YClampAndSatTAV
    Y_Clamp_And_Sat_C       VARCHAR2(255), -- YClampAndSatC
    Y_Clamp_And_Sat_I       VARCHAR2(255), -- YClampAndSatI
    Y_Sat_I                 VARCHAR2(255), -- YSatI
    Z_Clamp_T1              VARCHAR2(255), -- ZClampT1
    Z_Clamp_T2              VARCHAR2(255), -- ZClampT2
    Z_Clamp_T3              VARCHAR2(255), -- ZClampT3
    Z_Clamp_T4              VARCHAR2(255), -- ZClampT4
    Z_Clamp_T5              VARCHAR2(255), -- ZClampT5
    Z_Clamp_TAV             VARCHAR2(255), -- ZClampTAV
    Z_Clamp_C               VARCHAR2(255), -- ZClampC
    Z_Clamp_I               VARCHAR2(255), -- ZClampI
    Z_Clamp_And_Sat_T1      VARCHAR2(255), -- ZClampAndSatT1
    Z_Clamp_And_Sat_T2      VARCHAR2(255), -- ZClampAndSatT2
    Z_Clamp_And_Sat_T3      VARCHAR2(255), -- ZClampAndSatT3
    Z_Clamp_And_Sat_T4      VARCHAR2(255), -- ZClampAndSatT4
    Z_Clamp_And_Sat_T5      VARCHAR2(255), -- ZClampAndSatT5
    Z_Clamp_And_Sat_TAV     VARCHAR2(255), -- ZClampAndSatTAV
    Z_Clamp_And_Sat_C       VARCHAR2(255), -- ZClampAndSatC
    Z_Clamp_And_Sat_I       VARCHAR2(255), -- ZClampAndSatI
    Z_Sat_I                 VARCHAR2(255)  -- ZSatI
);