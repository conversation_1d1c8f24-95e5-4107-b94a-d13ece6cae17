package com.cirpoint.constant;

/**
 * 工时统计相关常量定义
 */
public class WorkTimeConstants {
    
    // Excel列名常量
    public static final String COLUMN_EMPLOYEE_NAME = "人员姓名";
    public static final String COLUMN_EMPLOYEE_ID = "工号";
    public static final String COLUMN_DEPARTMENT = "所属组织";
    public static final String COLUMN_ACCESS_POINT = "门禁点";
    public static final String COLUMN_CONTROLLER = "控制器";
    public static final String COLUMN_DIRECTION = "出/入";
    public static final String COLUMN_EVENT_TIME = "事件时间";
    
    // 输出Excel列名常量
    public static final String OUTPUT_COLUMN_SEQUENCE = "序号";
    public static final String OUTPUT_COLUMN_NAME = "姓名";
    public static final String OUTPUT_COLUMN_DATE = "日期";
    public static final String OUTPUT_COLUMN_DAILY_HOURS = "净时长";
    public static final String OUTPUT_COLUMN_AVERAGE_HOURS = "平均时长";
    
    // 方向常量
    public static final String DIRECTION_IN = "入";
    public static final String DIRECTION_OUT = "出";
    
    // 场所常量
    public static final String LOCATION_910 = "910厂房";
    public static final String LOCATION_920 = "920厂房";
    public static final String LOCATION_BUILDING_2 = "二号楼";
    public static final String LOCATION_OTHER = "其他场所";
    public static final String LOCATION_UNKNOWN = "未知场所";
    
    // 文件相关常量
    public static final String EXCEL_EXTENSION = ".xlsx";
    public static final String EXCEL_MIME_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    public static final long MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    public static final int MAX_RECORDS = 50000; // 最大记录数
    
    // 时间格式常量
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    
    // 数值格式常量
    public static final String DECIMAL_FORMAT = "%.2f";
    public static final int DECIMAL_SCALE = 2;
    
    // 验证规则常量
    public static final int MIN_NAME_LENGTH = 2;
    public static final int MAX_NAME_LENGTH = 10;
    public static final int EMPLOYEE_ID_LENGTH = 9;

    // 去重处理常量
    public static final long DUPLICATE_TIME_WINDOW_SECONDS = 120L; // 2分钟时间窗口
    public static final String DUPLICATE_GROUP_KEY_SEPARATOR = "|"; // 分组键分隔符

    // 迟到早退判定时间常量
    public static final int MORNING_LATE_HOUR = 8; // 上午迟到判定小时
    public static final int MORNING_LATE_MINUTE_WEEKDAY = 5; // 工作日上午迟到判定分钟（08:05）
    public static final int MORNING_LATE_MINUTE_SATURDAY = 30; // 周六上午迟到判定分钟（08:30）
    public static final int MORNING_EARLY_HOUR = 11; // 上午早退判定小时（11:00）
    public static final int MORNING_EARLY_MINUTE = 0; // 上午早退判定分钟
    public static final int AFTERNOON_LATE_HOUR = 12; // 下午迟到判定小时（12:30）
    public static final int AFTERNOON_LATE_MINUTE = 30; // 下午迟到判定分钟
    public static final int AFTERNOON_EARLY_HOUR = 16; // 下午早退判定小时（16:30）
    public static final int AFTERNOON_EARLY_MINUTE = 30; // 下午早退判定分钟
    
    // 错误信息常量
    public static final String ERROR_FILE_NOT_EXCEL = "请上传Excel格式的文件（.xlsx）";
    public static final String ERROR_FILE_TOO_LARGE = "文件大小不能超过100MB";
    public static final String ERROR_FILE_EMPTY = "文件内容为空";
    public static final String ERROR_MISSING_COLUMNS = "Excel文件缺少必需的列";
    public static final String ERROR_INVALID_DATA = "Excel文件包含无效数据";
    public static final String ERROR_NO_VALID_RECORDS = "未找到有效的打卡记录";
    public static final String ERROR_CALCULATION_FAILED = "工时计算失败";
    public static final String ERROR_DUPLICATE_PROCESSING_FAILED = "门禁数据去重处理失败";
    
    // 成功信息常量
    public static final String SUCCESS_FILE_PROCESSED = "文件处理成功";
    public static final String SUCCESS_CALCULATION_COMPLETED = "工时计算完成";
    public static final String SUCCESS_DUPLICATE_PROCESSING_COMPLETED = "门禁数据去重处理完成";
    
    // 默认值常量
    public static final double DEFAULT_HOURS = 0.0;
    public static final String DEFAULT_OUTPUT_FILENAME = "员工工时统计结果";
    
    // 私有构造函数，防止实例化
    private WorkTimeConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
