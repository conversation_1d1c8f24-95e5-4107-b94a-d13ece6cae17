package com.cirpoint.controller;

import cn.hutool.json.JSONObject;
import com.cirpoint.model.Result;
import com.cirpoint.service.FileService;
import com.cirpoint.util.FileDownloadUtil;
import java.io.File;
import java.io.IOException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件下载控制器
 */
@RestController
@RequestMapping("/file")
public class FileController {

	private final FileService fileService;

	@Autowired
	public FileController(FileService fileService) {
		this.fileService = fileService;
	}

	/**
	 * 从文件仓库下载文件
	 *
	 * @param fileName 文件名称
	 * @param filePath 文件相对路径（相对于文件仓库根目录）
	 * @return 文件下载响应
	 */
	@GetMapping("/download")
	public ResponseEntity<?> downloadFile(
			@RequestParam("fileName") String fileName,
			@RequestParam("filePath") String filePath) {
		return fileService.downloadFile(fileName, filePath);
	}

	/**
	 * 删除临时文件
	 *
	 * @return 删除结果
	 */
	@PostMapping("/delete/temps")
	public ResponseEntity<?> deleteTemporaryFiles() {
		JSONObject result = fileService.deleteTemporaryFiles();
		return ResponseEntity.ok(result);
	}

	/**
	 * 删除临时影像预览文件
	 *
	 * @return 删除结果
	 */
	@PostMapping("/delete/photo")
	public ResponseEntity<?> deleteTempPhotoFiles() {
		fileService.deleteTempPhotoFiles();
		return ResponseEntity.ok("删除临时影像文件成功");
	}

	/**
	 * 下载照片文件
	 *
	 * @param url 下载URL
	 * @return 下载结果
	 */
	@GetMapping("/download/photo")
	public ResponseEntity<?> downloadPhoto(@RequestParam("url") String url) {
		JSONObject result = fileService.downloadPhoto(url);
		return ResponseEntity.ok(result);
	}

	/**
	 * 导出型号研制进度
	 *
	 * @return Excel文件下载响应
	 */
	@PostMapping("/export/model/process")
	public ResponseEntity<?> exportModelProcess() {
		File file = fileService.exportModelProcess();
		return FileDownloadUtil.fileResponseAndDelete(file);
	}

	/**
	 * 获取文件大小
	 *
	 * @return 文件大小信息
	 */
	@PostMapping("/get/size")
	public ResponseEntity<String> getFileSize() {
		String fileSize = fileService.getFileSize();
		return ResponseEntity.ok()
				.contentType(MediaType.TEXT_HTML)
				.body(fileSize);
	}

	/**
	 * 更新型号文件大小统计
	 *
	 * @return 更新结果
	 */
	@PostMapping("/update/model/size")
	public ResponseEntity<?> updateModelFileSize() {
		fileService.updateModelFileSize();
		return Result.ok("更新型号文件大小统计成功");
	}

	/**
	 * 上传大文件（分片上传）
	 *
	 * @param file      上传的文件
	 * @param chunk     当前分片序号
	 * @param chunks    总分片数
	 * @param fileName  文件名
	 * @param extraData 额外参数
	 * @return 上传结果
	 */
	@PostMapping("/upload/big/file")
	public ResponseEntity<JSONObject> uploadBigFile(
			@RequestParam("file") MultipartFile file,
			@RequestParam(value = "chunk", required = false) Integer chunk,
			@RequestParam(value = "chunks", required = false) Integer chunks,
			@RequestParam("name") String fileName,
			@RequestParam("reqIdent") String reqIdent,
			@RequestParam(value = "extraData", required = false) String extraData) {

		JSONObject result = fileService.uploadBigFile(file, chunk, chunks, fileName, reqIdent, extraData);
		return ResponseEntity.ok(result);
	}

	/**
	 * 删除指定路径的文件
	 *
	 * @param filePath 文件路径
	 * @return ResponseEntity
	 */
	@PostMapping("/delete")
	public ResponseEntity<?> deleteFile(@RequestParam String filePath) {
		return ResponseEntity.ok(fileService.deleteFile(filePath));
	}

	/**
	 * 上传测试文件
	 *
	 * @param file 上传的文件数组
	 * @return ResponseEntity
	 */
	@PostMapping("/upload")
	public ResponseEntity<JSONObject> uploadFile(@RequestParam("file") MultipartFile file) throws IOException {
		return ResponseEntity.ok(fileService.uploadFile(file));
	}

	/**
	 * 上传Excel模板文件
	 *
	 * @param files 上传的文件列表
	 * @return 上传结果
	 */
	@PostMapping("/upload/excel/tpl")
	public ResponseEntity<?> uploadExcelTemplates(@RequestParam("file") MultipartFile[] files) throws IOException {
		return ResponseEntity.ok(fileService.uploadExcelTemplates(files));
	}
}
