package com.cirpoint.util;

import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import java.io.File;
import java.util.Comparator;
import java.util.List;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

public class DealThings {

	public static void main(String[] args) {
		TimeInterval timer = new TimeInterval();
		executeThings();
		System.out.println(timer.intervalPretty());
	}

	public static void executeThings() {
		String thingFilePath = "D:\\Program Files\\Apache Software Foundation\\Tomcat 8.5\\webapps\\DataPackageManagement\\twx\\twxXml\\Things.xml";
		String thingTemplatesFilePath = "D:\\Program Files\\Apache Software Foundation\\Tomcat 8.5\\webapps\\DataPackageManagement\\twx\\twxXml\\ThingTemplates_Thing.Tpl.OnlineConfirm.xml";
		String outPath = "D:\\Program Files\\Apache Software Foundation\\Tomcat 8.5\\webapps\\DataPackageManagement\\twx\\services\\";
		FileUtil.del(outPath);
		createThingFile(dealThings(thingFilePath, "Thing"), outPath);
		createThingFile(dealThings(thingTemplatesFilePath, "ThingTemplate"), outPath);
	}

	public static void createThingFile(JSONArray things, String outPath) {

		for (int x = 0; x < things.size(); x++) {
			JSONObject thing = things.getJSONObject(x);
			String thingName = thing.getStr("name");
			JSONArray services = thing.getJSONArray("services");
			if (services.isEmpty()) {
				continue;
			}
			String thingFolderPath = outPath + File.separator + thingName;
			FileUtil.mkdir(thingFolderPath);

			for (int i = 0; i < services.size(); i++) {
				JSONObject service = services.getJSONObject(i);
				String serviceName = service.getStr("name");
				String handlerName = service.getStr("handlerName");
				JSONArray parameters = service.getJSONArray("parameters");
				String noteStr = "";
				StrBuilder notes = StrUtil.strBuilder();

				notes.append("/**").append(StrUtil.CRLF);
				notes.append(" * @author" + StrUtil.TAB + "wanghq" + StrUtil.TAB + "自动创建").append(StrUtil.CRLF);
				notes.append(" * @function" + StrUtil.TAB).append(serviceName).append(StrUtil.CRLF);
				notes.append(" * @type" + StrUtil.TAB + "{").append(handlerName).append("}").append(StrUtil.CRLF);
				notes.append(" * @description" + StrUtil.TAB).append(service.getStr("description")).append(StrUtil.CRLF);
				if (!parameters.isEmpty()) {
					notes.append(" *").append(StrUtil.CRLF);
				}
				for (int j = 0; j < parameters.size(); j++) {
					JSONObject field = parameters.getJSONObject(j);
					notes.append(" * @param" + StrUtil.TAB + "{").append(field.getStr("baseType")).append("}").append(StrUtil.TAB).append(field.getStr("name")).append(StrUtil.TAB).append(field.getStr("description")).append(StrUtil.CRLF);
				}
				notes.append(" *").append(StrUtil.CRLF);
				notes.append(" * @returns" + StrUtil.TAB + "{").append(service.getStr("resultType")).append("}")
						.append(StrUtil.CRLF);
				notes.append(" */").append(StrUtil.CRLF);

				String fileFormat = ".js";
				if (!handlerName.equals("Script")) {
					fileFormat = ".sql";
				}
				String serviceFilePath = thingFolderPath + File.separator + serviceName + fileFormat;
				String code = service.getStr("code");

				if (StrUtil.isNotBlank(code)) {
					if (!(code.startsWith("/**") && code.contains("@author"))) {
						noteStr = notes.toString();
					}
					FileUtil.writeUtf8String(noteStr + code, serviceFilePath);
				}

			}
		}

	}

	public static JSONArray dealThings(String thingFilePath, String type) {
		Document document;
		JSONArray things = new JSONArray();
		try {
			SAXReader saxReader = new SAXReader();
			document = saxReader.read(new File(thingFilePath));
			// Entities
			Element rootEle = document.getRootElement();
			Element thingsEle = rootEle.element(type.equals("Thing") ? "Things" : "ThingTemplates");
			List<Element> thingEles = thingsEle.elements(type.equals("Thing") ? "Thing" : "ThingTemplate");
			for (Element thingEle : thingEles) {
				JSONObject thing = new JSONObject();
				thing.set("name", thingEle.attributeValue("name"));
				Element thingShapeEle = thingEle.element("ThingShape");
				Element serviceDefinitionsEle = thingShapeEle.element("ServiceDefinitions");
				List<Element> serviceDefinitionEles = serviceDefinitionsEle.elements();
				JSONArray services = new JSONArray();
				for (Element serviceDefinitionEle : serviceDefinitionEles) {
					JSONObject service = new JSONObject();
					service.set("name", serviceDefinitionEle.attributeValue("name"));
					service.set("description", serviceDefinitionEle.attributeValue("description"));
					// Service 返回类型
					String resultType = serviceDefinitionEle.element("ResultType").attributeValue("baseType");
					service.set("resultType", resultType);

					Element parameterDefinitionsEle = serviceDefinitionEle.element("ParameterDefinitions");
					List<Element> fieldDefinitionEles = parameterDefinitionsEle.elements();

					JSONArray parameters = new JSONArray();
					for (Element fieldDefinitionEle : fieldDefinitionEles) {

						JSONObject field = new JSONObject();
						field.set("baseType", fieldDefinitionEle.attributeValue("baseType"));
						field.set("description", fieldDefinitionEle.attributeValue("description"));
						field.set("name", fieldDefinitionEle.attributeValue("name"));
						field.set("ordinal", fieldDefinitionEle.attributeValue("ordinal"));
						parameters.add(field);
					}
					parameters.sort(Comparator.comparingInt(obj -> ((JSONObject) obj).getInt("ordinal")));
					service.set("parameters", parameters);
					services.add(service);
				}

				Element serviceImplementationsEle = thingShapeEle.element("ServiceImplementations");

				List<Element> serviceImplementationEles = serviceImplementationsEle.elements();

				for (Element serviceImplementationEle : serviceImplementationEles) {
					String name = serviceImplementationEle.attributeValue("name");
					String handlerName = serviceImplementationEle.attributeValue("handlerName");
					for (int i = 0; i < services.size(); i++) {
						JSONObject service = services.getJSONObject(i);
						String serviceName = service.getStr("name");
						if (serviceName.equals(name)) {

							Element configurationTableEle = serviceImplementationEle.element("ConfigurationTables")
									.element("ConfigurationTable");

							Element rowEle = configurationTableEle.element("Rows").element("Row");
							String code = "";
							try {
								if (handlerName.equals("Script")) {
									Element codeEle = rowEle.element("code");
									code = codeEle.getText();
								} else {
									Element sqlEle = rowEle.element("sql");
									code = sqlEle.getText();
								}

							} catch (Exception e) {
								System.out.println(thingEle.attributeValue("name") + ":" + serviceName + ",没有code");
							}
							service.set("handlerName", handlerName);
							service.set("code", code);
							break;
						}
					}
				}
				thing.set("services", services);
				things.add(thing);
			}
		} catch (Exception ignored) {
		}
		return things;
	}
}
