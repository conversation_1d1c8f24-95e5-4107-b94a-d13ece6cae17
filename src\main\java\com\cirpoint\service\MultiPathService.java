package com.cirpoint.service;

import com.cirpoint.model.PathEntry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 多路径管理服务
 */
@Service
public class MultiPathService {

    // 配置目录
    private static final String CONFIG_DIR = ".filehandle";

    // 多路径配置文件
    private static final String MULTI_PATHS_FILE = "multi_paths.json";

    // 路径列表
    private List<PathEntry> pathEntries = new ArrayList<>();

    // JSON对象映射器
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 初始化方法，在服务启动时加载路径数据
     */
    @PostConstruct
    public void init() {
        // 确保配置目录存在
        File configDir = new File(System.getProperty("user.home"), CONFIG_DIR);
        if (!configDir.exists()) {
            configDir.mkdirs();
        }

        // 加载路径数据
        loadPathEntries();
    }

    /**
     * 加载路径数据
     */
    private void loadPathEntries() {
        File pathsFile = getPathsFile();

        if (pathsFile.exists()) {
            try {
                pathEntries = objectMapper.readValue(pathsFile, new TypeReference<List<PathEntry>>() {});
            } catch (IOException e) {
                System.err.println("加载多路径配置失败: " + e.getMessage());
                pathEntries = new ArrayList<>();
            }
        }
    }

    /**
     * 保存路径数据
     */
    private void savePathEntries() {
        File pathsFile = getPathsFile();

        try {
            objectMapper.writeValue(pathsFile, pathEntries);
        } catch (IOException e) {
            System.err.println("保存多路径配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取配置文件
     */
    private File getPathsFile() {
        File configDir = new File(System.getProperty("user.home"), CONFIG_DIR);
        return new File(configDir, MULTI_PATHS_FILE);
    }

    /**
     * 获取所有路径
     */
    public List<PathEntry> getAllPaths() {
        return new ArrayList<>(pathEntries);
    }

    /**
     * 获取所有已启用的路径
     */
    public List<PathEntry> getEnabledPaths() {
        return pathEntries.stream()
                .filter(PathEntry::isEnabled)
                .collect(Collectors.toList());
    }

    /**
     * 添加新路径
     *
     * @param path 路径字符串
     * @return 添加结果
     */
    public boolean addPath(String path) {
        if (!StringUtils.hasText(path)) {
            return false;
        }

        // 检查路径是否有效
        File dir = new File(path);
        if (!dir.exists() || !dir.isDirectory()) {
            return false;
        }

        // 检查路径是否已存在
        boolean exists = pathEntries.stream()
                .anyMatch(entry -> entry.getPath().equals(path));

        if (exists) {
            return false;
        }

        // 创建新的路径条目
        String id = UUID.randomUUID().toString();
        PathEntry newEntry = new PathEntry(id, path, false);

        // 添加到列表并保存
        pathEntries.add(newEntry);
        savePathEntries();

        return true;
    }

    /**
     * 删除路径
     *
     * @param id 路径ID
     * @return 删除结果
     */
    public boolean removePath(String id) {
        if (!StringUtils.hasText(id)) {
            return false;
        }

        int initialSize = pathEntries.size();

        // 移除指定ID的路径
        pathEntries.removeIf(entry -> entry.getId().equals(id));

        // 如果列表大小变化，说明删除成功
        if (pathEntries.size() < initialSize) {
            savePathEntries();
            return true;
        }

        return false;
    }

    /**
     * 启用路径
     *
     * @param id 路径ID
     * @return 启用结果
     */
    public boolean enablePath(String id) {
        if (!StringUtils.hasText(id)) {
            return false;
        }

        // 查找并启用指定ID的路径
        for (PathEntry entry : pathEntries) {
            if (entry.getId().equals(id)) {
                entry.setEnabled(true);
                savePathEntries();
                return true;
            }
        }

        return false;
    }

    /**
     * 禁用路径
     *
     * @param id 路径ID
     * @return 禁用结果
     */
    public boolean disablePath(String id) {
        if (!StringUtils.hasText(id)) {
            return false;
        }

        // 查找并禁用指定ID的路径
        for (PathEntry entry : pathEntries) {
            if (entry.getId().equals(id)) {
                entry.setEnabled(false);
                savePathEntries();
                return true;
            }
        }

        return false;
    }

    /**
     * 获取单个路径信息
     *
     * @param id 路径ID
     * @return 路径信息，如果不存在则返回null
     */
    public PathEntry getPath(String id) {
        if (!StringUtils.hasText(id)) {
            return null;
        }

        // 查找并返回指定ID的路径
        for (PathEntry entry : pathEntries) {
            if (entry.getId().equals(id)) {
                return entry;
            }
        }

        return null;
    }
}
