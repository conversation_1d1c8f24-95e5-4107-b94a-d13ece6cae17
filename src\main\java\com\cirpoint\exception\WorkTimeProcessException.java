package com.cirpoint.exception;

/**
 * 工时处理异常类
 * 用于处理工时统计过程中的各种异常情况
 */
public class WorkTimeProcessException extends Exception {
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 用户友好的错误信息
     */
    private String userMessage;
    
    /**
     * 构造函数
     * @param message 异常信息
     */
    public WorkTimeProcessException(String message) {
        super(message);
        this.userMessage = message;
    }
    
    /**
     * 构造函数
     * @param message 异常信息
     * @param cause 原因异常
     */
    public WorkTimeProcessException(String message, Throwable cause) {
        super(message, cause);
        this.userMessage = message;
    }
    
    /**
     * 构造函数
     * @param errorCode 错误代码
     * @param userMessage 用户友好的错误信息
     */
    public WorkTimeProcessException(String errorCode, String userMessage) {
        super(userMessage);
        this.errorCode = errorCode;
        this.userMessage = userMessage;
    }
    
    /**
     * 构造函数
     * @param errorCode 错误代码
     * @param userMessage 用户友好的错误信息
     * @param cause 原因异常
     */
    public WorkTimeProcessException(String errorCode, String userMessage, Throwable cause) {
        super(userMessage, cause);
        this.errorCode = errorCode;
        this.userMessage = userMessage;
    }
    
    /**
     * 获取错误代码
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * 获取用户友好的错误信息
     * @return 用户友好的错误信息
     */
    public String getUserMessage() {
        return userMessage;
    }
    
    // 预定义的错误代码常量
    public static final String FILE_FORMAT_ERROR = "FILE_FORMAT_ERROR";
    public static final String FILE_SIZE_ERROR = "FILE_SIZE_ERROR";
    public static final String DATA_VALIDATION_ERROR = "DATA_VALIDATION_ERROR";
    public static final String CALCULATION_ERROR = "CALCULATION_ERROR";
    public static final String IO_ERROR = "IO_ERROR";
    public static final String UNKNOWN_ERROR = "UNKNOWN_ERROR";
    
    // 便捷的静态工厂方法
    public static WorkTimeProcessException fileFormatError(String message) {
        return new WorkTimeProcessException(FILE_FORMAT_ERROR, "文件格式错误：" + message);
    }
    
    public static WorkTimeProcessException fileSizeError(String message) {
        return new WorkTimeProcessException(FILE_SIZE_ERROR, "文件大小错误：" + message);
    }
    
    public static WorkTimeProcessException dataValidationError(String message) {
        return new WorkTimeProcessException(DATA_VALIDATION_ERROR, "数据验证错误：" + message);
    }
    
    public static WorkTimeProcessException calculationError(String message) {
        return new WorkTimeProcessException(CALCULATION_ERROR, "计算错误：" + message);
    }
    
    public static WorkTimeProcessException ioError(String message, Throwable cause) {
        return new WorkTimeProcessException(IO_ERROR, "文件处理错误：" + message, cause);
    }
    
    public static WorkTimeProcessException unknownError(String message, Throwable cause) {
        return new WorkTimeProcessException(UNKNOWN_ERROR, "未知错误：" + message, cause);
    }
}
