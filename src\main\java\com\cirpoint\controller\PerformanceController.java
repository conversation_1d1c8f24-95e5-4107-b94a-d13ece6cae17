package com.cirpoint.controller;

import com.cirpoint.aspect.PerformanceInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 性能统计控制器
 * 提供查看和管理性能统计数据的接口
 */
@RestController
@RequestMapping("/monitor")
public class PerformanceController {

    @Autowired
    private PerformanceInterceptor performanceInterceptor;

    /**
     * 获取指定方法的性能统计信息
     */
    @GetMapping(value = "/{className}/{methodName}", produces = MediaType.TEXT_HTML_VALUE)
    public String getMethodStatistics(@PathVariable String className, 
                                    @PathVariable String methodName) {
        // 尝试所有可能的请求类型
        String[] requestTypes = {"GET", "POST", "PUT", "DELETE", "PATCH"};
        for (String requestType : requestTypes) {
            String fullMethodName = requestType + " " + className + "." + methodName;
            String result = performanceInterceptor.getMethodStatistics(fullMethodName);
            if (!result.contains("未找到方法")) {
                return result;
            }
        }
        // 如果没有找到带请求类型的统计信息，尝试不带请求类型的方法名
        return performanceInterceptor.getMethodStatistics(className + "." + methodName);
    }

    /**
     * 获取所有方法的性能统计信息
     */
    @GetMapping(value = "/all", produces = MediaType.TEXT_HTML_VALUE)
    public String getAllMethodStatistics() {
        return performanceInterceptor.getAllMethodStatistics();
    }

    /**
     * 重置指定方法的性能统计信息
     */
    @DeleteMapping("/{className}/{methodName}")
    public String resetMethodStatistics(@PathVariable String className, 
                                      @PathVariable String methodName) {
        // 重置所有请求类型的统计信息
        String[] requestTypes = {"GET", "POST", "PUT", "DELETE", "PATCH"};
        boolean found = false;
        for (String requestType : requestTypes) {
            String fullMethodName = requestType + " " + className + "." + methodName;
            if (performanceInterceptor.resetMethodStatistics(fullMethodName)) {
                found = true;
            }
        }
        // 也尝试重置不带请求类型的方法名
        if (performanceInterceptor.resetMethodStatistics(className + "." + methodName)) {
            found = true;
        }
        return found ? "已重置方法统计信息：" + className + "." + methodName 
                    : "未找到方法：" + className + "." + methodName;
    }

    /**
     * 重置所有性能统计信息
     */
    @DeleteMapping("")
    public String resetAllStatistics() {
        performanceInterceptor.resetAllStatistics();
        return "已重置所有统计信息";
    }
}
