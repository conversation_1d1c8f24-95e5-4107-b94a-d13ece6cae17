# Maven 常用命令说明

## 构建相关命令

### 打包命令
```bash
mvn clean package -DskipTests
```
将项目打包为可发布的格式（如JAR、WAR），跳过测试以加快构建速度。

### 下载依赖
```bash
mvn dependency:go-offline
```
下载所有依赖，确保构建时不需要从远程仓库下载。

### 编译命令
```bash
mvn compile
```
编译项目的源代码，生成class文件。

### 测试命令
```bash
mvn test
```
运行项目中的单元测试用例。

### 安装到本地仓库
```bash
mvn install
```
将项目构建并安装到本地Maven仓库，使其他本地项目可以依赖。

### 部署到远程仓库
```bash
mvn deploy
```
将最终的包发布到远程Maven仓库，供他人使用。

## 项目维护命令

### 清理项目
```bash
mvn clean
```
删除项目的构建目录（通常是target目录），清理之前的构建文件。

### 生成站点文档
```bash
mvn site
```
生成项目的文档网站，包含项目信息、依赖、测试报告等。

### 查看依赖树
```bash
mvn dependency:tree
```
以树形结构显示项目的依赖关系，方便分析依赖冲突。

### 强制更新依赖
```bash
mvn clean install -U
```
强制检查并更新所有依赖的最新版本，解决依赖问题。

## 插件相关命令

### 运行特定插件的目标
```bash
mvn plugin:goal
```
执行Maven插件的特定目标，例如`mvn jetty:run`启动Jetty服务器。

### 跳过特定阶段
```bash
mvn install -Dmaven.test.skip=true
```
跳过测试编译和执行，比`-DskipTests`更彻底。

### 离线模式构建
```bash
mvn -o install
```
在离线模式下构建项目，不检查远程仓库更新。

## 多模块项目命令

### 构建指定模块
```bash
mvn -pl module-name clean install
```
只构建指定的模块，而不是整个项目。

### 构建模块及其依赖
```bash
mvn -pl module-name -am clean install
```
构建指定的模块及其依赖的模块。

### 从指定模块开始构建
```bash
mvn -rf module-name clean install
```
从指定的模块开始继续构建项目。
