
import com.actionsoft.bpms.api.OpenApiClient;
import com.actionsoft.sdk.service.response.ListMapResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 示例：调用 Actionsoft BPM OpenAPI 执行 BO 查询
 */
public class TestBpm {

	public static void main(String[] args) {
		// 1. 基本连接参数
		String apiServer = "http://10.124.0.41:8088/portal/openapi";
		String apiMethod = "bo.query";
		String accessKey = "bpmApiAccessKey888";
		String secret = "bpmApiSecret888";

		// 2. 组装请求参数
		Map<String, Object> param = new HashMap<>();
		param.put("boName", "BO_EU_ML_SBT2"); // 必填：BO 表名称

		// 3. 查询条件
		JSONArray querys = new JSONArray();

		// — 编号查询
		List<String> query1 = new ArrayList<>();
		query1.add("ZCBH=");                  // 字段＋运算符
		query1.add("102060201201500018");     // 值
		querys.add(query1);

		// — 名称查询
		List<String> query2 = new ArrayList<>();
		query2.add("ZCMC like ");             // 模糊查询
		query2.add("%试验厂房%");
		querys.add(query2);

		param.put("querys", querys.toJSONString()); // 非必填：查询条件 (JSON 字符串)
		// 条件格式示例：按 F1 > 0.2 查询 —— [[\"F1 >\",\"0.2\"]]

		// 4. 其它可选参数
		param.put("selectClause", "");   // 自定义查询语句
		param.put("orderBy", "");        // 排序字段，多个字段用 , 分隔，例如：F1,F2 DESC
		param.put("firstRow", "0");      // 首记录 (>=0)
		param.put("rowCount", "100");    // 返回记录数

		// 5. 调用 OpenAPI
		OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
		ListMapResponse r = client.exec(apiMethod, param, ListMapResponse.class);

		// 6. 输出结果
		System.out.println(JSONObject.toJSONString(r.getData()));
	}
}
