<div class="apiDetail">
<div>
	<h2><span>JSON / Function(treeId, treeNode)</span><span class="path">setting.view.</span>nodeClasses</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Use className to set text style, only applies to &lt;A&gt; object in the node DOM. (Decoupling CSS from JavaScript)</p>
			<p>Default: {add: [], remove: []}</p>
			<p class="highlight_red">v3.5.43+</p>
		</div>
	</div>
	<h3>JSON Format</h3>
	<div class="desc">
		<p>add: Array, The className collection to be added. e.g. <span class="highlight_red">{add: ['highlight']}</span></p>
		<p>remove: Array, The className collection to be removed. e.g. <span class="highlight_red">{remove: ['highlight']}</span></p>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>treeId</b><span>String</span></h4>
	<p>zTree unique identifier: <b class="highlight_red">treeId</b>.</p>
	<h4 class="topLine"><b>treeNode</b><span>JSON</span></h4>
	<p>JSON data object of the node which use the personalized text style</p>
	<h4 class="topLine"><b>Return </b><span>JSON</span></h4>
	<p>Return value is same as 'JSON Format'. e.g. <span class="highlight_red">{add: ['highlight'], remove: ['hide']}</span></p>
	</div>
	<h3>Examples of setting & function</h3>
	<h4>1. Set the node name's color to red</h4>
	<pre xmlns=""><code><style>.ztree li > a.highlight { color: red; }</style>
		var setting = {
			view: {
				nodeClasses : {add: ['highlight']}
			}
		};</code></pre>
	<h4>2. Remove the node name's special className: highlight</h4>
	<pre xmlns=""><code>var setting = {
		view: {
			nodeClasses : {remove: ['highlight']}
		}
	};</code></pre>	
	<h4>3. Set the root node name's color to red</h4>
	<pre xmlns=""><code>function setNodeClasses(treeId, treeNode) {
	return treeNode.level == 0 ? {add: ['highlight']} : {remove: ['highlight']};
};
var setting = {
	view: {
		nodeClasses: setNodeClasses
	}
};</code></pre>
</div>
</div>